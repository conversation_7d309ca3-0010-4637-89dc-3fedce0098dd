{"name": "schedulerprd", "script": "./server.js", "instances": "1", "env_prd": {"RABBIT_MQ_SERVICE": "rabbitmq-service:3900", "HAS_SERVER": 1, "AUTH_SERVICE": "auth-service:3800", "TENANT_SERVICE": "tenant-service:3801", "NODE_ENV": "qual", "CONTAINER_ADDRESS": "*************:4318", "CONTAINER_NAME": "schedulerprd"}, "exec_mode": "cluster", "wait_ready": true, "listen_timeout": 5000, "error_file": "/dev/null", "out_file": "/dev/null"}