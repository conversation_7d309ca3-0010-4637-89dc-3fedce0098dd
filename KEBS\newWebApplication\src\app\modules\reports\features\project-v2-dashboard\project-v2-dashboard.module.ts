import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SharedLazyLoadedModule } from '../../../project-management/shared-lazy-loaded/shared-lazy-loaded.module';


import { ProjectV2DashboardRoutingModule } from './project-v2-dashboard-routing.module';
import { DashboardLandingPageComponent } from './screens/dashboard-landing-page/dashboard-landing-page.component';
import { BarChartWidgetComponent } from './widgets/bar-chart-widget/bar-chart-widget.component';
import { DonutPieChartWidgetComponent } from './widgets/donut-pie-chart-widget/donut-pie-chart-widget.component';
import { StackedBarChartWidgetComponent } from './widgets/stacked-bar-chart-widget/stacked-bar-chart-widget.component';
import { MiniBarChartComponent } from './widgets/mini-bar-chart/mini-bar-chart.component';
import { MiniDonutChartCardComponent } from './widgets/mini-donut-chart-card/mini-donut-chart-card.component';
import { MiniPercentageCardComponent } from './widgets/mini-percentage-card/mini-percentage-card.component';


import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TooltipModule } from 'ng2-tooltip-directive';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgxDaterangepickerMd } from 'ngx-daterangepicker-material';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTableModule } from '@angular/material/table';
import {
  DxChartModule,
  DxDataGridModule,
  DxPieChartModule,
  DxTooltipModule,
  DxVectorMapModule
} from 'devextreme-angular';
import { CustomToasterComponent } from './components/custom-toaster/custom-toaster.component';
import { GeographyChartComponent } from './widgets/geography-chart/geography-chart.component';
import { SummaryDialogComponent } from './widgets/summary-dialog/summary-dialog.component';

@NgModule({
  declarations: [DashboardLandingPageComponent, BarChartWidgetComponent, DonutPieChartWidgetComponent, StackedBarChartWidgetComponent, MiniBarChartComponent, MiniDonutChartCardComponent, MiniPercentageCardComponent, CustomToasterComponent, GeographyChartComponent, SummaryDialogComponent],
  imports: [
    CommonModule,
    ProjectV2DashboardRoutingModule,
    MatIconModule,
    MatDividerModule,
    MatMenuModule,
    MatTooltipModule,
    InfiniteScrollModule,
    TooltipModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    DxChartModule,
    DxDataGridModule,
    DxPieChartModule,
    DxTooltipModule,
    MatButtonToggleModule,
    SharedLazyLoadedModule,
    DragDropModule,
    OverlayModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    NgxDaterangepickerMd.forRoot(),
    MatDialogModule,
    DxVectorMapModule,
    MatTableModule
  ]
})
export class ProjectV2DashboardModule { }
