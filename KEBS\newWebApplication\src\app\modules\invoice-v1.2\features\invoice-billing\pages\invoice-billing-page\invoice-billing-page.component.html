<div class="container-fluid invoice-billed-component" style="overflow: hidden;" *ngIf="show">
  <div class="row">
    <div class="col-3 card cardHeight">
      <div class="row d-flex align-items-center pl-0 pb-2 pr-2">
        <div class="col-1 p-0">
          <button class="icon-tray-button" mat-icon-button (click)="back()" matTooltip="Back">
            <mat-icon class="smallCardIcon">chevron_left</mat-icon>
          </button>
        </div>
        <div class="col-10 p-0">
          <div class="col-10 d-flex">
            <span class="p-1 mx-2 invoice-no">
              Invoice No.<span class="p-1 mx-2" style="word-wrap: break-word; white-space: normal;">
                {{ invoiceInfo?.invoice_no }}
              </span>
            </span>

          </div>          
        </div>
        <div class="col-1 p-0">
          <button class="icon-tray-button pr-3" mat-icon-button matTooltip="Go to project"
            (click)="launchProjectFromInvoice(projectId, projectName, itemId, itemName)">
            <mat-icon class="smallCardIcon">launch</mat-icon>
          </button>
        </div>

      </div>

      <hr class="mt-1 mb-1">
      <div class="row mt-4 mb-2" style="height: 30px; justify-content: center;">
        <div class="col-2 icon-container">
          <div class="icon" style="background: #E7F9F9; cursor: pointer;" (click)="viewInvoice()">
            <mat-icon class="iconClass pt-2">receipt</mat-icon>
          </div>
          <span class="hover-text">Invoice</span>
        </div>
        <div class="col-2 icon-container" *ngIf="(invoiceInfo?.status == 9 && isSendMailAuthorized )|| (invoiceInfo?.milestone_type == 6 && isSendMailAuthorized )">
          <div class="icon" style="background: #EAEEFD; cursor: pointer;"
          (click)="sendMail(invoiceInfo?.billing_id,invoiceInfo?.milestone_id,invoiceInfo?.gantt_id, invoiceInfo?.milestone_type)">
            <mat-icon class="iconClass pt-2">send</mat-icon>
            <mat-icon class="checkmark" *ngIf="invoiceInfo?.mail_sent_counter > 0 && invoiceInfo?.mail_sent_status == 1">check_circle</mat-icon>
          </div>
          <span class="hover-text">Send Mail</span>
        </div>
        <div class="col-2 icon-container" *ngIf="invoiceInfo?.status == 9 || invoiceInfo?.milestone_type == 6">
          <div class="icon" style="background: #F1EAFA; cursor: pointer;" (click)="markAsSent(invoiceInfo?.billing_id)">
            <mat-icon class="iconClass pt-2" [ngClass]="{'green-icon': invoiceInfo?.mail_sent_status == 2}">mark_email_read</mat-icon>
          </div>
          <span class="hover-text">Mark as Sent</span>
        </div>
        <div class="col-2 icon-container">
          <div class="icon" style="background: #FDEAF4; cursor: pointer;" (click)='openAnnexure()'>
            <mat-icon class="iconClass pt-2">description</mat-icon>
          </div>
          <span class="hover-text">Annexure</span>
        </div>
        <div class="col-2 icon-container">
          <div class="icon" style="background: #CDDEEE; cursor: pointer;" (click)='addNotes()'>
            <mat-icon class="iconClass pt-2" style="width: 18px !important;">chat_bubble_outline</mat-icon>
            <mat-icon class="checkmark" *ngIf="isCommentPresent">check_circle</mat-icon>
          </div>
          <span class="hover-text">Comments</span>
        </div>

      </div>
      <div class="row mt-3" style="background-color:#F7F9FB; height: 145px; position: relative;">
        <div class="col pt-3 pb-3">
          <div class="row position-relative align-items-center mt-2" style="height: 43px;">
            <div class="le-circle"></div>
            <div class="line"></div>
            <div class="col" style="margin-left: 20px;">
              <div class="row labelName">Legal Entity</div>
              <div class="row value text-wrap">{{ invoiceInfo?.entity_name }}</div>
            </div>
          </div>
          <div class="row position-relative align-items-center" style="height: 72px;">
            <div class="cus-circle"></div>
            <div class="col" style="margin-left: 20px;">
              <div class="row labelName">Customer</div>
              <div class="row value text-wrap">{{ invoiceInfo?.customer_name }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="row d-flex pt-2 pb-2" *ngIf="fieldKeyConfig?.milestone_name?.is_active">
        <div class="col-1 p-0 m-0">
          <div class="icon" [style.background-color]="fieldKeyConfig?.milestone_name?.icon_background_color">
            <mat-icon class="iconClass pt-2"
              [style.color]="fieldKeyConfig?.milestone_name?.icon_color">{{fieldKeyConfig?.milestone_name?.icon ?
              fieldKeyConfig?.milestone_name?.icon : 'crisis_alert'}}</mat-icon>
          </div>
        </div>
        <div class="col p-0" style="display: block;">
          <div class="labelName">{{fieldKeyConfig?.milestone_name?.label_name ?
            fieldKeyConfig?.milestone_name?.label_name : 'Milestone Name'}}</div>
          <div class="value text-wrap">{{ invoiceInfo?.milestone_name }}</div>
        </div>
      </div>
      <hr class="mt-1 mb-1">
      <div class="row d-flex pt-2 pb-2" *ngIf="fieldKeyConfig?.cost_center?.is_active">
        <div class="col-1 p-0 m-0">
          <div class="icon" [style.background-color]="fieldKeyConfig?.cost_center?.icon_background_color">
            <mat-icon class="iconClass pt-2"
              [style.color]="fieldKeyConfig?.cost_center?.icon_color">{{fieldKeyConfig?.cost_center?.icon ?
              fieldKeyConfig?.cost_center?.icon : 'account_balance'}}</mat-icon>
          </div>
        </div>
        <div class="col p-0" style="display: block;">
          <div class="labelName">{{fieldKeyConfig?.cost_center?.label_name ? fieldKeyConfig?.cost_center?.label_name :
            'Cost Center'}}</div>
          <div class="value text-wrap">{{ invoiceInfo?.cost_center }}</div>
        </div>
      </div>
      <hr class="mt-1 mb-1">
      <div class="row d-flex pt-2 pb-2" *ngIf="fieldKeyConfig?.service_type?.is_active">
        <div class="col-1 p-0 m-0">
          <div class="icon" [style.background-color]="fieldKeyConfig?.service_type?.icon_background_color">
            <mat-icon class="iconClass pt-2"
              [style.color]="fieldKeyConfig?.service_type?.icon_color">{{fieldKeyConfig?.service_type?.icon ?
              fieldKeyConfig?.service_type?.icon : 'construction'}}</mat-icon>
          </div>
        </div>
        <div class="col p-0" style="display: block;">
          <div class="labelName">{{fieldKeyConfig?.service_type?.label_name ? fieldKeyConfig?.service_type?.label_name :
            'Service Type'}}</div>
          <div class="value"> {{ invoiceInfo?.service_type }}</div>
        </div>
      </div>
      <hr class="mt-1 mb-1">
      <div class="row d-flex pt-2 pb-2" *ngIf="fieldKeyConfig?.amount_to_be_Collected?.is_active">
        <div class="col-1 p-0 m-0">
          <div class="icon" [style.background-color]="fieldKeyConfig?.amount_to_be_Collected?.icon_background_color">
            <mat-icon class="iconClass pt-2"
              [style.color]="fieldKeyConfig?.amount_to_be_Collected?.icon_color">{{fieldKeyConfig?.amount_to_be_Collected?.icon
              ? fieldKeyConfig?.amount_to_be_Collected?.icon : 'account_balance_wallet'}}</mat-icon>
          </div>
        </div>
        <div class="col p-0" style="display: block;">
          <div class="labelName">{{fieldKeyConfig?.amount_to_be_Collected?.label_name ?
            fieldKeyConfig?.amount_to_be_Collected?.label_name : 'Planned Collection'}}</div>
          <div class="value"><span>{{fixNumber(activityForm.value.amountToBeCollected)}}
              <span style="padding-left: 3px;">{{ activityForm.value.invoiceRaisedCurrency }}</span></span></div>
        </div>
      </div>
    </div>
    <div class="col ml-2 mr-0 pr-0 cardHeight">

      <div class="row card">
        <div class="col p-1">
          <div class="row">
            <div class="col-3">
              <div class="icon-container" style="display: flex;">
                <div class="row pb-2 pt-2 amount-label">Invoice Value</div>
                <mat-icon class="change-icon pl-2 pt-1" matTooltip="Next currency" (click)="changeCurrency()"
                  style="display: flex; justify-content: end; font-size: 19px; cursor: pointer;">loop</mat-icon>

              </div>
              <ng-container>
                <div class="row amount-value">{{currentCurrency}} {{fixNumberOnUI(invoiceValue)}}
                  <mat-icon class="pl-1" [tooltip]="invoiceVal" content-type="template" placement="bottom"
                    [max-width]="1200" [options]="{
                      'tooltip-class': 'tooltip-feedback'
                    }"
                    style="font-size: 14px; color: #6E7B8F; margin-top: -1px; margin-left: 2px; font-weight: 600;">info_outline</mat-icon>
                </div>
                <ng-template #invoiceVal>
                  <div class="">
                    <div class="row mb-1">
                      <span>Invoice Value Summary</span>
                    </div>
                    <div class="row">
                      <div class="col">
                        SubTotal
                      </div>
                      <div class="col">
                        {{currentCurrency}}
                      </div>
                      <div class="col">
                        {{fixNumber(subTotalValue)}}
                      </div>
                    </div>
                    <div class="row" *ngIf="invoiceInfo?.tax_amount">
                      <div class="col">
                        Tax
                      </div>
                      <div class="col">
                        {{currentCurrency}}
                      </div>
                      <div class="col">
                        {{fixNumber(taxAmount)}}
                      </div>
                    </div>
                    <div class="row" *ngIf="invoiceInfo?.tds_value">
                      <div class="col">
                        TDS
                      </div>
                      <div class="col">
                        {{currentCurrency}}
                      </div>
                      <div class="col">
                        {{fixNumber(tdsValue)}}
                      </div>
                    </div>
                    <div class="row" *ngIf="invoiceInfo?.tcs_value">
                      <div class="col">
                        TCS
                      </div>
                      <div class="col">
                        {{currentCurrency}}
                      </div>
                      <div class="col">
                        {{fixNumber(tcsValue)}}
                      </div>
                    </div>
                    <div class="row" *ngIf="invoiceInfo?.discount_value">
                      <div class="col">
                        Discount
                      </div>
                      <div class="col">
                        {{currentCurrency}}
                      </div>
                      <div class="col">
                        {{fixNumber(this.discountAmount)}}
                      </div>
                    </div>
                    <div class="row" *ngIf="invoiceInfo?.retention_value">
                      <div class="col">
                        Retention
                      </div>
                      <div class="col">
                        {{currentCurrency}}
                      </div>
                      <div class="col">
                        {{fixNumber(this.retentionAmount)}}
                      </div>
                    </div>
                  </div>
                </ng-template>
              </ng-container>
            </div>


            <div class="col-3 d-flex">
              <div class="dollar-icon" style="background-color: #EEF9E8;">
                <mat-icon style="color: #52C41A; font-size: 21px; margin-top: 7px;">attach_money</mat-icon>
              </div>
              <div class="ml-2">
                <div class="row  pb-2 amount-label">Payment Received</div>
                <div class="row amount-value">{{currentCurrency}} {{fixNumberOnUI(receivedValue)}}</div>
              </div>
            </div>
            <div class="col-3 d-flex" *ngIf = "invoiceInfo?.milestone_type != 6">
              <div class="dollar-icon" style="background-color: #EEF9E8;">
                <mat-icon style="color: #52C41A; font-size: 21px; margin-top: 7px;">attach_money</mat-icon>
              </div>
              <div class="ml-2">
                <div class="row  pb-2 amount-label">Outstanding</div>
                <div class="row amount-value" style="color:#FF3A46 !important;" class="ml-0">{{currentCurrency}}
                  {{fixNumberOnUI(collectedValue)}}</div>
              </div>
            </div>
            <div class="col-3 d-flex align-items-center" *ngIf = "invoiceInfo?.milestone_type != 6">
              <div class="circular-progress" [style.background]="conicGradient()">
                <span class="progress-value">
                  {{percentageCompleted ? percentageCompleted : 0}}%
                </span>
              </div>
              <div class="ml-3">
                <div class="row collection-label">Collection</div>
                <div class="row inprogress-label"> {{ getStatus() }}</div>
              </div>
            </div>

          </div>
          <hr class="mt-1 mb-1">
          <div class="row pt-3">
            <div class="col" style="padding-left: 8px;">
              <div class="row">
                <div class="col-1 p-0" style="max-width: 61.3px !important;">
                  <div class="cal-circle" style="background-color: #E8F4FF;">
                    <mat-icon class="cal-icon">calendar_month</mat-icon>
                  </div>
                </div>
                <div class="col-2 p-0" style="max-width: 13% !important;">
                  <div class="row pb-2 amount-label">Raised On</div>
                  <div class="row amount-value" style="color: #1B2140 !important; font-weight: 500;">{{
                    invoiceInfo?.invoice_raised_on }}</div>
                </div>
                <div class="col-2 p-0" style="max-width: 13% !important;" *ngIf = "invoiceInfo?.milestone_type != 6">
                  <div class="row pb-2 amount-label">Actual Due</div>
                  <div class="row amount-value" style="color:#1B2140 !important; font-weight: 500;">{{
                    invoiceInfo?.expected_on }}</div>
                </div>
                <div class="col-2 p-0" style="max-width: 13% !important;" *ngIf = "invoiceInfo?.milestone_type != 6">
                  <div class="row pb-2 amount-label">Credit Period</div>
                  <div class="row amount-value" style="color:#1B2140 !important; font-weight: 500;">{{invoiceInfo?.CP ?
                    invoiceInfo?.CP : '0' }} Days</div>
                </div>
                <div class="col-2 p-0" style="max-width: 13% !important;"  *ngIf="invoiceInfo?.status == 9 || invoiceInfo?.status == 10">
                  <mat-icon class="clock-icon" style="color:#EE4961 !important;font-size: 20px;">access_time</mat-icon>
                  <div class="days-text amount-value" style="color:#EE4961 !important; font-weight: 500;">
                    {{invoiceInfo?.expected_days}} days left</div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
      <div class="row card mt-3 subHeadingHeight"
        style="overflow-y: auto; overflow-x: hidden; padding: 0px !important;" [formGroup]="activityForm"
        *ngIf="currentScreen == 'payment' && invoiceInfo?.status != 11">
        <div class="col-12 pt-3">
          <div class="row d-flex mb-4 sticky-header">
            <div class="col-10 mt-2">
              <div class="row">
              <mat-button-toggle-group>
                <mat-button-toggle value = 'payment' (click)="navigateScreen('payment')" class="toggle-btn" [ngClass]="{ 'btn-toggle-selected': currentScreen === 'payment' }">Add Payments</mat-button-toggle>
                <mat-button-toggle value = 'history' (click)="navigateScreen('history')" class="toggle-btn" *ngIf="invoiceInfo?.status != 9" [ngClass]="{ 'btn-toggle-selected': currentScreen === 'history' }">History</mat-button-toggle>
                <mat-button-toggle value = 'documents' (click)="navigateScreen('documents')" class="toggle-btn" [ngClass]="{ 'btn-toggle-selected': currentScreen === 'documents' }">Documents</mat-button-toggle>
            </mat-button-toggle-group>
            </div>

              <!-- <div class="row d-flex">
                <div class="pr-0" *ngIf="currentScreen != 'payment'">
                  <button class=" icon-tray-button" mat-icon-button (click)="backToPayment()">
                    <mat-icon class="smallCardIcon"> chevron_left </mat-icon>
                  </button>
                </div>
                <div class="pl-2 pr-3" style="font-size: 16px; line-height: 16px; font-weight: 500; color: #34C759;">
                  {{title}}</div>
                <div class="pr-3 pt-1" (click)="navigateScreen('history')" *ngIf="invoiceInfo?.status != 9"
                  style="cursor: pointer;">
                  <mat-icon>history</mat-icon>
                </div>
                <div class="pr-3 pt-1" (click)="navigateScreen('documents')" style="cursor: pointer;">
                  <mat-icon>folder_open</mat-icon>
                </div>
              </div> -->
            </div>
            <div class="col-2 mt-2" style="display: flex; justify-content: end;" *ngIf="currentScreen == 'payment'">
              <div class="row">
                <button mat-raised-button class="submit" [ngClass]="{ 'button-disabled': 
                activityForm.get('amountReceived').invalid || 
                activityForm.get('receivedDate').invalid || 
                submitButtonDisabled }" [disabled]="
                activityForm.get('amountReceived').invalid ||
                activityForm.get('receivedDate').invalid || submitButtonDisabled == true
                    " (click)="submitPayment()">
                    Submit <mat-icon [ngStyle]="{ 'color': (activityForm.get('amountReceived').invalid || 
                                            activityForm.get('receivedDate').invalid || 
                                            submitButtonDisabled) ? '' : 'white', 
                                'font-size': '19px', 'margin-top' : '3px' }">
                      check_circle_outline
                    </mat-icon>
                  </button>
              </div>

            </div>
          </div>
          <div class="row" *ngIf="currentScreen == 'payment'">
            <div class="col-10">
              <div class="row">
                <div class="col-5">
                  <div class="row payment-label pb-2">Amount Received <span class="pl-2" style="color: red;">*</span>
                  </div>
                  <div class="row amount-received">
                    <mat-form-field appearance="outline" style="width: 80%;" floatLabel="always">
                      <!-- <mat-label>Amount Received</mat-label> -->
                      <input matInput class="pr-1" type="text" placeholder="0.00" formControlName="amountReceived"
                        min="1" required onkeydown="return event.keyCode !== 69" (input)="formatInput($event,'amountReceived',activityForm,$event)"/>
                      <mat-error class="pt-2" *ngIf="!activityForm.controls.amountReceived?.errors?.validAmount">
                        Amount Received can't be 0 or -ve
                      </mat-error>
                      <span matSuffix class="currency-suffix">
                        {{ activityForm.value.invoiceRaisedCurrency }}
                      </span>
                    </mat-form-field>

                  </div>
                </div>
                <div class="col-5">
                  <div class="row payment-label pb-2">Received Date <span class="pl-2" style="color: red;">*</span>
                  </div>
                  <div class="row date-picker">
                    <mat-form-field appearance="outline" style="width: 80%;" floatLabel="always">
                      <!-- <mat-label>Received Date</mat-label> -->
                      <input matInput [matDatepicker]="picker4" name="endDate" [max]="this.today" required
                        [placeholder]="invoiceDateFormat" formControlName="receivedDate" readonly />
                      <mat-datepicker-toggle matSuffix [for]="picker4"></mat-datepicker-toggle>
                      <mat-datepicker #picker4></mat-datepicker>
                    </mat-form-field>
                  </div>
                </div>
              </div>
              <div class="row mt-2">
                <div class="col-5">
                  <div class="row payment-label pb-2">Payment Received Bank</div>
                  <div class="row">
                    <mat-form-field appearance="outline" style="width: 80%;" floatLabel="always">
                      <!-- <mat-label>Payment Received Bank</mat-label> -->
                      <mat-select formControlName="paymentReceivedBank">
                        <mat-option value="" style="color: rgba(239, 225, 225, 0.54) !important;"
                          class="selectValue">Select</mat-option>
                        <mat-option *ngFor="let bank of bankDetails"
                          [value]="bank.bank_name">{{bank.bank_name}}</mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>
                </div>
                <div class="col-5">
                  <div class="row payment-label pb-2">Bank Reference Number</div>
                  <div class="row">
                    <mat-form-field appearance="outline" style="width: 80%;" floatLabel="always">
                      <!-- <mat-label>Bank Reference Number</mat-label> -->
                      <input matInput type="text" placeholder="Enter Ref No" formControlName="bankRefNo" maxlength="15"/>
                    </mat-form-field>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row mt-3" *ngIf="currentScreen == 'payment' && showPaymentRemittance">
            <div style="padding-left: 32px;">
              <span
                style="color: #EE4961; font-size: 12px; font-family: DM Sans; line-height: 12px; font-weight: 600;">! Important:
              </span><span
                style="color: #45546E;font-size: 12px; font-family: DM Sans; line-height: 12px; font-weight: 400;">You
                should attach documents or notes to complete the
                payment</span>
            </div>
          </div>
          <div class="row mt-3" *ngIf="currentScreen == 'payment' && showPaymentRemittance" style="padding-left: 32px;">
            <div class="row">
              <span
                style="color:#52C41A;font-size: 12px; font-family: DM Sans; line-height: 12px; font-weight: 500;">Notes
                <span class="pl-2" style="color: red;">*</span></span><br>
            </div>
            <div class="row" style="width: 100%;">
              <!-- <mat-form-field appearance="outline" class="textArea" style="width: 100%;">
                <textarea matInput cdkTextareaAutosize #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="1"
                  cdkAutosizeMaxRows="" formControlName="note" class="label-name"></textarea>
              </mat-form-field> -->
              <div class="col-10 p-0">
                <mat-form-field appearance="outline" class="textArea">
                  <textarea matInput cdkTextareaAutosize #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="5"
                    cdkAutosizeMaxRows="" formControlName="note" class="label-name"></textarea>
                </mat-form-field>
              </div>

            </div>

          </div>
          <div class="row pl-2" *ngIf="currentScreen == 'payment' && showPaymentRemittance">
            <div class="col pl-4">
              <div class="row mb-3"
                style="font-size: 12px; font-family: DM Sans; line-height: 16px; font-weight: 500;color: #000000;">
                Or</div>
              <div class="row mb-3"
                style="font-size:12px; font-weight: 500; color: #52C41A; font-size: 12px; font-family: DM Sans; line-height: 12px;">
                Attach Payment Remittance Document</div>
              <div class="row mb-3 d-flex">
                <div class="col-1 upload-container mt-2 position-relative">
                  <!-- Circle for selected file count -->
                  <span class="file-count">{{selectedFiles.length}}</span>

                  <!-- Hidden file input -->
                  <input type="file" #fileInput style="display: none" (change)="onFileSelected($event)" multiple
                    ng2FileSelect [uploader]="uploader" />

                  <mat-icon (click)="triggerFileInput()">cloud_upload</mat-icon>
                  <span>Upload</span>
                </div>
                <div class="col-11" style="display: flex; flex-wrap: wrap;">
                  <span *ngFor="let file of selectedFiles"
                    style="display: flex; align-items: center; border: 1px solid lightgray; background-color: lightgray; margin-right: 8px; padding: 5px; cursor: pointer; margin-bottom: 10px;">
                    <img class="pr-2" [src]="file?.file_format | fileIcon"
                      style="height: 20px; width: 20px; margin-right: 8px;" alt="" />
                    <div class="mt-1 filename" [matTooltip]="file.file_name"
                      style="flex-grow: 1; max-width: 188px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                      <span (click)="viewFile(file)">{{ file.file_name }}</span>
                    </div>

                    <mat-icon class="pl-1 delete-icon" (click)="deleteFile(file)"
                      style="cursor: pointer; color: #000000;">delete_outline</mat-icon>
                  </span>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row card mt-3 subHeadingHeight" style="overflow-y: auto; padding: 0px !important;"
      *ngIf="currentScreen == 'history' && (paymentInfo?.length != 0 || invoiceInfo?.milestone_type == 6) && invoiceInfo?.status != 9 ">
        <div class="col-12 pt-3">
          <div class="row d-flex justify-content-between align-items-center sticky-header mb-4">
            <div class="col-6 mt-2 d-flex align-items-center">
              <div class="row">
                <mat-button-toggle-group>
                  <mat-button-toggle value = 'payment' (click)="navigateScreen('payment')" class="toggle-btn" [ngClass]="{ 'btn-toggle-selected': currentScreen === 'payment' }" *ngIf="invoiceInfo?.status != 11">Add Payments</mat-button-toggle>
                  <mat-button-toggle value = 'history' (click)="navigateScreen('history')" class="toggle-btn" *ngIf="invoiceInfo?.status != 9" [ngClass]="{ 'btn-toggle-selected': currentScreen === 'history' }">History</mat-button-toggle>
                  <mat-button-toggle value = 'documents' (click)="navigateScreen('documents')"  class="toggle-btn" [ngClass]="{ 'btn-toggle-selected': currentScreen === 'documents' }">Documents</mat-button-toggle>
                </mat-button-toggle-group>
              </div>
              <!-- <div class="pr-0" *ngIf="invoiceInfo?.status != 11">
                <button class="icon-tray-button" mat-icon-button (click)="backToPayment()">
                  <mat-icon class="smallCardIcon">chevron_left</mat-icon>
                </button>
              </div>
              <div class="pr-3" style="font-size: 16px; line-height: 16px; font-weight: 500; color: #34C759;">
                {{title}}
              </div>
              <div class="pr-3 pt-1" (click)="navigateScreen('documents')" style="cursor: pointer;">
                <mat-icon>folder_open</mat-icon>
              </div> -->
            </div>
            <div class="col-6 mt-2 d-flex justify-content-end pr-4">
              <div class="d-flex align-items-center">
                <mat-icon class="pr-1" style="font-size: 18px; cursor: pointer;"
                  (click)="changeHistoryAmount()">loop</mat-icon>
                <span>{{paymentHistoryCurrency ? paymentHistoryCurrency : "-"}}</span>
              </div>
            </div>

          </div>

          <div class="row mt-3 pt-3 paymentHistoryHeight" *ngIf="paymentInfo?.length > 0"
            style="overflow-y: auto;">
            <div class="col">
              <div class="row pb-3 mb-3 pt-3" *ngFor="let item of paymentInfo" style="border: 1px solid #B9C0CA">
                <span class="payment-history-amount" [matTooltip]="item?.toolTipValue">{{item?.formattedValue}}</span>
                <div class="col">
                  <div class="row mt-2">
                    <div class="col" style="border-right: 1px solid #C3C7CF">
                      <div class="row payment-history-label">
                        Bank Reference Number
                      </div>
                      <div class="row payment-history-value text-wrap">
                        {{ (item.bank_reference_no ? item.bank_reference_no : (item.tally_no ? item.tally_no : '-')) }}
                      </div>
                    </div>
                    <div class="col" style="border-right: 1px solid #C3C7CF">
                      <div class="row payment-history-label">
                        Payment Received Bank
                      </div>
                      <div class="row payment-history-value text-wrap">
                        {{ item.payment_received_bank ? item.payment_received_bank : '-' }}
                      </div>
                    </div>
                    <div class="col">
                      <div class="row payment-history-label">
                        Payment Received Date
                      </div>
                      <div class="row payment-history-value">
                        {{ getDate(item.received_date) }}
                        <div>
                        </div>
                        <div class="col"></div>
                      </div>
                    </div>

                  </div>
                  <div class="row mt-1">
                    <div class="col payment-history-value" *ngIf="item.notes">
                      Notes: {{item.notes}}
                    </div>
                  </div>
                  <div class="row">
                    <div class="col d-flex" *ngIf="item.payment_context_id">
                      <span *ngFor="let file of getFilesForPayment(item.payment_id)">
                        <span (click)="viewFile(file)" class="payment-history-value pr-3 payment-history-filename"
                          [matTooltip]="file.file_name">{{file?.file_name}}</span>
                      </span>
                    </div>
                  </div>
                </div>

              </div>
            </div>

          </div>
        </div>
      </div>
      <div class="row card mt-3 subHeadingHeight" style="overflow-y: auto; padding: 0px !important;"
        *ngIf="currentScreen == 'documents'">
        <div class="row pt-4 mb-2 d-flex justify-content-between align-items-center sticky-header">
          <div class="col-6 d-flex">
            <div class="row">
              <mat-button-toggle-group>
                <mat-button-toggle value = 'payment' (click)="navigateScreen('payment')" class="toggle-btn" [ngClass]="{ 'btn-toggle-selected': currentScreen === 'payment' }" *ngIf="invoiceInfo?.status != 11">Add Payments</mat-button-toggle>
                <mat-button-toggle value = 'history' (click)="navigateScreen('history')" class="toggle-btn" *ngIf="invoiceInfo?.status != 9" [ngClass]="{ 'btn-toggle-selected': currentScreen === 'history' }">History</mat-button-toggle>
                <mat-button-toggle value = 'documents' (click)="navigateScreen('documents')" class="toggle-btn" [ngClass]="{ 'btn-toggle-selected': currentScreen === 'documents' }">Documents</mat-button-toggle>
              </mat-button-toggle-group>
            </div>
            <!-- <div class="pr-0 pl-1">
              <button class="icon-tray-button" mat-icon-button (click)="backToHistory(invoiceInfo?.status)">
                <mat-icon class="smallCardIcon">chevron_left</mat-icon>
              </button>
            </div>
            <div class="pr-3" style="font-size: 16px; line-height: 16px; font-weight: 500; color: #34C759;">
              {{title}}
            </div>
            <div class="pr-3 pt-1" (click)="navigateScreen('history')" *ngIf="invoiceInfo?.status != 9">
              <mat-icon>history</mat-icon>
            </div> -->
          </div>
          <div class="col-6" style="justify-content: end; display: flex;">
            <!-- Hidden file input -->
            <input type="file" #fileInput style="display: none" (change)="onMilestoneFileSelected($event)" multiple
              ng2FileSelect [uploader]="milestoneUploader" />
            <div class="row" *ngIf="currentScreen == 'documents'" style="justify-content: end !important;">
              <button mat-raised-button class="mx-auto addDocument" (click)="triggerFileInput()">
                Add Document <mat-icon style="transform: rotate(42deg);
                font-size: 18px;
                height: 17px;
                margin-left: 3px;">
                  attach_file
                </mat-icon>
              </button>
            </div>
          </div>
        </div>
        <div class="row" *ngIf="milestoneFiles?.length > 0">
          <div class="table-container">
            <mat-table [dataSource]="dataSource" matSort (matSortChange)="sortData()" class="custom-table"
              style="width: 100%;">

              <!-- File Type Column (Smaller Width) -->
              <ng-container matColumnDef="file_type">
                <mat-header-cell *matHeaderCellDef mat-sort-header
                  style="flex: 0 0 120px; text-align: left; font-size: 12px; color: #A8ACB2; line-height: 16px; font-weight: 400;">
                  File Type
                </mat-header-cell>
                <mat-cell *matCellDef="let file"
                  style="flex: 0 0 120px; text-align: left; font-size: 12px; color: #111434; line-height: 16px; font-weight: 500;">
                  <img [src]="file.file_format | fileIcon" alt="file icon" style="height: 20px; width: 20px;" />
                </mat-cell>
              </ng-container>

              <!-- File Name Column (Takes more space) -->
              <ng-container matColumnDef="file_name">
                <mat-header-cell *matHeaderCellDef mat-sort-header
                  style="flex: 1; text-align: left; font-size: 12px; color: #A8ACB2; line-height: 16px; font-weight: 400;">
                  File Name
                </mat-header-cell>
                <mat-cell *matCellDef="let file"
                  style="flex: 1; text-align: left; font-size: 12px; color: #111434; line-height: 16px; font-weight: 500; cursor: pointer;"
                  (click)="viewFile(file)">
                  {{ file.file_name }}
                </mat-cell>
              </ng-container>

              <!-- Header and Row Definitions -->
              <mat-header-row *matHeaderRowDef="['file_type', 'file_name']"></mat-header-row>
              <mat-row *matRowDef="let row; columns: ['file_type', 'file_name'];"></mat-row>

            </mat-table>
          </div>
        </div>
        <div class="row" *ngIf="milestoneFiles?.length == 0">
        <div class="table-container"
          style="display: flex; justify-content: center; text-align: center; align-items: center;flex-direction: column;">
          <div style="color: #52C41A; font-size: 16px; font-weight: 500; font-family: DM Sans;" class="pb-1">No Documents Found!</div>
          <img src="https://assets.kebs.app/images/no_data_found.png" height="150" width="250">
        </div>
        </div>
      </div>
    </div>
  </div>
</div>
<ngx-spinner size="medium" type="ball-clip-rotate" bdColor="rgba(236, 233, 233, 0.8)" color="#cf0001"
  *ngIf="!show"></ngx-spinner>