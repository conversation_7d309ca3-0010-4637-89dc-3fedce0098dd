import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatStepper } from '@angular/material/stepper';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { LoginService } from 'src/app/services/login/login.service';
import { LeaveUploadService } from '../../services/leave-upload.service';
import * as moment from 'moment';
import * as _ from 'underscore';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';

@Component({
  selector: 'app-leave-upload-landing-page',
  templateUrl: './leave-upload-landing-page.component.html',
  styleUrls: ['./leave-upload-landing-page.component.scss'],
})
export class LeaveUploadLandingPageComponent implements OnInit {
  exceluploadedData: any = [];
  tableColumnName: any = [];
  timesheetUploadData: any = [];
  currentUser;
  fileName;
  enableButtonCheck: boolean = false;
  isLinear: boolean = true;
  @ViewChild('stepper', { read: MatStepper }) stepper: MatStepper;
  batch_size: any = 500;
  stagingData: any = [];
  isUploading: boolean = false;

  constructor(
    private _leaveUploadService: LeaveUploadService,
    private _authService: LoginService,
    private excelService: JsonToExcelService,
    public matDialog: MatDialog,
    private toastService: ToasterService
  ) {}

  ngOnInit(): void {
    this.currentUser = this._authService.getProfile().profile;
  }

  leaveUpload(event) {
    this.fileName = event.target.files[0].name;
    this._leaveUploadService
      .leaveDataUpload(event, this.currentUser.aid, this.fileName)
      .then(
        (res) => {
          if (res['messType'] == 'S') {
            this.stepper.next();
            this.enableButtonCheck = true;
            this.exceluploadedData = res['uploadedData'];
            this.tableColumnName = res['tableColumns'];
            this.stagingData = res['stagingData'];
            event.target.value = '';
          } else {
            this.enableButtonCheck = false;
            this.toastService.showError('Leave Upload', res['messText'], 7000);
            event.target.value = '';
          }
        },
        (err) => {
          event.target.value = '';
          this.toastService.showError('Leave Upload', err, 7000);
          console.log(err);
        }
      );
  }

  uploadStagingDataToLeave() {
    let successCount = _.filter(this.exceluploadedData, { error: '' }).length;
    let errorCount = this.exceluploadedData.length - successCount;
    let total_batch = Math.ceil(
      this.exceluploadedData.length / this.batch_size
    );
    this.isUploading = true;
    for (let i = 0; i < total_batch; i++) {
      let startIndex = i * this.batch_size;
      let endIndex = Math.min(
        (i + 1) * this.batch_size,
        this.exceluploadedData.length
      );
      let batch_data = this.exceluploadedData.slice(startIndex, endIndex);
      let staging_data = this.stagingData.slice(startIndex, endIndex);
      this._leaveUploadService
        .insertLeaveData(
          batch_data,
          successCount,
          errorCount,
          total_batch,
          i + 1,
          this.fileName,
          staging_data
        )
        .subscribe(
          async (res) => {
            if (
              res['messType'] == 'S' &&
              this.exceluploadedData &&
              this.exceluploadedData.length > 0
            ) {
              if (i + 1 == total_batch) {
                this.isUploading = false;
                let currentDate = moment().format('DD-MM-YYYY');
                this.excelService.exportAsExcelFile(
                  this.exceluploadedData,
                  `KEBS_Leave_Upload_${currentDate}`
                );
                this.stepper.reset();
              }
              this.toastService.showInfo('Leave Upload', res['messText'], 7000);
            } else {
              this.isUploading = false;
              this.toastService.showError(
                'Leave Upload',
                res['messText'],
                7000
              );
            }
          },
          (err) => {
            this.isUploading = false;
            this.toastService.showInfo('Leave Upload', err, 7000);
            console.log(err);
          }
        );
    }
  }

  async openLogsDialog() {
    const { TsLogComponentComponent } = await import(
      '../../lazy-loaded-components/ts-log-component/ts-log-component.component'
    );

    const LogComponent = this.matDialog.open(TsLogComponentComponent, {
      height: '100%',
      minWidth: '50%',
      position: { right: '0px' },
    });
  }

  downloadTemplate() {
    let orginal_keys = [
      {
        'Leave Record Id': 123,
        'Employee Id': 1000,
        'Employee Name': 'Test User',
        'Leave Start Date': moment().format('DD-MM-YYYY'),
        'Leave End Date': moment().format('DD-MM-YYYY'),
        'From': 'Full Day',
        'To': 'Full Day',
        'Leave Type': 'Absence',
        'No Of Calendar Days': 1,
        'No Of Absent Days': 1,
        Status: 'Approved',
        'Leave Requested On': moment().format('DD-MM-YYYY'),
        'Leave Approved On': moment().format('DD-MM-YYYY'),
        'Leave Comments': 'Leave Due To Personal Works',
      },
    ];

    this.excelService.exportAsExcelFile(
      orginal_keys,
      `KEBS_Leave_Upload_Sample_Template`
    );
  }
}
