<div class="widget-container">
    <div class="header">
      <div class="d-flex align-items-center">
        <div class="chart-title">{{ widgetConfig?.label }}</div>
      </div>
      <div class="inline-filter" *ngIf="widgetConfig?.widget_config?.inline_filter">
        <ng-container *ngFor="let filter of widgetConfig?.widget_config?.inline_filter">
            <div class="selected-filter" (click)="openFilterOverlay($event,filter)" cdkOverlayOrigin
            #triggerInlineFilter="cdkOverlayOrigin" #triggerInlineFilter>
                {{ getDefaultFilterLabel(filter) }}
                <div class="pl-1">
                    <svg width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.91375 5.02644L0.144531 1.25724L0.847081 0.554688L3.91375 3.62135L6.98041 0.554688L7.68296 1.25724L3.91375 5.02644Z" fill="#1C1B1F"/>
                    </svg>                                
                </div>
            </div>
        </ng-container> 
      </div>
       <div class="summary-icon" *ngIf="widgetConfig?.widget_config?.show_summary" (click)="openSummaryForm()">
                <svg width="17" height="15" viewBox="0 0 14 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <!-- Outer box -->
                    <rect x="0.5" y="0.5" width="13" height="10" rx="1" stroke="#B9C0CA" stroke-width="1" />
            
                    <!-- Horizontal line (centered) -->
                    <line x1="1" y1="5.5" x2="13" y2="5.5" stroke="#B9C0CA" stroke-width="1" />
            
                    <!-- Two vertical lines (at 1/3 and 2/3 positions) -->
                    <line x1="4.67" y1="1" x2="4.67" y2="10" stroke="#B9C0CA" stroke-width="1" />
                    <line x1="9.33" y1="1" x2="9.33" y2="10" stroke="#B9C0CA" stroke-width="1" />
                </svg>
            </div>
    </div>
    <ng-container *ngIf="data.length > 0 && !isLoading">
        <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight }">
          <dx-pie-chart
            style="width: 55%"
            [type]="widgetType"
            [palette]="widgetConfig?.widget_config?.palette"
            [dataSource]="data"
            [customizePoint]="customizePoint"
            innerRadius="0.7"
            centerTemplate="centerTemplate"
          >
            <dxo-size [height]="calculatedChartWidgetHeight"> </dxo-size>
            <dxi-series
              [argumentField]="widgetConfig?.widget_config?.argument_field"
              [valueField]="widgetConfig?.widget_config?.value_field"
            >
            </dxi-series>
            <dxo-legend
              [visible]="widgetConfig?.widget_config?.is_legend_enabled"
              [horizontalAlignment]="
                widgetConfig?.widget_config?.legend_horizontal_alignment
              "
              [verticalAlignment]="
                widgetConfig?.widget_config?.legend_vertical_alignment
              "
              [customizeText]="customizeText"
              [rowItemSpacing]="16"
            ></dxo-legend>
            <dxo-tooltip [enabled]="true" [customizeTooltip]="customizeTooltip">
            </dxo-tooltip>
            <svg *dxTemplate="let pieChart of 'centerTemplate'">
              <text
                text-anchor="middle"
                style="font-size: 24px"
                x="110"
                y="120"
                fill="#494949"
              >
                <tspan x="105" dy="10px" style="font-weight: 500; color: #111434; font-family: var(--reportFontFamily);">
                  {{ calculateTotal(pieChart) }}
                </tspan>
                <tspan
                  x="103"
                  dy="20px"
                  style="font-size: 14px; font-weight: 400; color: #5f6c81; font-family: var(--reportFontFamily);"
                >
                 {{ this.widgetConfig?.widget_config?.center_label }}
                </tspan>
              </text>
            </svg>
          </dx-pie-chart>
          <div *ngIf="widgetConfig?.widget_config?.is_custom_legend" style="width: 80%;">
            <ng-container *ngFor="let items of data; let i = index">
              <div class="d-flex legend">
                <div class="pr-1">
                    <svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="4" cy="4.5" r="4" [attr.fill]="items['color']"></circle>
                      </svg>                      
                </div>
                <div class="label" [matTooltip]="items[widgetConfig?.widget_config?.argument_field]">
                  {{ items[widgetConfig?.widget_config?.argument_field] }}
                </div>
                <div class="count">
                    {{ items[widgetConfig?.widget_config?.value_field] }}
                </div>
              </div>
            </ng-container>
          </div>
        </div>
    </ng-container>
    <ng-container *ngIf="data.length == 0 && !isLoading">
        <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight }">
          <span class="empty-data">No Data Found!</span>
        </div>
    </ng-container>
</div>
<ng-template #triggerInlineFilterTemplateRef let-filter>
  <div class="dropdown" *ngIf="filter.filter_data.length > 0">
      <ng-container *ngFor="let item of filter.filter_data">
          <div class="dropdown-item" [matTooltip]="item.label" (click)="updateFilterData(item.id, filter)">{{item.label}}</div>
      </ng-container>
  </div>
</ng-template>