import {
  Component,
  OnInit,
  ViewChild,
  ChangeDetector<PERSON>ef,
  Inject,
  ElementRef,
  HostListener
} from "@angular/core";
import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import { MatStepper } from "@angular/material/stepper";
import * as _ from "underscore";
import { UtilityService } from "src/app/services/utility/utility.service";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { InvoiceBillingService } from "../../services/invoice-billing.service";
import swal from "sweetalert2";
import * as moment from "moment";
import { MatSnackBar } from '@angular/material/snack-bar';
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA
} from "@angular/material/dialog";
// import { SingleInvoicePageComponent } from '../../components/single-invoice-page/single-invoice-page.component';
// import {SingleAnnexPageComponent} from '../../components/single-annex-page/single-annex-page.component'
// import {ViewInvoiceComponent} from "../../components/view-invoice/view-invoice.component"
// import { SlimLoadingBarService } from 'ng2-slim-loading-bar';
import { TenantService } from 'src/app/services/tenant-service/tenant.service';
import { InvoiceCommonService } from "../../../../common-services/invoice-common.service";
import { NgxSpinnerService } from 'ngx-spinner';
import { AbstractControl, ValidationErrors } from '@angular/forms';
import { FileUploader } from 'ng2-file-upload';
import { LoginService } from 'src/app/services/login/login.service';
import { InvoiceHomeService } from "../../../invoice-home/services/invoice-home.service";
import { MailUtilityService } from 'src/app/app-shared/app-shared-components/mail-box-modal/services/mail-utility.service';
import { GraphApiService } from 'src/app/services/graph/graph-api.service';
import { DunningService } from 'src/app/modules/collector/services/dunning-service/dunning.service';
import { pluck } from 'rxjs/operators';
import { DocViewerComponent } from '../../../../../shared-lazy-loaded-components/attachment-mgmt/components/doc-viewer/doc-viewer.component';
import { SharedLazyLoadedComponentsService } from '../../../../../shared-lazy-loaded-components/services/shared-lazy-loaded-components.service';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { DateAdapter, MAT_DATE_FORMATS, MatDateFormats } from '@angular/material/core';
import { ChatCommentContextModalComponent } from 'src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component';
const DATE_FORMAT_1: MatDateFormats = {
  parse: {
    dateInput: 'DD-MM-YYYY',
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'DD-MM-YYYY',
    dateA11yLabel: 'DD-MM-YYYY',
    monthYearA11yLabel: 'DD-MM-YYYY',
  },
};

@Component({
  selector: 'app-invoice-billing-page',
  templateUrl: './invoice-billing-page.component.html',
  styleUrls: ['./invoice-billing-page.component.scss'],
  providers: [{
    provide: STEPPER_GLOBAL_OPTIONS, useValue: { displayDefaultIndicatorType: false }
  }]
})
export class InvoiceBillingPageComponent implements OnInit {
  invoiceNoArray: any;
  invoicePdfData: any;
  paymentInfo: any;
  plannedCollectionAmount: any;
  num_activities: any = 0;
  activities: any;
  invoiceRaisedDate: any;
  initialSelectedIndex: number = 0;
  currencyId: any;
  item_id: any;
  project_currency: any;
  projectData: any;
  invoicePDfDataFinal = []
  serviceTypeId: any;
  teamsId: any;
  channelId: any;
  folderId: any;
  displayPaymentMilestone: any;
  stepNo: any = 1;
  selectedMilestoneId: any;
  today: any;
  itemId: any;
  billingId: any;
  milestoneId: any;
  projectId: any;
  paymentActivityList: any;
  fromScreen: any;
  //last stepper visibility
  lastStepperVisible = false;
  projectName: any;
  itemName: any;
  activityCompletion = [];
  activityForm: FormGroup;
  isInvoiceDialogOpen: boolean = false;
  paymentForm: FormGroup;
  invoiceInfo;
  submitButtonDisabled = false;
  submitSpinnerVisible = false;
  annexureData: any;
  invoiceTenantDetails: any;
  paymentTermsList: any;
  showAttachment: boolean = false;
  contextId: any = null;
  isCollectionShow: boolean = true;
  url: any;
  dateFormats: any;
  legalEntityId: any;
  bankDetails: any;
  showBankPaymentDetails: boolean = false;
  fieldConfig: any;
  paymentFieldConfig: any;
  fieldKeyConfig: any = {};
  invoiceValueBillingCurrency: any;
  @ViewChild("stepper") stepper: MatStepper;
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  URL = '/api/exPrimary/uploadObjectFromDevice';
  uploader: FileUploader;
  milestoneUploader: FileUploader;
  destinationBucket: string;
  routingKey: string;
  paymentContextId: string = null;
  percentageCompleted: number;
  currencyIndex: number;
  invoiceValue: any;
  receivedValue: any;
  collectedValue: any;
  currentCurrency: any;
  currentScreen: any;
  title: any;
  mailOpened: any = false
  mailTemplate: any;
  mailBillingDetails: any;
  paymentHistoryCurrency: any;
  paymentHistoryCurrencyIndex: number;
  selectedFiles: any = []; // Variable to store selected files
  milestoneFiles: any = [];
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('paginator') paginator: MatPaginator;
  itemsPerPage = [5, 10, 15, 20]
  isDialogOpened = false;
  expHeaderId: any;
  attachmentRetrievalDetails: any;
  invoiceDateFormat: any;
  dataSource: MatTableDataSource<any>;
  show: boolean = false;
  showPaymentRemittance: boolean = false;
  paymentHistoryFiles: any = [];
    generateAndStorePDFInS3: boolean = false;
  tcsValue: any;
  tdsValue: any;
  taxAmount: any;
  discountAmount: any;
  retentionAmount: any;
  subTotalValue: any;
  commentsInput = {
    application_id: null,
    unique_id_1: '',
    unique_id_2: '',
    application_name: null,
    title: '',
  };
  ganttId: any;
  isCommentPresent: boolean = false;
  isSendMailAuthorized: any;
  constructor(
    public dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private invoiceService: InvoiceBillingService,
    private router: Router,
    public utilityService: UtilityService,
    private _snackBar: MatSnackBar,
    private tenantService: TenantService,
    private invoiceCommonService: InvoiceCommonService,
    private spinnerService: NgxSpinnerService,
    private _login: LoginService,
    private invoiceHomeService: InvoiceHomeService,
    private _graphService: GraphApiService,
    public mailUtilityService: MailUtilityService,
    private dunningService: DunningService,
    private _sharedService: SharedLazyLoadedComponentsService,
    @Inject(MAT_DATE_FORMATS) private matDateFormats: MatDateFormats,
    private dateAdapter: DateAdapter<any>

  ) {
    this.url = this.route['_routerState'].snapshot.url;
    this.activityForm = this.fb.group({
      // i created a form group which has a form array named " activities "
      tallyRef: ["", Validators.required],
      amountToBeCollected: [[""], Validators.required],
      amountReceived: ["", [Validators.required, validateAmount]],
      receivedDate: ["", Validators.required],
      invoiceRaisedCurrency: ["", Validators.required],
      bankRefNo: [""],
      paymentReceivedBank: [""],
      activities: this.fb.array([]),
      note: ""
    });

    function validateAmount(control: AbstractControl): ValidationErrors | null {
      if (control.value <= 0) {
        return { validAmount: false };
      }
      return null;
    }

    this.dataSource = new MatTableDataSource(this.milestoneFiles);
  }
  async viewInvoice(billingId?, milestoneId?) {
    if (this.isInvoiceDialogOpen) {
      this.isInvoiceDialogOpen = false; // Reset the flag
    }

    this.isInvoiceDialogOpen = true;
    console.log(this.paymentTermsList);

    // Auto-reset flag after 30 seconds as a safety measure
    setTimeout(() => {
      if (this.isInvoiceDialogOpen) {
        console.log('Auto-resetting isInvoiceDialogOpen flag after timeout');
        this.isInvoiceDialogOpen = false;
      }
    }, 30000);

    // Use provided parameters or fall back to instance properties
    const currentBillingId = billingId || this.billingId;
    const currentMilestoneId = milestoneId || this.invoiceInfo?.milestone_id;

    if(this.generateAndStorePDFInS3){
      let bucket = 'kebs-invoices';
      try {
        let res = await this.invoiceHomeService.retrieveUploadedObjects(bucket, currentMilestoneId).toPromise();
        if (res && res["data"]?.length > 0) {
          this.viewFile(res["data"][0]);
        }
        else{
          console.error('No S3 files found for milestoneId:', currentMilestoneId);
          this._snackBar.open('No invoice file found. Please contact KEBS team to resolve.', 'Close', {
            duration: 3000
          });
          this.isInvoiceDialogOpen = false;
        }
      } catch (error) {
        console.error('Error retrieving S3 objects:', error);
        this._snackBar.open('Error loading invoice. Please try again.', 'Close', {
          duration: 3000
        });
        this.isInvoiceDialogOpen = false;
      }
    }
    else{
    const { ViewInvoiceComponent } = await import('../../../../lazy-loaded-components/view-invoice/view-invoice.component')
    this.invoiceService.viewInvoice(currentBillingId).subscribe(
      res => {
        let result = [];
        result.push(res)
        console.log(res);
        if (result && result.length > 0 && res != null) {
          const openInvoice = this.dialog.open(ViewInvoiceComponent, {
            width: "65%",
            height: "100%",
            autoFocus: false,
            maxWidth: "90vw",
            data: {
              pdfData: res,
              billingId: currentBillingId,
              invoiceTenantDetails: this.invoiceTenantDetails,
              paymentTermsList: this.paymentTermsList
            }
          });
          openInvoice.afterClosed().subscribe(() => {
            // Reset the flag when the dialog is closed
            this.isInvoiceDialogOpen = false;
          });
        }
        else {
          this.utilityService.showMessage("No Data found for billing ID", 'Dismiss')
          this.isInvoiceDialogOpen = false;
        }
      },
      err => {
        console.error(err);
        this.isInvoiceDialogOpen = false;
      }
    );
  }
  }

  openAnnexure = () => {
    this.invoiceService.viewInvoice(this.billingId).subscribe(
      res => {
        let result = [];
        result.push(res);
        if (result && result.length > 0) {
          let invoicePdf = res;
          this.invoiceService
            .viewAnnexure(this.billingId, this.serviceTypeId)
            .subscribe(async res => {
              if (res != null) {
                console.log(res);
                let arr = [];
                arr.push({
                  serviceTypeId: res["serviceTypeId"] || this.serviceTypeId,
                  data: res['data'],
                  invoiceNo: _.pluck(invoicePdf, "invoiceNo").reverse(),
                  dateFormats: this.dateFormats,
                  invoiceTenantDetails: this.invoiceTenantDetails
                });
                const { ViewAnnexureComponent } = await import("../../../../lazy-loaded-components/view-annexure/view-annexure.component")
                const openAnnexure = this.dialog.open(ViewAnnexureComponent, {
                  width: "65%",
                  height: "100%",
                  autoFocus: false,
                  maxWidth: "90vw",
                  data: arr
                });
              }
              else {
                this.utilityService.showMessage("No Data found for annexure", 'Dismiss')
              }
            });
        }
        else {
          this.utilityService.showMessage("No Data found", 'Dismiss')
        }
      },
      err => {
        console.error(err);
      }
    );
  }
  submitPayment() {

    if (this.showPaymentRemittance) {
      if (!this.activityForm.value.note && this.selectedFiles.length === 0) {
        swal.fire({
          icon: "info",
          title: "Please attach document(s) or notes to complete the payment!",
          showConfirmButton: true
        });
        return
      }
    }
    this.submitButtonDisabled = true;
    this.submitSpinnerVisible = true;
    let totalLeft =
      this.invoiceInfo.amount_to_be_Collected -
      this.parseValue(this.activityForm.value.amountReceived);
    let dateReceived = this.utilityService.convertToLocalTime(this.activityForm.value.receivedDate);
    let tallyNo = this.activityForm.value.tallyRef;
    let bankRefNo = this.activityForm.value.bankRefNo
    let paymentReceivedBank = this.activityForm.value.paymentReceivedBank
    let receivedAmount = this.parseValue(this.activityForm.value.amountReceived);
    let note = this.activityForm.value.note;
    let billId = this.billingId;
    this.invoiceService
      .savepaymentInfo(
        receivedAmount,
        dateReceived,
        billId,
        tallyNo,
        this.milestoneId,
        this.itemId,
        totalLeft,
        bankRefNo,
        paymentReceivedBank,
        this.paymentContextId,
        note
      )
      .subscribe(
        res => {
          if (res["messType"] == "S") {
            this.invoiceService
              .getInvoiceInformationInBilledScreen(
                this.billingId
              )
              .subscribe(
                res => {
                  console.log(res);
                  this.submitButtonDisabled = false;
                  this.invoiceInfo = res;
                  this.activityForm.patchValue({
                    tallyRef: "",
                    amountReceived: "",
                    receivedDate: "",
                    bankRefNo: "",
                    amountToBeCollected: this.invoiceInfo.amount_to_be_Collected
                  });
                  if (this.activityForm.value.amountToBeCollected == 0) {

                    swal.fire({
                      icon: "success",
                      title: "Payment for this milestone is recorded succesfully !",
                      showConfirmButton: true
                    });

                    this.router.navigateByUrl('/main/invoice-v1.2/invoicelist/3')

                  } else {
                    swal.fire({
                      icon: "success",
                      title: "Partial payment recorded succesfully !",
                      showConfirmButton: true
                    });

                    this.router.navigateByUrl('/main/invoice-v1.2/invoicelist/2')

                    // this.router.navigateByUrl("/main/invoice/invoicelist/1");
                    this.submitSpinnerVisible = false;
                    this.getPaymentAndActivityRelatedInfo();

                  }
                },
                err => {
                  console.error(err);
                }
              );
          }
        },
        err => {
          this._snackBar.open(err.error.messText, 'Dismiss', { duration: 2000 })
          this.submitButtonDisabled = false;
          console.error(err);
        }
      );
  }

  savePlannedCollectionAmount(val) {
    this.plannedCollectionAmount = val;
    console.log(this.projectId + "**" + this.currencyId + "**" + this.plannedCollectionAmount + "***" + this.invoiceRaisedDate + "***" + this.billingId);
    this.invoiceService.updatePlannedCollection(this.projectId, this.currencyId, this.plannedCollectionAmount, this.invoiceRaisedDate, this.billingId).subscribe(res => {
      if (res == 'Success') {
        this._snackBar.open("updated successfully!", "close", {
          duration: 2000,
        });
      }
      // this.getInvoiceInfoDisplayedInleftSide();

    })
  }


  get ActivityArray() {
    /*
    this function returns the 'activities' array
    */
    return this.activityForm.get("activities") as FormArray;
  }
  getDate(date) {
    let invoice_date_format = this.dateFormats?.invoice_general_date_format ? this.dateFormats?.invoice_general_date_format : "DD-MM-YYYY";
    this.invoiceDateFormat = invoice_date_format
    return moment(date).format(invoice_date_format);
    // return moment(date).format("DD-MM-YY");
  }
  completeActivity(index) {
    this.invoiceService
      .completePaymentActivity(
        this.activityForm.value.activities[index].activityID
      )
      .subscribe(
        res => {
          console.log(res);
          if (res["message"] == "Payment Activity completed") {
            let template = {
              customClass: {
                title: "title-class",
                confirmButton: 'confirm-button-class'
              },
              type: "success",
              title: res["message"]
            }
            swal.fire(template);
            this.activityCompletion[index] = 1;
            this.stepper.next();
          } else {
            let template = {
              customClass: {
                title: "title-class",
                confirmButton: 'confirm-button-class'
              },
              type: "error",
              title: "Oops...",
              text: "Activity Not Completed"
            }
            swal.fire(template);
          }
        },
        err => {
          console.error(err);
        }
      );
  }


  setActivityForm(data, invoInfo) {
    this.activityForm.patchValue({
      amountToBeCollected: invoInfo.amount_to_be_Collected,
      invoiceRaisedCurrency: invoInfo.currency
    });
    // console.log(this.activityForm.value);

    // i create a control to the form
    let control = <FormArray>this.activityForm.controls.activities;
    /*
         i add a control to control the push operations in
         the activities array
         */
    console.log(data);
    data.forEach((x, index) => {
      /*
          I iterate throughout the activies and push the
          activity details  into the 'activities' array
          */
      control.push(
        this.fb.group({
          activityName: x.activity_name,
          customerRespPerson: x.customer_responsible_person,
          customerPersonsRole: x.customer_person_role,
          customerEmail: x.customer_person_email,
          numberOfDays: x.no_of_days,
          KaarResponsiblePerson: x.assigned_to_id,
          startDate: x.start_date != null ? this.utilityService.convertToLocalTime(x.start_date) : null,
          plannedEndDate: x.due_date != null ? this.utilityService.convertToLocalTime(x.due_date) : null,
          completionDate: x.end_date != null ? this.utilityService.convertToLocalTime(x.end_date) : null,
          activityID: x.payment_activity_id
        })
      );
      this.activityCompletion.push(x.is_completed);
    });
  }
  saveActivity(index) {
    // this function is executed when i click the save button for each activity
    this.invoiceService
      .saveActivityDetails(
        this.activityForm.value.activities[index],
        this.milestoneId
      )
      .subscribe(
        res => {
          console.log(res);
          if (res["message"] == "Activity Details Saved!") {
            let template = {
              customClass: {
                title: "title-class",
                confirmButton: 'confirm-button-class'
              },
              type: "success",
              title: res["message"]
            }
            swal.fire(template);
          }
          else {
            let template = {
              customClass: {
                title: "title-class",
                confirmButton: 'confirm-button-class'
              },
              type: "error",
              title: "Oops...",
              text: "Activity not Saved"
            }
            swal.fire(template);
          }
        },
        err => {
          console.error(err);
        }
      );
  }


  async ngOnInit() {
    this.calculateDynamicContentHeight();
    this.currentScreen = 'payment';
    this.title = 'ADD PAYMENT';
    this.percentageCompleted = 80;
    this.spinnerService.show();
    this.dateFormats = await this.getTenantDateFormats()
    let format = this.dateFormats?.invoice_general_date_format ? this.dateFormats?.invoice_general_date_format : "DD-MM-YY"
    const dynamicDateFormat: MatDateFormats = {
      parse: { dateInput: format },
      display: {
        dateInput: format,
        monthYearLabel: format,
        dateA11yLabel: format,
        monthYearA11yLabel: format,
      },
    };
    Object.assign(this.matDateFormats, dynamicDateFormat);
    this.fieldConfig = await this.formFieldConfig()
    this.paymentFieldConfig = await this.paymentFormFieldConfig()
    if (this.fieldConfig) {
      this.fieldConfig.forEach((element) => {
        this.fieldKeyConfig[element.key_name] = element
      });
    }
    this.getPaymentTerms()
    this.itemId = this.route.snapshot.params["itemId"];
    this.milestoneId = this.route.snapshot.params["milestoneId"];
    this.billingId = this.route.snapshot.params["billingId"];
    this.projectId = this.route.snapshot.params["projectId"];
    this.fromScreen = this.route.snapshot.params["itemType"];
    this.today = new Date();
    this.selectedFiles = []
    this.milestoneFiles = []
    this.destinationBucket = 'kebs-invoices';
    this.routingKey = 'invoices';
    this.paymentContextId = `${this.milestoneId}-${this.create_UUID()}`
    //getting Invoice Information column in Billed
    await this.getInvoiceInfoDisplayedInleftSide();
    this.dataSource = new MatTableDataSource<any>(this.milestoneFiles);

    let isMailAuthorized = await this.getSendMailAuthorization();
    this.isSendMailAuthorized = isMailAuthorized
    this.getPaymentAndActivityRelatedInfo();
    this.bankDetails = await this.invoiceService.getBankDetails(this.legalEntityId);
    await this.getTenantInfo();
    this.attachmentRetrievalDetails = [
      { destinationBucket: 'kebs-invoices', routingKey: 'invoices', contextId: this.contextId, type: 'documents' },
      { destinationBucket: 'kebs-project-s3', routingKey: 'project', contextId: `PRJ_${this.milestoneId}${this.ganttId}`, type: 'documents' },
    ]

    // if(this.paymentInfo && this.paymentInfo.length > 0){
    //   for(let l of this.paymentInfo){
    //     let context_id = l['payment_context_id']
    //     if(context_id){
    //       this.attachmentRetrievalDetails.push({ destinationBucket: 'kebs-invoices', routingKey: 'invoice-pdf', contextId: context_id, type: 'payment' })
    //     }
    //   }
    // }
    // this.retrievePaymentHistoryFiles();
    for (let l of this.attachmentRetrievalDetails) {
      if (l.contextId) {
        this.milestoneFiles = []
        this.retrieveUploadedFiles(l.destinationBucket, l.contextId, l.type);
      }
    }

    this.uploader = new FileUploader({
      url: this.URL,
      authToken: 'Bearer ' + this._login.getToken(),
      disableMultipart: false,
      headers: [
        {
          name: 'context-id',
          value: this.paymentContextId,
        },
        {
          name: 'routing-key',
          value: this.routingKey,
        },
        {
          name: 'bucket-name',
          value: this.destinationBucket,
        },
      ],
      maxFileSize: 1024 * 1024 * 10, //10mb
    });

    this.milestoneUploader = new FileUploader({
      url: this.URL,
      authToken: 'Bearer ' + this._login.getToken(),
      disableMultipart: false,
      headers: [
        {
          name: 'context-id',
          value: this.contextId,
        },
        {
          name: 'routing-key',
          value: this.routingKey,
        },
        {
          name: 'bucket-name',
          value: this.destinationBucket,
        },
      ],
      maxFileSize: 1024 * 1024 * 10, //10mb
    });

    this.detectUploadChanges();
    let invoice_date_format = this.dateFormats?.invoice_general_date_format ? this.dateFormats?.invoice_general_date_format : "DD-MM-YYYY";
    this.invoiceDateFormat = invoice_date_format
    this.getInvoiceConfig();
  }




  getinvoiceNo(arr) {
    let temparr = []
    arr.forEach((element, index) => {
      if ((element) != undefined) {
        temparr.push(element);
      }
      if (arr.length - 1 == index) {
        this.invoiceNoArray = temparr;
      }
    });
  }

  updatePlannedCollection(event) {
    console.log(event);
  }

  getPaymentAndActivityRelatedInfo() {
    this.invoiceService.getPaymentInformation(this.billingId)
      .subscribe(
        res => {
          this.paymentInfo = res;
          this.retrievePaymentHistoryFiles();
          this.getValue(this.paymentInfo)
          this.calculateAmountToBeCollected(this.paymentInfo);
          console.log(res);
        }, err => {
          console.error(err);
        });
  }
  launchProjectFromInvoice(projectId, projectName, itemId, itemName) {
    console.log(window.location.origin);
    let navigationUrl =
      window.location.origin +
      "/main/project-management/" +
      projectId +
      "/" +
      this.encodeURIComponent(projectName) + "/" + itemId + "/" + this.encodeURIComponent(itemName) + "/overview";
    window.open(navigationUrl);
  }
  encodeURIComponent(str) {
    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {
      return "%" + c.charCodeAt(0).toString(16);
    });
  }
  checkIfStepComplete(activities) {
    for (let k = 0; k < activities.length; k++) {
      if (activities[k].is_completed == "1") {
      } else {
        setTimeout(() => {
          this.initialSelectedIndex = k;
          console.log(this.initialSelectedIndex);
        }, 0);

        break;
      }
    }

  }

  async getInvoiceInfoDisplayedInleftSide() {
    try {
      const invoiceInfo = await this.invoiceService.getInvoiceInformationInBilledScreen(this.billingId).toPromise();
      console.log(invoiceInfo);
      this.invoiceInfo = invoiceInfo;
      this.itemName = invoiceInfo['item_name'];
      this.projectName = invoiceInfo['project_name'];
      this.serviceTypeId = invoiceInfo["service_type_id"];
      this.currencyId = invoiceInfo["currencyId"];
      this.invoiceRaisedDate = invoiceInfo["invoice_raised_on"];
      this.legalEntityId = invoiceInfo["legal_entity_id"];
      this.ganttId = invoiceInfo["gantt_id"];
      let milestoneId = invoiceInfo["milestone_id"];
      this.contextId = invoiceInfo["context_id"] != null ? invoiceInfo["context_id"] : `INV_${milestoneId}${this.ganttId}`;
      if (this.invoiceInfo?.status == 11) {
        this.currentScreen = 'history';
        this.title = 'PAYMENT HISTORY';
      }
      this.initialCurrencyValue()
      let expected_on = invoiceInfo["expected_date"]; 
      let expected_date = moment(expected_on, "DD-MM-YY").startOf('day'); // Set to midnight
      let current_date = moment().startOf('day'); // Set current date to midnight
      let diffDays = expected_date.diff(current_date, 'days');
      invoiceInfo["expected_days"] = diffDays;
      this.isCommentPresent = invoiceInfo["is_comment_present"]

      const activities = await this.invoiceService.getPaymentActivityListForMilestone(this.milestoneId).toPromise();
      this.show = true;
      this.spinnerService.hide();
      this.activities = activities;
      this.setActivityForm(activities, this.invoiceInfo);
      this.checkIfStepComplete(activities);
      this.lastStepperVisible = true;
      this.showAttachment = true;
    } catch (error) {
      console.error(error);
      this.spinnerService.hide();
      // Handle error uniformly
    }
  }


  /**********************here display payment milestone is undefined */

  ngAfterViewInit() {
    this.cdr.detectChanges();
    this.milestoneFiles.sort = this.sort;
    this.dataSource.sort = this.sort;
  }

  calculateAmountToBeCollected(data) {
    console.log(data);

  }
  ngOnDestroy() {

  }

  back() {
    if (this.fromScreen == 'billed') {
      this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/1");
    }

    else if (this.fromScreen == 'PP') {
      this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/2");
    }

    else if (this.fromScreen == 'PR') {
      this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/3");
    }

  }

  /** 
  * @description getTenantInfo
  */
  async getTenantInfo() {
    this.tenantService.getTenantInfo().then(async (tenantInfo: any) => {
      let tenant = tenantInfo
      this.invoiceTenantDetails = await this.getInvoiceTenantRoleCheckDetail(tenant.tenant_name, 'Role');
      this.isCollectionShow = this.invoiceTenantDetails['data'].is_collection_show_in_kebs;
      this.showBankPaymentDetails = this.invoiceTenantDetails['data'].is_show_bank_payment_details;
      this.showPaymentRemittance = this.invoiceTenantDetails['data']?.is_to_show_payment_remittance ? this.invoiceTenantDetails['data']?.is_to_show_payment_remittance : false
    },
      err => {
        console.log(err);
      })
  }

  /** 
  * @description getInvoiceTenantRoleCheckDetails
  */
  getInvoiceTenantRoleCheckDetail = (tenantName, checkType) => {
    return new Promise((resolve, reject) => {
      this.invoiceCommonService.getInvoiceTenantCheckDetail(tenantName, checkType).subscribe(res => {
        resolve(res);
      },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  };


  //Get Payment Terms
  getPaymentTerms() {
    this.invoiceCommonService.getPaymentTerms().then((res: any) => {
      for (let r of res) {
        r['id'] = r['name']
      }
      this.paymentTermsList = res
    }, (err) => {
      console.log(err)
    })
  }

  changeInAttachment() {
    if (this.contextId) {
      this.invoiceService
        .updateInvoiceAttachments(this.contextId, this.billingId)
        .subscribe(
          res => {
            console.log(res);
          },
          err => {
            console.log(err);
          }
        );
    }
  }

  //Get Tenant Date Formats
  getTenantDateFormats() {
    return new Promise((resolve, reject) => {
      this.invoiceCommonService.getTenantDateFormats().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          resolve(err);
        }
      );
    });

  }

  // To get invoice information 
  formFieldConfig() {
    return new Promise((resolve, reject) => {
      this.invoiceService.FormFieldConfig().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });

  }

  paymentFormFieldConfig() {
    return new Promise((resolve, reject) => {
      this.invoiceService.paymentFormFieldConfig().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });

  }

  triggerFileInput() {
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: any) {
    const fileList: FileList = event.target.files;
    for (let i = 0; i < fileList.length; i++) {
      this.selectedFiles.push(fileList[i]);
    }
  }

  onMilestoneFileSelected(event: any) {

    const fileList: FileList = event.target.files;
    for (let i = 0; i < fileList.length; i++) {
      this.milestoneFiles = this.milestoneFiles.push(fileList[i]);;
    }
    this.dataSource = new MatTableDataSource(this.milestoneFiles);
    this.dataSource.paginator = this.paginator;
    this.cdr.detectChanges();
  }

  sortData() {
    this.dataSource.sort = this.sort;
  }


  detectUploadChanges() {
    this.uploader.onProgressItem = (progress: any) => {
    };
    this.uploader.onCompleteItem = (
      item: any,
      response: any,
      status: any,
      headers: any
    ) => {
      this.uploader.removeFromQueue(item);
      let meta_data = JSON.parse(response).data;
      meta_data.isUploaded = true;
      let indexes = [];
      this.selectedFiles.push(meta_data);

    };

    this.uploader.onAfterAddingFile = (item) => {
      if (true) {

        let fileName = '';

        if (this.containsSpecialChars(item.file.name)) {

          fileName = item.file.name.replace(/[^a-zA-Z0-9. ]/g, '_');
          let temp_file_data = {
            file_name: fileName,
            file_format: item.file.name.split('.').pop(),
            cdn_link: `https://assets.kebs.app/lms/svgs/file.svg`,
            isUploaded: false,
          };
          this.uploader.uploadItem(item);
        }
        else {
          fileName = item.file.name;
          let temp_file_data = {
            file_name: fileName,
            file_format: item.file.name.split('.').pop(),
            cdn_link: `https://assets.kebs.app/lms/svgs/file.svg`,
            isUploaded: false,
          };
          this.uploader.uploadItem(item);
        }

      }
    };

    this.milestoneUploader.onProgressItem = (progress: any) => {
    };
    this.milestoneUploader.onCompleteItem = (
      item: any,
      response: any,
      status: any,
      headers: any
    ) => {
      this.milestoneUploader.removeFromQueue(item);
      let meta_data = JSON.parse(response).data;
      meta_data.isUploaded = true;
      let indexes = [];
      this.milestoneFiles.push(meta_data);
      this.dataSource = new MatTableDataSource(this.milestoneFiles);
      this.dataSource.paginator = this.paginator;
      this.sortData();
      this.cdr.detectChanges();
    };

    this.milestoneUploader.onAfterAddingFile = (item) => {
      if (true) {

        let fileName = '';

        if (this.containsSpecialChars(item.file.name)) {

          fileName = item.file.name.replace(/[^a-zA-Z0-9. ]/g, '_');
          let temp_file_data = {
            file_name: fileName,
            file_format: item.file.name.split('.').pop(),
            cdn_link: `https://assets.kebs.app/lms/svgs/file.svg`,
            isUploaded: false,
          };
          this.milestoneUploader.uploadItem(item);
        }
        else {
          fileName = item.file.name;
          let temp_file_data = {
            file_name: fileName,
            file_format: item.file.name.split('.').pop(),
            cdn_link: `https://assets.kebs.app/lms/svgs/file.svg`,
            isUploaded: false,
          };
          this.milestoneUploader.uploadItem(item);
        }

      }
    };
  }

  containsSpecialChars = (str) => {
    const specialChars = /[`!@#$%^&*()_+\-=\[\]{};':"\\|,<>\/?~]/;
    return specialChars.test(str);
  }

  // conicGradient() {
  //   if (this.receivedValue && this.invoiceValue && this.receivedValue !== '-' && this.invoiceValue !== '-') {
  //     this.percentageCompleted = Math.round((this.receivedValue / this.invoiceValue) * 100);
  //   } else {
  //     this.percentageCompleted = 0;
  //   }

  //   let value = this.percentageCompleted
  //   if (value < 0) {
  //     value = 0;
  //   }
  //   const degrees = (value / 100) * 360;
  //   return `conic-gradient(${'#52C41A'} ${degrees}deg, #ededed 0deg)`;
  // }

  conicGradient() {
    if (this.receivedValue && this.invoiceValue && this.receivedValue !== '-' && this.invoiceValue !== '-') {
      // Calculate exact percentage and format to 2 decimal places
      const exactPercentage = (this.receivedValue / this.invoiceValue) * 100;
      if (this.receivedValue >= this.invoiceValue) {
        this.percentageCompleted = 100;
      } else {
        this.percentageCompleted = parseFloat(exactPercentage.toFixed(2));
      }
    } else {
      this.percentageCompleted = 0;
    }
 
    let value = this.percentageCompleted
    if (value < 0) {
      value = 0;
    }
    const degrees = (value / 100) * 360;
    return `conic-gradient(${'#52C41A'} ${degrees}deg, #ededed 0deg)`;
  }
  
  changeCurrency() {
    let invoiceValue = this.invoiceInfo?.total_invoice_value;
    let receivedValue = this.invoiceInfo?.total_amount_received;
    let collectedValue = this.invoiceInfo?.Balance_amount;
    if (invoiceValue.length > 1)
      this.currencyIndex = ++this.currencyIndex % invoiceValue.length;

    this.invoiceValue = invoiceValue[this.currencyIndex]?.value;
    this.currentCurrency = invoiceValue[this.currencyIndex]?.currency_code

    this.receivedValue = receivedValue?.find(currency => currency.currency_code === this.currentCurrency)?.value ?? 0;
    this.collectedValue = collectedValue?.find(currency => currency.currency_code === this.currentCurrency)?.value ?? 0;
    this.changeSummaryValue();
  }

  changeSummaryValue(){

    let invoiceValue = this.invoiceInfo?.total_invoice_value;
    let subTotalValue = this.invoiceInfo?.subtotal_value;
    let taxValue = this.invoiceInfo?.tax_amount;
    let tcsValue = this.invoiceInfo?.tcs_value;
    let tdsValue = this.invoiceInfo?.tds_value;
    let discount = this.invoiceInfo?.discount_value;
    let retention = this.invoiceInfo?.retention_value;
    let billingCurrency = this.invoiceInfo?.currency
    if(billingCurrency == this.currentCurrency){
      this.subTotalValue = subTotalValue?.find(currency => currency.currency_code === this.currentCurrency)?.value ?? 0;
      this.tdsValue = tdsValue;
      this.tcsValue = tcsValue;
      this.taxAmount = taxValue;
      this.discountAmount = discount;
      this.retentionAmount = retention;
    }

    else{

      let invoiceBillingCurrencyValue = invoiceValue?.find(currency => currency.currency_code === billingCurrency)?.value ?? 0;
      let invoiceCurrentCurrencyValue = invoiceValue?.find(currency => currency.currency_code === this.currentCurrency)?.value ?? 0;
      if(invoiceBillingCurrencyValue && invoiceCurrentCurrencyValue){
        let rate = invoiceCurrentCurrencyValue / invoiceBillingCurrencyValue;
        console.log(rate)
        if(rate){
          this.subTotalValue = subTotalValue?.find(currency => currency.currency_code === this.currentCurrency)?.value ?? 0;
          this.tdsValue = tdsValue * rate;
          this.tcsValue = tcsValue * rate;
          this.taxAmount = taxValue * rate;
          this.discountAmount = discount * rate;
          this.retentionAmount = retention * rate;
        }
      }
    }

  }

  initialCurrencyValue() {
    let invoiceValue = this.invoiceInfo?.total_invoice_value;
    let billingCurrency = this.invoiceInfo?.currency;
    this.currentCurrency = this.invoiceInfo?.currency;
    let receivedValue = this.invoiceInfo?.total_amount_received;
    let collectedValue = this.invoiceInfo?.Balance_amount;
    this.currencyIndex = invoiceValue?.findIndex(currency => currency.currency_code === billingCurrency);
    this.invoiceValue = invoiceValue[this.currencyIndex]?.value;
    if (this.currencyIndex === -1 || !invoiceValue) {
      this.invoiceValue = '-';
    } else {
      this.invoiceValue = invoiceValue[this.currencyIndex]?.value ?? 0;
    }
    this.invoiceValueBillingCurrency = invoiceValue[this.currencyIndex]?.value
    this.receivedValue = receivedValue?.find(currency => currency.currency_code === this.currentCurrency)?.value ?? '-';
    this.collectedValue = collectedValue?.find(currency => currency.currency_code === this.currentCurrency)?.value ?? '-';
    this.changeSummaryValue();
  }

  fixNumberOnUI(no) {
    if (no == 0 || no == '0' || no == '' || no == '-') {
      return '0.00';
    }
    let num = parseFloat(no);
    if (this.currentCurrency == "INR")
      return new Intl.NumberFormat('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num));
    else
      return new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num));
  }

  fixNumber(no) {
    if (no == 0 || no == '0' || no == '' || no == '-') {
      return '0.00';
    }
    let num = parseFloat(no);
    if (this.currentCurrency == "INR")
      return new Intl.NumberFormat('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num));
    else
      return new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num));
  }

  navigateScreen(value) {
    this.currentScreen = value;
    if (value == 'history') {
      this.getValue(this.paymentInfo)
      this.title = 'PAYMENT HISTORY'
      // this.paymentHistoryFiles = []
      // this.retrievePaymentHistoryFiles()
    }
    if (value == 'documents') {
      this.title = 'DOCUMENTS'
      for (let l of this.attachmentRetrievalDetails) {
        if (l.contextId) {
          this.milestoneFiles = []
          this.retrieveUploadedFiles(l.destinationBucket, l.contextId, l.type);
        }
      }
    }
  }

  backToPayment() {
    this.currentScreen = 'payment';
    this.title = 'ADD PAYMENT';
  }

  backToHistory(status) {
    if(status == 11 ){
      this.currentScreen = 'history';
      this.title = 'PAYMENT HISTORY';
    }
    else{
      this.currentScreen = 'payment';
      this.title = 'ADD PAYMENT';
    }

  }

  // Mark as Sent
  async markAsSent(billingId) {
    let markConfirmation = {
      customClass: {
        title: "title-class",
        confirmButton: "confirm-button-class",
        cancelButton: "confirm-button-class"
      },
      title: "Are you sure want to mark this invoice as sent?",
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes"
    }
    swal
      .fire(markConfirmation)
      .then(result => {
        if (result.value) {
          return new Promise((resolve, reject) => {
            this.invoiceHomeService.markInvoiceAsSent(billingId).subscribe(
              (res: any) => {
                this.back()
                swal.fire({ title: "Invoice marked as sent successfully !", icon: "success", showConfirmButton: true });
              },
              (err) => {
                swal.fire({ title: "Failed to mark invoice as sent! !", icon: "success", showConfirmButton: true });
              }
            );
          });
        }
      })
  }

  // Send Mail to customer
  async sendMail(billingId: any, milestoneId: any, ganttId, milestone_type: any) {

    if (this.mailOpened)
      return

    this.mailOpened = true
    this.initMailBox()

    let billingDetails: any;
    let attachmentDetails: any;
    let mailTemplate: any = await this.invoiceHomeService.getMailTemplate(milestone_type, this.fromScreen);
    billingDetails = await this.invoiceHomeService.getCustomerBillingDetails(billingId);
    this.mailBillingDetails = billingDetails;
    this.mailTemplate = mailTemplate;
    this.dateFormats = await this.getTenantDateFormats()
    let formattedData = this.invoiceHomeService.formatDataForMailComponent(billingDetails, mailTemplate, this.dateFormats)
    this.mailUtilityService.mUtilityData['newMailTemplateData']
      .push(formattedData)

    this.mailUtilityService.mUtilityData.authorizedMailSenders = formattedData['authorizedMailSenders']
    this.mailUtilityService.mUtilityData.saveRecipientMailIdType['uniqueId'] = formattedData['uniqueId']

    this.mailUtilityService.mUtilityData['formatTableForTemplate'] = true
    if (mailTemplate && mailTemplate.length > 0 && mailTemplate[0].table_auto_structure) {
      this.mailUtilityService.mUtilityData['isTableLayoutAuto'] = mailTemplate[0]?.table_auto_structure == 1 ? true : false;
    } else {
      this.mailUtilityService.mUtilityData['isTableLayoutAuto'] = false;
    }
    this.mailUtilityService.mUtilityData.currentMailMode = { mode: 'create' }

    if(billingDetails?.send_mail_from_common_id){
      this.mailUtilityService.mUtilityData['fromCommonMailId'] = true
      this.mailUtilityService.mUtilityData.currentUserMailId = billingDetails?.common_from_mail_id
      this.mailUtilityService.mUtilityData.sendMailAPIDetails = {
      url:"/api/invoice/v2/sendMailFromCommonId",
      jwtToken:this._login.getJwtToken()
    }
    }
    else{
      this.mailUtilityService.mUtilityData.o365Token = { token: (await this._graphService.getO365Token()) }
      this.mailUtilityService.mUtilityData['fromCommonMailId'] = false
    }

    this.mailUtilityService.mUtilityData['saveHistoryInTable'] = true
    this.mailUtilityService.mUtilityData.saveHistoryInKebsApiData = {
      url: "/api/invoice/saveMailHistoryInTable",
      jwtToken: this._login.getJwtToken(),
      paramsArr: [
        {
          billingId: billingId
        }
      ]
    }

    this.mailUtilityService.mUtilityData.initiateNewMailTemplateData = {
      url: "/api/invoice/refreshMailTemplate",
      jwtToken: this._login.getJwtToken(),
      paramsArr: [
        {
          billingId: billingId
        }
      ]
    }

    this.mailUtilityService.mailUiData.showHistoryButton = true
    this.mailUtilityService.mUtilityData.retrieveHistoryFromApi = {
      url: "/api/invoice/retrieveHistoryFromTable",
      jwtToken: this._login.getJwtToken(),
      paramsArr: [
        {
          billingId: billingId
        }
      ]
    }

    if (mailTemplate && mailTemplate.length > 0 && mailTemplate[0].is_to_show_spoc) {
      this.mailUtilityService.mailUiData.showSpocName = true
    }
    if (mailTemplate && mailTemplate.length > 0 && mailTemplate[0].is_to_show_title) {
      this.mailUtilityService.mailUiData.showTitle = true
      let customername = _.uniq(_.pluck(billingDetails?.result, 'customerName'));
      if (customername) {
        this.mailUtilityService.mailUiData.title = `Mail to ${customername}`
      }
    }

    let data = this.invoiceTenantDetails['data']
    if (data && data.hasOwnProperty('is_mail_auto_attachment_applicable') && data.is_mail_auto_attachment_applicable) {
      this.mailUtilityService.mUtilityData.autoAttachmentApplicable = true
      this.mailUtilityService.mUtilityData.destinationBucket = 'kebs-invoices-mail-attachments'
      this.mailUtilityService.mUtilityData.routingKey = 'invoices-mail-attachments'
      this.mailUtilityService.mUtilityData.contextId = `INVM_${milestoneId}${ganttId}`
      this.mailUtilityService.mUtilityData.attachmentRetrievalDetails = [
        { destinationBucket: 'kebs-invoices', routingKey: 'invoice-pdf', contextId: `INV_${milestoneId}`},
        { destinationBucket: 'kebs-invoices', routingKey: 'invoices', contextId: `INV_${milestoneId}${ganttId}` },
        { destinationBucket: 'kebs-invoices-mail-attachments', routingKey: 'invoices-mail-attachments', contextId: `INVM_${milestoneId}${ganttId}`},
        { destinationBucket: 'kebs-invoices', routingKey: 'invoice-template-pdf', contextId: milestoneId}
      ]
      attachmentDetails = await this.invoiceHomeService.getAttachmentDetails(milestoneId);
      if (attachmentDetails.messType == 'S') {
        attachmentDetails = attachmentDetails['data']
        this.mailUtilityService.mUtilityData.attachmentRetrievalDetails.push(attachmentDetails?.AttachmentsDetails)
      }
    }


    const { ViewMailComponent } = await import('src/app/app-shared/app-shared-components/mail-box-modal/view-mail.component');
    const openViewMailComponent = this.dialog.open(ViewMailComponent, {
      width: "96%",
      height: "97%",
      maxWidth: "100vw",
      maxHeight: "100vw",
      data: {},
      disableClose: true
    });

    openViewMailComponent.afterClosed().subscribe(res => {
      this.mailUtilityService.resetMailData()
      // this.toggleClicked('BILLED')
      this.mailOpened = false
    })


  }

  /**
  * Init mail box 
  */
  initMailBox() {
    let user = this._login.getProfile().profile;
    this.mailUtilityService.mUtilityData.saveRecipientMailIds = this.saveMailIds.bind(this)
    this.mailUtilityService.mUtilityData.applicationId = 10
    this.mailUtilityService.mUtilityData.isCcRecipientFieldHasSaveButton = true
    this.mailUtilityService.mUtilityData.isBccRecipientFieldHasSaveButton = true
    this.mailUtilityService.mUtilityData.isToRecipientFieldHasSaveButton = true
    this.mailUtilityService.mUtilityData['hasInitiateNewMailTemplate'] = true
    this.mailUtilityService.mUtilityData['formatTableForTemplate'] = true
    this.mailUtilityService.mUtilityData.currentUserMailId = user['email']
    this.mailUtilityService.mUtilityData.saveSpocName = this.saveSpocNameForCustomer.bind(this)
  }

  /**
  * Save mail recipients - used in mail box
  */
  saveMailIds() {

    let type = this.mailUtilityService.mUtilityData.saveRecipientMailIdType['type']

    let recipientType = 
    type === "toMailId" ? "To" : 
    type === "ccMailId" ? "CC" : 
    type === "bccMailIds" ? "Bcc" : "";
  
    
    this.mailUtilityService.sendSweetAlert(`Are you sure you want to save the ${recipientType} recipients against customer?`, "")
      .then((Confirm) => {
        if (Confirm.value) {
          let mailFields = this.mailUtilityService.mUtilityData.saveRecipientMailIdType['mailFields']
          let customer_id = this.mailUtilityService.mUtilityData.saveRecipientMailIdType['uniqueId']
          console.log('customer', customer_id)
          let emailIds = []

          if (type == 'toMailId') {
            mailFields.value.toRecipients.length > 0
              ? _.each(mailFields.value.toRecipients, (item) => { emailIds.push(item.name) })
              : emailIds = []
          }
          else if (type == 'ccMailId') {
            mailFields.value.ccRecipients.length > 0
              ? _.each(mailFields.value.ccRecipients, (item) => { emailIds.push(item.name) })
              : emailIds = []
          }
          else if (type == 'bccMailIds') {
            mailFields.value.bccRecipients.length > 0
              ? _.each(mailFields.value.bccRecipients, (item) => { emailIds.push(item.name) })
              : emailIds = []
          }

          this.dunningService
            .saveDunningRecipientEmail(type, emailIds, customer_id)
            .subscribe((res: any) => {
              if (res.messType == 'S') {
                this._snackBar.open(res.userMess, "Dismiss", { duration: 2000 })
              }
              else {
                console.log(res)
              }
            }, (err) => {
              console.log(err)
            })
        }
        else {
          return
        }
      })
  }

  /**
* Save SPOC Name- used in mail box
*/
  saveSpocNameForCustomer() {
    this.mailUtilityService.mUtilityData.spocSaveEnabled = true
    let customer_id = this.mailUtilityService.mUtilityData.selectedNewMailTemplateData['uniqueId']
    let spoc_name = this.mailUtilityService.mailUiData.mailInputFields.value.spocName
    this.mailBillingDetails['spoc_name'] = this.mailUtilityService.mailUiData.mailInputFields.value.spocName
    this.dunningService
      .saveDunningSpocName(spoc_name, customer_id)
      .subscribe((res: any) => {
        if (res.messType == 'S') {
          this._snackBar.open(res.userMess, "Dismiss", { duration: 2000 })
        }
        else {
          console.log(res)
        }
      }, (err) => {
        console.log(err)
      })
    let body = this.mailUtilityService.mailUiData.mailInputFields.value.body
    body = body.replace(/<a[^>]*>/, '<a>');
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = body;
    const spanElement = tempDiv.querySelector('p > a');
    if (spanElement) {
      spanElement.textContent = this.mailUtilityService.mailUiData.mailInputFields.value.spocName;
    }
    const modifiedBody = tempDiv.innerHTML;

    let formattedData = this.invoiceHomeService.formatDataForMailComponent(this.mailBillingDetails, this.mailTemplate, this.dateFormats);
    formattedData['mailBody'] = modifiedBody
    this.mailUtilityService.mUtilityData['newMailTemplateData'] = []
    this.mailUtilityService.mUtilityData['newMailTemplateData'].push(formattedData);
    this.mailUtilityService.mUtilityData.currentMailMode = { mode: 'not selected' };
    this.mailUtilityService.initiateNewMail()

  }

  getValue(paymentInfo) {
    let billingCurrency = this.invoiceInfo?.currency;
    this.paymentHistoryCurrency = this.invoiceInfo?.currency;
    this.paymentHistoryCurrencyIndex = paymentInfo[0].amount_received?.findIndex(currency => currency.currency_code === billingCurrency);

    for (let l of paymentInfo) {
      let val: any;
      let value = l.amount_received
      if (value?.length > 1)
        this.paymentHistoryCurrencyIndex = this.paymentHistoryCurrencyIndex % value?.length;

      val = value[this.paymentHistoryCurrencyIndex]?.value ? value[this.paymentHistoryCurrencyIndex]?.value : 0
      this.paymentHistoryCurrency = value[this.paymentHistoryCurrencyIndex]?.currency_code
      l.toolTipValue = val
      if (this.paymentHistoryCurrency == "INR"){
        l.toolTipValue = (val)?.toFixed(2)
        val = (val / 10000000).toFixed(2) + " Cr";
      }
      else{
        l.toolTipValue = (val)?.toFixed(2)
        val = (val / 1000000).toFixed(2) + " M";
      }
      l.formattedValue = val;
    }
  }

  changeHistoryAmount() {
    let paymentCurrencyLoop = this.paymentInfo[0].amount_received;
    this.paymentHistoryCurrencyIndex = ++this.paymentHistoryCurrencyIndex % this.paymentInfo[0].amount_received?.length;
    for (let l of this.paymentInfo) {
      let value = l.amount_received
      let val: any;
      // if (value?.length > 1)
      //   this.paymentHistoryCurrencyIndex = ++this.paymentHistoryCurrencyIndex % value?.length;

      val = value[this.paymentHistoryCurrencyIndex]?.value ? value[this.paymentHistoryCurrencyIndex]?.value : 0
      this.paymentHistoryCurrency = value[this.paymentHistoryCurrencyIndex]?.currency_code
      l.toolTipValue = val
      if (this.paymentHistoryCurrency == "INR"){
        l.toolTipValue = (val)?.toFixed(2)
        val = (val / 10000000).toFixed(2) + " Cr";
      }
      else{
        l.toolTipValue = (val)?.toFixed(2)
        val = (val / 1000000).toFixed(2) + " M";
      }

      l.formattedValue = val;
    }

  }

  create_UUID() {
    var dt = new Date().getTime();
    var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        var r = (dt + Math.random() * 16) % 16 | 0;
        dt = Math.floor(dt / 16);
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
      }
    );
    return uuid;
  }

  viewFile(file: { cdn_link: string; file_format: string }): void {
    if (this.isInvoiceDialogOpen) {
      this.isInvoiceDialogOpen = false;
    }
    this.isInvoiceDialogOpen = true;
    const cdn_link = file.cdn_link;
    let uploadPopUpDialogRef: any = null;

    this._sharedService.getDownloadUrl(cdn_link).subscribe({
      next: (res: { base64Url?: string; data?: string }) => {
        if (res.base64Url) {
          try {
            let base64Data = res.base64Url;

            if (base64Data.startsWith('data:')) {
            const parts = base64Data.split(',');
              if (parts.length > 1) {
                base64Data = parts[1];
              }
            }
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);

            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }

            const byteArray = new Uint8Array(byteNumbers);
            const pdfFile = new Blob([byteArray], { type: 'application/pdf' });
            const fileURL = URL.createObjectURL(pdfFile);

            window.open(fileURL, '_blank');
            setTimeout(() => {
            URL.revokeObjectURL(fileURL);
            }, 5000); // Increased timeout to 5 seconds
          } catch (error) {
            window.open(res.base64Url, '_blank');
          } finally {
             this.isInvoiceDialogOpen = false;
          }
        }
        //  Fallback to component viewer
        else if (res.data) {
          uploadPopUpDialogRef = this.dialog.open(DocViewerComponent, {
            width: '58%',
            height: '100%',
            data: {
              selectedFileUrl: res.data,
              fileFormat: file.file_format,
              expHeaderId: ""
            },
          });

          uploadPopUpDialogRef.afterClosed().subscribe(() => {
            this.isInvoiceDialogOpen = false;
          });
        }
        else {
          console.error('No valid URL received from getDownloadUrl:', res);
          this._snackBar.open('Unable to load invoice. Please contact KEBS team.', 'Close', {
            duration: 5000
          });
          this.isInvoiceDialogOpen = false;
        }
      },
      error: (error) => {
        console.error('Error fetching file:', error);
        this._snackBar.open('Error loading file. Please try again.', 'Close', {
          duration: 3000
        });
        this.isInvoiceDialogOpen = false;
      },
    });
  }

  retrieveUploadedFiles = (destinationBucket: any, contextId: any, type) => {
    this._sharedService
      .retrieveUploadedObjects(destinationBucket, contextId)
      .pipe(pluck('data'))
      .subscribe(
        (res: any) => {
          const modifiedRes = res.map(item => {
            return { ...item, checked: true };
          });
          if (this.currentScreen == 'payment' && type == 'payment') {
            this.selectedFiles.push(...modifiedRes);
          }
          else if (this.currentScreen == 'documents' && type == 'documents') {
            this.milestoneFiles.push(...modifiedRes);
            this.dataSource = new MatTableDataSource(this.milestoneFiles);
            this.dataSource.paginator = this.paginator;
            this.sortData();
            this.cdr.detectChanges();
          }
          else if (this.currentScreen == 'history' && type == 'history') {
            return modifiedRes;
          }
        },
        (err) => {
          console.error(err);
        }
      );
  };

  deleteFile(file): void {
    // Find the index of the file with the matching cdn_link
    let cdnLink = file?.cdn_link
    const index = this.selectedFiles.findIndex(file => file.cdn_link === cdnLink);
    this.onContextMenuAction(file, 'Delete', index)
  }


  onContextMenuAction(file, action, index) {
    if (true) {
      if (action == 'Delete') {
        swal
          .fire({
            title: 'Are you sure?',
            text: 'Your file will deleted permanently!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Delete',
          })
          .then((result) => {
            if (result.isConfirmed) {
              this._sharedService
                .deleteObj(file, 't_app_attachments_meta')
                .subscribe(
                  (res: any) => {
                    this.selectedFiles.splice(index, 1);
                  },
                  (err) => {
                    console.error(err);
                  }
                );
            }
          });
      }
    }
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }


  //Adjusts UI based on screen size
  calculateDynamicContentHeight() {
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      window.innerHeight - 112 + 'px'
    );
    document.documentElement.style.setProperty(
      '--dynamicSubHeight',
      window.innerHeight - 294 + 'px'
    );
    document.documentElement.style.setProperty(
      '--dynamicPaymentHistoryHeight',
      window.innerHeight - 369 + 'px'
    );
  }

  retrievePaymentHistoryFiles() {
    if (this.paymentInfo && this.paymentInfo?.length) {
      this.paymentHistoryFiles = []
      for (let l of this.paymentInfo) {
        if (l.payment_context_id) {
          this._sharedService
            .retrieveUploadedObjects(this.destinationBucket, l.payment_context_id)
            .pipe(pluck('data'))
            .subscribe(
              (res: any) => {
                const modifiedRes = res.map(item => {
                  return { ...item, paymentId: l.payment_id };
                });
                this.paymentHistoryFiles.push(...modifiedRes)
              },
              (err) => {
                console.error(err);
              }
            );
        }

      }
    }

  }

  getFilesForPayment(paymentId: number) {
    return this.paymentHistoryFiles?.filter(file => file.paymentId === paymentId);
  }


  formatInput(event: Event, keyName, formgroup, keyevent: KeyboardEvent) {
    const inputElement = event.target as HTMLInputElement;
    let inputValue = inputElement.value;
    const key = keyevent['inputType'];
    const cursorPosition = inputElement.selectionStart;
    // Remove commas and leading zeros
    inputValue = inputValue.replace(/,/g, '').replace(/^0+(?=\d)/, '0')


    if (!/^\d*\.?\d*$/.test(inputValue)) {
      inputElement.value = '';
      this.activityForm?.get(keyName)?.setValue('');
      return
    }
    // Check if inputValue can be parsed as a number
    const numericValue = parseFloat(inputValue);

    // Check if numericValue is a valid number and the input value contains only digits
    if (!isNaN(numericValue)) {

      // Ensure that the input value is not empty
      if (inputValue === '') {
        inputElement.value = '';
        this.activityForm?.get(keyName)?.setValue('');
        return;
      }

      inputValue = inputValue.trim();
      const parts = inputValue.split('.');
      let integerPart = parts[0];
      let decimalPart = parts[1] !== undefined ? '.' + parts[1] : '';

      // Add commas to the integer part
      let currency =  this.activityForm.value.invoiceRaisedCurrency
      if (currency == "INR")
        integerPart = new Intl.NumberFormat('en-IN').format(Number(integerPart));
      else
        integerPart = new Intl.NumberFormat('en-US').format(Number(integerPart));

      // Combine integer and decimal parts with a dot
      inputValue = decimalPart ? `${integerPart}${decimalPart}` : integerPart;
      inputElement.value = inputValue;
      if (key === 'deleteContentBackward' || key === 'deleteContentForward') {
        inputElement.setSelectionRange(cursorPosition, cursorPosition);
      }
    } else {
      // If inputValue cannot be parsed as a number or contains non-digit characters, set it to an empty string
      inputElement.value = '';
      this.activityForm?.get(keyName)?.setValue('');
    }

  }

  parseValue(input: string | number): number {
    if (typeof input === 'string') {
      const sanitizedInput = input.trim(); // Remove leading/trailing spaces
      return sanitizedInput === '' ? 0.00 : parseFloat(sanitizedInput.replace(/,/g, ''));
      // return parseFloat(input.replace(/,/g, ''));
    } else if (typeof input === 'number') {
      return input;
    } else {
      return 0.00;
    }
  }

  getStatus(){
    if(this.invoiceInfo?.status == 11){
      return 'Completed';
    }
    else if(this.invoiceInfo?.status == 10){
      return 'Inprogress';
    }
    else{
      return 'Open';
    }

  }

  addNotes() {
    this.commentsInput.application_id = 915;
    this.commentsInput.application_name = "Invoices";
    this.commentsInput.title = this.invoiceInfo.milestone_name;
    this.commentsInput.unique_id_1 = this.invoiceInfo.milestone_id;
    let invoice_date_format = this.dateFormats?.invoice_general_date_format ? this.dateFormats?.invoice_general_date_format : "DD-MM-YYYY"
    let modalParams = {
      inputData: this.commentsInput,
      context: {
        'Milestone Name': this.invoiceInfo.milestone_name,
        'Milestone Start Date': moment(this.invoiceInfo?.planned_start_date).utc().format(invoice_date_format),
        'Milestone End Date': moment(this.invoiceInfo?.planned_end_date).utc().format(invoice_date_format),
        'Milestone Status': this.invoiceInfo?.milestone_status_name
      },
      commentBoxHeight: '100vh',
      commentBoxScrollHeight: '80%'
    };
    const openChatCommentContextModalComponent = this.dialog.open(ChatCommentContextModalComponent, {
      height: '100%',
      width: '75%',
      position: { right: '0px' },
      data: { modalParams: modalParams },
      disableClose: true
    });
  
    openChatCommentContextModalComponent.afterClosed().subscribe(async (res) => {
      console.log(res)
      let comments = await this.invoiceHomeService.getCommentStatus(this.invoiceInfo.milestone_id);
      if(comments && comments["messType"] == 'S'){
        this.isCommentPresent = comments["data"]
      }
      else{
        this.isCommentPresent = false
      }

    })
  }

  async getInvoiceConfig(){
    let invoiceConfig = await this.invoiceHomeService.getInvoiceConfig();
    if(invoiceConfig['data'][0] && invoiceConfig['data'][0].config!=null){
      let invoiceConfigDetails = JSON.parse(invoiceConfig['data'][0].config);
      this.generateAndStorePDFInS3 = invoiceConfigDetails.hasOwnProperty('generate_store_pdf_in_s3') && invoiceConfigDetails['generate_store_pdf_in_s3'] ? invoiceConfigDetails['generate_store_pdf_in_s3'] : false;
    }   
  }

  getSendMailAuthorization() {
    return new Promise((resolve, reject) => {
      this.invoiceHomeService.getSendMailAuthorization().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          resolve(err);
        }
      );
    });
  
  }
}




