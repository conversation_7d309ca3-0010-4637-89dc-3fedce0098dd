.RoleGroupDetailsPage {
    .popupTitle {
        font-size: 16px;
        color: #ff5e5e;
        align-items: center;
    }

    .create-pr-btn {
        background-color: #cf0001;
        color: white;
    }

    .create-pr-btn-loading {
        background-color: white;
    }

    .add_btn {
        height: 33px;
        width: 33px;
        display: flex;
        cursor: pointer;
        border: 1px solid #efa6a6;
        align-items: center;
        border-radius: 4px;
        justify-content: center;
    }

    .createNewRoleBtn {
        border-radius: 4px;
        background: var(--primary-maroon, #EE4961);
        width: auto;
        height: auto;
        border: none !important;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 4px;
        padding-right: 7px;
        font-size: 14px;
        color: white;
        font-weight: 500;
    }

    .deleteRoleBtn {
        border-radius: 4px;
        background: var(--primary-maroon, #EE4961);
        width: auto;
        height: 32px;
        border: none !important;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 4px;
        padding-right: 4px;
        font-size: 14px;
        color: white;
        font-weight: 500;
    }

    .deleteRoleBtn-loading {
        background-color: white;
    }

    .addNewPermissionFormDisplay {
        padding: 0px;
        margin-top: 2%;
        margin-bottom: 2%;
    }

    .addPermissionCard {
        border: 1px solid #ccc;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        padding: 16px;
    }

    .titleRow {
        z-index: 55;
        position: sticky;
        top: 0px;
        background: white;
    }

    ::ng-deep .mat-tab-header {
        z-index: 55;
        position: sticky;
        top: 43px;
        background: white;
    }

    ::ng-deep .dx-datagrid-header-panel {
        display: none;
    }

    ::ng-deep .dx-toolbar .dx-toolbar-items-container {
        height: 36px;
    }

    // Data Row Height
    ::ng-deep .dx-datagrid .dx-row>td {
        padding-top: 6px;
        padding-bottom: 6px;
        font-size: 13px;
        line-height: 25px;
    }

    // columns
    ::ng-deep .dx-datagrid-headers .dx-datagrid-table .dx-row>td {
        border-bottom: 1px solid #e0e0e0;
        padding-top: 11px;
        padding-bottom: 11px;
        line-height: 15px;
        font-weight: 500;
        font-size: 11px;
    }

    //Filter Search row
    ::ng-deep .dx-datagrid-filter-row {
        background-color: #fafafa;
        height: 15px !important;
    }

    ::ng-deep .dx-header-row>td>.dx-datagrid-text-content {
        font-size: 12px;
    }

    ::ng-deep .dx-datagrid .dx-editor-with-menu {
        height: 15px !important;
    }

    ::ng-deep .dx-datagrid .dx-menu .dx-menu-item .dx-menu-item-content,
    ::ng-deep .dx-datagrid-container .dx-menu .dx-menu-item .dx-menu-item-content {
        //  padding: 11px 4px !important;
        line-height: 15px !important;
        padding-top: 1px !important;
        padding-bottom: 3px !important;
        margin-top: -3px;

    }

    ::ng-deep .dx-editor-cell .dx-texteditor .dx-texteditor-input {
        background: rgba(255, 255, 255, 0);
        font-size: 13px;
        height: 15px;
        line-height: 15px;
        // padding: 10px 38px;
        padding-top: 10px;
        padding-bottom: 10px;
        margin-top: -3px;
    }

    ::ng-deep .dx-datagrid .dx-menu .dx-menu-item .dx-menu-item-content .dx-icon,
    ::ng-deep .dx-datagrid-container .dx-menu .dx-menu-item .dx-menu-item-content .dx-icon {
        width: 22px;
        height: 22px;
        background-position: 0 0;
        background-size: 22px 22px;
        padding: 2px 2px;
        font-size: 18px;
        text-align: center;
        line-height: 15px;
        margin: 0 3px;
    }

    ::ng-deep .dx-icon-refresh::before {
        color: gray;
    }

    ::ng-deep .dx-icon-trash::before {
        color: gray;
    }

    ::ng-deep .dx-icon-add::before {
        color: gray;
    }

    ::ng-deep .dx-icon-download::before {
        color: gray;
    }

    ::ng-deep .dx-icon-column-chooser::before {
        color: gray;
    }

    ::ng-deep .dx-datagrid-content .dx-datagrid-table .dx-row .dx-command-edit .dx-link {
        display: inline-block;
        color: gray;
    }

    .rolePermissionCreationContainer{
        display: block;
        flex-direction: row;
        flex-wrap: wrap;
        border: 1px dashed red;
        padding-top:18px;
        margin-bottom: 10px;
    }
    
    .add_btn_permission{
        height: 33px;
        width: 33px;
        display: flex;
        cursor: pointer;
        border: 1px solid #efa6a6;
        align-items: center;
        border-radius: 4px;
        justify-content: center;
    }

    .permissionFormSection{
        // margin-top: 3%;
        max-height: 60vh;
        overflow-y: auto;
    }
}