import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { RoleAuthGuardGuard } from './role-auth-guard.guard';
import { RoleListHomePageComponent } from './components/role-list-home-page/role-list-home-page.component';

const routes: Routes = [
   {
    path: '',
    children: [
      {
        path: '',
        canActivate: [RoleAuthGuardGuard],
        component: RoleListHomePageComponent,
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RoleAndAccessControlReportRoutingModule { }
