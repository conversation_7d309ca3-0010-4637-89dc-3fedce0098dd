import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class WidgetsService {
  constructor(private _http: HttpClient) {}

  getDataDynamically(url, payload) {
    return this._http.post(url, payload);
  }

  onClickActionsIcon(data, apiURL) {
    return this._http.post(apiURL, data);
  }
  getWidgetDetails(payload) {
    return this._http.post(
      '/api/pm/dashbord/getDashboardWidgets',
      payload
    );
  }
}