import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import * as XLSX from 'xlsx';

@Component({
  selector: 'app-summary-dialog',
  templateUrl: './summary-dialog.component.html',
  styleUrls: ['./summary-dialog.component.scss']
})
export class SummaryDialogComponent implements OnInit {
  displayedColumns: string[] = [];
  dataSource = new MatTableDataSource<any>();
  columnFilters: { [key: string]: string } = {};
  filterOptions: { [key: string]: string[] } = {};
  showFilterMenu: { [key: string]: boolean } = {};
  
  @ViewChild(MatSort) sort!: MatSort;

  constructor(@Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<SummaryDialogComponent>) {}

  ngOnInit(): void {
    if (this.data?.widgetData) {
      const summary = this.data.widgetData;
      this.displayedColumns = summary.columns.map((col: any) => col.key);
      this.dataSource.data = summary.rows || [];
      
      // Initialize filter values and options
      this.displayedColumns.forEach(col => {
        this.columnFilters[col] = '';
        this.showFilterMenu[col] = false;
        this.filterOptions[col] = this.getUniqueValues(col);
      });
    }
  }

  ngAfterViewInit() {
    this.dataSource.sort = this.sort;
  }

  getUniqueValues(column: string): string[] {
    const values = new Set<string>();
    this.dataSource.data.forEach(row => {
      if (row[column]) {
        values.add(row[column].toString());
      }
    });
    return Array.from(values).sort();
  }

  toggleFilterMenu(column: string, event: MouseEvent) {
    event.stopPropagation();
    this.showFilterMenu[column] = !this.showFilterMenu[column];
  }

  applyColumnFilter(column: string, value: string) {
    this.columnFilters[column] = value;
    this.dataSource.filter = JSON.stringify(this.columnFilters);
    
    this.dataSource.filterPredicate = (data: any, filter: string) => {
      const filters = JSON.parse(filter);
      return Object.keys(filters).every(key => {
        if (!filters[key]) return true;
        return data[key]?.toString().toLowerCase().includes(filters[key].toLowerCase());
      });
    };
  }

  clearColumnFilter(column: string) {
    this.columnFilters[column] = '';
    this.applyColumnFilter(column, '');
  }

  getColumnLabel(key: string): string {
    const col = this.data?.widgetData?.columns.find((c: any) => c.key === key);
    return col?.label || key;
  }

  downloadExcel(): void {
    const fileName = this.data?.widgetData?.label 
      ? `${this.data.widgetData.label.replace(/\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}`
      : `Summary_${new Date().toISOString().slice(0, 10)}`;

    // Map data to use labels instead of keys
    const dataWithLabels = this.dataSource.filteredData.map(row => {
      const newRow: any = {};
      this.data.widgetData.columns.forEach((col: any) => {
        newRow[col.label] = row[col.key];
      });
      return newRow;
    });

    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataWithLabels);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, `${fileName}.xlsx`);
  }

  onClose() {
    this.dialogRef.close();
  }
}