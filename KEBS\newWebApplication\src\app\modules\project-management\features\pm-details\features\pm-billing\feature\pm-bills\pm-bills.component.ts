// import { Component, OnInit } from '@angular/core';
import { Component, OnInit, HostListener } from '@angular/core';
import * as moment from 'moment';
import { PmBillingService } from '../../services/pm-billing.service'
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import * as _ from 'underscore';
import { Router } from '@angular/router';
import { PmBillingAdviceComponent } from '../pm-billing-advice/pm-billing-advice.component'
import { AttachTaskComponent } from '../attach-task/attach-task.component'
import { ChatCommentContextModalComponent } from 'src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component'
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { AttachmentServiceService } from "src/app/modules/project-management/shared-lazy-loaded/services/attachment-service.service"
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import Swal from 'sweetalert2';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import { PmLumpsumBillingAdviseComponent } from '../pm-lumpsum-billing-advise/pm-lumpsum-billing-advise/pm-lumpsum-billing-advise.component'
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { PmBillsEditComponent } from '../pm-bills-edit/pm-bills-edit.component';
import { MatDialog } from '@angular/material/dialog';
import { GetNameByIdPipe } from 'src/app/modules/project-management/shared-lazy-loaded/pipes/get-name-by-id.pipe';

@Component({
  selector: 'app-pm-bills',
  templateUrl: './pm-bills.component.html',
  styleUrls: ['./pm-bills.component.scss'],
  providers: [GetNameByIdPipe]
})

export class PmBillsComponent implements OnInit {
  border: any;
  background: any;
  color: any;
  ItemID: any
  projectID: any
  milestoneId: any
  listMilestoneId: any
  currentDate = moment().format()
  display = false
  milestone_status_list: any
  divVisible: boolean = false
  idCheck: any = null
  status_color: any
  isPopupVisible: boolean = false
  data: any = []
  loadedData: any
  retriveddData: any
  profit_center: any
  check: boolean = false
  left: any = '28px'
  lumpsum_left: any = '-15px'
  edit_data: any
  openfilter: boolean = false
  duedate: boolean = false
  projectInfo: any;
  status: any = []
  item_value_json: any = [];
  tagCheck: number
  convertCurrencyRecords: any = [];
  excluded_status: any = [6];
  milestoneList: any = []
  edit: any
  idHigh: any
  isDisplayVisible: boolean = false
  searchTag: any
  tagsPopUp: any
  formConfig: any;
  billingTabFlag: boolean = false;
  loading: boolean = true
  tags: any
  sort_tooltip: any = `<p>Sort newest -&gt; oldest</p>`

  chips: any = []
  commentsInput = {
    application_id: null,
    unique_id_1: '',
    unique_id_2: '',
    application_name: null,
    title: '',
  }
  outsideClick: boolean = false
  check_month: boolean
  code: any
  project_end_date: any
  project_start_date: any
  quote: any
  totalMilestoneValue: any = 0
  listView: boolean = false;
  moreOptionClicked: boolean = false;
  selectedRowIndex: any = null;
  at_risk: any = 0;
  calendarIcon: boolean = false;
  milestone_filter_status_list: any = []
  temp: any
  status_filter_name: any = 'All'
  status_filter_id: number = -1;
  temp_status: any;
  service_type_id: any
  retrieveMessages: any;
  button: any;
  shades: any;
  boxShadow: any;
  overallMilestoneValue: any = [];
  billWriteAccess: boolean = false;
  projectValueAccess: boolean = this.authService.getProjectObjectAccess(58)
  billingAdviceAccess: boolean = false;
  lumpsumBillingAdviceProject: boolean = false;
  noDataImage: any;
  fontStyle: any;
  font_size_currency: any = '11px'
  font_size_currency_card: any = '11px'
  item_status_id: number
  payment_terms_list: any
  payment_term_id: any
  scrollColor: any;
  milestone_group_array: any = []
  overallBillsValue: any[];
  @HostListener('document:click', ['$event'])
  onClick(event: MouseEvent) {
    // Check if the click target is outside of the div
    if (this.divVisible == true) {
      if (!(event.target as HTMLElement).closest('#myDiv')) {
        this.divVisible = false;
        this.data[this.idCheck].isPopoverOpen = false
      }

    }

    if (this.isDisplayVisible == true) {
      if (!(event.target as HTMLElement).closest('#displayTagDiv')) {
        this.isDisplayVisible = false
        this.outsideClick = true
        this.data[this.tagCheck].tagPopUpOpen = false
        this.stepperFormGroup.patchValue({ ['textInputControlDisplay']: '' })
        //console.log('test')

      }
    }
  }
  stepperFormGroup = this.formBuilder.group({
    textInputControlDisplay: [''],
  });
  milestone_grouped_data: any = []
  milestoneMatrix: any=[];
  constructor(private formBuilder: FormBuilder,
    private router: Router,
    private pmMasterService: PmMasterService,
    public dialog: MatDialog,
    private PmBillingService: PmBillingService,
    private attachmentService: AttachmentServiceService,
    private authService: PmAuthorizationService,
    private toasterService: ToasterMessageService,
    private utilityService: UtilityService, private _router: Router,
    private getNameByIdPipe: GetNameByIdPipe) {}

  async ngOnInit() {

    this.ItemID = parseInt(this.router.url.split("/")[5])  
    this.projectID = parseInt(this.router.url.split("/")[3])
    this.calculateDynamicContentHeight();
    console.log("ProjectItem", this.projectID, this.ItemID)
    this.loading = true;
    await this.authService.getReadWriteAccess(this.projectID, this.ItemID).then(async (res) => {
      if (res) {
        this.billWriteAccess = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 149)
        this.billingAdviceAccess = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 59)
      }
    })

    await this.PmBillingService.getProjectStatus(this.projectID, this.ItemID).then((res: any) => {
      if (res['messType'] == "S") {
        this.projectInfo = res['data']
        this.at_risk = this.projectInfo['at_risk'];
        this.service_type_id = this.projectInfo['service_type_id']
        this.profit_center = this.projectInfo['profit_center']
        this.lumpsumBillingAdviceProject = _.findWhere(this.pmMasterService.service_type_list, { billing_advice_type: "Lumpsum Advice", id: this.service_type_id }) ? true : false
        //console.log(this.projectInfo);
      }
    })
    await this.PmBillingService.getMilestoneMatrix().then((res: any)=>{
      if(res['messType']=="S")
      {
        this.milestoneMatrix = _.where(res['data'],{service_type_id: this.service_type_id})
      }
    })
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      this.formConfig = res;
      if (this.formConfig.length > 0) {
        const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
        this.noDataImage = retrieveStyles[0].data.no_data_image ? retrieveStyles[0].data.no_data_image : "https://assets.kebs.app/No-milestone-image.png";
        this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
        this.shades = retrieveStyles.length > 0 ? retrieveStyles[0].data.shades_color ? retrieveStyles[0].data.shades_color : "#C9E3B4" : "#C9E3B4";
        this.scrollColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.scroll_color ? retrieveStyles[0].data.scroll_color : "#90ee90" : "#90ee90";
        document.documentElement.style.setProperty('--milestoneScroll', this.scrollColor)
        document.documentElement.style.setProperty('--milestoneShades', this.shades)
        document.documentElement.style.setProperty('--milestoneButton', this.button)
        this.fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
        document.documentElement.style.setProperty('--milestoneFont', this.fontStyle);
        this.boxShadow = '0px 0px 0px 0px ' + this.button;
      }
    })
    await this.getFinnancialValues()
    await this.getMilestones()

    this.milestone_status_list = this.pmMasterService.milestone_status_list
    this.payment_terms_list = this.pmMasterService.paymentTerms_list
    for (let items of this.milestone_status_list) {

      //console.log(items['status_color'])
      this.status_color = items['status_color'] != "null" && items['status_color'] != "" && items['status_color'] != null ? JSON.parse(items['status_color']) : []
      for (let item of this.status_color) {
        items['border'] = item["border"];
        items["background"] = item["background"];
        items["color"] = item["color"]
      }
    }
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });
    this.retrieveMessages = _.where(this.formConfig, { type: "billing-page", field_name: "messages", is_active: true });

    if (this.data.length > 0) {
      this.display = true
      this.loading = false
    }
    //console.log(this.data)

    await this.PmBillingService.getFinanceStatus().then((res) => {
      this.milestone_filter_status_list = []
      this.milestone_filter_status_list = res['data']
      for (let items of this.milestone_filter_status_list) {
        //console.log(items['status_color'])
        this.status_color = JSON.parse(items['status_color'])
        for (let item of this.status_color) {
          items['border'] = item["border"];
          items["background"] = item["background"];
          items["color"] = item["color"]
        }
      }
      this.milestone_filter_status_list.push({ id: -1, name: 'All', color: '#7D838B' })
      //console.log('ALL', this.milestone_filter_status_list)
    })
    await this.PmBillingService.getExistingTags().then((res) => {
      //console.log(res)
      if (res['messType'] == 'S') {
        this.tagsPopUp = res['data']
        this.searchTag = res['data']
        //console.log(this.searchTag)
        //console.log(this.tagsPopUp)
      }
    })
    this.stepperFormGroup.get('textInputControlDisplay').valueChanges.subscribe(async (res) => {
      if (this.stepperFormGroup.get('textInputControlDisplay').value != '') {
        if (this.stepperFormGroup.get('textInputControlDisplay').value.length > 0) {
          //console.log(res)
          if (res) {
            this.tagsPopUp = this.searchTag.filter(item =>
              item.name.toLowerCase().includes(res.toLowerCase())
            );
          }
        }
        else {
          //console.log(this.searchTag)
          // this.tags.splice(0, this.tags.length);
          //console.log(this.searchTag)
          this.tagsPopUp = this.searchTag
          //console.log(this.searchTag)
          //console.log(this.tagsPopUp)
          if (this.outsideClick == true) {
            this.isDisplayVisible = false
            //console.log('test')
          }
          else {
            this.isDisplayVisible = true

          }
        }
      }
    })

    // this.loading=false;
  }

  // startEditing() {
  //   this.editing = true;
  // }
  // stopEditing() {
  //   this.editing = false;
  // }
  createBills() {
    if (this.billWriteAccess) {
      const dialogRef = this.dialog.open(PmBillsEditComponent, {
        disableClose: true,
        data: {
          mode: 'Create',
          data: '',
          at_risk: this.at_risk,
          button: this.button
        }
      });
      dialogRef.afterClosed().subscribe(async (result) => {
        if (result['messType'] == "S") {
          await this.getFinnancialValues()
          this.getMilestones()
        }
      });
    }
    else {
      this.toasterService.showWarning("You are not having access to create Milestones", 10000)
    }
  }
  openBillingAdivise(Mid: any, start_date: any, end_date: any, Sid: any, value: any, status_name: any, background: any, border: any, color: any, label: any, i) {
    //console.log(Mid)
    // const dialogConfig = new MatDialogConfig();
    // dialogConfig.width =`1200px`
    if (_.findWhere(this.pmMasterService.service_type_list, { billing_advice_type: "Lumpsum Advice", id: this.service_type_id })) {
      const dialogRef = this.dialog.open(PmLumpsumBillingAdviseComponent, {
        data: { "Mid": Mid, "start_date": moment(start_date).format('DD-MMM-YYYY'), "end_date": moment(end_date).format('DD-MMM-YYYY'), "itemID": this.ItemID, "projectID": this.projectID, "status_id": Sid, "value": value, "status_name": status_name, "background": background, "border": border, "color": color, "label": label },
        disableClose: true
      });
    }
    else if ((_.findWhere(this.pmMasterService.service_type_list, { billing_advice_type: "T&M Advice", id: this.service_type_id }))) {
      let full_screen_advice = _.where(this.formConfig, { type: "billing-page", field_name: "T&M_Billing_advice_full_screen", is_active: true });
      if (full_screen_advice.length == 0) {
        const dialogRef = this.dialog.open(PmBillingAdviceComponent, {
          data: { "Mid": Mid, "start_date": moment(start_date).format('DD-MMM-YYYY'), "end_date": moment(end_date).format('DD-MMM-YYYY'), "itemID": this.ItemID, "projectID": this.projectID, "status_id": Sid, "value": value, "status_name": status_name, "background": background, "border": border, "color": color, "label": label, "code": this.code },
          disableClose: true,

        });
        dialogRef.afterClosed().subscribe(async (result) => {
          console.log(result)
          if (result['messType'] == "C") {
            await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 8, this.data[i].id, this.data[i].currencyList, this.data[i].month, moment().format('YYYY-MM-DD'),null, null, null)
            await this.getFinnancialValues()
            this.getMilestones()
          }
        });
      }
      else {
        this._router.navigateByUrl(`main/project-management/${this.projectID}/${this.ItemID}/${Mid}/billing_advice`);
      }
    }
  }
  openStatusBox(id: any) {
    //console.log(id)
    let status: any = []
    let statusData = this.milestone_status_list.find(item => item.id === id);
    let allowedStatus = statusData?.allowed_bills_status_transitions ? JSON.parse(statusData?.allowed_bills_status_transitions) : null;
    if(!allowedStatus){
      this.toasterService.showWarning('Status change is not allowed',10000);
      return;
    }
    for (let items of this.milestone_status_list) {
      if (items['id'] != id) {
        status.push({ "border": items["border"], "background": items["background"], "color": items["color"], "id": items['id'], 'name': items['name'] })
      }
    }
  }
  togglePopover(id: any, i: any) {
    this.status = [];
    let statusData = this.milestone_status_list.find(item => item.id === id);
    let allowedStatus = statusData?.allowed_bills_status_transitions ? JSON.parse(statusData?.allowed_bills_status_transitions) : null;
    if(!allowedStatus){
      this.toasterService.showWarning('Status change is not allowed',10000);
      return;
    }
    for (let items of this.milestone_status_list) {
      if (_.includes(allowedStatus,items['id'])) {
        this.status.push({ "border": items["border"], "background": items["background"], "color": items["color"], "id": items['id'], 'name': items['name'] });
      }
    }
    if (this.idCheck != null) {
      if (this.idCheck == i) {
        this.data[i].isPopoverOpen = false;
      } else {
        this.data[this.idCheck].isPopoverOpen = false;
      }
    }
      this.data[i].isPopoverOpen = true;
      this.idCheck = i;
      this.divVisible = true;
  }

  async statusChange(i: any, j: any) {
    if (this.at_risk == 1 && j == 8) {
      const content1 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.at_risk_error ? this.retrieveMessages[0].errors.at_risk_error : 'Project in AT Risk Flag' : 'Project in AT Risk Flag';
      const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.ytb_error ? this.retrieveMessages[0].errors.ytb_error : 'Cannot be moved to YTB' : 'Cannot be moved to YTB';
      this.toasterService.showWarning(content1 + ' ' + content2, 10000);
    }
    else if ((this.item_status_id == 7 || this.item_status_id == 12 || this.item_status_id == 6) && j == 8) {
      let checkName = _.findWhere(this.pmMasterService.status_list, { id: this.item_status_id })
      const content1 = checkName ? this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.status_error + checkName['name'] + " Status! " ? this.retrieveMessages[0].errors.status_error + checkName['name'] + " Status! " : 'Project is in ' + checkName['name'] + " Status! " : 'Project is in ' + checkName['name'] + " Status! " : "";
      const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.ytb_error ? this.retrieveMessages[0].errors.ytb_error : 'Cannot be moved to YTB' : 'Cannot be moved to YTB';
      this.toasterService.showWarning(content1 + ' ' + content2, 10000);
    }
    else if ((this.profit_center == '' || this.profit_center == ' ' || this.profit_center == null || this.profit_center == "null") && j == 8) {
      const content1 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.at_risk_error ? this.retrieveMessages[0].errors.at_risk_error : 'Project code is Empty' : 'Project code is Empty';
      const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.ytb_error ? this.retrieveMessages[0].errors.ytb_error : 'Cannot be moved to YTB' : 'Cannot be moved to YTB';
      this.toasterService.showWarning(content1 + ' ' + content2, 10000);
    }
    else {

      let allow = true;
      if (j == 8) {
        const content3 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.ytb_error_line2 ? this.retrieveMessages[0].errors.ytb_error_line2 : '' : '';
        await this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to move to YTB?", content3).then((result) => {
          if (!result) {
            allow = false;
          }

        });
      }

      if (allow) {
        const oldStatus = this.data[i].financial_status;
        for (let items of this.milestone_status_list) {
          if (items['id'] == j) {
            this.data[i].financial_status_id = items['id']
            this.data[i].financial_status = items['name']
            for (let item of JSON.parse(items['status_color'])) {
              this.data[i].color = item['color']
              this.data[i].background = item['background']
              this.data[i].border = item['border']
            }
            if (j == 8) {
              this.data[i].completion_percentage = 100
            }
          }
        }
        let oldData = { Status: oldStatus }
        let newData = { Status: this.getNameByIdPipe.transform(j, this.milestone_status_list)}
        this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, j, this.data[i].id, this.data[i].currencyList, this.data[i].month, moment().format('YYYY-MM-DD'),oldData, newData,26)
      }
    }

    this.data[i].isPopoverOpen = false
  }
  openAttachTask(Mid: any, start_date: any, end_date: any, Sid: any, value: any) {
    const dialogRef = this.dialog.open(AttachTaskComponent, {
      data: { "Mid": Mid, "start_date": start_date, "end_date": end_date, "itemID": this.ItemID, "projectID": this.projectID, "status_id": Sid, "value": value },
      disableClose: true,
    });
  }
  addTag(i: any) {
    if (this.data[i].financial_status_id < 8) {
      if (this.data[i].Tags.length < 8) {
        this.tagCheck = i
        this.data[i].tagPopUpOpen = !this.data[i].tagPopUpOpen
        this.isDisplayVisible = true
        this.stepperFormGroup.patchValue({ ['textInputControlDisplay']: '' })
      }
      else {
        const max_tag = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.max_tag ? this.retrieveMessages[0].errors.max_tag : 'Cannot be add more than 8 tags' : 'Cannot be add more than 8 tags';
        this.toasterService.showWarning(max_tag, 10000);
      }
    }
    else {
      const tagRestrict_ytb = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.tagRestrict_ytb ? this.retrieveMessages[0].errors.tagRestrict_ytb : 'Tag Cannot be added Milestone status YTB/Billed/Payment-received/partial-payment' : 'Tag Cannot be added Milestone status YTB/Billed/Payment-received/partial-payment';
      this.toasterService.showWarning(tagRestrict_ytb, 10000);
    }
  }


  openEditName(i: number, month_check: boolean) {
    this.lumpsum_left = '48px'
    if (this.idHigh == undefined || this.idHigh == null) {
      this.idHigh = i
      this.edit = 'yes'
      this.data[i].box = '0px 0px 8px 0px #DB6E61'
      this.check = true
      this.edit_data = this.data[i]
      this.tags = this.data[i].Tags
      this.check_month = month_check
    }
    else if (this.idHigh != i) {
      this.data[this.idHigh].box = '0px 0px 0px 0px #DB6E61'
      this.idHigh = i
      this.edit = 'yes'
      this.data[i].box = '0px 0px 8px 0px #DB6E61'
      this.check = true
      this.edit_data = this.data[i]
      this.tags = this.data[i].Tags
      this.check_month = month_check
    }
    else {
      this.data[this.idHigh].box = '0px 0px 0px 0px #DB6E61'
      this.idHigh = undefined
      this.check_month = false
      this.check = false
    }
  }

  addNotes(id: number) {
    this.commentsInput.application_id = 915;
    this.commentsInput.application_name = "Project Management";
    this.commentsInput.title = this.data[id].label;
    this.commentsInput.unique_id_1 = this.data[id].id;
    let modalParams = {
      inputData: this.commentsInput,
      context: {
        'Bill Name': this.data[id].label,
        'Bill Start Date': moment(this.data[id].start_date).format('DD-MMM-YY'),
        'Bill End Date': moment(this.data[id].end_date).format("DD-MMM-YY"),
        'Bill Status': this.data[id].financial_status
      },
      commentBoxHeight: '100vh',
      commentBoxScrollHeight: '80%'
    };
    const openChatCommentContextModalComponent = this.dialog.open(ChatCommentContextModalComponent, {
      height: '100%',
      width: '75%',
      position: { right: '0px' },
      data: { modalParams: modalParams }
    });
  }
  async getDisplayTag(k: number, i: number) {
    let duplicate_check = false
    this.data[i].tagActive = true
    if (this.data[i].Tags.length > 0) {
      for (let item of this.data[i].Tags) {
        if (item['unique_id_2'] == this.data[i].id && item['id'] == this.tagsPopUp[k].id) {
          duplicate_check = true
        }
      }
    }
    if (duplicate_check) {
      const tag_selected = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.tag_selected ? this.retrieveMessages[0].errors.tag_selected : 'Tag already selected' : 'Tag already selected';
      this.toasterService.showWarning(tag_selected, 10000);
    }

    else {
      this.data[i].Tags.push({ id: this.tagsPopUp[k].id, name: this.tagsPopUp[k].name, color: this.tagsPopUp[k].color, unique_id_2: this.data[i].id })
      this.data[i].tagPopUpOpen = false
      this.stepperFormGroup.patchValue({ ['textInputControlDisplay']: '' })
      await this.PmBillingService.insertTagFromBilling({ id: this.tagsPopUp[k].id, name: this.tagsPopUp[k].name, color: this.tagsPopUp[k].color, unique_id_2: this.data[i].id }).then((res) => {

      })
    }
  }


  async addAttachment(milestone_id, gantt_id) {
    let context_id = "PRJ_" + milestone_id + gantt_id
    this.attachmentService.openAttachment(context_id, this.formConfig)
  }

  create() {
    if (this.billWriteAccess) {
      const dialogRef = this.dialog.open(PmBillsEditComponent, {
        disableClose: true,
        data: {
          mode: 'Create',
          data: '',
          at_risk: this.at_risk,
          button: this.button
        },
      });
      dialogRef.afterClosed().subscribe(async (result) => {
        this.loading = true
        //console.log(result)
        if (result['messType'] == "S") {
          await this.getFinnancialValues()
          this.getMilestones()
        }
        else {
          this.loading = false
        }
      });
    }
    else {
      const noAccess_tocreate = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.noAccess_tocreate ? this.retrieveMessages[0].errors.noAccess_tocreate : 'You are not having access to create Bills' : 'You are not having access to create Bills';
      this.toasterService.showWarning(noAccess_tocreate, 10000)
    }
  }

  async handleValueEmitted(emittedValue: any) {
    //console.log(emittedValue)
    if (emittedValue.value1 == 'yes') {
      this.left = '28px'
      this.lumpsum_left = '48px'
      this.check = true
      //console.log(this.check)
      this.edit_data = emittedValue.value2
    }
    else if (emittedValue.value1 == 'new') {
      this.loading = true
      await this.getFinnancialValues()
      this.getMilestones()
    }
    else {
      this.left = '28px'
      this.lumpsum_left = '-15px'
      this.check = false
      this.edit_data = emittedValue.value2
    }
  }
  async openEdit() {
    if (this.billWriteAccess) {
      this.check = false
      this.left = '28px'
      this.lumpsum_left = '48px'
      const editConfig = _.where(this.formConfig, { type: "bills-landing", field_name: "edit_bills", is_active: true });
      const disabledActionType = editConfig && editConfig[0]?.isDisabledActionBy ? editConfig[0]?.isDisabledActionBy : [];
      if( _.includes(disabledActionType,this.edit_data.action_type)){ 
        const disable_edit_message = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.disable_edit_message ? this.retrieveMessages[0].errors.disable_edit_message : 'System generated bills cannot be edited' : 'System generated bills cannot be edited';
        this.toasterService.showWarning(disable_edit_message, 10000);
        return;
      }
      if (this.edit_data.financial_status_id < 8 || (this.edit_data.financial_status_id == 8 && this.edit_data.milestone_type == 1)) {
        const dialogRef = this.dialog.open(PmBillsEditComponent, {
          disableClose: true,
          data: {
            mode: 'Edit',
            data: this.edit_data,
            tags: this.tags,
            at_risk: this.at_risk,
            button: this.button
          },
        });
        dialogRef.afterClosed().subscribe(async (result) => {
          // this.loading = true
          //console.log(result)
          if (result['messType'] == "S") {
            // this.getEditedMilestones(result['data'])
            this.loading = true
            await this.getFinnancialValues()
            await this.getMilestones()
            await this.clearCheckedItem()
            this.check = false;
            this.idHigh = undefined
            this.check_month = false
          }
          else {
            await this.clearCheckedItem()
            this.loading = false
          }
        });
      }
      else {
        await this.clearCheckedItem()
        const cannotEdit_YTB = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.cannotEdit_YTB ? this.retrieveMessages[0].errors.cannotEdit_YTB : 'Bill status YTB/Billed/Payment-received/partial-payment cannot be edited' : 'Bill status YTB/Billed/Payment-received/partial-payment cannot be edited';
        this.toasterService.showWarning(cannotEdit_YTB, 10000);
      }
    }
    else {
      await this.clearCheckedItem()
      const noAccess_toedit = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.noAccess_toedit ? this.retrieveMessages[0].errors.noAccess_toedit : 'You are not having access to Edit Bill' : 'You are not having access to Edit Bill';
      this.toasterService.showWarning(noAccess_toedit, 10000)
    }
  }
  async openFilter() {
    this.openfilter = !this.openfilter
    await this.dueDate()

    //console.log(this.openfilter)
  }
  async dueDate() {
    this.openfilter = false
    this.duedate = !this.duedate
    this.loading = true
    if (this.duedate) {
      this.sort_tooltip = `<p>Sort oldest -&gt; newest</p>`
      await this.getFinnancialValues()
      await this.getMilestones(true)
      await this.getStatusFilterData(this.status_filter_id, this.status_filter_name)
    }
    else {
      this.sort_tooltip = 'Sort newest -> oldest'
      await this.getFinnancialValues()
      await this.getMilestones()
      await this.getStatusFilterData(this.status_filter_id, this.status_filter_name)

    }
    this.loading = false;
  }
  deleteBills() {

    if (this.billWriteAccess) {
      const editConfig = _.where(this.formConfig, { type: "bills-landing", field_name: "edit_bills", is_active: true });
      const disabledActionType = editConfig && editConfig[0]?.isDisabledActionBy ? editConfig[0]?.isDisabledActionBy : [];
      if( _.includes(disabledActionType,this.edit_data.action_type)){ 
        const disable_edit_message = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.disable_edit_message ? this.retrieveMessages[0].errors.disable_edit_message : `Bills generated from ${this.edit_data.action_type} cannot be deleted` : `Bills generated from ${this.edit_data.action_type} cannot be deleted`;
        this.toasterService.showWarning(disable_edit_message, 10000);
        return;
      }
      if (this.edit_data.financial_status_id < 8) {
        Swal.fire({
          title: 'Alert Message',
          icon: 'warning',
          html: `
          
            <h2>Do you want to delete Bill?</h2>
        `,
          showCancelButton: true,
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          allowOutsideClick: false,
          allowEscapeKey: false,
        }).then(async (result) => {
          if (result.isConfirmed) {
            await this.PmBillingService.deleteBill(this.edit_data.id).then(async (res) => {
              if (res['messType'] == 'S') {
                const delete_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.delete_success ? this.retrieveMessages[0].errors.delete_success : 'Bill deleted successfully' : 'Bill deleted successfully';
                this.toasterService.showSuccess(delete_success, 10000);
                this.loading = true
                await this.getFinnancialValues()
                this.getMilestones()
                this.check = false;
              }
              else {
                const delete_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.delete_unsuccess ? this.retrieveMessages[0].errors.delete_unsuccess : 'Bill deleting unsuccessfull' : 'Bill deleting unsuccessfull';
                this.toasterService.showError(delete_unsuccess);
              }
            })
          }
          else if (result.dismiss === Swal.DismissReason.cancel) {

          }
        });
      }
      else {
        const cannotDelete_YTB = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.cannotDelete_YTB ? this.retrieveMessages[0].errors.cannotDelete_YTB : 'Bill status YTB/Billed/Payment-received/partial-payment cannot be deleted' : 'Bill status YTB/Billed/Payment-received/partial-payment cannot be deleted';

        this.toasterService.showWarning(cannotDelete_YTB, 10000);
      }
    }
    else {
      const noAccess_todelete = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.noAccess_todelete ? this.retrieveMessages[0].errors.noAccess_todelete : 'You are not having access to Delete Bill' : 'You are not having access to Delete Bill';

      this.toasterService.showWarning(noAccess_todelete, 10000)
    }
  }


  validateCreation() {
    if (this.authService.getProjectObjectAccess(39)) {
      if (this.projectInfo && moment(this.currentDate).isAfter(moment(this.projectInfo['planned_end_date']).endOf('day'))) {
        //console.log('Date-failure', this.projectInfo['planned_end_date'], this.currentDate);
        return false;
      }
      if (this.projectInfo && !_.contains([1, 4, 7], this.projectInfo['item_status_id'])) {
        //console.log('status-failure')
        return false;
      }

      return true;
    }
    else {
      return false;
    }
  }
  async getMilestones(sort:any = false) {
    try {
      let sort_type = 'DESC'
      if(sort){
        sort_type = 'ASC'
      }
      const billsRes = await this.PmBillingService.getBillsData(this.projectID, this.ItemID, sort_type);
        if (billsRes['data'] && billsRes['data'].length > 0 && billsRes['data'][0].end_date) {
          let month = moment(billsRes['data'][0].end_date).format('M');
          billsRes['data'][0].month_check = true;
          billsRes['data'][0].month = moment(billsRes['data'][0].end_date).format('MMM YYYY');
          let id = billsRes['data'][0].id;
          console.log('Matrix:',this.milestoneMatrix)
          for (let items of billsRes['data']) {
            items['tagActive'] = false;
            if (id != items['id']) {
              if (month == moment(items['end_date']).format('M')) {
                items['month_check'] = false;
                items['month'] = moment(items['end_date']).format('MMM YYYY');
              } else {
                month = moment(items['end_date']).format('M');
                items['month_check'] = true;
                items['month'] = moment(items['end_date']).format('MMM YYYY');
              }
            }

            items['status_color'] = JSON.parse(items['status_color']);
            items['isPopoverOpen'] = false;
            items['tagPopUpOpen'] = false;
            items['box'] = '0px 0px 0px 0px #DB6E61';
            items['selected'] = false;

            if (items['financial_status_id'] == 7 || items['financial_status_id'] == 4) {
              items['completion_percentage'] = 0;
            } else {
              items['completion_percentage'] = 100;
            }

            if (items['financial_status_id'] == 4 && items['end_date'] < moment().format()) {
              items['status'] = 'Overdue';
            }

            for (let item of items['status_color']) {
              items["border"] = item["border"];
              items["background"] = item["background"];
              items["color"] = item["color"];
            }

            if (!items['milestone_type']) {
              items['milestone_type'] = 4;
              items['milestone_type_name'] = 'Standard';
              items['milestone_type_color'] = '#FFBD3D';
            }

            if (this.milestone_grouped_data.length > 0) {
              let temp_id = _.where(this.milestone_grouped_data, { parentId: items['id'] });
              if (temp_id.length > 0) {
                items['parent'] = 1;
                items['child'] = 0;
                items['expand'] = true;
                items['child_data'] = [];
                for (let item of temp_id) {
                  let temp_data = _.where(billsRes['data'], { id: item['childId'] });
                  if (temp_data.length > 0) {
                    items['child_data'].push(temp_data[0]);
                  }
                }
              } else {
                let temp_child_id = _.where(this.milestone_grouped_data, { childId: items['id'] });
                if (temp_child_id.length > 0) {
                  items['parent'] = 0;
                  items['child'] = 1;
                } else {
                  items['parent'] = 0;
                  items['child'] = 0;
                }
              }
            } else {
              items['parent'] = 0;
              items['child'] = 0;
            }

            //For Bills Reversal, Credit Note and Cancel Options
            await this.authService.getReadWriteAccess(this.projectID, this.ItemID).then(async(res)=>{
              if(res)
              {
                //console.log("Hellow", {service_type_id: this.service_type_id, milestone_status_id: items['financial_status_id'], milestone_type_id:items['milestone_type']})
                let matrix = _.findWhere(this.milestoneMatrix,{service_type_id: this.service_type_id, milestone_status_id: items['financial_status_id'], milestone_type_id:items['milestone_type'], is_bills: 1})
                console.log(matrix)
                //Open & Execution Milestone
                if(items['financial_status_id']<8 || items['financial_status_id']==15)
                {
                  items['cancelButtonEnabled'] = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 305) && (matrix ? (matrix['cancel_submit'] ? true : false) : false)
                  console.log(this.service_type_id,items['financial_status_id'],items['milestone_type'])
                  console.log('Cancel',await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 305) && (matrix ? (matrix['cancel_submit'] ? true : false) : false))
                }
                
                //YTB Milestone
                if(items['financial_status_id'] == 8)
                {
                  items['reversalButtonEnabled'] = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 95) && (matrix ? (matrix['reversal_submit'] ? true : false): false)
                }

                //Accrual Milestone
                if(items['financial_status_id'] == 15)
                {
                  items['reversalButtonEnabled'] = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 94) && (matrix ? (matrix['reversal_submit'] ? true : false): false)
                  items['partialInvoiceButtonEnabled'] = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 307) && (matrix ? (matrix['partial_invoice_submit'] ? true :  false) : false)
                }

                //YTB, Billed, Partial Payment, Payment Recieved
                if(items['financial_status_id'] >=8 && items['financial_status_id']<15)
                {
                  items['creditNoteButtonEnabled'] = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 306) && (matrix ? (matrix['credit_note_submit'] ? true : false): false)
                }
              }
            })
          }
          
          this.data = billsRes['data'];
          this.temp = this.data;
          console.log('Data:',this.data);
          // const tagsRes = await this.PmBillingService.getTags(this.listMilestoneId);
          // if (tagsRes['messType'] === 'S') {
          //   this.tags = tagsRes['data'];
          //   for (let i = 0; i < this.listMilestoneId.length; i++) {
          //     const mandate = _.where(this.tags, { unique_id_2: this.listMilestoneId[i] });
          //     if (mandate.length > 0) {
          //       for (let items of this.data) {
          //         if (items['id'] == this.listMilestoneId[i]) {
          //           items['tagActive'] = true;
          //           items['Tags'] = mandate;
          //         }
          //       }
          //     } else {
          //       for (let items of this.data) {
          //         if (items['id'] == this.listMilestoneId[i]) {
          //           items['tagActive'] = false;
          //           items['Tags'] = mandate;
          //         }
          //       }
          //     }
          //   }
          //   this.chips = this.tags;
          // }

          this.loading = false;
        } else {
          console.error('Bills data is not in the expected format:', billsRes['data']);
          this.data = [];
          this.loading = false;
        }
    } catch (error) {
      console.error('Error retrieving data:', error);
      this.data = [];
      this.loading = false;
    }
  }

  // async getMilestones() {
  //   await this.PmBillingService.retrieveMilestoneId(this.ItemID).then(async (res: any) => {
  //     this.milestoneId = res['data']
  //     //console.log(this.milestoneId)
  //     //     if(this.milestoneId.length>15){
  //     //     const loadSize = 15;
  //     //   for (let i = 0; i < this.milestoneId.length; i += loadSize) {
  //     //     this.loadedData.push(this.milestoneId.slice(i, i + loadSize));
  //     //     //console.log(this.loadedData)
  //     //     this.retriveddData=this.PmBillingService.retrieveMilestone(this.loadedData)
  //     //     this.data=[...this.data,...this.retriveddData]
  //     //   }
  //     // }
  //     if (this.milestoneId.length > 0) {
  //       this.listMilestoneId = _.pluck(this.milestoneId, 'id')
  //       //console.log(this.listMilestoneId)
  //       await this.PmBillingService.getBillsData(this.projectID, this.ItemID).then((res: any) => {
  //         console.log("Date Res" ,res)
  //         // res['data'][0].end_date=moment(res['data'][0].end_date).format('YYYY-MM-DD')
  //         // res['data'][0].start_date=moment(res['data'][0].start_date).format('YYYY-MM-DD')
  //         let month = moment(res['data'][0].end_date).format('M')
  //         res['data'][0].month_check = true
  //         res['data'][0].month = moment(res['data'][0].end_date).format('MMM YYYY')
  //         let id = res['data'][0].id
  //         for (let items of res['data']) {
  //           //console.log(month)
  //           items['tagActive'] = false
  //           if (id != items['id']) {
  //             if (month == moment(items['end_date']).format('M')) {
  //               items['month_check'] = false
  //               items['month'] = moment(items['end_date']).format('MMM YYYY')
  //             }
  //             else {
  //               month = moment(items['end_date']).format('M')
  //               items['month_check'] = true
  //               items['month'] = moment(items['end_date']).format('MMM YYYY')
  //             }
  //           }


  //           items['status_color'] = JSON.parse(items['status_color'])
  //           items['isPopoverOpen'] = false
  //           items['tagPopUpOpen'] = false
  //           items['box'] = '0px 0px 0px 0px #DB6E61'
  //           items['selected'] = false
  //           //  items['border_box']='1px solid white'
  //           if (items['financial_status_id'] == 7) {
  //             items['completion_percentage'] = 0
  //           }
  //           else if (items['financial_status_id'] == 4) {
  //             items['completion_percentage'] = 0
  //           }
  //           else {
  //             items['completion_percentage'] = 100
  //           }
  //           if (items['financial_status_id'] == 4 && items['end_date'] < moment().format()) {
  //             items['status'] = 'Overdue'
  //           }

  //           //console.log(items)
  //           for (let item of items['status_color']) {
  //             items["border"] = item["border"]
  //             items["background"] = item["background"]
  //             items["color"] = item["color"]

  //           }
  //           if (items['milestone_type'] == null || items['milestone_type'] == '' || items['milestone_type'] == undefined) {
  //             items['milestone_type'] = 4
  //             items['milestone_type_name'] = 'Standard'
  //             items['milestone_type_color'] = '#FFBD3D'
  //           }
  //           if (this.milestone_grouped_data.length > 0) {
  //             let temp_id = []
  //             temp_id = _.where(this.milestone_grouped_data, { parentId: items['id'] })
  //             if (temp_id.length > 0) {
  //               items['parent'] = 1
  //               items['child'] = 0
  //               items['expand'] = true
  //               items['child_data'] = []
  //               for (let item of temp_id) {
  //                 let temp_data = []
  //                 temp_data = _.where(res['data'], { id: item['childId'] })
  //                 items['child_data'].push(temp_data[0])
  //               }
  //             }
  //             else {
  //               let temp_child_id = _.where(this.milestone_grouped_data, { childId: items['id'] })
  //               if (temp_child_id.length > 0) {
  //                 items['parent'] = 0
  //                 items['child'] = 1
  //               }
  //               else {
  //                 items['parent'] = 0
  //                 items['child'] = 0
  //               }

  //             }
  //           }
  //           else {
  //             items['parent'] = 0
  //             items['child'] = 0
  //           }
  //         }
  //         this.data = res['data']
  //         this.temp = this.data
  //         //console.log(this.data)
  //       })
  //       await this.PmBillingService.getTags(this.listMilestoneId).then((res: any) => {
  //         if (res['messType'] == 'S') {
  //           this.tags = res['data']
  //           //console.log(this.tags)
  //           for (let i = 0; i < this.listMilestoneId.length; i++) {
  //             const mandate = _.where(this.tags, { unique_id_2: this.listMilestoneId[i] });
  //             //console.log(mandate)
  //             if (mandate.length > 0) {
  //               for (let items of this.data) {
  //                 if (items['id'] == this.listMilestoneId[i]) {
  //                   items['tagActive'] = true
  //                   items['Tags'] = mandate
  //                 }
  //               }

  //             }
  //             else {
  //               for (let items of this.data) {
  //                 if (items['id'] == this.listMilestoneId[i]) {
  //                   items['tagActive'] = false
  //                   items['Tags'] = mandate
  //                 }
  //               }
  //             }
  //           }
  //           this.chips = this.tags
  //         }
  //       })
  //       this.loading = false
  //     }
  //     else {
  //       this.data = []
  //       this.loading = false
  //     }
  //   })
  // }
  async getEditedMilestones(id) {
    this.loading = true
    await this.PmBillingService.getEditedMilestone(id).then((res) => {
      //console.log(res)
      if (res['data'] && res['data'].length > 0) {
        let month = moment(res['data'][0].end_date).format('M')
        res['data'][0].month_check = this.check_month
        res['data'][0].month = moment(res['data'][0].end_date).format('MMM YYYY')
        let id = res['data'][0].id
        for (let items of res['data']) {
          //console.log(month)
          items['tagActive'] = false
          if (id != items['id']) {
            if (month == moment(items['end_date']).format('M')) {
              items['month_check'] = this.check_month
              items['month'] = moment(items['end_date']).format('MMM YYYY')
            }
            else {
              month = moment(items['end_date']).format('M')
              items['month_check'] = this.check_month
              items['month'] = moment(items['end_date']).format('MMM YYYY')
            }
          }

          items['status_color'] = JSON.parse(items['status_color'])
          items['isPopoverOpen'] = false
          items['tagPopUpOpen'] = false
          items['box'] = '0px 0px 0px 0px #DB6E61'
          //  items['border_box']='1px solid white'
          if (items['financial_status_id'] == 7) {
            items['completion_percentage'] = 0
          }
          else if (items['financial_status_id'] == 4) {
            items['completion_percentage'] = 0
          }
          else {
            items['completion_percentage'] = 100
          }
          if (items['financial_status_id'] == 4 && items['end_date'] < moment().format()) {
            items['status'] = 'Overdue'
          }
          //console.log(items)
          for (let item of items['status_color']) {
            items["border"] = item["border"]
            items["background"] = item["background"]
            items["color"] = item["color"]
          }

        }
        //console.log(id)
        for (let i = 0; i < this.data.length; i++) {
          if (this.data[i].id == id) {
            //console.log('test edit')
            this.data[i] = res['data'][0]
            //console.log(this.data[i])
          }
        }
        this.temp = this.data
        //console.log(this.data)
      }
    })
    await this.PmBillingService.getTags(this.listMilestoneId).then((res: any) => {
      if (res['messType'] == 'S') {
        this.tags = res['data']
        //console.log(this.tags)
        for (let i = 0; i < this.listMilestoneId.length; i++) {
          const mandate = _.where(this.tags, { unique_id_2: this.listMilestoneId[i] });
          //console.log(mandate)
          if (mandate.length > 0) {
            for (let items of this.data) {
              if (items['id'] == this.listMilestoneId[i]) {
                items['tagActive'] = true
                items['Tags'] = mandate
              }
            }

          }
          else {
            for (let items of this.data) {
              if (items['id'] == this.listMilestoneId[i]) {
                items['tagActive'] = false
                items['Tags'] = mandate
              }
            }
          }
        }
        this.chips = this.tags
      }
    })
    this.loading = false
  }
  async getFinnancialValues() {
    this.totalMilestoneValue = 0
    await this.PmBillingService.getProjectQuote(this.projectID, this.ItemID).then((res) => {
      if (res['messType'] == 'S') {
        this.code = res['data'][0].currency_code
        this.project_end_date = moment(res['data'][0].planned_end_date).format('YYYY-MM-DD')
        this.project_start_date = moment(res['data'][0].planned_start_date).format('YYYY-MM-DD')
        this.item_value_json = res['data'][0]['item_value_json']
        this.quote = res['data'][0].planned_quote
        this.item_status_id = res['data'][0].item_status_id
        this.payment_term_id = res['data'][0].payment_terms
        //console.log(this.code)
      }
    })

    await this.PmBillingService.fetchProjectCurrency(this.projectID, this.ItemID, this.code).then((res) => {
      this.convertCurrencyRecords = res;
    })
    await this.PmBillingService.getSumOFBillsValue(this.ItemID).then((res) => {
      if (res['messType'] == 'S') {
        for (let items of res['data']) {
          items['milestone_value'] = JSON.parse(items['milestone_value'])
          for (let item of items['milestone_value']) {
            if (item['currency_code'] == this.code) {
              this.totalMilestoneValue = this.totalMilestoneValue + item['value'];
            }
          }
        }

        this.overallBillsValue = this.convertMilestoneOverallCurrency()
      }
    })
  }
  toggleList(value: any) {
    this.loading = true
    for (let d of this.data) {
      d['selected'] = false
    }
    this.check = false;
    if (value == 'list') {
      this.listView = true;
      this.loading = false

    }
    else {
      this.listView = false;
      this.loading = false
    }
  }
  moreOption() {
    this.moreOptionClicked = !this.moreOptionClicked;
  }
  onRowSelect(index: number, item: any): boolean {
    return this.selectedRowIndex === index;
  }
  getSelectedRow(clickedIndex: number, month_check: any) {
    for (let i = 0; i < this.data.length; i++) {
      if (i === clickedIndex) {
        if (this.data[i].selected) {
          this.data[i].selected = false
        }
        else {
          this.data[i].selected = true
        }

        // if(this.data[i].selected == true){
        this.openEditName(clickedIndex, month_check);
        //}
      } else {
        this.data[i].selected = false;
      }
      console.log("Sselected Data", this.data)
      this.check = _.findWhere(this.data, { selected: true }) ? true : false
    }
  }
  getStatusFilterData(id, name) {
    if (this.temp.length > 0) {
      //console.log(id)
      //console.log('temp', this.temp)
      //console.log('data', this.data)
      this.loading = true
      this.data = []
      if (id == -1) {
        this.data = this.temp
        this.status_filter_id = id;
        this.status_filter_name = name
        this.loading = false

      }
      else {
        for (let item of this.temp) {
          //console.log('id', item['financial_status_id'])
          if (item['financial_status_id'] == id) {
            this.data.push(item)
          }
        }
        this.status_filter_id = id;
        this.status_filter_name = name
        this.loading = false
      }
    }
  }

  convertMilestoneOverallCurrency() {
    let converted_format = []
    if (this.convertCurrencyRecords) {
      //console.log("Milesotne Value", this.totalMilestoneValue)
      for (let [key, val] of Object.entries(this.convertCurrencyRecords)) {
        let res = {
          "currency_code": key,
          "value": this.totalMilestoneValue * val['conversion_rate']
        }

        converted_format.push(res)
      }


    }

    //console.log(converted_format)
    return converted_format
  }
  openLumpsumAdvise() {
    const dialogRef = this.dialog.open(PmLumpsumBillingAdviseComponent, {
      data: { "itemID": this.ItemID, "projectID": this.projectID },
      disableClose: true,

    });
  }

  editMilestone(p) {
    this.edit_data = p
    this.openEdit();
  }
  async revertStatus() {
    console.log(this.edit_data)
    if (this.edit_data.financial_status_id == 8) {
      await this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to revert from YTB?", "").then(async (result) => {
        if (result) {
          await this.PmBillingService.revertMilestoneFromYTB(this.edit_data.id).then(async (res) => {
            if (res['messType'] == 'S') {
              this.loading = true
              await this.getFinnancialValues()
              this.getMilestones()
              this.check = false
              const revert_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.revert_success ? this.retrieveMessages[0].errors.revert_success : 'Milestone Successfully Reverted From YTB Status' : 'Milestone Successfully Reverted From YTB Status';
              this.toasterService.showSuccess(revert_success, 10000)
            }
            else {
              this.loading = false
              const revert_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.revert_unsuccess ? this.retrieveMessages[0].errors.revert_unsuccess : 'Milestone UnSuccessfull to Revert From YTB Status' : 'Milestone UnSuccessfull to Revert From YTB Status';
              this.toasterService.showWarning(revert_unsuccess, 10000)
            }
          })
        }
      });
    }
    if (this.edit_data.financial_status_id == 15) {
      await this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to revert from Accrued?", "").then(async (result) => {
        if (result) {
          await this.PmBillingService.revertMilestoneFromYTB(this.edit_data.id).then(async (res) => {
            if (res['messType'] == 'S') {
              this.loading = true
              await this.getFinnancialValues()
              this.getMilestones()
              this.check = false
              const revert_success = 'Milestone Successfully Reverted From Accrued Status';
              this.toasterService.showSuccess(revert_success, 10000)
            }
            else {
              this.loading = false
              const revert_unsuccess = 'Milestone UnSuccessfull to Revert From Accrued Status';
              this.toasterService.showWarning(revert_unsuccess, 10000)
            }
          })
        }
      });
    }
  }

  calculateDynamicContentHeight() {
    let dynamicHeight = window.innerHeight - 286 + 'px';
    document.documentElement.style.setProperty(
      '--milestoneListHeight',
      dynamicHeight
    );
  }

  /**
   * @description For Clearing check boxes
   */
  clearCheckedItem(){
    for (let item of this.data) {
      item['selected'] = false;
    }
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }
  showChild(id: any) {
    this.data[id].expand = !this.data[id].expand
  }

  /**
   * @description Routing to Full Screen Billing Advice
   * @param milestone_id 
   */
  openBillingAdvice(milestone_id: any){
    if(this.billingAdviceAccess)
      {
        this._router.navigateByUrl(`main/project-management/${this.projectID}/${this.ItemID}/${milestone_id}/billing_advice?action='work'&source=bill`);
      }
      else
      {
         this.toasterService.showWarning("You are not having access to Billing Advice", 10000)
      }
  }

  /**
   * @description Cancelling Bill Milestone
   * @param milestone 
   */
  cancelMilestone(milestone){
    this._router.navigateByUrl(`main/project-management/${this.projectID}/${this.ItemID}/${milestone.id}/billing_advice?action='cancel'&source=bill`);
  }

  /**
   * @description reverting Bill Milestone
   * @param milestone 
   */
  revertMilestone(milestone){
    this._router.navigateByUrl(`main/project-management/${this.projectID}/${this.ItemID}/${milestone.id}/billing_advice?action='revert'&source=bill`);
  }

  /**
   * @description Credit Note for Bill Milestone
   * @param milestone 
   */
  raiseCreditNote(milestone){
    this._router.navigateByUrl(`main/project-management/${this.projectID}/${this.ItemID}/${milestone.id}/billing_advice?action='creditNote'&source=bill`);
  }

}
