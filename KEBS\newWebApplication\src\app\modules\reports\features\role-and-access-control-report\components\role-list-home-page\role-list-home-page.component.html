<div class="roleListHomePageContainer">
    <div *ngIf="!isDataLoading" class="row justify-content-center align-items-center" style="height: 90vh;">
        <img [src]="'https://assets.kebs.app/KEBS_MAIN_GIF.gif'" [width]="90" [height]="90" [matTooltip]="'Loading...'"
            alt="Loading..." />
    </div>

    <div class="roleListContainer pt-2" *ngIf="isDataLoading">
        <div class="col d-flex justify-content-between align-items-center" style="margin-bottom:5px;">
            <div class="row">
                <div style="color: #ee4961; font-weight: bold;">Role List</div>
            </div>
            <div class="col-7"></div>

            <div class="row">
                <dx-button matTooltip="Download Permission List Report" class="row d-flex downloadPermissionListBtn" (onClick)="downloadPermissionList()" [disabled]="isPermissionListDownloaded">
                    <span *ngIf="!isPermissionListDownloaded">Download Permission List</span>
                    <img *ngIf="isPermissionListDownloaded" style="margin-top: -12px; align-items: center;"
                                            src="https://assets.kebs.app/loading_animation_gif.gif" height="29"
                                            width="30" alt="loading">                </dx-button>

                <dx-button icon="refresh" matTooltip="Refresh" (onClick)="loadRoleList()"></dx-button>
                <!-- <div>
                    <dx-button icon="trash" matTooltip="Delete {{rolebandLableConfig?.home_page_button?.role || 'Role'}}"
                        (onClick)="deleteSelectedRoles()" *ngIf="!isDeleteRoleLoading && deleteRoleGroupAccess">
                    </dx-button>
                    <div *ngIf="isDeleteRoleLoading" class="spinner-align">
                        <mat-spinner diameter="20" matTooltip="Please wait..."
                            style="margin-top: 8px;color:gray;"></mat-spinner>
                    </div>
                </div> -->
                <!-- <dx-button icon="add" matTooltip="Create New {{rolebandLableConfig?.home_page_button?.role || 'Role'}}"
                    (onClick)="createNewRoleAction()" *ngIf="addRoleGroupAccess"> </dx-button> -->
                <dx-button icon="download" matTooltip="Download Role List Report" (onClick)="exportGridToExcel()"> </dx-button>
                <dx-button icon="column-chooser" matTooltip="Configure Report" (onClick)="openColumnChooser()">
                </dx-button>
            </div>
        </div>

        <dx-data-grid #roleGroupDataGrid [allowColumnResizing]="true" [dataSource]="tableData" [showBorders]="true"
            [hoverStateEnabled]="true" class="dev-style" style="padding:10px;padding-top:0px;" [columnAutoWidth]="ture"
            (onExporting)="onExporting($event)" [width]="'100%'" [selectedRowKeys]="selectedRowKeys"
            (onSelectionChanged)="onSelectionChanged($event)"
            [selection]="{ mode: 'single', showCheckBoxesMode: 'always' }" (onRowUpdating)="onRowUpdating($event)">

            <dxo-toolbar>
                <dxi-item location="before">
                    <div>Role List</div>
                </dxi-item>
                <dxi-item location="after">
                    <dx-button icon="refresh" (onClick)="refreshFun()"> </dx-button>
                    <dx-button icon="add"> </dx-button>
                    <dx-button icon="trash"> </dx-button>

                    <dx-button icon="column-chooser" (onClick)="openColumnChooser()"></dx-button>
                </dxi-item>
            </dxo-toolbar>

            <!-- <dxo-editing mode="row" [allowUpdating]="true"> </dxo-editing> -->

            <!-- <dxo-selection [selectAllMode]="allMode" [showCheckBoxesMode]="checkBoxesMode" mode="multiple"></dxo-selection> -->

            <dxo-column-chooser [enabled]="true" mode="select"> </dxo-column-chooser>

            <!-- <dxo-search-panel [visible]="true" [width]="240" placeholder="Search..."></dxo-search-panel> -->
            <!-- <dxo-export [enabled]="true"></dxo-export> -->

            <dxo-header-filter [visible]="true"></dxo-header-filter>
            <dxo-filter-row [visible]="true"></dxo-filter-row>

            <dxi-column dataField="role_id" caption="Role ID" [allowSorting]="true" [allowFiltering]="true" width="110"
                [allowReordering]="true" alignment='left' [allowEditing]="false">
            </dxi-column>

            <dxi-column dataField="role_name" caption="Role Name" [allowSorting]="true" [allowFiltering]="true"
                alignment='left' [minWidth]="auto" [allowReordering]="true">
            </dxi-column>

            <dxi-column dataField="role_description" caption="Role Description" [allowSorting]="true"
                [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
            </dxi-column>

            <dxi-column dataField="is_visible" caption="Role Visibility" [allowSorting]="true" [allowFiltering]="true"
                alignment='left' width="150" [allowReordering]="true">
                <dxo-lookup [dataSource]="roleVisible" displayExpr="Name" valueExpr="ID">
                </dxo-lookup>
            </dxi-column>

            <dxi-column caption="" name="actionColumn" dataField="view_role_details" [allowSorting]="false" [allowFiltering]="false" cellTemplate="notificationTemplate"
                [showFilterRow]="false" [allowReordering]="false">
                <div style="text-align: right;"></div>
            </dxi-column>

            <div style="display: flex; align-items: center;gap:5px;float:right;cursor: pointer;"
                matTooltip="View Role Details" [allowReordering]="true"
                *dxTemplate="let data of 'notificationTemplate'">
                    <div class="col d-flex" style="
                                align-items: center;
                                gap: 3%;" (click)="viewRoleDetails(data.data)">
                    <span>View
                        <mat-icon style="font-size: 13px;color: red;padding-top:5px;">open_in_new</mat-icon>
                    </span>

                </div>
            </div>
        </dx-data-grid>
    </div>
</div>