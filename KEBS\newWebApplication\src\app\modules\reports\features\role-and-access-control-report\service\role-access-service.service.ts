import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class RoleAccessServiceService {

  constructor(private http: HttpClient) { }

  checkAccessForRoleAccessReport = () => {
    return this.http.post(`/api/roleAccess/checkAccessForRoleAccessReport`, {})
  }

  getRoleList = () => {
    return this.http.post("/api/roleAccess/getRoleList", {})
  }

  getApplicationObjectList = () => {
    return this.http.post("/api/roleAccess/getApplicationObjectList", {})
  }

  getApplicationList = () => {
    return this.http.post("/api/roleAccess/getApplicationList", {})
  }

  getRolePermissionDetails = (role_id) => {
    return this.http.post("/api/roleAccess/getRolePermissionDetails", {
      role_id: role_id
    })
  }

  getRoleUsersDetails = (role_id) => {
    return this.http.post("/api/roleAccess/getRoleUsersDetails", {
      role_id: role_id
    })
  }

  downloadPermissionList = () => {
    return this.http.post("/api/roleAccess/downloadPermissionList", {});
  }
}
