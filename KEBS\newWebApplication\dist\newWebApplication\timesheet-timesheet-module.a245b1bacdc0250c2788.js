(window.webpackJsonp=window.webpackJsonp||[]).push([[1015],{RUIj:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetModule",(function(){return u}));var o=n("ofXK"),a=n("wZkO"),r=n("tyNb"),i=n("fXoL"),l=n("ug40");function s(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"a",4,5),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](1),t=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275property"]("routerLink",t.path)("active",e.isActive),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",t.label," ")}}function c(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275template"](1,s,3,3,"a",3),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.toDisplay)}}const p=[{path:"",component:(()=>{class e{constructor(e){this.tsService=e,this.projectTabAccess=this.tsService.getTabAccess(158),this.opportunityTabAccess=this.tsService.getTabAccess(159),this.wfhTabAccess=this.tsService.getTabAccess(160),this.timesheetTabLinks=[{label:"Submission",path:"submission",toDisplay:!0},{label:"Approvals",path:"approvals",toDisplay:!0},{label:"My Team",path:"myteam",toDisplay:!0},{label:"Remote WorX",path:"wfh",toDisplay:this.wfhTabAccess},{label:"Project",path:"project-governance-report",toDisplay:this.projectTabAccess},{label:"Opportunity",path:"opportunity-governance-report",toDisplay:this.opportunityTabAccess}]}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](l.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["timesheet-landing-page"]],decls:4,vars:1,consts:[["mat-tab-nav-bar",""],[4,"ngFor","ngForOf"],[1,"project-tab-content"],["mat-tab-link","","routerLinkActive","",3,"routerLink","active",4,"ngIf"],["mat-tab-link","","routerLinkActive","",3,"routerLink","active"],["rla","routerLinkActive"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"nav",0),i["\u0275\u0275template"](1,c,2,1,"div",1),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275element"](3,"router-outlet"),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",t.timesheetTabLinks))},directives:[a.f,o.NgForOf,r.l,o.NgIf,r.j,a.e,r.i],styles:["a[_ngcontent-%COMP%]:hover{color:unset!important;text-decoration:none!important}nav[_ngcontent-%COMP%]{position:fixed;width:100%;z-index:14;background:#f8f9fa}.project-tab-content[_ngcontent-%COMP%]{padding-top:48px!important}"]}),e})(),children:[{path:"",redirectTo:"submission",pathMatch:"full"},{path:"submission",loadChildren:()=>Promise.all([n.e(4),n.e(29),n.e(34),n.e(40),n.e(171),n.e(695)]).then(n.bind(null,"DmbW")).then(e=>e.TsSubmissionModule),data:{breadcrumb:"Submission"}},{path:"approvals",loadChildren:()=>Promise.all([n.e(4),n.e(29),n.e(34),n.e(170),n.e(0),n.e(691)]).then(n.bind(null,"+Nod")).then(e=>e.TsApprovalsModule),data:{breadcrumb:"Approvals"}},{path:"myteam",loadChildren:()=>Promise.all([n.e(170),n.e(0),n.e(694)]).then(n.bind(null,"sMdI")).then(e=>e.TsMyteamModule),data:{breadcrumb:"My Team"}},{path:"wfh",loadChildren:()=>Promise.all([n.e(8),n.e(1019)]).then(n.bind(null,"SSbP")).then(e=>e.WfhModule),data:{breadcrumb:"Remote WorX"}},{path:"project-governance-report",loadChildren:()=>Promise.all([n.e(1),n.e(2),n.e(3),n.e(4),n.e(5),n.e(6),n.e(7),n.e(9),n.e(10),n.e(11),n.e(12),n.e(13),n.e(14),n.e(15),n.e(16),n.e(17),n.e(18),n.e(21),n.e(23),n.e(30),n.e(29),n.e(34),n.e(162),n.e(861)]).then(n.bind(null,"L/uB")).then(e=>e.ProjectGovernanceReportModule),data:{breadcrumb:"Project Governance Report"}},{path:"opportunity-governance-report",loadChildren:()=>Promise.all([n.e(1),n.e(2),n.e(3),n.e(4),n.e(5),n.e(6),n.e(7),n.e(9),n.e(10),n.e(11),n.e(12),n.e(13),n.e(14),n.e(15),n.e(16),n.e(17),n.e(18),n.e(21),n.e(23),n.e(30),n.e(29),n.e(34),n.e(168),n.e(980)]).then(n.bind(null,"jP7X")).then(e=>e.SalesGovernanceNewModule),data:{breadcrumb:"Opportunity"}}]}];let d=(()=>{class e{}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(p)],r.k]}),e})(),u=(()=>{class e{}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,d,a.g]]}),e})()}}]);