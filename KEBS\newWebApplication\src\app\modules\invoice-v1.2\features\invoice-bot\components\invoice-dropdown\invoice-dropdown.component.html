<!-- <p>invoice-dropdown works!</p> -->
<div class="drop-down" [formGroup]="form" *ngIf="field.key_name != 'milestoneTagId'">
  <div class="row label-name">
    {{ field.label_name }}<span *ngIf="field.is_mandatory == 1" class="field-mandatory">&nbsp;*</span>
  </div>
  <div class="row label-name">
    <ng-container *ngIf="field.key_name != 'paymentTerm' || stepIndex == 0; else readOnlyField">
    <mat-form-field appearance="outline" class="full-width" [class.non-editable]="stepIndex === 1 && field.key_name == 'invoiceTemplateName'">
      <mat-select [id]="field.label_name" [formControlName]="field.key_name" [placeholder]="field.label_name"  [disabled]="!field.is_editable"
        [required]="field.is_mandatory == 1" [matTooltip]="getFieldValue(field.key_name)"  [disableOptionCentering]="true" panelClass="custom-mat-select-options">
        <mat-option *ngFor="let val of optionList" [value]="val.name" (click)="optionClicked(val,field.key_name)" class="label-name" >
          {{ field.key_name === 'placeOfSupply' && val.code ? '[' + val.code + '] - ' + val.name : val.name }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    </ng-container>
    <ng-template #readOnlyField>
      <!-- <input matInput [formControlName]="field.key_name" [readonly]="true"> -->
      <mat-form-field appearance="outline" class="full-width">
        <input matInput [formControlName]="field.key_name" type="text"
            [placeholder]="field.label_name"
            [matTooltip]="getFieldValue(field.key_name)"
            [readonly]="true"
            >
        </mat-form-field>
    </ng-template>

    <!-- {{index}} -->
  </div>
</div>

<div class="drop-down" [formGroup]="form" *ngIf="field.key_name == 'milestoneTagId'">
  <div class="row label-name">
    {{ field.label_name }}<span *ngIf="field.is_mandatory == 1" class="field-mandatory">&nbsp;*</span>
  </div>
  <div class="row label-name">
    <ng-container *ngIf="field.key_name != 'paymentTerm' || stepIndex == 0; else readOnlyField">
    <mat-form-field appearance="outline" class="full-width" [class.non-editable]="stepIndex === 1 && field.key_name == 'invoiceTemplateName'">
      <mat-select [id]="field.label_name" [formControlName]="field.key_name" [placeholder]="field.label_name"  [disabled]="!is_editable"
        [required]="field.is_mandatory == 1" [matTooltip]= "getTooltip(field.key_name)" [disableOptionCentering]="true" panelClass="custom-mat-select-options">
        <mat-option *ngFor="let val of optionList" [value]="val.id" (click)="tagOptionClicked(val,field.key_name)" class="label-name" [matTooltip]="val.id + ' - ' + val.name">
          {{val.id}} - {{ val.name }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    </ng-container>
    <ng-template #readOnlyField>
      <!-- <input matInput [formControlName]="field.key_name" [readonly]="true"> -->
      <mat-form-field appearance="outline" class="full-width">
        <input matInput [formControlName]="field.key_name" type="text"
            [placeholder]="field.label_name"
            [matTooltip]="getFieldValue(field.key_name)"
            [readonly]="true"
            >
        </mat-form-field>
    </ng-template>

    <!-- {{index}} -->
  </div>
</div>