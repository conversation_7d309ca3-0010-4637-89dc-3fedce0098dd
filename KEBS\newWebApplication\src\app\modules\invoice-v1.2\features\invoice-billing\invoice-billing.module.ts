import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { InvoiceBillingRoutingModule } from './invoice-billing-routing.module';
import { InvoiceBillingPageComponent } from './pages/invoice-billing-page/invoice-billing-page.component';
import {InlineEdit2Component} from "./components/inline-edit2/inline-edit2.component";
import {InlineFormFieldComponent} from  "./components/inline-form-field/inline-form-field.component"


import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatIconModule} from '@angular/material/icon';
import {MatButtonModule} from '@angular/material/button';
import {MatTooltipModule} from '@angular/material/tooltip';
import {FormsModule}   from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import {MatExpansionModule} from '@angular/material/expansion';
import {MatDatepickerModule} from '@angular/material/datepicker';
import {MatAutocompleteModule} from '@angular/material/autocomplete';
import {MatStepperModule} from '@angular/material/stepper';
import {MatCardModule} from '@angular/material/card';
import {MatTabsModule} from '@angular/material/tabs';
import { AttachmentMgmtModule } from 'src/app/modules/shared-lazy-loaded-components/attachment-mgmt/attachment-mgmt.module';

// import {SharedComponentsModule} from "src/app/app-shared/app-shared-components/components.module";
import { MomentModule } from 'ngx-moment';
import { NgxSpinnerModule } from 'ngx-spinner';
import { MatSelectModule } from '@angular/material/select';
import { FileUploadModule } from 'ng2-file-upload';
import { TooltipModule } from "ng2-tooltip-directive";
import {FileIconPipe} from "./pipes/file-icon.pipe";
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatHeaderCell, MatCell } from '@angular/material/table';
import {MatPaginatorModule} from '@angular/material/paginator'
import {InvoiceModule} from '../../invoice.module';
import { UserProfileComponent } from './components/user-profile/user-profile.component';
import { UserImageComponent } from './components/user-image/user-image.component';
import {MatButtonToggleModule} from '@angular/material/button-toggle';
@NgModule({
  declarations: [
    InvoiceBillingPageComponent,
    InlineEdit2Component,
    InlineFormFieldComponent,
    FileIconPipe,
    UserProfileComponent,
    UserImageComponent,
    FileIconPipe
  ],
  imports: [
    CommonModule,
    InvoiceBillingRoutingModule,
    MatFormFieldModule,
    MatInputModule,
    MatCardModule,
    MatStepperModule,
    MatAutocompleteModule,
    MatTabsModule,
    MatDatepickerModule,
    MatExpansionModule,
    ReactiveFormsModule,
    FormsModule,
    MatTooltipModule,
    MatIconModule,
    MatButtonModule,
    AttachmentMgmtModule,
    MomentModule,
    NgxSpinnerModule,
    MatSelectModule,
    InvoiceModule,
    FileUploadModule,
    TooltipModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    InvoiceModule,
    MatButtonToggleModule
  ]
})
export class InvoiceBillingModule { }
