import { Component, OnInit } from '@angular/core';
import * as moment from 'moment';
import { PmExpenseServiceService } from '../../services/pm-expense-service.service'
import { Router } from '@angular/router';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
@Component({
  selector: 'app-pm-expense-landing-page',
  templateUrl: './pm-expense-landing-page.component.html',
  styleUrls: ['./pm-expense-landing-page.component.scss']
})
export class PmExpenseLandingPageComponent implements OnInit {
  data: any = [];
  temp: any = [];
   projectInfo: any;
   currentDate = moment().format();
   ItemID: any
  projectID: any
  milestoneId: any
  costCentre: any
  // projectValueAccess: boolean = this.authService.getProjectObjectAccess(58)
  loading: boolean = true
    constructor(private PmExpenseServiceService: PmExpenseServiceService,private authService: PmAuthorizationService,private router: Router,) {}

    async getMilestones() {
  try {
    const billsRes = await this.PmExpenseServiceService.getExpenseData(this.projectID, this.ItemID);
    console.log("HI",billsRes)
    if (billsRes['data']) {
      // let month = moment(billsRes['data'][0].end_date).format('M');
      // billsRes['data'][0].month_check = true;
      // billsRes['data'][0].month = moment(billsRes['data'][0].end_date).format('MMM YYYY');
      let id = billsRes['data'][0].id;


      for (let items of billsRes['data']) {
        items['tagActive'] = false;

        // if (id != items['id']) {
        //   if (month == moment(items['end_date']).format('M')) {
        //     items['month_check'] = false;
        //     items['month'] = moment(items['end_date']).format('MMM YYYY');
        //   } else {
        //     month = moment(items['end_date']).format('M');
        //     items['month_check'] = true;
        //     items['month'] = moment(items['end_date']).format('MMM YYYY');
        //   }
        // }

        items['status_color'] = JSON.parse(items['status_color']);
        items['isPopoverOpen'] = false;
        items['tagPopUpOpen'] = false;
        items['box'] = '0px 0px 0px 0px #DB6E61';
        items['selected'] = false;

        for (let item of items['status_color']) {
          items["border"] = item["border"];
          items["background"] = item["background"];
          items["color"] = item["color"];
        }
      }

      this.data = billsRes['data'];
      this.temp = this.data;
      console.log('Data:', this.data);

      this.loading = false;
    } else {
      console.error('Bills data is not in the expected format:', billsRes['data']);
      this.data = [];
      this.loading = false;
    }
  } catch (error) {
    console.error('Error retrieving data:', error);
    this.data = [];
    this.loading = false;
  }
}


  ngOnInit(): void {
    this.ItemID = parseInt(this.router.url.split("/")[5])
    this.projectID = parseInt(this.router.url.split("/")[3])
    this.getMilestones()
  }

}
