<div *ngIf="data?.length == 0">
  <div>
    <div>
      <h1
        class="d-flex justify-content-center align-items-center mt-5 slide-in-top"
      >
        No Upcoming Payments Found !
      </h1>
    </div>
    <div
      class="d-flex justify-content-center align-items-center slide-from-down"
    >
      <img
        src="assets/images/nomilestone.png"
        class="mt-4"
        height="300"
        width="350"
      />
    </div>
  </div>

</div>
<div class="more-details container-fluid" *ngIf="data?.length != 0">
    <div class="row  pt-2 pl-2 pb-1">
      <div class="col-4 pl-0 tileName d-flex">
        <span class="my-auto"> Upcoming expected payments </span>
      </div>
      <div class="col-8 d-flex">
        <button
          mat-button
          style="font-weight: normal; color: #1E2733"
          class="ml-auto my-auto"
          (click)="toggleFilter()"
        >
          <mat-icon
            class="pr-2"
            matPrefix
            style="font-size: 19px; vertical-align: middle;  "
            >filter_list</mat-icon
          >
          Filter
        </button>
      </div>
    </div>
    <div class="filter mx-5" [@slideInOut]="filterVisible ? 'in' : 'out'">
      <div class="row pb-3">
        <div class="col-4">
          <div class="row">
            <mat-selection-list
              #list
              [(ngModel)]="pnlOptions"
              (ngModelChange)="onFilterChange($event, 'pnlSelected')"
            >
              <h2 mat-subheader class="p-0 mb-0">P & L</h2>
              <mat-list-option
                checkboxPosition="before"
                *ngFor="let item of pnlNames"
                [value]="item"
              >
                {{ item }}
              </mat-list-option>
            </mat-selection-list>
          </div>
        </div>
        <div class="col-4">
          <div class="row">
            <mat-selection-list
              #list
              [(ngModel)]="serviceTypeOptions"
              (ngModelChange)="onFilterChange($event, 'serviceSelected')"
            >
              <h2 mat-subheader class="p-0 mb-1">Project Type</h2>
              <mat-list-option
                checkboxPosition="before"
                *ngFor="let item of serviceTypeNames"
                [value]="item"
              >
                {{ item }}
              </mat-list-option>
            </mat-selection-list>
          </div>
        </div>
        <div class="col-4"></div>
        <div class="col-4"></div>
      </div>
    </div>
    <div *ngIf="upcomingPaymentData?.length != 0">
      <div class="row card-header-row pl-0 pb-2 pt-2">
        <div class="col-1">Cost Center</div>
        <div class="col-3 d-flex">
          <span class="pl-2"> Milestone Name</span>
        </div>
        <div class="col-3 d-flex">
          <span class="pl-2">Project Name </span>
        </div>
        <div class="pr-2 col-1 d-flex pl-3">
          <span> Value </span>
        </div>
        <div class="col-2 pl-3 d-flex">
          <span>Expected On </span>
        </div>
        <!-- <div class="col-3 pl-4 d-flex">
          <span>Payment to collection activities </span>
        </div> -->
      </div>
      <div
        *ngFor="
          let item of upcomingPaymentData
            | paginate: { itemsPerPage: 12, currentPage: p }
        "
        class=" row "
      >
        <div class="col-12 pl-0 pr-0">
          <div
            class="card listcard"
            style=" border-left: 3px solid #FF7200 !important;"
          >
            <div class="card-body" style="padding: 2px !important;">
              <div class="row card-details p-0">
                <div class="col-1 cost-center pl-3 mt-1">
                  {{ item.profit_center }}
                </div>
                <div
                  class="col-3 mt-1 card-content"
                  style="color:#cf0001 !important;font-weight:500"
                  [matTooltip]="item.milestone_name"
                >
                  {{ item.milestone_name }}
                </div>
                <div class="col-3 mt-1 card-content" [matTooltip]="item.item_name">
                  {{ item.item_name }}
                </div>
                <div class="col-1 mt-1 card-content">
                  <app-currency
                    [currencyList]="item.balance_amount"
                    class=""
                    type="small"
                  ></app-currency>
                </div>
                <div class="col-2  pl-3 mt-1 card-content">
                  {{ changeDate(item.expected_date) }}
                </div>
                <div class="col-3 mt-1 d-flex  card-content">
                  <!-- <div
                    *ngFor="let x of item.activities"
                    [matTooltip]="
                      x.activity_name +
                      ' - ' +
                      x.activity_due_date +
                      ' - ' +
                      (x.activity_status == 0 ? 'Open' : 'Completed')
                    "
                    class="wrapper"
                    [ngStyle]="{
                      'background-color': getColor(x.activity_status)
                    }"
                  >
                    <button mat-raised-button disabled></button>
                  </div> -->
                  <!-- <div class="ml-auto">
                      <button
                        mat-icon-button
                        matTooltip="more"
                        class="icon-tray-button"
                        [matMenuTriggerFor]="options"
                      >
                        <mat-icon class="smallCardIcon">more_vert</mat-icon>
                      </button>
                      <mat-menu #options="matMenu">
                        <button mat-menu-item class="drop-btn">
                          View Invoice
                        </button>
                        <button mat-menu-item class="drop-btn">
                          Download
                        </button>
                        <button mat-menu-item class="drop-btn">
                          Go to Project
                        </button>
                      </mat-menu>
                    </div> -->
                  <button
                    mat-icon-button
                    matTooltip="Go to Projects"
                    class="icon-tray-button ml-auto"
                    (click)="
                      launchProjectFromInvoice(item.project_id, item.project_name, item.item_id, item.item_name)
                    "
                  >
                    <mat-icon class="smallCardIcon">launch</mat-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="upcomingPaymentData?.length == 0">
      <div class="row card-header-row pl-0 pb-2 pt-2">
        <div class="col-1"></div>
        <div class="col-3 d-flex">
          <span class="pl-2"> Milestone Name</span>
        </div>
        <div class="col-3 d-flex">
          <span class="pl-2">Item Name </span>
        </div>
        <div class="pr-2 col-1 d-flex pl-3">
          <span> Value </span>
        </div>
        <div class="col-2 pl-3 d-flex">
          <span>Expected On </span>
        </div>
        <div class="col-3 pl-4 d-flex">
          <span>Payment to collection activities </span>
        </div>
      </div>
      <div
        *ngFor="
          let item of shimmArr | paginate: { itemsPerPage: 12, currentPage: p }
        "
        class=" row "
      >
        <div class="col-12 pl-0 pr-0">
          <div
            class="card listcard"
            style=" border-left: 3px solid #FF7200 !important;"
          >
            <div class="card-body" style="padding: 2px !important;">
              <div class="row card-details p-0">
                <div class="col-1 cost-center pl-3 mt-1">
                  <ngx-content-loading
                    [speed]="'1500ms'"
                    [width]="1000"
                    [height]="100"
                    [primaryColor]="'#ebebeb'"
                    [secondaryColor]="'#f7f7f7'"
                  >
                    <svg:g
                      ngx-rect
                      width="1000"
                      height="70"
                      y="50"
                      x="0"
                      rx="5"
                      ry="5"
                    ></svg:g>
                  </ngx-content-loading>
                </div>
                <div
                  class="col-3 mt-1 card-content"
                  style="color:#cf0001 !important"
                >
                  <ngx-content-loading
                    [speed]="'1500ms'"
                    [width]="1000"
                    [height]="100"
                    [primaryColor]="'#ebebeb'"
                    [secondaryColor]="'#f7f7f7'"
                  >
                    <svg:g
                      ngx-rect
                      width="1000"
                      height="70"
                      y="50"
                      x="0"
                      rx="5"
                      ry="5"
                    ></svg:g>
                  </ngx-content-loading>
                </div>
                <div class="col-3 mt-1 card-content">
                  <ngx-content-loading
                    [speed]="'1500ms'"
                    [width]="1000"
                    [height]="100"
                    [primaryColor]="'#ebebeb'"
                    [secondaryColor]="'#f7f7f7'"
                  >
                    <svg:g
                      ngx-rect
                      width="1000"
                      height="70"
                      y="50"
                      x="0"
                      rx="5"
                      ry="5"
                    ></svg:g>
                  </ngx-content-loading>
                </div>
                <div class="col-1 mt-1 card-content">
                  <ngx-content-loading
                    [speed]="'1500ms'"
                    [width]="1000"
                    [height]="100"
                    [primaryColor]="'#ebebeb'"
                    [secondaryColor]="'#f7f7f7'"
                  >
                    <svg:g
                      ngx-rect
                      width="1000"
                      height="70"
                      y="50"
                      x="0"
                      rx="5"
                      ry="5"
                    ></svg:g>
                  </ngx-content-loading>
                </div>
                <div class="col-2  pl-3 mt-1 card-content">
                  <ngx-content-loading
                    [speed]="'1500ms'"
                    [width]="1000"
                    [height]="100"
                    [primaryColor]="'#ebebeb'"
                    [secondaryColor]="'#f7f7f7'"
                  >
                    <svg:g
                      ngx-rect
                      width="1000"
                      height="70"
                      y="50"
                      x="0"
                      rx="5"
                      ry="5"
                    ></svg:g>
                  </ngx-content-loading>
                </div>
                <div class="col-4 mt-1 d-flex  card-content">
                  <ngx-content-loading
                    [speed]="'1500ms'"
                    [width]="1000"
                    [height]="100"
                    [primaryColor]="'#ebebeb'"
                    [secondaryColor]="'#f7f7f7'"
                  >
                    <svg:g
                      ngx-rect
                      width="1000"
                      height="70"
                      y="50"
                      x="0"
                      rx="5"
                      ry="5"
                    ></svg:g>
                  </ngx-content-loading>
                  <!-- <div class="ml-auto">
                      <button
                        mat-icon-button
                        matTooltip="more"
                        class="icon-tray-button"
                        [matMenuTriggerFor]="options"
                      >
                        <mat-icon class="smallCardIcon">more_vert</mat-icon>
                      </button>
                      <mat-menu #options="matMenu">
                        <button mat-menu-item class="drop-btn">
                          View Invoice
                        </button>
                        <button mat-menu-item class="drop-btn">
                          Download
                        </button>
                        <button mat-menu-item class="drop-btn">
                          Go to Project
                        </button>
                      </mat-menu>
                    </div> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  
    <div class="pt-2">
      <pagination-controls
        *ngIf="upcomingPaymentData?.length != 0"
        (pageChange)="p = $event"
        class="invoicePagination"
      ></pagination-controls>
    </div>
  </div>
  