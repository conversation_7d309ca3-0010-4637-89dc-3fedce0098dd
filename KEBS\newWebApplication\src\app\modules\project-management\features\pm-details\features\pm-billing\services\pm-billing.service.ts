import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { LoginService } from 'src/app/services/login/login.service';
@Injectable({
  providedIn: 'root'
})
export class PmBillingService {

  currentUser = this.loginService.getProfile().profile;

  constructor(private http: HttpClient, private loginService: LoginService) { }
  saveMilestone(portfolio_id,project_id,data,isBot,isEmail,code,displayTag){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/saveMilestone",{portfolio_id,project_id,data,isBot,isEmail,code,displayTag}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  retrieveMilestoneId(itemID){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/retrieveMilestoneId",{itemID}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  retrieveMilestone(milestoneId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/retrieveMilestone",{milestoneId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getMilestoneData(milestoneId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getMilestoneData",{milestoneId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getBillingAdviceData(data,billing,actual_hours_config,withOpportunity){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getBillingAdviceData",{data,billing,actual_hours_config,withOpportunity}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getLumpsumBillingAdviceData(data,billing,actual_hours_config,withOpportunity){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getLumpsumBillingAdviceData",{data,billing,actual_hours_config,withOpportunity}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  updateMilestoneStatus(project_id, item_id, Sid,Mid,value,month,date,oldData,newData, source){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateMilestoneStatus",{project_id, item_id, Sid,Mid,value,month,date,oldData, newData, source}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  saveBillingAdvise(Mid,data, positionData, project_id,item_id,invoiceList=[],po_number=null,invoice_date=null, billing_value, api_call = true){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/saveBillingAdvise",{Mid:Mid,data:data,  positionData: positionData, project_id:project_id,invoiceList:invoiceList,item_id:item_id,po_number:po_number,invoice_date:invoice_date, billing_value: billing_value, api_call: api_call}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getBillingInfo(Mid){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getBillingInfo",{Mid}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getDependencies(data){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getDependencies",{data}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  addDependencies(selected,data){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/addDependencies",{selected,data}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  retrieveSelectedDependencies(data){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/retrieveSelectedDependencies",{data}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getResponsible(project_id,item_id){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/getResponsible",{project_id,item_id}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }
    getProjectQuote(project_id,item_id){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/getProjectQuote",{project_id,item_id}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }

    getProfitCenter(projectId,itemId){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/getProfitCenterHeader",{projectId,itemId}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }
    saveMultipleMilestone(portfolio_id,project_id,data,code,first_quote_id,first_po_number){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/saveMultipleMilestone",{portfolio_id,project_id,data,code,first_quote_id,first_po_number}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }
    saveGenerateMilestone(portfolio_id,project_id,data){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/saveGenerateMilestone",{portfolio_id,project_id,data}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }
    updateMilestone(previousData,updatedData,portfolio_id,project_id,data,isBot,isEmail,id,code,tags){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/updateMilestone",{previousData,updatedData,portfolio_id,project_id,data,isBot,isEmail,id,code,tags}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }
    getMilestoneDataASC(milestoneId){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/getMilestoneDataASC",{milestoneId}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }
    deleteMilestone(milestoneId){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/deleteMilestone",{milestoneId}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }
    getExistingTags(){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/getExistingTags",{}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    getTagsColor(){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/getTagsColor",{}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    insertTag(name,color){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/insertTag",{name,color}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    } 
    getTags(id){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/getTags",{id}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    insertTagFromBilling(data){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/insertTagFromBilling",{data}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }

    getProjectStatus(project_id, item_id){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/getProjectStatus",{project_id, item_id}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    getEditedMilestone(id){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/getEditedMilestone",{id}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    getSumOFMilestoneValue(id){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/getSumOFMilestoneValue",{id}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    updateBillingAdvise(Mid,data, positionData, project_id,item_id,invoiceList=[],po_number=null,invoice_date=null, billing_value, api_call = true){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/updateBillingAdvise",{Mid:Mid,data:data, positionData: positionData, project_id:project_id,invoiceList:invoiceList,item_id:item_id,po_number:po_number,invoice_date:invoice_date, billing_value, api_call: api_call}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    getFinanceStatus(){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/getFinanceStatus",{}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    getQuotePosition(id, resourceTypeList){
    
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/getQuotePosition",{id, resourceTypeList}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    
      }
      getProjectFinnacialData(item_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getProjectFinnacialData",{item_id:item_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getSubDivisionAllocatedForTheProject(item_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getSubDivisionAllocatedForTheProject",{item_id:item_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getLumpsumBillingAdviseData(data,item_id,profit_center){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getLumpsumBillingAdviseData",{data,item_id,profit_center}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getLumpsumBillingData(item_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getLumpsumBillingData",{item_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      saveLumpsumBilling(data,project_id,item_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/saveLumpsumBilling",{data,project_id,item_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      confirmLumpsumBilling(data,project_id,item_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/confirmLumpsumBilling",{data,project_id,item_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getSnapshotList(project_id,item_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getSnapshotList",{project_id,item_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getLumpsumDataByVersion(item_id,id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getLumpsumDataByVersion",{item_id,id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getMISPeriodDate(){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getMISPeriodDate",{}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }

      fetchProjectCurrency(project_id, item_id, code){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/financial/fetchProjectCurrency",{project_id: project_id, item_id: item_id, currency_code: code }).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      revertMilestoneFromYTB(milestone_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/financial/revertMilestoneFromYTB",{milestone_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getProjectDailyWorkingHours(project_id,item_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getProjectDailyWorkingHours",{project_id,item_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getMilestoneDetails(project_id,item_id,milestone_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getMilestoneDetails",{project_id,item_id,milestone_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getCustomerRateCard(customerId, date){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/integration/getCustomerRateCard",{customer_id: customerId, date: date}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getPoNumberList(project_id,item_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getPoNumberList",{project_id,item_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getProjectPoMaster(itemId,quoteId){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/masterData/getProjectPoMaster",{itemId,quoteId}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getGroupedMilestone(item_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getGroupedMilestone",{item_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getQuoteIDList(project_id,item_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getQuoteIDList",{project_id,item_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      getMilestoneForAccured(item_id,mid){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/getMilestoneForAccured",{item_id,mid}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }


      syncRoundOff(itemId, milestoneId, milestoneStartDate, milestoneEndDate, associates){
        return new Promise((resolve, reject) => {
          this.http.post("/api/bgjPrimary/schedulererunProgram",{item_id: itemId, milestone_id: milestoneId, start_date: milestoneStartDate, end_date: milestoneEndDate, associate_ids: associates, employeeAid: this.currentUser.aid}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }
      addChildMilestone(parent_id,child_id,parent_milestone_type,child_value,code,portfolio_id, project_id){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/addChildMilestone",{parent_id,child_id,parent_milestone_type,child_value,code,portfolio_id, project_id}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      }

      updateRoundOffFlag(milestoneID){
        return new Promise((resolve, reject) => {
          this.http.post("/api/pm/planning/updateRoundOffFlag",{milestone_id: milestoneID}).subscribe((res) => {
            return resolve(res)
          }, (err) => {
            return reject(err)
          })
        })
      } 
      
      getMilestoneInvoiceDetails(milestoneID)
      {
        return this.http.post("api/pm/planning/getMilestoneInvoiceDetails",{milestone_id:milestoneID})
      }

      getProjectFinancialValues(projectItemID){
        return new Promise((resolve, reject)=>{
          this.http.post("/api/pm/financial/getProjectFinancialValues",{projectItemID}).subscribe((res)=>{
            return resolve(res)
          },(err)=>{
            return reject(err)
          })
        })
      }
  getUnitListMasterData(){
    return this.http.post("/api/pm/masterData/getBillingAdviceUOMMaster",{})
  }

  getMilestoneMatrix(){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/getMilestoneMatrix",{}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  getBillsData(project_id, item_id, sort='DESC') {
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/getBillData", { project_id, item_id,sort }).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  getSumOFBillsValue(id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getSumofBillsValue", { id }).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  updateBills(portfolio_id, project_id, data, isBot, isEmail, id, code, tags, oldData, newData) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateBills", { portfolio_id, project_id, data, isBot, isEmail, id, code, tags, oldData, newData }).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  saveBills(portfolio_id, project_id, data, isBot, isEmail, code, displayTag) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/saveBills", { portfolio_id, project_id, data, isBot, isEmail, code, displayTag }).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  deleteBill(milestoneId) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/deleteBill", { milestoneId }).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getBillingAdviceVersionHistory(milestoneID)
  {
    return this.http.post("/api/pm/financial/getBillingAdviceVersionHistory",{milestone_id:milestoneID})
  }


  updateInvoiceDate(ItemID,milestoneID, invoice_date, oldInvoiceDate, newInvoiceDate){
    return this.http.post("/api/pm/financial/updateInvoiceDate",{project_item_id: ItemID,milestone_id: milestoneID, invoice_date: invoice_date,oldInvoiceDate: oldInvoiceDate,newInvoiceDate: newInvoiceDate  })
  }


  validateYTBData(project_id, item_id, milestone_id, milestone_type, value){
    return this.http.post("/api/pm/financial/validateYTBData",{project_id: project_id, item_id: item_id, milestone_id: milestone_id, milestone_type: milestone_type, value: value})
  }


  validateAccruedData(project_id, item_id, milestone_id, value){
    return this.http.post("/api/pm/financial/validateAccruedData",{project_id: project_id, item_id: item_id, milestone_id: milestone_id, value: value})
  }

  insertTimeTrackerInsertion(project_id, project_item_id){
    return this.http.post("/api/bgjPrimary/buildEmployeeBilledHours",{project_id: project_id, project_item_id: project_item_id})
  }

  getBillingAdviceBillingPlanData(project_id, project_item_id, quote_id){
    return new Promise((resolve, reject) => {
    this.http.post("/api/pm/financial/getBillingAdviceBillingPlanData", { project_id, project_item_id, quote_id }).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getPoMasterData(project_id, project_item_id)
  {
    return this.http.post("/api/pm/financial/getPoMasterData",{project_id: project_id, project_item_id: project_item_id})
  }
  deletePoMaster(data)
  {
    return this.http.post("/api/pm/financial/deletePoMaster",{recordid: data})
  }
  getProjectCurrency(project_id, project_item_id)
  {
    return this.http.post("/api/pm/financial/getProjectCurrency",{project_id: project_id, project_item_id: project_item_id})
  }
  insertPoMaster(details,project_item_id, project_id){
    return this.http.post("/api/pm/financial/insertPoMaster",{details:details,project_item_id: project_item_id,project_id: project_id})
  }
  updatePoMaster(details,project_item_id, project_id,record_id, oldData, newData){
    return this.http.post("/api/pm/financial/updatePoMaster",{details:details,project_item_id: project_item_id,project_id: project_id,record_id:record_id, oldData: oldData, newData: newData})
  }
  checkPoNumberExists(po_number,projectID,ItemID){
    return this.http.post("/api/pm/financial/checkPoNumberExists",{ponumber:po_number,project:projectID,item:ItemID})
  }
  getPoMastercheckData(project_id, project_item_id)
  {
    return this.http.post("/api/pm/financial/getPoMastercheckData",{project_id: project_id, project_item_id: project_item_id})
  }
  checkmilestonestatus(project_id,project_item_id,po_number)
  {
    return this.http.post("/api/pm/financial/checkmilestonestatus",{project_id: project_id, project_item_id: project_item_id,po_number:po_number})
  }
  getUpdatedBillingValue(projectID, ItemID, milestoneID, value, currency_code,  date){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/getUpdatedBillingValue", { projectID, ItemID, milestoneID, value, currency_code, date }).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
  }
  getMaxMinProjectDates(project_id, item_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getMaxMinProjectDates",{project_id, item_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getMaxMinProjectDatesPO(project_id, item_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getMaxMinProjectDatesPO",{project_id, item_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  checkActualEffortsMilestone(project_id, project_item_id){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/financial/checkChangingActualEffortsMilestone", { project_id, project_item_id }).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  moveMilestoneToReverseAccrued(milestone_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/moveMilestoneToReverseAccrued",{milestone_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }


  
  getTotalMilestoneQuoteId(project_item_id, quote_id, currency_code,milestone_type){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/getTotalMilestoneQuoteId", { project_item_id, quote_id, currency_code, milestone_type }).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getOpportunityStatusDetails(project_id,item_id, quote_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getOpportunityStatusDetails",{project_id,item_id, quote_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  createPartialInvoiceMilestone(projectId, itemId, partialInvoiceData, writeOffInvoiceData, parent_milestone_id, milestone_start_date, milestone_end_date, statusId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/createPartialInvoiceMilestone",{projectId, itemId, partialInvoiceData, writeOffInvoiceData, parent_milestone_id, milestone_start_date, milestone_end_date, statusId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  checkAttachmentPresentForMilestones(milestoneIds){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/checkAttachmentPresentForMilestones",{milestoneIds}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getTotalMilestonePONumber(project_item_id, po_number, currency_code,milestone_type){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/getTotalMilestonePONumber", { project_item_id, po_number, currency_code, milestone_type }).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  createCreditNoteInvoiceMilestone(projectId, itemId, creditNoteInvoiceData, invoiceList, parent_milestone_id, milestone_start_date, milestone_end_date, value_config, milestoneStatus){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/createCreditNoteInvoiceMilestone",{projectId, itemId, creditNoteInvoiceData, invoiceList, parent_milestone_id, milestone_start_date, milestone_end_date, value_config, milestoneStatus}).subscribe((res) => {

        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  createCreditNoteInvoiceMilestoneForBills(projectId, itemId, creditNoteInvoiceData, invoiceList, parent_milestone_id, milestone_start_date, milestone_end_date, value_config, milestoneStatus,is_bill = 1){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/createCreditNoteInvoiceMilestoneForBills",{projectId, itemId, creditNoteInvoiceData, invoiceList, parent_milestone_id, milestone_start_date, milestone_end_date, value_config, milestoneStatus,is_bill}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
 


   

  

 
        
 

  revertMilestoneFromAccrual(milestone_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/revertMilestoneFromAccrual",{milestone_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  triggerApprovalForHoursDeviation(projectId, itemId, requestedStatus, milestoneId, milestoneStatusId, approvalGroupId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/triggerApprovalForHoursDeviation",{projectId, itemId, requestedStatus, milestoneId, milestoneStatusId, approvalGroupId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  
  triggerApprovalForValueDeviation(projectId, itemId, milestoneId, oldBillingValue, newBillingValue, milestoneDetails, approvalGroupId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/triggerApprovalForValueDeviation",{projectId, itemId, milestoneId, oldBillingValue, newBillingValue, milestoneDetails, approvalGroupId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getWorkflowDetailsBasedOnSource(projectId, itemId, sourceId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/getWorkflowDetailsBasedOnSource",{projectId, itemId, sourceId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  updateWorkflowRequest(approvalType,workflowHeaderId,comments){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/updateProjectWorkflowRequest",{approvalType,workflowHeaderId,comments}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  checkCommentPresentForMilestones(milestoneIds){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/checkCommentPresentForMilestones",{milestoneIds}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  checkOpportunityBlanketPO(projectId, itemId, quoteId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/checkOpportunityBlanketPO",{projectId, itemId, quoteId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getApproversDetails(projectId, itemId, approvalGroupId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/getApproversDetails",{projectId, itemId, approvalGroupId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getBillingAdviceDataForBills(data,withOpportunity){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getBillingAdviceDataForBills",{data,withOpportunity}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  updateMilestoneDependencies(projectId, itemId, milestoneId, dependencies){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateMilestoneDependencies",{projectId, itemId, milestoneId, dependencies}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  triggerApprovalForCreditNoteCreation(projectId, itemId, requestedStatus, milestoneId, milestoneStatusId, approvalGroupId, milestoneDetails){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/triggerApprovalForCreditNoteCreation",{projectId, itemId, requestedStatus, milestoneId, milestoneStatusId, approvalGroupId, milestoneDetails}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  insertMilestoneInvoiceDetails(milestoneID,invoiceDetails)
  {
    return this.http.post("api/pm/planning/insertMilestoneInvoiceDetails",{milestone_id:milestoneID,invoice_details: invoiceDetails})
  }

  validateAttachDependencies(projectId,itemId,milestoneId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/validateAttachDependencies",{projectId,itemId,milestoneId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  checkOpportunityStatus(project_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/report/checkOpportunityStatus",{project_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
 
  

  checkOpportunityQuoteStatus(quote_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/report/checkOpportunityQuoteStatus",{quote_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
    checkActualEffortValue(project_id, project_item_id,quote_id,milestone_id){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/financial/checkActualEffortValue", { project_id, project_item_id,quote_id,milestone_id }).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  checkStandardAccuralToActualEfforts(milestone_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/invoice/standardAccruedToActuals",{milestoneId: milestone_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return resolve(err?.error)
      })
    })
  }




  checkPreviousMilestoneStatus(milestone_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/financial/checkPreviousMilestoneStatus",{milestone_id: milestone_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  updateMilestoneAsSaved(milestoneID){
    return this.http.post("/api/pm/planning/updateMilestoneAsSaved",{milestone_id: milestoneID })
  }
 
}

 