(window.webpackJsonp=window.webpackJsonp||[]).push([[1014],{"/rGH":function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("fXoL");let a=(()=>{class e{transform(e,t,n="name"){return e||0===e?(Array.isArray(e)||(e=[e]),e.map(e=>{let i=t.find(t=>t.id==e);return i?i[n]:"-"}).join(", ")):"-"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"masterData",type:e,pure:!0}),e})()},IeBn:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var i=n("fXoL"),a=n("3Pt+"),r=n("ofXK");const o=["inputField"];function l(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275namespaceSVG"](),i["\u0275\u0275elementStart"](0,"svg",9),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().onEnterKeyPressed()})),i["\u0275\u0275element"](1,"path",10),i["\u0275\u0275elementEnd"]()}}function s(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275namespaceSVG"](),i["\u0275\u0275elementStart"](0,"svg",11),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]();return t.searchText="",t.onEnterKeyPressed()})),i["\u0275\u0275elementStart"](1,"g",12),i["\u0275\u0275element"](2,"path",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"defs"),i["\u0275\u0275elementStart"](4,"clipPath",14),i["\u0275\u0275element"](5,"rect",15),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function c(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",19),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index;return i["\u0275\u0275nextContext"](2).onSelectRecentSearch(n)})),i["\u0275\u0275elementStart"](1,"div",20),i["\u0275\u0275namespaceSVG"](),i["\u0275\u0275elementStart"](2,"svg",21),i["\u0275\u0275elementStart"](3,"mask",22),i["\u0275\u0275element"](4,"rect",23),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"g",24),i["\u0275\u0275element"](6,"path",25),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275namespaceHTML"](),i["\u0275\u0275element"](7,"div",26),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](7),i["\u0275\u0275property"]("innerHTML",n.highlightSearch(e),i["\u0275\u0275sanitizeHtml"])}}function p(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275element"](1,"div",16),i["\u0275\u0275elementStart"](2,"span",17),i["\u0275\u0275text"](3,"Recently Searched"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](4,c,8,1,"div",18),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngForOf",e.recentSearch)}}let d=(()=>{class e{constructor(){this.onEnter=new i.EventEmitter,this.searchText=""}ngOnInit(){this.currentSearchText&&""!=this.currentSearchText&&(this.searchText=this.currentSearchText)}ngAfterViewInit(){this.inputField&&this.inputField.nativeElement&&this.inputField.nativeElement.focus()}highlightSearch(e){if(!this.searchText)return e;let t=this.searchText.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");const n=new RegExp(t,"gi");return e.replace(n,e=>`<b>${e}</b>`)}onEnterKeyPressed(){this.onEnter.emit(this.searchText.trim())}onSelectRecentSearch(e){this.searchText=this.recentSearch[e],this.onEnterKeyPressed()}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-overlay"]],viewQuery:function(e,t){if(1&e&&i["\u0275\u0275viewQuery"](o,!0),2&e){let e;i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.inputField=e.first)}},inputs:{recentSearch:"recentSearch",currentSearchText:"currentSearchText"},outputs:{onEnter:"onEnter"},decls:9,vars:4,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between"],[1,"search-bar"],["type","text","placeholder","Search Here...",3,"ngModel","ngModelChange","keydown.enter"],["inputField",""],[2,"cursor","pointer"],["width","18","height","18","viewBox","0 0 18 18","fill","none",3,"click",4,"ngIf"],["style","cursor: pointer","width","18","height","18","viewBox","0 0 18 18","fill","none",3,"click",4,"ngIf"],[4,"ngIf"],["width","18","height","18","viewBox","0 0 18 18","fill","none",3,"click"],["d","M7.01916 12.6153C5.31148 12.6153 3.86533 12.023 2.68073 10.8384C1.49612 9.65383 0.903809 8.20768 0.903809 6.49999C0.903809 4.79231 1.49612 3.34616 2.68073 2.16154C3.86533 0.976943 5.31148 0.384644 7.01916 0.384644C8.72684 0.384644 10.173 0.976943 11.3576 2.16154C12.5422 3.34616 13.1345 4.79231 13.1345 6.49999C13.1345 7.21409 13.0147 7.89614 12.7749 8.54614C12.5352 9.19614 12.2153 9.76153 11.8153 10.2423L17.5692 15.9961C17.7076 16.1346 17.7784 16.3087 17.7816 16.5183C17.7849 16.7279 17.714 16.9051 17.5692 17.05C17.4243 17.1948 17.2486 17.2673 17.0422 17.2673C16.8358 17.2673 16.6602 17.1948 16.5153 17.05L10.7615 11.2961C10.2615 11.7089 9.68646 12.032 9.03646 12.2653C8.38646 12.4987 7.71403 12.6153 7.01916 12.6153ZM7.01916 11.1154C8.30763 11.1154 9.39897 10.6683 10.2932 9.77404C11.1874 8.87981 11.6346 7.78846 11.6346 6.49999C11.6346 5.21153 11.1874 4.12018 10.2932 3.22594C9.39897 2.33171 8.30763 1.88459 7.01916 1.88459C5.73069 1.88459 4.63934 2.33171 3.74511 3.22594C2.85089 4.12018 2.40378 5.21153 2.40378 6.49999C2.40378 7.78846 2.85089 8.87981 3.74511 9.77404C4.63934 10.6683 5.73069 11.1154 7.01916 11.1154Z","fill","#45546E"],["width","18","height","18","viewBox","0 0 18 18","fill","none",2,"cursor","pointer",3,"click"],["clip-path","url(#clip0_22386_12410)"],["d","M8.00048 7.05781L11.3005 3.75781L12.2431 4.70048L8.94315 8.00048L12.2431 11.3005L11.3005 12.2431L8.00048 8.94315L4.70048 12.2431L3.75781 11.3005L7.05781 8.00048L3.75781 4.70048L4.70048 3.75781L8.00048 7.05781Z","fill","#6E7B8F"],["id","clip0_22386_12410"],["width","16","height","16","fill","white"],[1,"divider"],[1,"recent-search-title"],["class","d-flex align-items-center search-text-list",3,"click",4,"ngFor","ngForOf"],[1,"d-flex","align-items-center","search-text-list",3,"click"],[2,"margin-bottom","1px"],["width","12","height","12","viewBox","0 0 12 12","fill","none"],["id","mask0_9594_64568","maskUnits","userSpaceOnUse","x","0","y","0","width","12","height","12",2,"mask-type","alpha"],["width","12","height","12","fill","#D9D9D9"],["mask","url(#mask0_9594_64568)"],["d","M5.99166 10.25C4.90897 10.25 3.96538 9.89357 3.1609 9.18075C2.35641 8.46793 1.89328 7.57434 1.77148 6.49999H2.53685C2.66313 7.36345 3.05175 8.07931 3.70271 8.64759C4.35368 9.21586 5.11666 9.49999 5.99166 9.49999C6.96666 9.49999 7.79374 9.16041 8.47291 8.48124C9.15208 7.80207 9.49166 6.97499 9.49166 5.99999C9.49166 5.02499 9.15208 4.1979 8.47291 3.51874C7.79374 2.83957 6.96666 2.49999 5.99166 2.49999C5.44551 2.49999 4.93365 2.6213 4.45608 2.86393C3.97853 3.10655 3.56731 3.44036 3.22243 3.86536H4.53012V4.61535H1.9917V2.07691H2.74167V3.26154C3.14744 2.7827 3.63333 2.41106 4.19936 2.14664C4.76538 1.88221 5.36282 1.75 5.99166 1.75C6.5814 1.75 7.13396 1.86154 7.64935 2.08463C8.16472 2.3077 8.61408 2.6109 8.99741 2.99423C9.38074 3.37756 9.68395 3.82692 9.90702 4.3423C10.1301 4.85768 10.2416 5.41025 10.2416 5.99999C10.2416 6.58973 10.1301 7.14229 9.90702 7.65768C9.68395 8.17306 9.38074 8.62242 8.99741 9.00575C8.61408 9.38908 8.16472 9.69228 7.64935 9.91535C7.13396 10.1384 6.5814 10.25 5.99166 10.25ZM7.49262 8.01344L5.63108 6.15191V3.49999H6.38107V5.84806L8.01953 7.48654L7.49262 8.01344Z","fill","#8B95A5"],[1,"recent-search-text",3,"innerHTML"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"input",3,4),i["\u0275\u0275listener"]("ngModelChange",(function(e){return t.searchText=e}))("keydown.enter",(function(){return t.onEnterKeyPressed()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",5),i["\u0275\u0275template"](6,l,2,0,"svg",6),i["\u0275\u0275template"](7,s,6,0,"svg",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](8,p,5,1,"ng-container",8),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngModel",t.searchText),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",""==t.searchText),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.searchText&&""!=t.searchText),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.recentSearch&&t.recentSearch.length>0))},directives:[a.e,a.v,a.y,r.NgIf,r.NgForOf],styles:[".bg-container[_ngcontent-%COMP%]{width:350px;padding:8px;border:2px solid #b9c0ca;border-radius:8px;box-shadow:0 4px 8px 0 rgba(0,0,0,.25098039215686274);background-color:#fff}.bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;outline:none;border:none}.bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.bg-container[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{height:1px;width:100%;background:#dadce2;margin-bottom:8px;margin-top:8px}.bg-container[_ngcontent-%COMP%]   .search-text-list[_ngcontent-%COMP%]{cursor:pointer;gap:8px;width:-moz-fit-content;width:fit-content;margin-bottom:4px}.bg-container[_ngcontent-%COMP%]   .recent-search-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-style:italic;font-weight:400;color:#b9c0ca}.bg-container[_ngcontent-%COMP%]   .recent-search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;color:#8b95a5}"]}),e})()},Jzeh:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("yuIm"),a=n("fXoL"),r=n("ofXK");function o(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",4),a["\u0275\u0275text"](1," Oops! You're not authorized to view this content. Contact your administrator for access. "),a["\u0275\u0275elementEnd"]())}function l(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",4),a["\u0275\u0275text"](1," KEBS System is unavailable, kindly try after sometime! "),a["\u0275\u0275elementEnd"]())}let s=(()=>{class e{constructor(){this.isRDSPeak=!1}ngOnInit(){this.isRDSPeak=null==i?void 0:i.is_rds_peak}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-access-denied"]],decls:5,vars:2,consts:[[1,"bg-container"],[1,"contents"],["src","https://assets.kebs.app/ATS-noAccess.png",1,"image-styles"],["class","message",4,"ngIf"],[1,"message"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275element"](2,"img",2),a["\u0275\u0275template"](3,o,2,0,"div",3),a["\u0275\u0275template"](4,l,2,0,"div",3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",!t.isRDSPeak),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.isRDSPeak))},directives:[r.NgIf],styles:[".bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px;align-items:center;justify-content:center;background-color:#fff;height:var(--dynamicAccessDeniedHeight)}.bg-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434;text-align:center}.bg-container[_ngcontent-%COMP%]   .image-styles[_ngcontent-%COMP%]{height:200px;width:200px}.bg-container[_ngcontent-%COMP%]   .contents[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}"]}),e})()},KVz4:function(e,t,n){"use strict";n.r(t),n.d(t,"TemplatesModule",(function(){return Mt}));var i=n("ofXK"),a=n("tyNb"),r=n("mrSG"),o=n("1G5W"),l=n("XNiG"),s=n("yuIm"),c=n("fXoL"),p=n("rQiX"),d=n("XNFG"),m=n("0IaG"),g=n("URR/"),h=n("Qu3c"),u=n("f0Cb"),f=n("+rOU"),C=n("XQl4"),v=n("rDax"),x=n("3Pt+"),y=n("IeBn"),_=n("pEYl"),M=n("wC0v");const w=["triggerSearchBarTemplateRef"];function O(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",13,14),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275reference"](2),n=c["\u0275\u0275nextContext"](),i=c["\u0275\u0275reference"](16);return n.openSearchBarOverlay(t,i,-37,!1)})),c["\u0275\u0275elementStart"](3,"div",15),c["\u0275\u0275elementStart"](4,"input",16,17),c["\u0275\u0275listener"]("ngModelChange",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().searchParams=t}))("keydown.backspace",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275nextContext"]();return t.onEnterSearch(t.searchParams)})),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"div",18),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](7,"svg",19),c["\u0275\u0275element"](8,"path",20),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](4),c["\u0275\u0275property"]("ngModel",e.searchParams)}}function S(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",21,14),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275reference"](2),n=c["\u0275\u0275nextContext"](),i=c["\u0275\u0275reference"](16);return n.openSearchBarOverlay(t,i,-30,n.isTemplateCreationAccessAvailable?-325:5)})),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](3,"svg",19),c["\u0275\u0275element"](4,"path",20),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}}function b(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",22),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().handleCreateTemplate()})),c["\u0275\u0275text"](1," + New Template "),c["\u0275\u0275elementEnd"]()}}function P(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",23),c["\u0275\u0275elementStart"](1,"div",24),c["\u0275\u0275elementStart"](2,"div",25),c["\u0275\u0275element"](3,"img",26),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",27),c["\u0275\u0275elementStart"](5,"div",28),c["\u0275\u0275text"](6,"Loading..."),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],c["\u0275\u0275sanitizeUrl"])}}function E(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",58),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275nextContext"]().$implicit;return c["\u0275\u0275nextContext"](3).navigateToDetailEdit(t.id)})),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](1,"svg",59),c["\u0275\u0275element"](2,"path",60),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}}function D(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",58),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275nextContext"]().$implicit;return c["\u0275\u0275nextContext"](3).removeTemplate(t.id)})),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](1,"svg",61),c["\u0275\u0275element"](2,"path",62),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}}function k(e,t){1&e&&c["\u0275\u0275element"](0,"mat-divider",63)}const T=function(e,t){return[e,t,0,0,"E"]},F=function(e,t){return[e,t,0,0,"DE"]};function I(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",32),c["\u0275\u0275elementStart"](1,"div",33),c["\u0275\u0275elementStart"](2,"div",34),c["\u0275\u0275elementStart"](3,"div"),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](4,"svg",35),c["\u0275\u0275element"](5,"path",36),c["\u0275\u0275element"](6,"path",37),c["\u0275\u0275element"](7,"path",38),c["\u0275\u0275element"](8,"path",39),c["\u0275\u0275element"](9,"path",40),c["\u0275\u0275element"](10,"path",41),c["\u0275\u0275element"](11,"path",42),c["\u0275\u0275element"](12,"path",43),c["\u0275\u0275element"](13,"path",44),c["\u0275\u0275element"](14,"path",45),c["\u0275\u0275element"](15,"path",46),c["\u0275\u0275element"](16,"path",47),c["\u0275\u0275element"](17,"path",48),c["\u0275\u0275element"](18,"path",49),c["\u0275\u0275element"](19,"path",50),c["\u0275\u0275element"](20,"path",51),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275namespaceHTML"](),c["\u0275\u0275elementStart"](21,"div",52),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const n=t.$implicit;return c["\u0275\u0275nextContext"](3).navigateToDetail(n.id)})),c["\u0275\u0275elementStart"](22,"div",53),c["\u0275\u0275text"](23),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](24,"div",54),c["\u0275\u0275text"](25),c["\u0275\u0275pipe"](26,"dateFormat"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](27,"div",55),c["\u0275\u0275template"](28,E,3,0,"div",56),c["\u0275\u0275pipe"](29,"access"),c["\u0275\u0275template"](30,D,3,0,"div",56),c["\u0275\u0275pipe"](31,"access"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](32,k,1,0,"mat-divider",57),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.last,i=c["\u0275\u0275nextContext"](3);c["\u0275\u0275advance"](23),c["\u0275\u0275textInterpolate1"](" ",e.name," "),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate2"](" Edited On: ",c["\u0275\u0275pipeBind2"](26,6,e.created_on,"DD MMM YYYY hh:mm A")," ",null!=e&&e.user?"By "+e.user:""," "),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBindV"](29,9,c["\u0275\u0275pureFunction2"](21,T,i.access.moduleId.settings,i.access.subModuleId.templateSettings))),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBindV"](31,15,c["\u0275\u0275pureFunction2"](24,F,i.access.moduleId.settings,i.access.subModuleId.templateSettings))),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",!n)}}function V(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275template"](1,I,33,27,"div",31),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2);c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",e.templateData)}}function A(e,t){1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275elementStart"](1,"div",64),c["\u0275\u0275element"](2,"img",65),c["\u0275\u0275elementStart"](3,"div",66),c["\u0275\u0275text"](4,"No Templates Found"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementContainerEnd"]())}function L(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",29),c["\u0275\u0275template"](1,V,2,1,"ng-container",30),c["\u0275\u0275template"](2,A,5,0,"ng-container",30),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.templateData&&e.templateData.length>0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!e.templateData||0==e.templateData.length)}}const B=function(){return[]};function H(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"app-search-overlay",67),c["\u0275\u0275listener"]("onEnter",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().onEnterSearch(t)})),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("currentSearchText",e.searchParams)("recentSearch",c["\u0275\u0275pureFunction0"](2,B))}}const j=function(e,t){return[e,t,0,0,"C"]};let z=(()=>{class e{constructor(e,t,n,i,a,r,o){this._toaster=e,this._dialog=t,this._atsSettingsService=n,this._atsMasterDataService=i,this._overlay=a,this._viewContainerRef=r,this._atsTemplateSettingsService=o,this.title="Template",this.uiTextConfig={},this.valueEmitter=new c.EventEmitter,this._onDestroy=new l.b,this.templateData=[],this.isLoading=!0,this.searchParams="",this.access=s,this.isTemplateCreationAccessAvailable=!1,this.candidateFilterConfig={search_params:""}}handleCreateTemplate(){return Object(r.c)(this,void 0,void 0,(function*(){const{CreateTemplateDialogComponent:e}=yield n.e(929).then(n.bind(null,"FYQT"));this._dialog.open(e,{width:"600px",height:"225px",data:{tempalteCategory:"emailTemplate"},disableClose:!0}).afterClosed().subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){e&&("blankTemplate"==e?this.openCreateTemplateStrach():"copyTemplate"==e&&this.openCreateTemplateCopy())})))}))}openCreateTemplateStrach(){return Object(r.c)(this,void 0,void 0,(function*(){const{CreateBlankTemplateBlankComponent:e}=yield Promise.all([n.e(0),n.e(927)]).then(n.bind(null,"lE0A"));this._dialog.open(e,{width:"400px",height:"316px",data:{tempalteCategory:"emailTemplate"},disableClose:!0}).afterClosed().subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){e&&this.valueEmitter.emit({data:e,mode:"create",key:"emailTemplate"})})))}))}openCreateTemplateCopy(){return Object(r.c)(this,void 0,void 0,(function*(){const{CreateTemplateCopyDialogComponent:e}=yield Promise.all([n.e(0),n.e(928)]).then(n.bind(null,"1q9G"));this._dialog.open(e,{width:"400px",height:"316px",data:{tempalteCategory:"emailTemplate"},disableClose:!0}).afterClosed().subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){e&&this.valueEmitter.emit({data:e,mode:"create",key:"emailTemplate"})})))}))}navigateToDetail(e){this.valueEmitter.emit({data:{templateName:"",template:e},mode:"edit",key:"emailTemplate"})}navigateToDetailEdit(e){this.valueEmitter.emit({data:{templateName:"",template:e},mode:"edit",key:"emailTemplate"})}removeTemplate(e){return Object(r.c)(this,void 0,void 0,(function*(){yield this.deleteEmailTemplate(e),this.isLoading=!0,this.fetchAllEmailTemplates()}))}fetchAllEmailTemplates(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsTemplateSettingsService.fetchAllEmailTemplates(this.searchParams,null,null).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this.templateData=t.data,this.isLoading=!1):(this.isLoading=!1,this._toaster.showError("Error",t.msg,7e3)),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Template Data Retrieval Failed!",7e3),this.isLoading=!1,t()}}))}))}deleteEmailTemplate(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsTemplateSettingsService.deleteEmailTemplate(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this._toaster.showSuccess("Success \u2705","Template Deleted Successfully!",7e3):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}openSearchBarOverlay(e,t,n,i){var a;if(!(null===(a=this.overlayRef)||void 0===a?void 0:a.hasAttached())){const a=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"}]);a.withDefaultOffsetY(n),i&&a.withDefaultOffsetX(i);const r=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:a,scrollStrategy:r,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const o=new f.h(t,this._viewContainerRef);this.overlayRef.attach(o),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}onEnterSearch(e){return Object(r.c)(this,void 0,void 0,(function*(){this.closeOverlay(),this.isLoading=!0,this.searchParams=e,yield this.fetchAllEmailTemplates()}))}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}ngOnInit(){this.isTemplateCreationAccessAvailable=s.checkAccessForGeneralRole(s.moduleId.settings,s.subModuleId.templateSettings,0,0,"C"),this.fetchAllEmailTemplates()}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](d.a),c["\u0275\u0275directiveInject"](m.b),c["\u0275\u0275directiveInject"](C.a),c["\u0275\u0275directiveInject"](p.a),c["\u0275\u0275directiveInject"](v.e),c["\u0275\u0275directiveInject"](c.ViewContainerRef),c["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-email-template"]],viewQuery:function(e,t){if(1&e&&c["\u0275\u0275viewQuery"](w,!0),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.triggerSearchBarTemplateRef=e.first)}},inputs:{title:"title",uiTextConfig:"uiTextConfig"},outputs:{valueEmitter:"valueEmitter"},decls:17,vars:15,consts:[[1,"d-flex","flex-column","email-template"],[1,"header"],[1,"align-items-column"],[1,"title-text"],[1,"sub-text"],[1,"d-flex","align-items-center","sub-menu-2"],["class","d-flex align-items-center justify-content-between search-ui","cdkOverlayOrigin","",3,"click",4,"ngIf"],["class","header-icon","cdkOverlayOrigin","",3,"click",4,"ngIf"],["class","create-template",3,"click",4,"ngIf"],["class","bg-container",4,"ngIf"],["class","d-flex flex-column bg-container email-list",4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerSearchBarTemplateRef",""],["cdkOverlayOrigin","",1,"d-flex","align-items-center","justify-content-between","search-ui",3,"click"],["triggerSearchBar","cdkOverlayOrigin","triggerSearchField",""],[1,"search-bar"],["type","text","placeholder","Search Here...",3,"ngModel","ngModelChange","keydown.backspace"],["inputField",""],[1,"header-icon"],["width","18","height","18","viewBox","0 0 18 18","fill","none"],["d","M7.01916 12.6153C5.31148 12.6153 3.86533 12.023 2.68073 10.8384C1.49612 9.65383 0.903809 8.20768 0.903809 6.49999C0.903809 4.79231 1.49612 3.34616 2.68073 2.16154C3.86533 0.976943 5.31148 0.384644 7.01916 0.384644C8.72684 0.384644 10.173 0.976943 11.3576 2.16154C12.5422 3.34616 13.1345 4.79231 13.1345 6.49999C13.1345 7.21409 13.0147 7.89614 12.7749 8.54614C12.5352 9.19614 12.2153 9.76153 11.8153 10.2423L17.5692 15.9961C17.7076 16.1346 17.7784 16.3087 17.7816 16.5183C17.7849 16.7279 17.714 16.9051 17.5692 17.05C17.4243 17.1948 17.2486 17.2673 17.0422 17.2673C16.8358 17.2673 16.6602 17.1948 16.5153 17.05L10.7615 11.2961C10.2615 11.7089 9.68646 12.032 9.03646 12.2653C8.38646 12.4987 7.71403 12.6153 7.01916 12.6153ZM7.01916 11.1154C8.30763 11.1154 9.39897 10.6683 10.2932 9.77404C11.1874 8.87981 11.6346 7.78846 11.6346 6.49999C11.6346 5.21153 11.1874 4.12018 10.2932 3.22594C9.39897 2.33171 8.30763 1.88459 7.01916 1.88459C5.73069 1.88459 4.63934 2.33171 3.74511 3.22594C2.85089 4.12018 2.40378 5.21153 2.40378 6.49999C2.40378 7.78846 2.85089 8.87981 3.74511 9.77404C4.63934 10.6683 5.73069 11.1154 7.01916 11.1154Z","fill","#45546E"],["cdkOverlayOrigin","",1,"header-icon",3,"click"],[1,"create-template",3,"click"],[1,"bg-container"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"],[1,"d-flex","flex-column","bg-container","email-list"],[4,"ngIf"],["class","d-flex flex-column email-item",4,"ngFor","ngForOf"],[1,"d-flex","flex-column","email-item"],[1,"d-flex","align-items-center","justify-content-between",2,"gap","6px"],[1,"d-flex","align-items-center","justify-content-start",2,"gap","15px"],["width","48","height","45","viewBox","0 0 48 45","fill","none","xmlns",""],["d","M-0.00195312 42.75V2.25C-0.00195312 0.975 0.973047 0 2.24805 0H45.748C47.023 0 47.998 0.975 47.998 2.25V42.75C47.998 44.025 47.023 45 45.748 45H2.24805C0.973047 45 -0.00195312 44.025 -0.00195312 42.75Z","fill","#CAD1D8"],["d","M2.24805 3.75C2.66226 3.75 2.99805 3.41421 2.99805 3C2.99805 2.58579 2.66226 2.25 2.24805 2.25C1.83384 2.25 1.49805 2.58579 1.49805 3C1.49805 3.41421 1.83384 3.75 2.24805 3.75Z","fill","#9BA7AF"],["d","M5.24805 3.75C5.66226 3.75 5.99805 3.41421 5.99805 3C5.99805 2.58579 5.66226 2.25 5.24805 2.25C4.83384 2.25 4.49805 2.58579 4.49805 3C4.49805 3.41421 4.83384 3.75 5.24805 3.75Z","fill","#9BA7AF"],["d","M8.24805 3.75C8.66227 3.75 8.99805 3.41421 8.99805 3C8.99805 2.58579 8.66227 2.25 8.24805 2.25C7.83382 2.25 7.49805 2.58579 7.49805 3C7.49805 3.41421 7.83382 3.75 8.24805 3.75Z","fill","#9BA7AF"],["d","M2.24805 6H45.748C46.198 6 46.498 6.3 46.498 6.75V42.75C46.498 43.2 46.198 43.5 45.748 43.5H2.24805C1.79805 43.5 1.49805 43.2 1.49805 42.75V6.75C1.49805 6.3 1.79805 6 2.24805 6Z","fill","white"],["d","M45.748 3.75H11.248C10.798 3.75 10.498 3.45 10.498 3C10.498 2.55 10.798 2.25 11.248 2.25H45.748C46.198 2.25 46.498 2.55 46.498 3C46.498 3.45 46.198 3.75 45.748 3.75Z","fill","#E2E5E7"],["d","M8.99805 17.25C10.6549 17.25 11.998 15.9068 11.998 14.25C11.998 12.5932 10.6549 11.25 8.99805 11.25C7.34119 11.25 5.99805 12.5932 5.99805 14.25C5.99805 15.9068 7.34119 17.25 8.99805 17.25Z","fill","#F7FFF2"],["d","M11.248 16.1988C11.248 15.9738 11.098 15.7488 10.798 15.5988L10.048 15.1488C9.97305 15.0738 9.89805 15.0738 9.74805 14.9988V14.6238C9.97305 14.3988 10.123 14.0988 10.123 13.7988C10.123 13.1988 9.59805 12.6738 8.99805 12.6738C8.39805 12.6738 7.87305 13.1988 7.87305 13.7988C7.87305 14.0988 8.02305 14.3988 8.24805 14.6238V14.9988C8.17305 14.9988 8.02305 15.0738 7.94805 15.1488L7.19805 15.5988C6.97305 15.7488 6.82305 15.9738 6.74805 16.1988C7.27305 16.7988 8.09805 17.2488 8.99805 17.2488C9.89805 17.2488 10.723 16.8738 11.248 16.1988Z","fill","#79BA44"],["d","M5.99805 20.25C5.99805 19.8 6.29805 19.5 6.74805 19.5H11.998C12.448 19.5 12.748 19.8 12.748 20.25C12.748 20.7 12.448 21 11.998 21H6.74805C6.29805 21 5.99805 20.7 5.99805 20.25Z","fill","#CAD1D8"],["d","M5.99805 23.25C5.99805 22.8 6.29805 22.5 6.74805 22.5H10.498C10.948 22.5 11.248 22.8 11.248 23.25C11.248 23.7 10.948 24 10.498 24H6.74805C6.29805 24 5.99805 23.7 5.99805 23.25Z","fill","#CAD1D8"],["d","M32.25 26.75H19.5C19.05 26.75 18.75 26.45 18.75 26C18.75 25.55 19.05 25.25 19.5 25.25H32.25C32.7 25.25 33 25.55 33 26C33 26.45 32.7 26.75 32.25 26.75Z","fill","#CAD1D8"],["d","M32.25 20.75H19.5C19.05 20.75 18.75 20.45 18.75 20C18.75 19.55 19.05 19.25 19.5 19.25H32.25C32.7 19.25 33 19.55 33 20C33 20.45 32.7 20.75 32.25 20.75Z","fill","#CAD1D8"],["d","M30 23.75H19.5C19.05 23.75 18.75 23.45 18.75 23C18.75 22.55 19.05 22.25 19.5 22.25H30C30.45 22.25 30.75 22.55 30.75 23C30.75 23.45 30.45 23.75 30 23.75Z","fill","#CAD1D8"],["d","M34.5 29.75H19.5C19.05 29.75 18.75 29.45 18.75 29C18.75 28.55 19.05 28.25 19.5 28.25H34.5C34.95 28.25 35.25 28.55 35.25 29C35.25 29.45 34.95 29.75 34.5 29.75Z","fill","#CAD1D8"],["d","M36 17.75H19.5C19.05 17.75 18.75 17.45 18.75 17C18.75 16.55 19.05 16.25 19.5 16.25H36C36.45 16.25 36.75 16.55 36.75 17C36.75 17.45 36.45 17.75 36 17.75Z","fill","#CAD1D8"],["d","M27.75 32.75H19.5C19.05 32.75 18.75 32.45 18.75 32C18.75 31.55 19.05 31.25 19.5 31.25H27.75C28.2 31.25 28.5 31.55 28.5 32C28.5 32.45 28.2 32.75 27.75 32.75Z","fill","#CAD1D8"],[1,"d-flex","flex-column","content",3,"click"],[1,"template-name"],[1,"template-detail"],[1,"d-flex","justify-content-start",2,"gap","10px"],["class","action-icon",3,"click",4,"ngIf"],["style","margin-top: 12px",4,"ngIf"],[1,"action-icon",3,"click"],["opacity","0.5","width","16","height","16","viewBox","0 0 16 16","fill","none"],["d","M1.41175 14.5883H2.59907L12.2335 4.9538L11.0462 3.76648L1.41175 13.4009V14.5883ZM0 16V12.8145L12.4145 0.405437C12.5569 0.276166 12.714 0.176275 12.886 0.105765C13.0579 0.0352549 13.2383 0 13.427 0C13.6157 0 13.7985 0.0334908 13.9754 0.100471C14.1523 0.167436 14.3089 0.273915 14.4452 0.419907L15.5946 1.5837C15.7406 1.72004 15.8446 1.87694 15.9068 2.05438C15.9689 2.23181 16 2.40924 16 2.58667C16 2.77592 15.9677 2.95653 15.903 3.1285C15.8384 3.30049 15.7356 3.45764 15.5946 3.59996L3.18549 16H0ZM11.6294 4.37055L11.0462 3.76648L12.2335 4.9538L11.6294 4.37055Z","fill","#1C1B1F"],["opacity","0.5","width","16","height","18","viewBox","0 0 16 18","fill","none"],["d","M3.3077 17.5C2.81058 17.5 2.38502 17.323 2.03102 16.969C1.67701 16.615 1.5 16.1894 1.5 15.6923V3.00005H1.25C1.0375 3.00005 0.859375 2.92814 0.715625 2.78433C0.571875 2.64053 0.5 2.46233 0.5 2.24973C0.5 2.03715 0.571875 1.85906 0.715625 1.71548C0.859375 1.57188 1.0375 1.50008 1.25 1.50008H4.99997C4.99997 1.25521 5.08619 1.04656 5.25863 0.874129C5.43106 0.701695 5.63971 0.615479 5.88457 0.615479H10.1154C10.3602 0.615479 10.5689 0.701695 10.7413 0.874129C10.9138 1.04656 11 1.25521 11 1.50008H14.75C14.9625 1.50008 15.1406 1.57199 15.2843 1.7158C15.4281 1.85962 15.5 2.03782 15.5 2.2504C15.5 2.463 15.4281 2.6411 15.2843 2.78468C15.1406 2.92826 14.9625 3.00005 14.75 3.00005H14.5V15.6923C14.5 16.1894 14.3229 16.615 13.9689 16.969C13.6149 17.323 13.1894 17.5 12.6922 17.5H3.3077ZM13 3.00005H2.99997V15.6923C2.99997 15.7821 3.02883 15.8558 3.08652 15.9135C3.14422 15.9712 3.21795 16.0001 3.3077 16.0001H12.6922C12.782 16.0001 12.8557 15.9712 12.9134 15.9135C12.9711 15.8558 13 15.7821 13 15.6923V3.00005ZM6.15417 14.0001C6.36676 14.0001 6.54484 13.9282 6.68842 13.7844C6.83202 13.6407 6.90382 13.4626 6.90382 13.2501V5.75003C6.90382 5.53755 6.83192 5.35943 6.6881 5.21568C6.54428 5.07193 6.36608 5.00005 6.1535 5.00005C5.9409 5.00005 5.76281 5.07193 5.61922 5.21568C5.47564 5.35943 5.40385 5.53755 5.40385 5.75003V13.2501C5.40385 13.4626 5.47576 13.6407 5.61958 13.7844C5.76337 13.9282 5.94158 14.0001 6.15417 14.0001ZM9.84645 14.0001C10.059 14.0001 10.2371 13.9282 10.3807 13.7844C10.5243 13.6407 10.5961 13.4626 10.5961 13.2501V5.75003C10.5961 5.53755 10.5242 5.35943 10.3804 5.21568C10.2366 5.07193 10.0584 5.00005 9.84577 5.00005C9.63319 5.00005 9.45511 5.07193 9.31152 5.21568C9.16792 5.35943 9.09613 5.53755 9.09613 5.75003V13.2501C9.09613 13.4626 9.16803 13.6407 9.31185 13.7844C9.45567 13.9282 9.63387 14.0001 9.84645 14.0001Z","fill","#1C1B1F"],[2,"margin-top","12px"],[1,"empty-state"],["src","https://assets.kebs.app/ATS-Empty-State.png"],[1,"empty-title"],[3,"currentSearchText","recentSearch","onEnter"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275elementStart"](3,"div",3),c["\u0275\u0275text"](4,"Email Template"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"div",4),c["\u0275\u0275text"](6,"Customize the email template the way you wish."),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"div"),c["\u0275\u0275elementStart"](8,"div",5),c["\u0275\u0275template"](9,O,9,1,"div",6),c["\u0275\u0275template"](10,S,5,0,"div",7),c["\u0275\u0275template"](11,b,2,0,"div",8),c["\u0275\u0275pipe"](12,"access"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](13,P,7,1,"div",9),c["\u0275\u0275template"](14,L,3,2,"div",10),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](15,H,1,3,"ng-template",11,12,c["\u0275\u0275templateRefExtractor"])),2&e&&(c["\u0275\u0275advance"](9),c["\u0275\u0275property"]("ngIf",""!=t.searchParams&&null!=t.searchParams),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",""==t.searchParams||null==t.searchParams),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBindV"](12,6,c["\u0275\u0275pureFunction2"](12,j,t.access.moduleId.settings,t.access.subModuleId.templateSettings))),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",t.isLoading),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!t.isLoading),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerSearchBar))},directives:[i.NgIf,v.a,v.b,x.e,x.v,x.y,i.NgForOf,u.a,y.a],pipes:[_.a,M.a],styles:['.email-template[_ngcontent-%COMP%]{height:var(--dynamicSubHeight)}.email-template[_ngcontent-%COMP%]   .email-item[_ngcontent-%COMP%]{padding:0 20px}.email-template[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]{cursor:pointer}.email-template[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:18px;height:18px;font-size:18px}.email-template[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{cursor:pointer}.email-template[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-weight:500;font-size:14px;color:#45546e}.email-template[_ngcontent-%COMP%]   .template-detail[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-weight:400;font-size:12px;color:#b9c0ca}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;min-height:57px;background-color:#f9fafc;padding-left:16px;padding-right:16px;position:sticky;top:0}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .align-items-column[_ngcontent-%COMP%]{display:flex;flex-direction:column}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{cursor:pointer}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]{width:350px;height:36px;padding:0 8px;border:2px solid #dadce2;border-radius:8px;cursor:text}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;outline:none;border:none}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#5f6c81;width:280px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .create-template[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-family:var(--atsfontFamily);font-weight:700;color:#45546e;cursor:pointer;font-size:13px;padding:4px 12px;border-radius:8px;border:1.5px solid}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .sub-menu-2[_ngcontent-%COMP%]{gap:16px}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#111434;line-height:20px}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:10px;font-weight:400;color:#8b95a5;line-height:16px}.email-template[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{cursor:pointer;font-size:16px}.email-template[_ngcontent-%COMP%]   .loading-img[_ngcontent-%COMP%]{height:-webkit-fill-available;display:flex;flex-direction:column;align-items:center;justify-content:center;background-color:#fff}.email-template[_ngcontent-%COMP%]   .loading-gradient[_ngcontent-%COMP%]{height:24px;width:100%;background:linear-gradient(90deg,#f1efef -24.18%,#f9f8f8 50.26%,#e7e5e5 114.84%);animation:gradientLoading 10s linear infinite;background-size:200% 100%;border-radius:8px}.email-template[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.email-template[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.email-template[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}@keyframes gradientLoading{0%{background-position:200% 0}to{background-position:-200% 0}}.bg-container[_ngcontent-%COMP%]{overflow-y:scroll;gap:12px}.bg-container[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]{height:var(--dynamicSubHeight)}.empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;overflow:hidden}.empty-state[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434;margin-bottom:4px}']}),e})();var R=n("Jzeh"),G=n("B0y8"),U=n("JLuW"),N=n("NFeN"),Q=n("UVjm"),Z=n("su5B"),X=n("kmnG"),Y=n("qFsG"),K=n("4/q7"),q=n("6t9p"),W=n("1jcm"),J=n("yx4D");function $(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",7),c["\u0275\u0275text"](1," Unable to generate preview "),c["\u0275\u0275elementEnd"]())}function ee(e,t){if(1&e&&(c["\u0275\u0275element"](0,"div",8),c["\u0275\u0275pipe"](1,"svgSecurityBypass")),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("innerHTML",c["\u0275\u0275pipeBind1"](1,1,e.previewHTML),c["\u0275\u0275sanitizeHtml"])}}let te=(()=>{class e{constructor(){this.valueEmitter=new c.EventEmitter}ngOnInit(){}goBacktoTemplate(){this.valueEmitter.emit()}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-template-preview"]],inputs:{previewHTML:"previewHTML"},outputs:{valueEmitter:"valueEmitter"},decls:9,vars:2,consts:[[1,"d-flex","flex-column","preview-template"],[1,"d-flex","align-items-center","justify-content-start",2,"gap","15px"],[1,"back-button",3,"click"],[1,"back-icon"],[1,"title-text"],["class","preview-blank-text",4,"ngIf"],["class","inner-template",3,"innerHTML",4,"ngIf"],[1,"preview-blank-text"],[1,"inner-template",3,"innerHTML"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275listener"]("click",(function(){return t.goBacktoTemplate()})),c["\u0275\u0275elementStart"](3,"mat-icon",3),c["\u0275\u0275text"](4," navigate_before "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"div",4),c["\u0275\u0275text"](6,"Preview"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](7,$,2,0,"div",5),c["\u0275\u0275template"](8,ee,2,3,"div",6),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](7),c["\u0275\u0275property"]("ngIf",!t.previewHTML||""==t.previewHTML),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.previewHTML&&""!=t.previewHTML))},directives:[N.a,i.NgIf],pipes:[J.a],styles:[".preview-template[_ngcontent-%COMP%]{height:var(--dynamicHeight);padding:24px;gap:24px}.preview-template[_ngcontent-%COMP%]   .inner-template[_ngcontent-%COMP%]{padding-top:35px;width:100%;padding-left:140px}.preview-template[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-family:var(--atsFontFamily);font-weight:700;font-size:16px}.preview-template[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{width:16px;height:16px;border:1px solid #526179;border-radius:4px;cursor:pointer}.preview-template[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{font-size:14px;color:#526179}.preview-template[_ngcontent-%COMP%]   .preview-blank-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:400;color:#6e7b8f}.preview-template[_ngcontent-%COMP%]   .inner-template[_ngcontent-%COMP%]{background-color:#fff;overflow-x:auto;padding:8px}"]}),e})();var ne=n("y5kQ"),ie=n("pPzn");const ae=["triggerPlaceholderChange"],re=["bodyEditor"];function oe(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",21),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2);c["\u0275\u0275property"]("matTooltip",(null==e.templateBasicData?null:e.templateBasicData.templateName)||"-"),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",(null==e.templateBasicData?null:e.templateBasicData.templateName)||"-"," ")}}function le(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",22),c["\u0275\u0275element"](1,"input",23),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2);c["\u0275\u0275property"]("formGroup",e.mailFormGroup)}}function se(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",24),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275nextContext"](2);return t.isTemplateEditMode=!t.isTemplateEditMode})),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](1,"svg",25),c["\u0275\u0275element"](2,"path",26),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}}function ce(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"mat-icon",27),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275nextContext"](2);return t.isTemplateEditMode=!t.isTemplateEditMode,t.templateBasicData.templateName=t.mailFormGroup.get("templateName").value})),c["\u0275\u0275text"](1," close "),c["\u0275\u0275elementEnd"]()}}function pe(e,t){}function de(e,t){if(1&e&&c["\u0275\u0275template"](0,pe,0,0,"ng-template",31),2&e){c["\u0275\u0275nextContext"](3);const e=c["\u0275\u0275reference"](6);c["\u0275\u0275property"]("ngTemplateOutlet",e)}}const me=function(){return{field:"from"}};function ge(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275elementStart"](1,"span",29),c["\u0275\u0275text"](2),c["\u0275\u0275pipe"](3,"columnCustomization"),c["\u0275\u0275template"](4,de,1,1,void 0,1),c["\u0275\u0275pipe"](5,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"app-single-select-chip",30),c["\u0275\u0275listener"]("onValueChange",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).onCustomSelectValueChange(t)})),c["\u0275\u0275pipe"](7,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"](2);let t=null;c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"]("",c["\u0275\u0275pipeBind3"](3,8,e.mailFormConfig,"from","label")," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](5,12,e.mailFormConfig,"from","isMandatory")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("isChip",!0)("placeholder",c["\u0275\u0275pipeBind3"](7,16,e.mailFormConfig,"from","placeholder"))("masterData",e.fromMailPlaceHolderMasterData)("selectedValue",null==e.mailFormGroup||null==(t=e.mailFormGroup.get("from"))?null:t.value)("data",c["\u0275\u0275pureFunction0"](20,me))("displayClose",!1)}}function he(e,t){}function ue(e,t){if(1&e&&c["\u0275\u0275template"](0,he,0,0,"ng-template",31),2&e){c["\u0275\u0275nextContext"](3);const e=c["\u0275\u0275reference"](6);c["\u0275\u0275property"]("ngTemplateOutlet",e)}}const fe=function(){return{field:"to"}};function Ce(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275elementStart"](1,"span",29),c["\u0275\u0275text"](2),c["\u0275\u0275pipe"](3,"columnCustomization"),c["\u0275\u0275template"](4,ue,1,1,void 0,1),c["\u0275\u0275pipe"](5,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"app-multi-select-chip",32),c["\u0275\u0275listener"]("onValueChange",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).onCustomSelectValueChange(t)})),c["\u0275\u0275pipe"](7,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"](2);let t=null;c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"]("",c["\u0275\u0275pipeBind3"](3,7,e.mailFormConfig,"to","label")," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](5,11,e.mailFormConfig,"to","isMandatory")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("type",2)("placeholder",c["\u0275\u0275pipeBind3"](7,15,e.mailFormConfig,"to","placeholder"))("masterData",e.mailPlaceHolderMasterData)("selectedValues",null==e.mailFormGroup||null==(t=e.mailFormGroup.get("to"))?null:t.value)("data",c["\u0275\u0275pureFunction0"](19,fe))}}function ve(e,t){}function xe(e,t){if(1&e&&c["\u0275\u0275template"](0,ve,0,0,"ng-template",31),2&e){c["\u0275\u0275nextContext"](3);const e=c["\u0275\u0275reference"](6);c["\u0275\u0275property"]("ngTemplateOutlet",e)}}const ye=function(){return{field:"cc"}};function _e(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275elementStart"](1,"span",29),c["\u0275\u0275text"](2),c["\u0275\u0275pipe"](3,"columnCustomization"),c["\u0275\u0275template"](4,xe,1,1,void 0,1),c["\u0275\u0275pipe"](5,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"app-multi-select-chip",32),c["\u0275\u0275listener"]("onValueChange",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).onCustomSelectValueChange(t)})),c["\u0275\u0275pipe"](7,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"](2);let t=null;c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"]("",c["\u0275\u0275pipeBind3"](3,7,e.mailFormConfig,"cc","label")," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](5,11,e.mailFormConfig,"cc","isMandatory")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("type",2)("placeholder",c["\u0275\u0275pipeBind3"](7,15,e.mailFormConfig,"cc","placeholder"))("masterData",e.mailPlaceHolderMasterData)("selectedValues",null==e.mailFormGroup||null==(t=e.mailFormGroup.get("cc"))?null:t.value)("data",c["\u0275\u0275pureFunction0"](19,ye))}}function Me(e,t){}function we(e,t){if(1&e&&c["\u0275\u0275template"](0,Me,0,0,"ng-template",31),2&e){c["\u0275\u0275nextContext"](3);const e=c["\u0275\u0275reference"](6);c["\u0275\u0275property"]("ngTemplateOutlet",e)}}const Oe=function(){return{field:"replyTo"}};function Se(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275elementStart"](1,"span",29),c["\u0275\u0275text"](2),c["\u0275\u0275pipe"](3,"columnCustomization"),c["\u0275\u0275template"](4,we,1,1,void 0,1),c["\u0275\u0275pipe"](5,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"app-multi-select-chip",32),c["\u0275\u0275listener"]("onValueChange",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).onCustomSelectValueChange(t)})),c["\u0275\u0275pipe"](7,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"](2);let t=null;c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"]("",c["\u0275\u0275pipeBind3"](3,7,e.mailFormConfig,"replyTo","label")," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](5,11,e.mailFormConfig,"replyTo","isMandatory")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("type",2)("placeholder",c["\u0275\u0275pipeBind3"](7,15,e.mailFormConfig,"replyTo","placeholder"))("masterData",e.mailPlaceHolderMasterData)("selectedValues",null==e.mailFormGroup||null==(t=e.mailFormGroup.get("replyTo"))?null:t.value)("data",c["\u0275\u0275pureFunction0"](19,Oe))}}function be(e,t){}function Pe(e,t){if(1&e&&c["\u0275\u0275template"](0,be,0,0,"ng-template",31),2&e){c["\u0275\u0275nextContext"](3);const e=c["\u0275\u0275reference"](6);c["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Ee(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275elementStart"](1,"span",29),c["\u0275\u0275text"](2),c["\u0275\u0275pipe"](3,"columnCustomization"),c["\u0275\u0275template"](4,Pe,1,1,void 0,1),c["\u0275\u0275pipe"](5,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"mat-form-field",33),c["\u0275\u0275element"](7,"input",34),c["\u0275\u0275pipe"](8,"columnCustomization"),c["\u0275\u0275pipe"](9,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2);c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"]("",c["\u0275\u0275pipeBind3"](3,4,e.mailFormConfig,"subject","label")," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](5,8,e.mailFormConfig,"subject","isMandatory")),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("placeholder",c["\u0275\u0275pipeBind3"](8,12,e.mailFormConfig,"subject","placeholder"))("disabled",c["\u0275\u0275pipeBind3"](9,16,e.mailFormConfig,"subject","isDisabled"))}}function De(e,t){}function ke(e,t){if(1&e&&c["\u0275\u0275template"](0,De,0,0,"ng-template",31),2&e){c["\u0275\u0275nextContext"](3);const e=c["\u0275\u0275reference"](6);c["\u0275\u0275property"]("ngTemplateOutlet",e)}}const Te=function(){return["8pt","10pt","12pt","14pt","18pt","24pt","36pt"]},Fe=function(){return{"aria-label":"Font size"}},Ie=function(e){return{inputAttr:e}},Ve=function(){return["Arial","Courier New","Georgia","Impact","Lucida Console","Tahoma","Times New Roman","Verdana"]},Ae=function(){return{"aria-label":"Font family"}};function Le(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275elementStart"](1,"span",29),c["\u0275\u0275text"](2),c["\u0275\u0275pipe"](3,"columnCustomization"),c["\u0275\u0275template"](4,ke,1,1,void 0,1),c["\u0275\u0275pipe"](5,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"dx-html-editor",35,36),c["\u0275\u0275element"](8,"dxo-table-context-menu",37),c["\u0275\u0275element"](9,"dxo-table-resizing",37),c["\u0275\u0275elementStart"](10,"dxo-toolbar",38),c["\u0275\u0275element"](11,"dxi-item",39),c["\u0275\u0275element"](12,"dxi-item",40),c["\u0275\u0275element"](13,"dxi-item",41),c["\u0275\u0275element"](14,"dxi-item",42),c["\u0275\u0275element"](15,"dxi-item",43),c["\u0275\u0275element"](16,"dxi-item",44),c["\u0275\u0275element"](17,"dxi-item",45),c["\u0275\u0275element"](18,"dxi-item",46),c["\u0275\u0275element"](19,"dxi-item",47),c["\u0275\u0275element"](20,"dxi-item",48),c["\u0275\u0275element"](21,"dxi-item",49),c["\u0275\u0275element"](22,"dxi-item",50),c["\u0275\u0275element"](23,"dxi-item",51),c["\u0275\u0275element"](24,"dxi-item",52),c["\u0275\u0275element"](25,"dxi-item",53),c["\u0275\u0275element"](26,"dxi-item",54),c["\u0275\u0275element"](27,"dxi-item",55),c["\u0275\u0275element"](28,"dxi-item",53),c["\u0275\u0275element"](29,"dxi-item",56),c["\u0275\u0275element"](30,"dxi-item",57),c["\u0275\u0275element"](31,"dxi-item",58),c["\u0275\u0275element"](32,"dxi-item",59),c["\u0275\u0275element"](33,"dxi-item",53),c["\u0275\u0275element"](34,"dxi-item",60),c["\u0275\u0275element"](35,"dxi-item",61),c["\u0275\u0275element"](36,"dxi-item",53),c["\u0275\u0275element"](37,"dxi-item",62),c["\u0275\u0275element"](38,"dxi-item",63),c["\u0275\u0275element"](39,"dxi-item",64),c["\u0275\u0275element"](40,"dxi-item",53),c["\u0275\u0275element"](41,"dxi-item",65),c["\u0275\u0275element"](42,"dxi-item",66),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](43,"div",67),c["\u0275\u0275elementStart"](44,"span",68),c["\u0275\u0275text"](45),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](46,"span",69,70),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275reference"](48),n=c["\u0275\u0275nextContext"](2),i=c["\u0275\u0275reference"](3);return n.openOverlay(t,i)}))("mouseenter",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275reference"](7);return c["\u0275\u0275nextContext"](2).saveCursorPosition(t)})),c["\u0275\u0275text"](49,"Choose Placeholder"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"](2);c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"]("",c["\u0275\u0275pipeBind3"](3,10,e.mailFormConfig,"body","label")," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](5,14,e.mailFormConfig,"body","isMandatory")),c["\u0275\u0275advance"](4),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("multiline",!1),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("acceptedValues",c["\u0275\u0275pureFunction0"](18,Te))("options",c["\u0275\u0275pureFunction1"](20,Ie,c["\u0275\u0275pureFunction0"](19,Fe))),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("acceptedValues",c["\u0275\u0275pureFunction0"](22,Ve))("options",c["\u0275\u0275pureFunction1"](24,Ie,c["\u0275\u0275pureFunction0"](23,Ae))),c["\u0275\u0275advance"](33),c["\u0275\u0275textInterpolate"]("@ Placeholders")}}function Be(e,t){}function He(e,t){if(1&e&&c["\u0275\u0275template"](0,Be,0,0,"ng-template",31),2&e){c["\u0275\u0275nextContext"](3);const e=c["\u0275\u0275reference"](6);c["\u0275\u0275property"]("ngTemplateOutlet",e)}}function je(e,t){1&e&&c["\u0275\u0275element"](0,"dx-html-editor",75)}function ze(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275elementStart"](1,"div",71),c["\u0275\u0275elementStart"](2,"div"),c["\u0275\u0275elementStart"](3,"span",29),c["\u0275\u0275text"](4),c["\u0275\u0275pipe"](5,"columnCustomization"),c["\u0275\u0275template"](6,He,1,1,void 0,1),c["\u0275\u0275pipe"](7,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](8,"div"),c["\u0275\u0275elementStart"](9,"div",72),c["\u0275\u0275element"](10,"mat-slide-toggle",73),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](11,je,1,0,"dx-html-editor",74),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2);let t=null,n=null;c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate1"]("",c["\u0275\u0275pipeBind3"](5,3,e.mailFormConfig,"signature","label")," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](7,7,e.mailFormConfig,"signature","isMandatory")||(null==e.mailFormGroup||null==(t=e.mailFormGroup.get("isSignatureOn"))?null:t.value)),c["\u0275\u0275advance"](5),c["\u0275\u0275property"]("ngIf",null==e.mailFormGroup||null==(n=e.mailFormGroup.get("isSignatureOn"))?null:n.value)}}function Re(e,t){}function Ge(e,t){if(1&e&&c["\u0275\u0275template"](0,Re,0,0,"ng-template",31),2&e){c["\u0275\u0275nextContext"](3);const e=c["\u0275\u0275reference"](6);c["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Ue(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275elementStart"](1,"span",29),c["\u0275\u0275text"](2),c["\u0275\u0275pipe"](3,"columnCustomization"),c["\u0275\u0275template"](4,Ge,1,1,void 0,1),c["\u0275\u0275pipe"](5,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"div",76),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).openAttachmentPlugin()})),c["\u0275\u0275text"](7," Click to Add or View Attachment(s) "),c["\u0275\u0275element"](8,"br"),c["\u0275\u0275elementStart"](9,"span",77),c["\u0275\u0275text"](10),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"](2);c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"]("",c["\u0275\u0275pipeBind3"](3,3,e.mailFormConfig,"attachment","label")," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](5,7,e.mailFormConfig,"attachment","isMandatory")),c["\u0275\u0275advance"](6),c["\u0275\u0275textInterpolate1"]("",e.attachmentCount," attachment(s) added!")}}function Ne(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",5),c["\u0275\u0275elementStart"](1,"div",6),c["\u0275\u0275elementStart"](2,"div",7),c["\u0275\u0275elementStart"](3,"div",8),c["\u0275\u0275elementStart"](4,"div",9),c["\u0275\u0275elementStart"](5,"div",10),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().goBacktoTemplate()})),c["\u0275\u0275elementStart"](6,"mat-icon",11),c["\u0275\u0275text"](7," navigate_before "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](8,oe,2,2,"div",12),c["\u0275\u0275template"](9,le,2,1,"div",13),c["\u0275\u0275template"](10,se,3,0,"div",14),c["\u0275\u0275template"](11,ce,2,0,"mat-icon",15),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](12,"div"),c["\u0275\u0275elementStart"](13,"div",16),c["\u0275\u0275elementStart"](14,"div",17),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().handlePreview()})),c["\u0275\u0275text"](15,"Preview"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](16,"div",18),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().handleCreateTemplate()})),c["\u0275\u0275text"](17," Save Template "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](18,"div",19),c["\u0275\u0275template"](19,ge,8,21,"div",20),c["\u0275\u0275pipe"](20,"columnCustomization"),c["\u0275\u0275template"](21,Ce,8,20,"div",20),c["\u0275\u0275pipe"](22,"columnCustomization"),c["\u0275\u0275template"](23,_e,8,20,"div",20),c["\u0275\u0275pipe"](24,"columnCustomization"),c["\u0275\u0275template"](25,Se,8,20,"div",20),c["\u0275\u0275pipe"](26,"columnCustomization"),c["\u0275\u0275template"](27,Ee,10,20,"div",20),c["\u0275\u0275pipe"](28,"columnCustomization"),c["\u0275\u0275template"](29,Le,50,26,"div",20),c["\u0275\u0275pipe"](30,"columnCustomization"),c["\u0275\u0275template"](31,ze,12,11,"div",20),c["\u0275\u0275pipe"](32,"columnCustomization"),c["\u0275\u0275template"](33,Ue,11,11,"div",20),c["\u0275\u0275pipe"](34,"columnCustomization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](8),c["\u0275\u0275property"]("ngIf",!e.isTemplateEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.isTemplateEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!e.isTemplateEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.isTemplateEditMode),c["\u0275\u0275advance"](7),c["\u0275\u0275property"]("formGroup",e.mailFormGroup),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](20,13,e.mailFormConfig,"from","isActive")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](22,17,e.mailFormConfig,"to","isActive")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](24,21,e.mailFormConfig,"cc","isActive")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](26,25,e.mailFormConfig,"replyTo","isActive")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](28,29,e.mailFormConfig,"subject","isActive")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](30,33,e.mailFormConfig,"body","isActive")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](32,37,e.mailFormConfig,"signature","isActive")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBind3"](34,41,e.mailFormConfig,"attachment","isActive"))}}function Qe(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275elementStart"](1,"app-template-preview",78),c["\u0275\u0275listener"]("valueEmitter",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().handleValueFromChild(t)})),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("previewHTML",e.previewHTML)}}function Ze(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"app-placeholder-group-menu-overlay",79),c["\u0275\u0275listener"]("onConfirmation",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().onSelectPlaceholder(t)})),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("list",e.placeholdersMasterData)}}function Xe(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",5),c["\u0275\u0275elementStart"](1,"div",80),c["\u0275\u0275elementStart"](2,"div",81),c["\u0275\u0275element"](3,"img",82),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",83),c["\u0275\u0275elementStart"](5,"div",84),c["\u0275\u0275text"](6,"Loading..."),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],c["\u0275\u0275sanitizeUrl"])}}function Ye(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"span",85),c["\u0275\u0275text"](1,"*"),c["\u0275\u0275elementEnd"]())}let Ke=(()=>{class e{constructor(e,t,n,i,a,r,o,s,p,d){this._sharedService=e,this._viewContainerRef=t,this._overlay=n,this._dialog=i,this._masterService=a,this._settingsService=r,this._toaster=o,this._fb=s,this._router=p,this._atsTemplateSettingsService=d,this._onDestroy=new l.b,this.mode="",this.uiTextConfig={},this.fromMailPlaceHolderMasterData=[],this.mailPlaceHolderMasterData=[],this.placeholdersMasterData=[],this.attachmentCount=0,this.attachmentConfig={},this.filesUploaded=[],this.attachmentPath="",this.companyLogo="",this.createdValue=new c.EventEmitter,this.previewEmit=new c.EventEmitter,this.mailFormConfig=[],this.data={},this.savedCursorPosition=0,this.previewHTML="",this.showPreview=!1,this.isTemplateEditMode=!1,this.loading=!0,this.mailFormGroup=this._fb.group({})}ngOnInit(){var e,t,n,i,a,o,l,s,c,p,d,m,g,h;return Object(r.c)(this,void 0,void 0,(function*(){yield this.getAtsFormsConfig(),yield this.createMailForm(),this.getPlaceHolders(),this.getReceiverEmail(),this.getSenderEmail(),yield this.getAttachmentConfig(),this.mailFormGroup.valueChanges.subscribe(e=>{this.previewHTML=(this.companyLogo||"")+(this.mailFormGroup.get("header").value||"")+(this.mailFormGroup.get("body").value||"")+(this.mailFormGroup.get("signature").value||"")}),(null===(e=this.templateBasicData)||void 0===e?void 0:e.template)&&(yield this.fetchSingleEmailTemplate(null===(t=this.templateBasicData)||void 0===t?void 0:t.template)),this.mailFormGroup.patchValue({templateName:(null===(n=this.templateBasicData)||void 0===n?void 0:n.templateName)||null,stageType:(null===(i=this.templateBasicData)||void 0===i?void 0:i.stageType)||null}),this.data&&(this.mailFormGroup.patchValue({subject:(null===(a=this.data)||void 0===a?void 0:a.subject)?this.data.subject:null,body:(null===(o=this.data)||void 0===o?void 0:o.body)?this.data.body:null,signature:(null===(l=this.data)||void 0===l?void 0:l.signature)?this.data.signature:null,isSignatureOn:!!(null===(s=this.data)||void 0===s?void 0:s.signature),header:(null===(c=this.data)||void 0===c?void 0:c.header)?this.data.header:null,from:(null===(p=this.data)||void 0===p?void 0:p.from)?this.data.from:null,to:(null===(d=this.data)||void 0===d?void 0:d.to)?this.data.to:null,cc:(null===(m=this.data)||void 0===m?void 0:m.cc)?this.data.cc:null,attachment:(null===(g=this.data)||void 0===g?void 0:g.attachment)?null===(h=this.data)||void 0===h?void 0:h.attachment:null}),this.previewHTML=(this.companyLogo||"")+(this.mailFormGroup.get("header").value||"")+(this.mailFormGroup.get("body").value||"")+(this.mailFormGroup.get("signature").value||"")),this.mailFormGroup.get("from").valueChanges.subscribe(e=>{var t;if(e){let n=null===(t=this.fromMailPlaceHolderMasterData.find(t=>t.id==e))||void 0===t?void 0:t.signature;n&&(this.mailFormGroup.get("signature").setValue(n),this.mailFormGroup.get("isSignatureOn").setValue(!0))}else this.mailFormGroup.get("signature").setValue(null),this.mailFormGroup.get("isSignatureOn").setValue(!1)}),this.mailFormGroup.get("isSignatureOn").valueChanges.subscribe(e=>{e?this.mailFormGroup.get("signature").setValidators(x.H.required):(this.mailFormGroup.get("signature").clearValidators(),this.mailFormGroup.get("signature").setErrors(null),this.mailFormGroup.get("signature").setValue(null),this.mailFormGroup.get("signature").updateValueAndValidity())}),this.retrieveUploadedObjects(),this.loading=!1}))}createMailForm(){return Object(r.c)(this,void 0,void 0,(function*(){for(let e=0;e<this.mailFormConfig.length;e++)this.mailFormGroup.addControl(this.mailFormConfig[e].key,this._fb.control(null,[this.mailFormConfig[e].isMandatory?x.H.required:null].filter(e=>null!=e))),"signature"==this.mailFormConfig[e].key&&this.mailFormGroup.addControl("isSignatureOn",this._fb.control(!1)),this.mailFormGroup.addControl("header",this._fb.control(null))}))}onCustomSelectValueChange(e){this.mailFormGroup.get(e.data.field).setValue(e.val)}openOverlay(e,t){var n;if(!(null===(n=this.overlayRef)||void 0===n?void 0:n.hasAttached())){const n=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(30).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"top",overlayX:"center",overlayY:"bottom"}]),i=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:n,scrollStrategy:i,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const a=new f.h(t,this._viewContainerRef);this.overlayRef.attach(a),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}goBacktoTemplate(){this.previewEmit.emit({key:"",html:""})}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}saveCursorPosition(e){const t=e.instance.getSelection();this.savedCursorPosition=t?t.index:0}onSelectPlaceholder(e){this.closeOverlay(),this.insertPlaceholder(this.bodyEditor,e.placeholder,{color:"#000"})}insertPlaceholder(e,t,n){e.instance.insertText(this.savedCursorPosition,t,n)}openAttachmentPlugin(){var e;let t={destinationBucket:this.attachmentConfig.bucket_name,routingKey:this.attachmentConfig.routing_key,contextId:(null===(e=this.mailFormGroup.get("attachment"))||void 0===e?void 0:e.value)||null,allowEdit:!0};this._dialog.open(G.a,{width:"100%",height:"90%",data:{data:t},disableClose:!0}).afterClosed().subscribe(e=>{var t;this.attachmentCount=e.fileCount,null===(t=this.mailFormGroup.get("attachment"))||void 0===t||t.setValue((null==e?void 0:e.contextId)||null),this.filesUploaded.splice(0),this.retrieveUploadedObjects()})}retrieveUploadedObjects(){var e,t;return Object(r.c)(this,void 0,void 0,(function*(){(null===(e=this.data)||void 0===e?void 0:e.attachment)&&this._sharedService.retrieveUploadedObjects(this.attachmentConfig.bucket_name,null===(t=this.data)||void 0===t?void 0:t.attachment).subscribe(e=>{this.attachmentCount=e.data.length,this.filesUploaded=e.data},e=>{console.error(e)})}))}handlePreview(){this.showPreview=!0}handleCreateTemplate(){var e,t,n,i;if(this.mailFormGroup.valid){if("create"==this.mode){let a=this.mailFormGroup.value;a.template_type_id=1,a.email_template_type_id=(null===(e=this.templateBasicData)||void 0===e?void 0:e.stageType)||this.data.email_template_type_id||null,a.is_custom_attachment=null===(t=this.data)||void 0===t?void 0:t.is_custom_attachment,a.custom_attachment_template=null===(n=this.data)||void 0===n?void 0:n.custom_attachment_template,a.attachment_html_template=null===(i=this.data)||void 0===i?void 0:i.attachment_html_template,this.createEmailTemplate(a)}else if("edit"==this.mode){let e=this.mailFormGroup.value;e.template_id=this.data.template_id,e.template_type_id=this.data.template_type_id,e.email_template_type_id=this.data.email_template_type_id,e.is_custom_attachment=this.data.is_custom_attachment,e.custom_attachment_template=this.data.custom_attachment_template,e.attachment_html_template=this.data.attachment_html_template,this.updateEmailTemplate(e)}}else this._toaster.showError("Error","Kindly Fill All Mandaory Fields",7e3)}handleValueFromChild(){this.showPreview=!1}getAtsFormsConfig(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._masterService.getAtsFormsConfig("emailTemplateCreationForm").pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.mailFormConfig=t.data.form[0].formFields:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),t()}}))}))}getPlaceHolders(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._masterService.getPlaceHolders().pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.placeholdersMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),t()}}))}))}getAttachmentConfig(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._masterService.getAttachmentConfig(1).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.attachmentConfig=t.data[0]:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),t()}}))}))}getReceiverEmail(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._masterService.getReceiverEmail().pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.mailPlaceHolderMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),t()}}))}))}getSenderEmail(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._masterService.getSenderEmail().pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.fromMailPlaceHolderMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),t()}}))}))}createEmailTemplate(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsTemplateSettingsService.createEmailTemplate(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.createdValue.emit({key:"",msg:"Created Successfully"}),this._toaster.showSuccess("Success",e.msg,7e3)):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}updateEmailTemplate(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsTemplateSettingsService.updateEmailTemplate(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.createdValue.emit({key:"",msg:"Created Successfully"}),this._toaster.showSuccess("Success",e.msg,7e3)):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}fetchSingleEmailTemplate(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsTemplateSettingsService.fetchSingleEmailTemplate(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.data=null==e?void 0:e.data[0],""==this.templateBasicData.templateName&&(this.templateBasicData.templateName=null==e?void 0:e.data[0].template_name)):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](U.a),c["\u0275\u0275directiveInject"](c.ViewContainerRef),c["\u0275\u0275directiveInject"](v.e),c["\u0275\u0275directiveInject"](m.b),c["\u0275\u0275directiveInject"](p.a),c["\u0275\u0275directiveInject"](C.a),c["\u0275\u0275directiveInject"](d.a),c["\u0275\u0275directiveInject"](x.i),c["\u0275\u0275directiveInject"](a.g),c["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-create-template"]],viewQuery:function(e,t){if(1&e&&(c["\u0275\u0275viewQuery"](ae,!0),c["\u0275\u0275viewQuery"](re,!0)),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.triggerPlaceholderChange=e.first),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.bodyEditor=e.first)}},inputs:{mode:"mode",templateBasicData:"templateBasicData",uiTextConfig:"uiTextConfig"},outputs:{createdValue:"createdValue",previewEmit:"previewEmit"},decls:7,vars:4,consts:[["class","bg-container",4,"ngIf"],[4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerPlaceholderChange",""],["mandatoryTemplate",""],[1,"bg-container"],[1,"d-flex","flex-column","template-page"],[1,"header"],[1,"align-items-column"],[1,"d-flex","align-items-center","justify-content-start",2,"gap","15px"],[1,"back-button",3,"click"],[1,"back-icon"],["class","title-text",3,"matTooltip",4,"ngIf"],["class","template-edit",3,"formGroup",4,"ngIf"],["class","svg-icon",3,"click",4,"ngIf"],["class","template-name-close-icon",3,"click",4,"ngIf"],[1,"d-flex","align-items-center","sub-menu-2",2,"gap","17px"],[1,"preview",3,"click"],[1,"save-template",3,"click"],[1,"d-flex","flex-wrap","mail-template",3,"formGroup"],["class","d-flex flex-column col-12 p-0",4,"ngIf"],[1,"title-text",3,"matTooltip"],[1,"template-edit",3,"formGroup"],["type","text","maxlength","200","formControlName","templateName","placeholder","Template Name"],[1,"svg-icon",3,"click"],["width","16","height","16","viewBox","0 0 16 16","fill","none"],["d","M1.41175 14.5883H2.59907L12.2335 4.9538L11.0462 3.76648L1.41175 13.4009V14.5883ZM0 16V12.8145L12.4145 0.405437C12.5569 0.276166 12.714 0.176275 12.886 0.105765C13.0579 0.0352549 13.2383 0 13.427 0C13.6157 0 13.7985 0.0334908 13.9754 0.100471C14.1523 0.167436 14.3089 0.273915 14.4452 0.419907L15.5946 1.5837C15.7406 1.72004 15.8446 1.87694 15.9068 2.05438C15.9689 2.23181 16 2.40924 16 2.58667C16 2.77592 15.9677 2.95653 15.903 3.1285C15.8384 3.30049 15.7356 3.45764 15.5946 3.59996L3.18549 16H0ZM11.6294 4.37055L11.0462 3.76648L12.2335 4.9538L11.6294 4.37055Z","fill","#AEAEAE"],[1,"template-name-close-icon",3,"click"],[1,"d-flex","flex-column","col-12","p-0"],[1,"form-label"],[3,"isChip","placeholder","masterData","selectedValue","data","displayClose","onValueChange"],[3,"ngTemplateOutlet"],[3,"type","placeholder","masterData","selectedValues","data","onValueChange"],["appearance","outline",1,"form-field-class-wo-width"],["maxlength","255","type","text","matInput","","formControlName","subject",3,"placeholder","disabled"],["height","300px","formControlName","body",1,"dev-extreme-body-styles"],["bodyEditor",""],[3,"enabled"],[3,"multiline"],["name","size",3,"acceptedValues","options"],["name","font",3,"acceptedValues","options"],["name","bold"],["name","italic"],["name","strike"],["name","underline"],["name","alignLeft"],["name","alignCenter"],["name","alignRight"],["name","alignJustify"],["name","orderedList"],["name","bulletList"],["name","color"],["name","background"],["name","separator"],["name","link"],["name","image"],["name","insertTable"],["name","insertHeaderRow"],["name","insertRowAbove"],["name","insertRowBelow"],["name","insertColumnLeft"],["name","insertColumnRight"],["name","deleteColumn"],["name","deleteRow"],["name","deleteTable"],["name","cellProperties"],["name","tableProperties"],[1,"d-flex","align-items-center","placeholder-container"],[1,"text"],["cdkOverlayOrigin","",1,"click-to-open",3,"click","mouseenter"],["triggerPlaceholder","cdkOverlayOrigin","triggerPlaceholderField",""],[1,"d-flex","align-items-center","justify-content-between"],[1,"slide-toggle"],["formControlName","isSignatureOn"],["class","dev-extreme-sign-styles","height","150px","formControlName","signature",4,"ngIf"],["height","150px","formControlName","signature",1,"dev-extreme-sign-styles"],[1,"d-flex","flex-column","align-items-center","justify-content-center","attachment-plugin",3,"click"],[1,"attachment-text"],[3,"previewHTML","valueEmitter"],[3,"list","onConfirmation"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"],[2,"color","#cf0001"]],template:function(e,t){1&e&&(c["\u0275\u0275template"](0,Ne,35,45,"div",0),c["\u0275\u0275template"](1,Qe,2,1,"ng-container",1),c["\u0275\u0275template"](2,Ze,1,1,"ng-template",2,3,c["\u0275\u0275templateRefExtractor"]),c["\u0275\u0275template"](4,Xe,7,1,"div",0),c["\u0275\u0275template"](5,Ye,2,0,"ng-template",null,4,c["\u0275\u0275templateRefExtractor"])),2&e&&(c["\u0275\u0275property"]("ngIf",!t.loading&&!t.showPreview),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!t.loading&&t.showPreview),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerPlaceholder),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",t.loading))},directives:[i.NgIf,v.a,N.a,x.w,x.n,h.a,x.e,x.q,x.v,x.l,Q.a,i.NgTemplateOutlet,Z.a,X.c,Y.b,K.a,q.Ge,q.o,v.b,W.a,te,ne.a],pipes:[ie.a],styles:['.template-page[_ngcontent-%COMP%]{background-color:#f1f3f8;position:sticky;top:0}.template-page[_ngcontent-%COMP%]   .preview[_ngcontent-%COMP%]{color:#45546e;padding:6px 12px;border-radius:8px;border:1.5px solid #45546e}.template-page[_ngcontent-%COMP%]   .preview[_ngcontent-%COMP%], .template-page[_ngcontent-%COMP%]   .save-template[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-family:var(--atsfontFamily);font-weight:700;cursor:pointer;font-size:14px}.template-page[_ngcontent-%COMP%]   .save-template[_ngcontent-%COMP%]{color:#fff;padding:7px 13px;border-radius:8px;background-color:var(--atsprimaryColor)}.template-page[_ngcontent-%COMP%]   .create-preview[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-family:var(--atsfontFamily);font-weight:700;color:#45546e;cursor:pointer;font-size:13px;padding:4px 10px 4px 11px;border-radius:8px;border:1.5px solid}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;background-color:#fff;padding:10px 58px 10px 18px;border-bottom:1px solid #e8e9ee}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{width:16px;height:16px;border:1px solid #526179;border-radius:4px;cursor:pointer}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{font-size:14px;color:#526179}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .align-items-column[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:65%}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;color:#111434;line-height:20px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .template-edit[_ngcontent-%COMP%]{width:-webkit-fill-available}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .template-edit[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;outline:none;border:none;border-bottom:1px solid #6e7b8f}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .template-edit[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .template-edit[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#6e7b8f}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .template-name-close-icon[_ngcontent-%COMP%]{color:#aeaeae;width:16px;height:16px;font-size:16px;cursor:pointer}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:10px;font-weight:400;color:#8b95a5;line-height:16px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]{width:100%;padding:8px 24px 20px}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%]{border:1px solid #e8e9ee;border-radius:8px;padding:2px 12px;gap:8px}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%]   .stage-name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#5f6c81}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .rating-frame[_ngcontent-%COMP%]{border:1px solid #fa8c16;padding:4px 8px;border-radius:8px;width:60px;gap:10px}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .rating-frame[_ngcontent-%COMP%]   .dark-color[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#526179}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .rating-frame[_ngcontent-%COMP%]   .light-color[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#b9c0ca}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .svg[_ngcontent-%COMP%]{cursor:pointer;stroke:#1b2140}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]{display:flex;margin:0 24px;height:var(--dynamicSubHeight)}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]{width:15%;margin-right:2%;height:var(--dynamicSubHeight);border-radius:4px;padding:16px 0;background-color:#fff;overflow-y:scroll}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]   .section-highlight[_ngcontent-%COMP%]{background-color:var(--atssecondaryColor4)}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]   .align-section-center[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;height:32px;border-radius:4px;padding-left:10%;padding-right:10%;cursor:pointer;width:-webkit-fill-available}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]   .align-section-center[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background-color:#d9d9d9}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]   .align-section-center[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#6e7b8f;width:85%;padding-right:2%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#dadce2;height:15px;padding-left:10%;visibility:hidden}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:initial!important}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]::-webkit-scrollbar{width:0!important;height:0!important}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section-content[_ngcontent-%COMP%]{width:83%;height:var(--dynamicSubHeight);background-color:#fff;border-radius:4px;overflow-y:scroll}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:initial!important}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:0!important;height:0!important}.mail-template[_ngcontent-%COMP%]{padding:20px 50px;gap:30px;height:var(--dynamicSubHeight);overflow-y:scroll;overflow-x:hidden}.mail-template[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{width:16px!important;height:16px!important;transform:translate(50%,50%)}.mail-template[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:#dadce2;border-radius:15px!important;height:24px!important;width:40px!important}.mail-template[_ngcontent-%COMP%]     .mat-slide-toggle-thumb-container{top:-4px!important;left:-5px!important}.mail-template[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:var(--atssecondaryColor)}.mail-template[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#fff}.mail-template[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:var(--atssecondaryColor)!important}.mail-template[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{color:#5f6c81}.mail-template[_ngcontent-%COMP%]   .attachment-plugin[_ngcontent-%COMP%], .mail-template[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400}.mail-template[_ngcontent-%COMP%]   .attachment-plugin[_ngcontent-%COMP%]{height:60px;width:-webkit-fill-available;border:2px dashed #b9c0ca;border-radius:6px;color:#b9c0ca;cursor:pointer;text-align:center}.mail-template[_ngcontent-%COMP%]   .attachment-plugin[_ngcontent-%COMP%]   .attachment-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:13px;font-weight:700;color:#111434}.mail-template[_ngcontent-%COMP%]   .placeholder-container[_ngcontent-%COMP%]{width:-webkit-fill-available;background-color:#e8e9ee;border-radius:4px;border:1px solid #d2d2d2;gap:16px;padding:12px 16px}.mail-template[_ngcontent-%COMP%]   .placeholder-container[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#515965}.mail-template[_ngcontent-%COMP%]   .placeholder-container[_ngcontent-%COMP%]   .click-to-open[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#1b2140;cursor:pointer;border:1px solid #111434;border-radius:4px;padding:4px}.loading-img[_ngcontent-%COMP%]{height:var(--dynamicSubHeight);display:flex;flex-direction:column;align-items:center;justify-content:center;background-color:#fff}.loading-gradient[_ngcontent-%COMP%]{height:24px;width:100%;background:linear-gradient(90deg,#f1efef -24.18%,#f9f8f8 50.26%,#e7e5e5 114.84%);animation:gradientLoading 10s linear infinite;background-size:200% 100%;border-radius:8px}.loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}.preview-no-template[_ngcontent-%COMP%]{width:48%;height:65vh;background-color:#f1f3f8}.preview-no-template[_ngcontent-%COMP%]   .preview-blank-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:400;color:#6e7b8f}.preview-template[_ngcontent-%COMP%]{width:48%;background-color:#f1f3f8;padding:12px}.preview-template[_ngcontent-%COMP%]   .inner-template[_ngcontent-%COMP%]{background-color:#fff;overflow-x:auto;padding:8px}@keyframes gradientLoading{0%{background-position:200% 0}to{background-position:-200% 0}}.bg-container[_ngcontent-%COMP%]{height:var(--dynamicHeight);overflow:hidden}.preview[_ngcontent-%COMP%]{display:flex}']}),e})();function qe(e,t){1&e&&c["\u0275\u0275element"](0,"mat-divider",19),2&e&&c["\u0275\u0275property"]("vertical",!0)}const We=function(e){return{"section-highlight":e}},Je=function(e){return{"background-color":e}},$e=function(e){return{color:e}};function et(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275elementStart"](1,"div",13),c["\u0275\u0275elementStart"](2,"div",14),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const n=t.index;return c["\u0275\u0275nextContext"](3).switchToSelectedMenu(n)})),c["\u0275\u0275element"](3,"div",15),c["\u0275\u0275elementStart"](4,"div",16),c["\u0275\u0275text"](5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"div",17),c["\u0275\u0275template"](7,qe,1,1,"mat-divider",18),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=c["\u0275\u0275nextContext"](3);c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngClass",c["\u0275\u0275pureFunction1"](6,We,null==e?null:e.isSelected)),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngStyle",c["\u0275\u0275pureFunction1"](8,Je,null!=e&&e.isSelected?"#52C41A":"")),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("matTooltip",e.name)("ngStyle",c["\u0275\u0275pureFunction1"](10,$e,null!=e&&e.isSelected?"#111434":"")),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.name," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",n!=i.sideNave.length-1)}}function tt(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"app-email-template",21),c["\u0275\u0275listener"]("valueEmitter",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](4).handleValueFromChild(t)})),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]().$implicit,t=c["\u0275\u0275nextContext"](3);c["\u0275\u0275property"]("title",e.name)("uiTextConfig",t.uiTextConfig)}}function nt(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275template"](1,tt,1,2,"app-email-template",20),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",1==e.id&&e.isSelected)}}function it(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275elementStart"](1,"div",9),c["\u0275\u0275elementStart"](2,"div",10),c["\u0275\u0275template"](3,et,8,12,"ng-container",11),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",12),c["\u0275\u0275template"](5,nt,2,1,"ng-container",11),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2);c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("ngForOf",e.sectionList),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngForOf",e.sectionList)}}function at(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",22),c["\u0275\u0275element"](1,"app-access-denied"),c["\u0275\u0275elementEnd"]())}const rt=function(e,t){return[e,t,0,0,"V"]};function ot(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",2),c["\u0275\u0275elementStart"](1,"div",3),c["\u0275\u0275elementStart"](2,"div",4),c["\u0275\u0275elementStart"](3,"div",5),c["\u0275\u0275text"](4,"Template Settings"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"div",6),c["\u0275\u0275text"](6,"Set up hiring workflows and use them on jobs."),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"div"),c["\u0275\u0275element"](8,"img",7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](9,it,6,2,"ng-container",1),c["\u0275\u0275pipe"](10,"access"),c["\u0275\u0275template"](11,at,2,0,"div",8),c["\u0275\u0275pipe"](12,"access"),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](9),c["\u0275\u0275property"]("ngIf",c["\u0275\u0275pipeBindV"](10,2,c["\u0275\u0275pureFunction2"](14,rt,e.access.moduleId.settings,e.access.subModuleId.templateSettings))),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",!c["\u0275\u0275pipeBindV"](12,8,c["\u0275\u0275pureFunction2"](17,rt,e.access.moduleId.settings,e.access.subModuleId.templateSettings)))}}function lt(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"app-create-template",23),c["\u0275\u0275listener"]("createdValue",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().handleValueFromCreate(t)}))("previewEmit",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().handlePreview(t)})),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("templateBasicData",e.templateValues)("mode",e.mode)("uiTextConfig",e.uiTextConfig)}}const st=[{path:"",component:(()=>{class e{constructor(e,t,n,i){this._atsMasterService=e,this._toaster=t,this._dialog=n,this._atsTemplateSettingsService=i,this._onDestroy=new l.b,this.configKey="templateSettingsConfiguation",this.access=s,this.sideNave=[],this.templateCategory="",this.sectionList=[],this.templateValues={},this.mode="",this.uiTextConfig={}}onResize(){this.calculateDynamicContentHeight()}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.calculateDynamicContentHeight(),this.getAtsMasterUiConfig(this.configKey),yield this.fetchAllTemplateTypes(),this.switchToSelectedMenu(0)}))}calculateDynamicContentHeight(){this.dynamicHeight=window.innerHeight-57-2+"px",document.documentElement.style.setProperty("--dynamicHeight",this.dynamicHeight),this.dynamicContentHeight=window.innerHeight-57-104+"px",document.documentElement.style.setProperty("--dynamicContentHeight",this.dynamicContentHeight),this.dynamicSubHeight=window.innerHeight-57-75+"px",document.documentElement.style.setProperty("--dynamicSubHeight",this.dynamicSubHeight),document.documentElement.style.setProperty("--dynamicAccessDeniedHeight",this.dynamicSubHeight)}getAtsMasterUiConfig(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsMasterUiConfig(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.uiTextConfig=e.data.uiTextConfig:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}fetchAllTemplateTypes(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsTemplateSettingsService.fetchAllTemplateTypes().pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.sectionList=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),t()}}))}))}handleValueFromChild(e){this.templateCategory=e.key||"",this.templateValues=(null==e?void 0:e.data)||{},this.mode=(null==e?void 0:e.mode)||"edit"}handleValueFromCreate(e){this.ngOnInit(),this.templateCategory="",this.templateValues=e.data}switchToSelectedMenu(e){return Object(r.c)(this,void 0,void 0,(function*(){this.sectionList.forEach(e=>e.isSelected=!1),this.sectionList[e].isSelected=!0}))}handlePreview(e){this.previewHtml=e.html||"",this.templateCategory=e.key||""}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](p.a),c["\u0275\u0275directiveInject"](d.a),c["\u0275\u0275directiveInject"](m.b),c["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],hostBindings:function(e,t){1&e&&c["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,c["\u0275\u0275resolveWindow"])},decls:2,vars:2,consts:[["class","d-flex flex-column template-page",4,"ngIf"],[4,"ngIf"],[1,"d-flex","flex-column","template-page"],[1,"header"],[1,"align-items-column"],[1,"title-text"],[1,"sub-text"],["src","https://assets.kebs.app/ats-kebs-header-logo.png"],["style","margin: 0px 24px",4,"ngIf"],[1,"template-detail-view-content"],[1,"template-detail-view-content-section"],[4,"ngFor","ngForOf"],[1,"template-detail-view-content-section-content"],[1,"d-flex","flex-column","align-items-start"],[1,"align-section-center",3,"ngClass","click"],[1,"circle",3,"ngStyle"],[1,"section-text",3,"matTooltip","ngStyle"],[2,"padding-left","11.5%"],["class","divider",3,"vertical",4,"ngIf"],[1,"divider",3,"vertical"],[3,"title","uiTextConfig","valueEmitter",4,"ngIf"],[3,"title","uiTextConfig","valueEmitter"],[2,"margin","0px 24px"],[3,"templateBasicData","mode","uiTextConfig","createdValue","previewEmit"]],template:function(e,t){1&e&&(c["\u0275\u0275template"](0,ot,13,20,"div",0),c["\u0275\u0275template"](1,lt,2,3,"div",1)),2&e&&(c["\u0275\u0275property"]("ngIf",""==t.templateCategory),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","emailTemplate"==t.templateCategory))},directives:[i.NgIf,i.NgForOf,i.NgClass,i.NgStyle,h.a,u.a,z,R.a,Ke],pipes:[_.a],styles:[".template-page[_ngcontent-%COMP%]{background-color:#f1f3f8;height:var(--dynamicHeight)}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;height:48px;background-color:#fff;padding-left:16px;margin-bottom:16px;border-bottom:1px solid #e8e9ee}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .align-items-column[_ngcontent-%COMP%]{display:flex;flex-direction:column}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;color:#111434;line-height:20px}.template-page[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:10px;font-weight:400;color:#8b95a5;line-height:16px}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]{width:100%;padding:8px 24px 20px}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{width:16px;height:16px;border:1px solid #526179;border-radius:4px;cursor:pointer}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{font-size:14px;color:#526179}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%]{border:1px solid #e8e9ee;border-radius:8px;padding:2px 12px;gap:8px}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%]   .stage-name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#5f6c81}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .rating-frame[_ngcontent-%COMP%]{border:1px solid #fa8c16;padding:4px 8px;border-radius:8px;width:60px;gap:10px}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .rating-frame[_ngcontent-%COMP%]   .dark-color[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#526179}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .rating-frame[_ngcontent-%COMP%]   .light-color[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#b9c0ca}.template-page[_ngcontent-%COMP%]   .candidate-detail-view-header[_ngcontent-%COMP%]   .svg[_ngcontent-%COMP%]{cursor:pointer;stroke:#1b2140}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]{display:flex;margin:0 24px;height:var(--dynamicSubHeight)}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]{width:15%;margin-right:2%;height:var(--dynamicSubHeight);border-radius:4px;padding:16px 0;background-color:#fff;overflow-y:scroll}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]   .section-highlight[_ngcontent-%COMP%]{background-color:var(--atssecondaryColor4)}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]   .align-section-center[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;height:32px;border-radius:4px;padding-left:10%;padding-right:10%;cursor:pointer;width:-webkit-fill-available}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]   .align-section-center[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background-color:#d9d9d9}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]   .align-section-center[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#6e7b8f;width:85%;padding-right:2%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#dadce2;height:15px;padding-left:10%;visibility:hidden}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:initial!important}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section[_ngcontent-%COMP%]::-webkit-scrollbar{width:0!important;height:0!important}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section-content[_ngcontent-%COMP%]{width:83%;height:var(--dynamicSubHeight);background-color:#fff;border-radius:4px;overflow-y:hidden}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:initial!important}.template-page[_ngcontent-%COMP%]   .template-detail-view-content[_ngcontent-%COMP%]   .template-detail-view-content-section-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:0!important;height:0!important}"]}),e})()},{path:"email",component:z}];let ct=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(st)],a.k]}),e})();var pt=n("1+mW"),dt=n("Xi0T"),mt=n("dlKe"),gt=n("bSwM"),ht=n("iadO"),ut=n("FKr1"),ft=n("QibW"),Ct=n("d3UM"),vt=n("Xa2L"),xt=n("5+WD"),yt=n("vxfF"),_t=n("lVl8");let Mt=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,ct,N.b,h.b,u.b,X.e,gt.b,Y.c,x.p,x.E,ht.h,ut.n,W.b,ft.c,Ct.d,K.b,xt.g,yt.g,v.h,vt.b,mt.b,_t.b,dt.a,pt.ApplicantTrackingSystemModule]]}),e})()},UVjm:function(e,t,n){"use strict";n.d(t,"a",(function(){return P}));var i=n("+rOU"),a=n("fXoL"),r=n("rDax"),o=n("ofXK"),l=n("NFeN"),s=n("Qu3c"),c=n("3Pt+"),p=n("dlKe"),d=n("/rGH");const m=["inputField"],g=["width"],h=["overlayTemplateRef"];function u(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"span",6),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.placeholder)}}function f(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",7),a["\u0275\u0275elementStart"](1,"mat-icon",7),a["\u0275\u0275text"](2,"expand_more"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}function C(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",13),a["\u0275\u0275elementStart"](1,"div",14),a["\u0275\u0275pipe"](2,"masterData"),a["\u0275\u0275text"](3),a["\u0275\u0275pipe"](4,"masterData"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matTooltip",a["\u0275\u0275pipeBind2"](2,2,e.fieldControl,e.masterData)),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",a["\u0275\u0275pipeBind2"](4,5,e.fieldControl,e.masterData)," ")}}function v(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",14),a["\u0275\u0275pipe"](1,"masterData"),a["\u0275\u0275text"](2),a["\u0275\u0275pipe"](3,"masterData"),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("matTooltip",a["\u0275\u0275pipeBind2"](1,2,e.fieldControl,e.masterData)),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",a["\u0275\u0275pipeBind2"](3,5,e.fieldControl,e.masterData)," ")}}function x(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",15),a["\u0275\u0275listener"]("click",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](2).clearAll(t)})),a["\u0275\u0275elementStart"](1,"mat-icon",7),a["\u0275\u0275text"](2,"close"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}function y(e,t){if(1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",8),a["\u0275\u0275template"](2,C,5,8,"div",9),a["\u0275\u0275template"](3,v,4,8,"div",10),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",11),a["\u0275\u0275template"](5,x,3,0,"div",12),a["\u0275\u0275elementStart"](6,"div",7),a["\u0275\u0275elementStart"](7,"mat-icon",7),a["\u0275\u0275text"](8,"expand_more"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",e.isChip),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!e.isChip),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",e.fieldControl&&e.displayClose)}}function _(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",26),a["\u0275\u0275namespaceSVG"](),a["\u0275\u0275elementStart"](1,"svg",27),a["\u0275\u0275elementStart"](2,"mask",28),a["\u0275\u0275element"](3,"rect",29),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"g",30),a["\u0275\u0275element"](5,"path",31),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}function M(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",23),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.$implicit;return a["\u0275\u0275nextContext"](3).onChangeInValue(n.id)})),a["\u0275\u0275elementStart"](2,"div",24),a["\u0275\u0275text"](3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](4,_,6,0,"div",25),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](2),a["\u0275\u0275classMap"](e.id==n.fieldControl?"checked-list":"list"),a["\u0275\u0275property"]("matTooltip",e.name),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.name," "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.id==n.fieldControl)}}function w(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",21),a["\u0275\u0275listener"]("scrolled",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](2).loadMoreData()})),a["\u0275\u0275template"](2,M,5,5,"ng-container",22),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("infiniteScrollDistance",3)("infiniteScrollThrottle",100)("scrollWindow",!1),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.filteredData)}}function O(e,t){1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"span",32),a["\u0275\u0275text"](2,"No Results Found!"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]())}const S=function(e){return{width:e}};function b(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",16),a["\u0275\u0275elementStart"](1,"div",17),a["\u0275\u0275elementStart"](2,"input",18,19),a["\u0275\u0275listener"]("ngModelChange",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().searchText=t}))("ngModelChange",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().onSearch()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](4,"div",20),a["\u0275\u0275template"](5,w,3,4,"ng-container",2),a["\u0275\u0275template"](6,O,3,0,"ng-container",2),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("ngStyle",a["\u0275\u0275pureFunction1"](4,S,e.overlayWidth)),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngModel",e.searchText),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",e.filteredData.length>0),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==e.filteredData.length)}}let P=(()=>{class e{constructor(e,t){this._overlay=e,this._viewContainerRef=t,this.placeholder="Select",this.isChip=!1,this.disabled=!1,this.executeMasterOnChanges=!0,this.height="40px",this.widthHorizontal="100%",this.marginTop=null,this.patchSelectedValue=!0,this.onValueChange=new a.EventEmitter,this.searchText="",this.overlayWidth="0px",this.currentMasterData=[],this.filteredData=[],this.hasMoreData=!1,this.currentPage=1,this.pageSize=15,this.searchQuery="",this.fieldControl=null}ngOnInit(){this.currentMasterData=[...this.masterData],this.filteredData=this.currentMasterData.slice(0,this.pageSize),this.hasMoreData=this.currentMasterData.length>this.pageSize,this.selectedValue&&this.patchSelectedValue&&(this.fieldControl=this.selectedValue)}ngOnChanges(e){e.masterData&&(this.currentMasterData=[...this.masterData],this.executeMasterOnChanges&&(this.currentMasterData.some(e=>e.id==this.selectedValue)||this.onValueChange.emit({data:this.data,val:null}))),e.selectedValue&&(this.currentMasterData=[...this.masterData],this.fieldControl=this.selectedValue&&this.patchSelectedValue?this.selectedValue:null)}onChangeInValue(e){this.patchSelectedValue&&(this.fieldControl=e),this.onValueChange.emit({data:this.data,val:e}),this.closeOverlay()}setValues(){this.searchText="",this.searchQuery="",this.currentPage=1,this.filteredData=[],this.hasMoreData=!1,this.loadMoreData()}onSearch(){this.searchQuery=this.searchText.toLowerCase(),this.currentPage=1,this.filteredData=[],this.hasMoreData=!1,this.loadMoreData()}loadMoreData(){const e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;let n=this.currentMasterData;this.searchQuery&&(n=n.filter(e=>e.name.toLowerCase().includes(this.searchQuery))),this.filteredData=this.filteredData.concat(n.slice(e,t)),this.hasMoreData=n.length>t,this.currentPage++}clearAll(e){e.stopPropagation(),this.fieldControl=null,this.onValueChange.emit({data:this.data,val:null})}openOverlay(e){var t,n;if(!(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const t=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(0).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"top"}]),a=this._overlay.scrollStrategies.close();t.withDefaultOffsetY(-parseInt(this.height)),this.overlayRef=this._overlay.create({positionStrategy:t,scrollStrategy:a,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const r=new i.h(this.overlayTemplateRef,this._viewContainerRef);this.overlayRef.attach(r),this.setValues(),setTimeout(()=>{this.inputField&&this.inputField.nativeElement&&this.inputField.nativeElement.focus()},200),this.overlayWidth=this.width&&(null===(n=this.width)||void 0===n?void 0:n.nativeElement)?this.width.nativeElement.offsetWidth+"px":"0px",this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](r.e),a["\u0275\u0275directiveInject"](a.ViewContainerRef))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-single-select-chip"]],viewQuery:function(e,t){if(1&e&&(a["\u0275\u0275viewQuery"](m,!0),a["\u0275\u0275viewQuery"](g,!0),a["\u0275\u0275viewQuery"](h,!0)),2&e){let e;a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.inputField=e.first),a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.width=e.first),a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.overlayTemplateRef=e.first)}},inputs:{masterData:"masterData",selectedValue:"selectedValue",data:"data",placeholder:"placeholder",displayClose:"displayClose",isChip:"isChip",disabled:"disabled",executeMasterOnChanges:"executeMasterOnChanges",height:"height",widthHorizontal:"widthHorizontal",marginTop:"marginTop",patchSelectedValue:"patchSelectedValue"},outputs:{onValueChange:"onValueChange"},features:[a["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:12,consts:[["cdkOverlayOrigin","",1,"main-container",3,"click"],["width","","triggerOverlay","cdkOverlayOrigin","triggerOverlayField",""],[4,"ngIf"],["class","icon",4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["overlayTemplateRef",""],[1,"search-text"],[1,"icon"],[1,"d-flex","align-items-center",2,"width","90%"],["class","d-flex align-items-center chip",4,"ngIf"],["class","chip-text",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center",2,"gap","8px"],["class","icon",3,"click",4,"ngIf"],[1,"d-flex","align-items-center","chip"],[1,"chip-text",3,"matTooltip"],[1,"icon",3,"click"],[1,"overlay-container",3,"ngStyle"],[1,"search-bar"],["type","text","placeholder","Search...",3,"ngModel","ngModelChange"],["inputField",""],[1,"divider"],["infinite-scroll","",1,"d-flex","flex-column","list-view",3,"infiniteScrollDistance","infiniteScrollThrottle","scrollWindow","scrolled"],[4,"ngFor","ngForOf"],[1,"d-flex","align-items-center","justify-content-between",2,"cursor","pointer",3,"click"],[3,"matTooltip"],["class","svg",4,"ngIf"],[1,"svg"],["width","18","height","18","viewBox","0 0 18 18"],["id","mask0_9363_260624","maskUnits","userSpaceOnUse","x","0","y","0","width","18","height","18",2,"mask-type","alpha"],["width","18","height","18"],["mask","url(#mask0_9363_260624)"],["d","M7.49961 10.1996L11.9246 5.77461C12.0621 5.63711 12.2371 5.56836 12.4496 5.56836C12.6621 5.56836 12.8371 5.63711 12.9746 5.77461C13.1121 5.91211 13.1809 6.08711 13.1809 6.29961C13.1809 6.51211 13.1121 6.68711 12.9746 6.82461L8.02461 11.7746C7.87461 11.9246 7.69961 11.9996 7.49961 11.9996C7.29961 11.9996 7.12461 11.9246 6.97461 11.7746L5.02461 9.82461C4.88711 9.68711 4.81836 9.51211 4.81836 9.29961C4.81836 9.08711 4.88711 8.91211 5.02461 8.77461C5.16211 8.63711 5.33711 8.56836 5.54961 8.56836C5.76211 8.56836 5.93711 8.63711 6.07461 8.77461L7.49961 10.1996Z"],[1,"no-result-text"]],template:function(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",0,1),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=a["\u0275\u0275reference"](3);return t.openOverlay(n)})),a["\u0275\u0275template"](4,u,3,1,"div",2),a["\u0275\u0275template"](5,f,3,0,"div",3),a["\u0275\u0275template"](6,y,9,3,"ng-container",2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](7,b,7,6,"ng-template",4,5,a["\u0275\u0275templateRefExtractor"])}if(2&e){const e=a["\u0275\u0275reference"](2);a["\u0275\u0275styleProp"]("pointer-events",t.disabled?"none":"")("height",t.height)("width",t.widthHorizontal)("margin-top",t.marginTop),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngIf",!t.fieldControl),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!t.fieldControl),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.fieldControl),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e)}},directives:[r.b,o.NgIf,r.a,l.a,s.a,o.NgStyle,c.e,c.v,c.y,p.a,o.NgForOf],pipes:[d.a],styles:[".main-container[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;width:-webkit-fill-available;height:40px;border-radius:4px;border:1px solid #d2d2d2;padding:4px 8px;overflow:hidden;white-space:nowrap;cursor:pointer}.main-container[_ngcontent-%COMP%]   .search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:rgba(0,0,0,.42)}.main-container[_ngcontent-%COMP%]   .chip[_ngcontent-%COMP%]{padding:3px 7px;border:1px solid #94a2ab;border-radius:3px;gap:4px}.main-container[_ngcontent-%COMP%]   .chip-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#526179;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%}.main-container[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#5f6c81;cursor:pointer}.overlay-container[_ngcontent-%COMP%]{height:160px;background-color:#fff;border-radius:8px;border:1px solid #dadce2;padding:8px}.overlay-container[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background, .overlay-container[_ngcontent-%COMP%]     .mat-checkbox-indeterminate .mat-checkbox-background{background-color:var(--atssecondaryColor)!important}.overlay-container[_ngcontent-%COMP%]     .mat-checkbox-inner-container{width:14px!important;height:14px!important}.overlay-container[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#b9c0ca!important}.overlay-container[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]{width:14px;height:14px;margin:0 8px 0 0}.overlay-container[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{height:1px;width:100%;background:#e8e9ee;margin-bottom:8px;margin-top:8px}.overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;outline:none;border:none}.overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.overlay-container[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]{overflow-y:auto;height:110px;gap:8px}.overlay-container[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important}.overlay-container[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important}.overlay-container[_ngcontent-%COMP%]   .checked-list[_ngcontent-%COMP%]{color:var(--atsprimaryColor)}.overlay-container[_ngcontent-%COMP%]   .checked-list[_ngcontent-%COMP%], .overlay-container[_ngcontent-%COMP%]   .list[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.overlay-container[_ngcontent-%COMP%]   .list[_ngcontent-%COMP%]{color:#5f6c81}.overlay-container[_ngcontent-%COMP%]   .svg[_ngcontent-%COMP%]{fill:var(--atsprimaryColor)}.overlay-container[_ngcontent-%COMP%]   .no-result-text[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f}"]}),e})()},XQl4:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("fXoL"),a=n("tk/3");let r=(()=>{class e{constructor(e){this._http=e}createTemplate(e){return this._http.post("api/ats/candidate/createTemplate",{templateData:e})}updateTemplate(e){return this._http.post("api/ats/candidate/updateTemplate",{templateData:e})}deleteTemplate(e){return this._http.post("api/ats/candidate/deleteTemplates",{templateIds:[e]})}fetchTemplate(e){return this._http.post("api/ats/candidate/fetchTemplate",{templateId:e})}fetchAllTemplates(e){return this._http.post("api/ats/candidate/fetchAllTemplates",{searchParams:e})}getAllCollegeDetails(e,t){return this._http.post("api/ats/settings/getAllCollegeDetails",{sort:e,searchParams:t})}getCollegeCount(){return this._http.post("api/ats/settings/getCollegeCount",{})}getCollegeDetailsByCollegeId(e){return this._http.post("api/ats/settings/getCollegeDetailsByCollegeId",{collegeId:e})}createCollege(e){return this._http.post("api/ats/settings/createCollege",{collegeBasicDetails:e})}updateImage(e){return this._http.post("api/ats/settings/updateImage",e)}getCollegeMasterData(){return this._http.post("api/ats/settings/getCollegeMasterData",{})}updateCollegeDetail(e,t,n){return this._http.post("api/ats/settings/updateCollegeDetail",{collegeDetail:e,status:t,image:n})}getCollegeEligibilityDetails(e){return this._http.post("api/ats/settings/getCollegeEligibilityDetails",{collegeId:e})}updateAttachment(e){return this._http.post("api/ats/settings/updateAttachment",e)}updateEligibilityDetails(e){return this._http.post("api/ats/settings/updateEligibilityDetails",{eligibilityDetails:e})}getDegreeMasterForCollege(){return this._http.post("api/ats/settings/getDegreeMasterForCollege",{})}getCourseMasterForCollege(){return this._http.post("api/ats/settings/getCourseMasterForCollege",{})}getStageCategory(){return this._http.post("api/ats/masterService/getStageCategory",{})}getTemplateModuleType(){return this._http.post("api/ats/masterService/getTemplateModuleType",{})}fetchCollegeUserDetails(e){return this._http.post("api/ats/settings/fetchCollegeUserDetails",{collegeId:e})}assignUserToCollege(e,t){return this._http.post("api/ats/settings/assignUserToCollege",{collegeId:e,users:t})}fetchCollegeUserDetailById(e){return this._http.post("api/ats/settings/fetchCollegeUserDetailById",{userId:e})}updateCollegeUserDetails(e,t,n){return this._http.post("api/ats/settings/updateCollegeUserDetails",{collegeId:e,userId:t,user:n})}deleteUserById(e){return this._http.post("api/ats/settings/deleteUserById",{userId:e})}generatePassword(){return this._http.post("api/ats/settings/generatePassword",{})}getAllUserDetails(e,t){return this._http.post("api/ats/settings/getAllUserDetails",{sort:e,searchParams:t})}getUserCount(){return this._http.post("api/ats/settings/getUserCount",{})}getUserMasterData(){return this._http.post("api/ats/settings/getUserMasterData",{})}createUser(e,t,n){return this._http.post("api/ats/settings/createUser",{createUserValue:e,hashedPassword:t,domain:n})}getUserDetailsByUserId(e){return this._http.post("api/ats/settings/getUserDetailsByUserId",{userId:e})}updateUserDetail(e,t,n,i){return this._http.post("api/ats/settings/updateUserDetail",{UserDetail:e,status:t,image:n,UserId:i})}getAllVendorDetails(e,t){return this._http.post("api/ats/settings/getAllVendorDetails",{sort:e,searchParams:t})}createVendor(e){return this._http.post("api/ats/settings/createVendor",{vendor_basic_details:e})}getVendorById(e){return this._http.post("api/ats/settings/getVendorById",{vendor_id:e})}updateVendorDetail(e,t,n){return this._http.post("api/ats/settings/updateVendorDetail",{vendorDetailValue:e,image:t,status:n})}fetchVendorUserDetails(e){return this._http.post("api/ats/settings/fetchVendorUserDetails",{vendorId:e})}assignUserToVendor(e,t,n){return this._http.post("api/ats/settings/assignUserToVendor",{vendorId:e,users:t,domain:n})}updateVendorUserDetails(e,t,n){return this._http.post("api/ats/settings/updateVendorUserDetails",{vendorId:e,userId:t,user:n})}fetchVendorUserDetailById(e){return this._http.post("api/ats/settings/fetchVendorUserDetailById",{userId:e})}deleteVendorUserById(e,t){return this._http.post("api/ats/settings/deleteVendorUserById",{userId:e,vendorId:t})}getProfileById(){return this._http.post("api/ats/settings/getProfileById",{})}updateProfileDetail(e,t,n){return this._http.post("api/ats/settings/updateProfileDetail",{generalDetailValue:e,image:t,status:n})}integrateOutlook(e,t){return this._http.post("api/ats/settings/integrateOutlook",{code:e,redirectUri:t})}integrateGoogle(e,t){return this._http.post("api/ats/settings/integrateGoogle",{code:e,redirectUri:t})}connectOutlookCalendar(e,t){return this._http.post("api/ats/settings/connectOutlookCalendar",{code:e,redirectUri:t})}getOrganizationDetail(){return this._http.post("api/ats/settings/getOrganizationDetail",{})}updateOrganizationDetail(e,t){return this._http.post("api/ats/settings/updateOrganizationDetail",{companyDetailValue:e,image:t})}getUserCalendarDetail(){return this._http.post("api/ats/settings/getUserCalendarDetail",{})}getOrganizationDetailsForPreview(){return this._http.post("api/ats/settings/getOrganizationDetailsForPreview",{})}createDegreeDetails(e){return this._http.post("api/ats/settings/createDegreeDetails",{degreeDetails:e})}updateDegreeDetails(e){return this._http.post("api/ats/settings/updateDegreeDetails",{degreeDetails:e})}createCourseDetails(e){return this._http.post("api/ats/settings/createCourseDetails",{courseDetails:e})}updateCourseDetails(e){return this._http.post("api/ats/settings/updateCourseDetails",{courseDetails:e})}createCourseDegreeMappingDetails(e){return this._http.post("api/ats/settings/createCourseDegreeMappingDetails",{insertionValues:e})}updateMappingDetails(e){return this._http.post("api/ats/settings/updateMappingDetails",{updateValues:e})}createStateDetails(e){return this._http.post("api/ats/settings/createStateDetails",{stateDetails:e})}updateStateDetails(e){return this._http.post("api/ats/settings/updateStateDetails",{stateDetails:e})}createCountryDetails(e){return this._http.post("api/ats/settings/createCountryDetails",{countryDetails:e})}updateCountryDetails(e){return this._http.post("api/ats/settings/updateCountryDetails",{countryDetails:e})}createRegionDetails(e){return this._http.post("api/ats/settings/createRegionDetails",{regionDetails:e})}updateRegionDetails(e){return this._http.post("api/ats/settings/updateRegionDetails",{regionDetails:e})}createCityDetails(e){return this._http.post("api/ats/settings/createCityDetails",{cityDetails:e})}updateCityDetails(e){return this._http.post("api/ats/settings/updateCityDetails",{cityDetails:e})}getDegreeCourseMasterForCollege(){return this._http.post("api/ats/settings/getDegreeCourseMasterForCollege",{})}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275inject"](a.c))},e.\u0275prov=i["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},pEYl:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("yuIm"),a=n("fXoL");let r=(()=>{class e{transform(e=0,t=0,n=0,a=0,r=""){if(!r||""==r)return!1;let o={module_id:e,sub_module_id:t,section_id:n,sub_section_id:a};"V"==r&&(o.view_permission=1),"C"==r&&(o.create_permission=1),"E"==r&&(o.edit_permission=1),"DE"==r&&(o.delete_permission=1),"DO"==r&&(o.download_permission=1),"U"==r&&(o.upload_permission=1),"B"==r&&(o.bulk_operation=1);const l=Object.keys(o);return i.roleAccessList.find(e=>l.every(t=>e[t]===o[t]))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=a["\u0275\u0275definePipe"]({name:"access",type:e,pure:!0}),e})()},pPzn:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("fXoL");let a=(()=>{class e{transform(e,t,n){var i;return!!(e&&t&&n)&&(null===(i=e.find(e=>e.key==t))||void 0===i?void 0:i[n])}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"columnCustomization",type:e,pure:!0}),e})()},rQiX:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("fXoL"),a=n("tk/3");let r=(()=>{class e{constructor(e){this._http=e}getAtsMasterUiConfig(e){return this._http.post("api/ats/masterService/getAtsMasterUiConfig",{configKey:e})}getAtsFormsConfig(e){return this._http.post("api/ats/masterService/getAtsFormsConfig",{formKey:e})}getAtsFormsConfigForCandidateBasedOnJob(e){return this._http.post("api/ats/masterService/getAtsFormsConfigForCandidateBasedOnJob",{jobId:e})}getToken(e){return this._http.post("api/ats/masterService/getToken",{db:e})}getDBdetails(e){return this._http.post("api/ats/masterService/getDBdetails",{hostName:e})}refreshToken(e){return this._http.post("api/ats/masterService/refreshToken",{tokenDetails:e})}getAtsHiringMasterData(e){return this._http.post("api/ats/masterService/getAtsHiringMasterData",{jobId:e})}getPreviewMasterData(e){return this._http.post("api/ats/masterService/getPreviewMasterData",{key:e})}getEntity(){return this._http.post("api/ats/masterService/getEntity",{})}getDivision(e){return this._http.post("api/ats/masterService/getDivision",{entityId:e})}getSubDivision(e,t){return this._http.post("api/ats/masterService/getSubDivision",{entityId:e,divisionId:t})}getDepartment(){return this._http.post("api/ats/masterService/getDepartment",{})}getFrequency(){return this._http.post("api/ats/masterService/getFrequency",{})}getCurrency(){return this._http.post("api/ats/masterService/getCurrency",{})}getEmploymentType(){return this._http.post("api/ats/masterService/getEmploymentType",{})}getWorkLocation(){return this._http.post("api/ats/masterService/getWorkLocation",{})}getExperience(){return this._http.post("api/ats/masterService/getExperience",{})}getCountry(){return this._http.post("api/ats/masterService/getCountry",{})}getAtsEmployee(){return this._http.post("api/ats/masterService/getAtsEmployee",{})}getSkill(){return this._http.post("api/ats/masterService/getSkill",{})}getJobStatus(){return this._http.post("api/ats/masterService/getJobStatus",{})}getStageActionType(){return this._http.post("api/ats/masterService/getStageActionType",{})}getStageTransitionType(){return this._http.post("api/ats/masterService/getStageTransitionType",{})}getReminderFrequency(){return this._http.post("api/ats/masterService/getReminderFrequency",{})}getReceiverEmail(){return this._http.post("api/ats/masterService/getReceiverEmail",{})}getSenderEmail(){return this._http.post("api/ats/masterService/getSenderEmail",{})}getPlaceHolders(){return this._http.post("api/ats/masterService/getPlaceHolders",{})}getAttachmentConfig(e){return this._http.post("api/ats/masterService/getAttachmentConfig",{module_id:e})}getHiringFlowStatusByStageId(e){return this._http.post("api/ats/masterService/getHiringFlowStatusByStageId",{stageId:e})}fetchAllTemplates(e){return this._http.post("api/ats/candidate/fetchAllTemplates",{templateCategory:e})}getAtsCustomQuestionType(){return this._http.post("api/ats/masterService/getAtsCustomQuestionType",{})}getAllRole(){return this._http.post("api/ats/profile/getAllRole",{})}getAssociateDetailsInSystem(){return this._http.post("api/ats/profile/getAssociateDetailsInSystem",{})}getShiftTiming(){return this._http.post("api/ats/masterService/getShiftTiming",{})}getGenderDetails(){return this._http.post("api/ats/masterService/getGenderDetails",{})}getState(e){return this._http.post("api/ats/masterService/getState",{countryId:e})}getCity(e){return this._http.post("api/ats/masterService/getCity",{stateId:e})}getCandidateApplyType(){return this._http.post("api/ats/masterService/getCandidateApplyType",{})}getNotificationMode(){return this._http.post("api/ats/masterService/getNotificationMode",{})}getScorecardReminderType(){return this._http.post("api/ats/masterService/getScorecardReminderType",{})}getReminderTiming(){return this._http.post("api/ats/masterService/getReminderTiming",{})}getReminderStopConditions(){return this._http.post("api/ats/masterService/getReminderStopConditions",{})}getStageTransitionFrequency(){return this._http.post("api/ats/masterService/getStageTransitionFrequency",{})}getIntershipDuration(){return this._http.post("api/ats/masterService/getIntershipDuration",{})}getDesignation(){return this._http.post("api/ats/masterService/getDesignation",{})}getStageStatus(){return this._http.post("api/ats/masterService/getStageStatus",{})}getJobHiringStageBasedOnCategory(e,t){return this._http.post("api/ats/masterService/getJobHiringStageBasedOnCategory",{jobId:e,hiringStageCategory:t})}getInterviewPlatform(){return this._http.post("api/ats/masterService/getInterviewPlatform",{})}getScheduleType(){return this._http.post("api/ats/masterService/getScheduleType",{})}getTimezone(){return this._http.post("api/ats/masterService/getTimezone",{})}getInterviewMode(){return this._http.post("api/ats/masterService/getInterviewMode",{})}getCommentsVisibilityTypeMasterData(){return this._http.post("api/ats/masterService/getCommentsVisibilityTypeMasterData",{})}getAllCollegeDetails(){return this._http.post("api/ats/masterService/getAllCollegeDetails",{})}getDegreeList(){return this._http.post("api/ats/masterService/getDegreeList",{})}getCourseList(){return this._http.post("api/ats/masterService/getCourseList",{})}getEducationLevelList(){return this._http.post("api/ats/masterService/getEducationLevelList",{})}getScorecardInterviewStatus(e,t){return this._http.post("api/ats/masterService/getScorecardInterviewStatus",{jobID:e,currentUserAID:t})}getNationality(){return this._http.post("api/ats/masterService/getNationality",{})}getTemplateModuleType(){return this._http.post("api/ats/masterService/getTemplateModuleType",{})}getCustomQuestionsFormSections(e){return this._http.post("api/ats/masterService/getCustomQuestionsFormSections",{jobId:e})}getAtsVendorType(){return this._http.post("api/ats/masterService/getAtsVendorType",{})}getVendorStatusList(){return this._http.post("api/ats/masterService/getVendorStatusList",{})}getPhoneCode(){return this._http.post("api/ats/masterService/getPhoneCode",{})}getProfileStatusList(){return this._http.post("api/ats/masterService/getProfileStatusList",{})}getAtsMasterTheme(){return this._http.post("api/ats/masterService/getAtsMasterTheme",{})}getAtsMasterMailPrivacyConfig(){return this._http.post("api/ats/masterService/getAtsMasterMailPrivacyConfig",{})}getAtsMasterMailDetailConfig(){return this._http.post("api/ats/masterService/getAtsMasterMailDetailConfig",{})}getAtsCalendarDetailConfig(){return this._http.post("api/ats/masterService/getAtsCalendarDetailConfig",{})}getAtsMasterCalendarPrivacyConfig(){return this._http.post("api/ats/masterService/getAtsMasterCalendarPrivacyConfig",{})}getAtsCandidateAvailability(){return this._http.post("api/ats/masterService/getAtsCandidateAvailability",{})}getDegreeListForSettings(e,t){return this._http.post("api/ats/masterService/getDegreeListForSettings",{searchParams:e,sortParams:t})}getCourseListForSettings(e,t){return this._http.post("api/ats/masterService/getCourseListForSettings",{searchParams:e,sortParams:t})}getAssociatedDegreeMasterDetails(){return this._http.post("api/ats/masterService/getAssociatedDegreeMasterDetails",{})}getCourseDegreeMappingDetails(e,t){return this._http.post("api/ats/masterService/getCourseDegreeMappingDetails",{searchParams:e,sortParams:t})}getCourseMasterDetails(){return this._http.post("api/ats/masterService/getCourseMasterDetails",{})}getDegreeMasterDetails(){return this._http.post("api/ats/masterService/getDegreeMasterDetails",{})}getStateListForSettings(e,t,n,i){return this._http.post("api/ats/masterService/getStateListForSettings",{searchParams:e,sortParams:t,skip:n,limit:i})}getCountryMasterForSettings(){return this._http.post("api/ats/masterService/getCountryMasterForSettings",{})}getCountryListForSettings(e,t,n,i){return this._http.post("api/ats/masterService/getCountryListForSettings",{searchParams:e,sortParams:t,skip:n,limit:i})}getRegionListForSettings(e,t,n,i){return this._http.post("api/ats/masterService/getRegionListForSettings",{searchParams:e,sortParams:t,skip:n,limit:i})}getCityMasterForSettings(){return this._http.post("api/ats/masterService/getCityMasterForSettings",{})}getCityListForSettings(e,t,n,i){return this._http.post("api/ats/masterService/getCityListForSettings",{searchParams:e,sortParams:t,skip:n,limit:i})}getStateMasterForSettings(){return this._http.post("api/ats/masterService/getStateMasterForSettings",{})}getSalutationMaster(){return this._http.post("/api/ats/masterService/getSalutationMaster",{})}getRelationshipStatusMaster(){return this._http.post("/api/ats/masterService/getRelationshipStatusMaster",{})}getRelationshipTypeMaster(){return this._http.post("/api/ats/masterService/getRelationshipTypeMaster",{})}getCandidateNationalityDocumentsMaster(){return this._http.post("/api/ats/masterService/getCandidateNationalityDocumentsMaster",{})}getBloodGroupMaster(){return this._http.post("/api/ats/masterService/getBloodGroupMaster",{})}getSkills(){return this._http.post("/api/ats/masterService/getSkills",{})}getSkillLevelMaster(){return this._http.post("/api/ats/masterService/getSkillLevelMaster",{})}getWorkSchedule(){return this._http.post("/api/ats/masterService/getWorkSchedule",{})}getHolidayCalendar(){return this._http.post("/api/ats/masterService/getHolidayCalendar",{})}getRegion(){return this._http.post("/api/ats/masterService/getRegion",{})}getLevel(){return this._http.post("/api/ats/masterService/getLevel",{})}getJobType(){return this._http.post("/api/ats/masterService/getJobType",{})}getPayrollMasterData(){return this._http.post("/api/payrollapp/payrollPrimary/getPayrollMasterData",{})}generateCTCSplittup(e,t,n,i,a){return this._http.post("/api/ats/utilService/generateCTC",{ctc:e,payscaleArea:t,payscaleGroup:n,payscaleLevel:i,currency:a})}getAllCities(){return this._http.post("api/ats/masterService/getAllCities",{})}getDegreeCourseMapping(){return this._http.post("api/ats/masterService/getDegreeCourseMapping",{})}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275inject"](a.c))},e.\u0275prov=i["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},wC0v:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("wd/R"),a=n("fXoL");let r=(()=>{class e{transform(e,t,n=!0){if(!e||!i(e,i.ISO_8601,!0).isValid())return"-";if(i(e,"YYYY-MM-DD",!0).isValid())return i(e,"YYYY-MM-DD").format(t);const a=i(e);return n?a.local().format(t):a.format(t)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=a["\u0275\u0275definePipe"]({name:"dateFormat",type:e,pure:!0}),e})()},yx4D:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("fXoL"),a=n("jhN1");let r=(()=>{class e{constructor(e){this._sanitizer=e}transform(e){return this._sanitizer.bypassSecurityTrustHtml(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](a.c))},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"svgSecurityBypass",type:e,pure:!0}),e})()}}]);