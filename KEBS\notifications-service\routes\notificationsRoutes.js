const express = require("express");
const { logger } = require("../logger");
const notificationsService = require('../services/notificationsService')
const mongo_native = require("../mongo_conn_native").Connection;
const passport = require("passport");
const _ = require("lodash")
const router = express.Router();

router.post("/notificationsCheck",
    // passport.authenticate('jwt'),
     async (req, res) => {
           try {
               res.json({
                   messType: "S",
                   messText: "test API for notifications"
               })
           } catch (error) {
               logger.info("Error at notifications demo !")
               logger.info(error)
               let errorDetails = {
                   message: error.message,
                   stack: error.stack
               }
               res.json({
                   messType: "E",
                   messText: "Error on Demo Test API...!",
                   error: errorDetails
               })
           }
       }
)

router.post('/getNotificationsReport',
    passport.authenticate('jwt'),
    notificationsService.getNotificationsReport
)

router.post('/getAssociateIDMasterList',
    passport.authenticate('jwt'),
    notificationsService.getAssociateIDMasterList
)
 
router.post('/getEmployeeNameMasterList',
     passport.authenticate('jwt'),
    notificationsService.getEmployeeNameMasterList
)
 
router.post('/getApplicationNameMasterList',
    passport.authenticate('jwt'),
    notificationsService.getApplicationNameMasterList
)
 
router.post('/getRecipientMailIDMasterList',
    passport.authenticate('jwt'),
    notificationsService.getRecipientMailIDMasterList
)
 
router.post('/getNotifiedMasterList',
     passport.authenticate('jwt'),
    notificationsService.getNotifiedMasterList
)
 
router.post('/getNotificationTypeMasterList',
    passport.authenticate('jwt'),
    notificationsService.getNotificationTypeMasterList
)

router.post("/downloadLogs",
    passport.authenticate('jwt'),
    notificationsService.downloadLogs
);

router.post("/deleteLogs",
    passport.authenticate('jwt'),
    notificationsService.deleteLogs
);

module.exports = router