import { Component, OnInit, Compiler, ViewContainerRef, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import *  as _ from 'underscore';
import * as moment from 'moment';
import { ComponentPortal } from '@angular/cdk/portal';

import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { BilledInvoicesService } from '../../../../services/billed-invoice-service/billed-invoices.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ErrorService } from 'src/app/services/error/error.service';

import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { TenantService } from 'src/app/services/tenant-service/tenant.service';
import { InvoiceCommonService } from "src/app/modules/invoice/common-services/invoice-common.service";

@Component({
  selector: 'app-billed-invoice-landing-page',
  templateUrl: './billed-invoice-landing-page.component.html',
  styleUrls: ['./billed-invoice-landing-page.component.scss']
})
export class BilledInvoiceLandingPageComponent implements OnInit, OnDestroy {

  applicationId = 221;
  requestDataSubscription: Subscription;
  collectorItemDataCurrentIndex = 0;
  protected $onDestroy = new Subject<void>();
  protected $onAppApiCalled = new Subject<void>();
  selectedItemId: number;
  overlayRef: OverlayRef;
  $billedInvoicesSubscription: Subscription
  $billedInvoicesTotalSubscription: Subscription
  isSideNavLoading: boolean = true
  filterId: any
  dummySideNavData = Array.from({ length: 15 }, (_, i) => i + 1)
  sideNavData = [];
  collectorRoleAccessData: any;
  finalExcelData =[];
  udrfBodyColumns:any;
  showInvoiceV2:boolean = false

  // Columns 
  // udrfBodyColumns = [
  //   {
  //     item: 'project_name',
  //     header: 'Project Name',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'text',
  //     textClass: 'value13Bold cp colorRed',
  //     position: 1,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   },
  //   {
  //     item: 'milestone_name',
  //     header: 'Milestone Name',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'text',
  //     textClass: 'value13Bold',
  //     position: 2,
  //     colSize: 2,
  //     sortOrder: 'N',
  //     width: 280,
  //   },
  //   {
  //     item: 'customer_invoice_no',
  //     header: 'Customer Invoice no',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'text',
  //     textClass: 'value13Bold',
  //     position: 2,
  //     colSize: 2,
  //     sortOrder: 'N',
  //     width: 280,
  //   },
  //   {
  //     item: 'ar',
  //     // itemTooltip :  "ar_value_formatted",
  //     header: 'AR Value',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'currency',
  //     textClass: 'value13Bold',
  //     position: 3,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   },
  //   {
  //     item: 'tds_converted_value',
  //     header: 'TDS Value',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'currency',
  //     textClass: 'value13Bold',
  //     position: 14,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   },
  //   {
  //     item: 'billed_on',
  //     header: 'Billed On',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'date',
  //     textClass: 'value13light',
  //     position: 4,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   },
  //   {
  //     item: 'CP_days',
  //     item1: 'aging_days',
  //     header: 'CP-Age',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'multitext',
  //     textClass: 'value13Bold',
  //     item1Class: 'status_color',
  //     position: 5,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   },
  //   // {
  //   //   item: 'action',
  //   //   header: 'Actions',
  //   //   isActive: true,
  //   //   isVisible: 'false',
  //   //   type: 'action',
  //   //   position: 6,
  //   //   colSize: 2,
  //   //   width: 240,
  //   //   sortOrder: 'N'
  //   // },
  //   {
  //     item: 'invoice_status',
  //     header: 'Status',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'status',
  //     textClass: 'value13Bold',
  //     position: 7,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   },
  //   {
  //     item: 'billed_by',
  //     header: 'Billed By',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'text',
  //     textClass: 'value13Bold',
  //     position: 8,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   },
  //   {
  //     item: 'customer_name',
  //     header: 'Customer',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'text',
  //     textClass: 'value13Bold',
  //     position: 9,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   },
  //   // {
  //   //   item: 'planned_on',
  //   //   header: 'Planned On',
  //   //   isActive: true,
  //   //   isVisible: 'true',
  //   //   type: 'text',
  //   //   textClass: 'value13Bold',
  //   //   position: 2,
  //   //   colSize: 3,
  //   //   sortOrder: 'N',
  //   //   width: 240,      
  //   // },
  //   {
  //     item: 'payment_expected_on',
  //     header: 'Payment Expected On',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'date',
  //     textClass: 'value13light',
  //     position: 10,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   },
  //   {
  //     item: 'pl',
  //     header: 'P&L',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'text',
  //     textClass: 'value13Bold',
  //     position: 11,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   },
  //   {
  //     item: 'service_type',
  //     header: 'Service Type',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'text',
  //     textClass: 'value13Bold',
  //     position: 12,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   },
  //   {
  //     item: 'cost_center',
  //     header: 'Cost Center',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'text',
  //     textClass: 'value13Bold',
  //     position: 12,
  //     colSize: 3,
  //     sortOrder: 'N',
  //     width: 240,
  //   }

  // ]

  // Status Color
  udrfItemStatusColor: any[] = [
    {
      status: "Billed",
      color: "#ffb142"
    },
    {
      status: "Legal",
      color: "#cf0001"
    },
    {
      status: "Partial Payment",
      color: "rgb(0, 230, 77)"
    }
  ]

  constructor(
    public _udrfService: UdrfService,
    public _billedInvoicesServices: BilledInvoicesService,
    public errorService: ErrorService,
    private overlay: Overlay,
    private compiler: Compiler,
    private router: Router,
    private _utilityService: UtilityService,
    private dialog: MatDialog,
    private snackBar : MatSnackBar,
    private _excelService: JsonToExcelService,
    private spinner: NgxSpinnerService,
    private tenantService: TenantService,
    private invoiceCommonService: InvoiceCommonService) { }

  

  async ngOnInit() {

    // Get Collector Role access Data
    this.collectorRoleAccessData = await this.collectorRoleAccess();
    let result = await this.getTenantInfo();
    if(this.collectorRoleAccessData["messType"] == "E")
    this.snackBar.open(this.collectorRoleAccessData["messText"], "Dismiss")

    if (!this.showInvoiceV2) {
      // Columns 
      this.udrfBodyColumns = [
        {
          item: 'project_name',
          header: 'Project Name',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold cp colorRed',
          position: 1,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'item_name',
          header: 'Item Name',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold cp colorRed',
          position: 1,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'milestone_name',
          header: 'Milestone Name',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 2,
          colSize: 2,
          sortOrder: 'N',
          width: 280,
        },
        {
          item: 'customer_invoice_no',
          header: 'Customer Invoice no',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 2,
          colSize: 2,
          sortOrder: 'N',
          width: 280,
        },
        {
          item: 'ar',
          // itemTooltip :  "ar_value_formatted",
          header: 'AR Value',
          isActive: true,
          isVisible: 'true',
          type: 'currency',
          textClass: 'value13Bold',
          position: 3,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'tds_converted_value',
          header: 'TDS Value',
          isActive: true,
          isVisible: 'true',
          type: 'currency',
          textClass: 'value13Bold',
          position: 14,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'billed_on',
          header: 'Billed On',
          isActive: true,
          isVisible: 'true',
          type: 'date',
          textClass: 'value13light',
          position: 4,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'CP_days',
          item1: 'aging_days',
          header: 'CP-Age',
          isActive: true,
          isVisible: 'true',
          type: 'multitext',
          textClass: 'value13Bold',
          item1Class: 'status_color',
          position: 5,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        // {
        //   item: 'action',
        //   header: 'Actions',
        //   isActive: true,
        //   isVisible: 'false',
        //   type: 'action',
        //   position: 6,
        //   colSize: 2,
        //   width: 240,
        //   sortOrder: 'N'
        // },
        {
          item: 'invoice_status',
          header: 'Status',
          isActive: true,
          isVisible: 'true',
          type: 'status',
          textClass: 'value13Bold',
          position: 7,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'billed_by',
          header: 'Billed By',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 8,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'customer_name',
          header: 'Customer',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 9,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        // {
        //   item: 'planned_on',
        //   header: 'Planned On',
        //   isActive: true,
        //   isVisible: 'true',
        //   type: 'text',
        //   textClass: 'value13Bold',
        //   position: 2,
        //   colSize: 3,
        //   sortOrder: 'N',
        //   width: 240,      
        // },
        {
          item: 'payment_expected_on',
          header: 'Payment Expected On',
          isActive: true,
          isVisible: 'true',
          type: 'date',
          textClass: 'value13light',
          position: 10,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'pl',
          header: 'Sales Region',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 11,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'service_type',
          header: 'Service Type',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 12,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'cost_center',
          header: 'Cost Center',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 12,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        }

      ]
    }
    else {

      this.udrfBodyColumns = [
        {
          item: 'project_name',
          header: 'Portfolio Name',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold cp colorRed',
          position: 1,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'item_name',
          header: 'Project Name',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold cp colorRed',
          position: 1,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'milestone_name',
          header: 'Milestone Name',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 2,
          colSize: 2,
          sortOrder: 'N',
          width: 280,
        },
        {
          item: 'customer_invoice_no',
          header: 'Customer Invoice no',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 2,
          colSize: 2,
          sortOrder: 'N',
          width: 280,
        },
        {
          item: 'ar',
          // itemTooltip :  "ar_value_formatted",
          header: 'AR Value',
          isActive: true,
          isVisible: 'true',
          type: 'currency',
          textClass: 'value13Bold',
          position: 3,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'ar_in_project_currency',
          header: 'Project Currency AR',
          isActive: true,
          // itemTooltip: "currency",
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 2,
          colSize: 2,
          sortOrder: 'N',
          width: 280,
        },
        {
          item: 'ar_in_standard_currency',
          header: `Standard Currency AR `,
          isActive: true,
          isVisible: 'false',
          type: 'text',
          textClass: 'value13Bold',
          position: 2,
          colSize: 2,
          sortOrder: 'N',
          width: 280,
        },
        {
          item: 'ar_in_common_currency',
          header: `Common Currency AR`,
          isActive: true,
          isVisible: 'false',
          type: 'text',
          textClass: 'value13Bold',
          position: 2,
          colSize: 2,
          sortOrder: 'N',
          width: 280,
        },
        {
          item: 'tds_converted_value',
          header: 'TDS Value',
          isActive: true,
          isVisible: 'true',
          type: 'currency',
          textClass: 'value13Bold',
          position: 14,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'billed_on',
          header: 'Billed On',
          isActive: true,
          isVisible: 'true',
          type: 'date',
          textClass: 'value13light',
          position: 4,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'CP_days',
          item1: 'aging_days',
          header: 'CP-Age',
          isActive: true,
          isVisible: 'true',
          type: 'multitext',
          textClass: 'value13Bold',
          item1Class: 'status_color',
          position: 5,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        // {
        //   item: 'action',
        //   header: 'Actions',
        //   isActive: true,
        //   isVisible: 'false',
        //   type: 'action',
        //   position: 6,
        //   colSize: 2,
        //   width: 240,
        //   sortOrder: 'N'
        // },
        {
          item: 'invoice_status',
          header: 'Status',
          isActive: true,
          isVisible: 'true',
          type: 'status',
          textClass: 'value13Bold',
          position: 7,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'billed_by',
          header: 'Billed By',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 8,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'customer_name',
          header: 'Customer',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 9,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        // {
        //   item: 'planned_on',
        //   header: 'Planned On',
        //   isActive: true,
        //   isVisible: 'true',
        //   type: 'text',
        //   textClass: 'value13Bold',
        //   position: 2,
        //   colSize: 3,
        //   sortOrder: 'N',
        //   width: 240,      
        // },
        {
          item: 'payment_expected_on',
          header: 'Payment Expected On',
          isActive: true,
          isVisible: 'true',
          type: 'date',
          textClass: 'value13light',
          position: 10,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'pl',
          header: 'Sales Region',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 11,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'service_type',
          header: 'Service Type',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 12,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        },
        {
          item: 'cost_center',
          header: 'Cost Center',
          isActive: true,
          isVisible: 'true',
          type: 'text',
          textClass: 'value13Bold',
          position: 12,
          colSize: 3,
          sortOrder: 'N',
          width: 240,
        }

      ]
    }
        
    // Invoice Raised On (Duration)
    let durationRanges = [
      {
        checkboxId: 'CDCRD',
        checkboxName: 'Current Year',
        checkboxStartValue: moment().startOf('year'),
        checkboxEndValue: moment().endOf('year'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD2',
        checkboxName: 'This Week',
        checkboxStartValue: this._utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this._utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD3',
        checkboxName: 'This Month',
        checkboxStartValue: this._utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this._utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD4',
        checkboxName: 'Previous Month',
        checkboxStartValue: this._utilityService.getFormattedDate(
          moment().subtract(1, 'months').startOf('month'),
          moment(moment().subtract(1, 'months').startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this._utilityService.getFormattedDate(
          moment().subtract(1, 'months').endOf('month'),
          moment(moment().subtract(1, 'months').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD5',
        checkboxName: 'All',
        checkboxStartValue: moment('1920-01-01'),
        checkboxEndValue: moment('2100-12-12'),
        isCheckboxDefaultSelected: true
      }
    ];

    this._udrfService.udrfFunctions.constructCustomRangeData(
      5,
      'date',
      durationRanges
    );

    let paymentExpectedDurationRanges = [
      {
        checkboxId: 'CDCRD',
        checkboxName: 'Current Year',
        checkboxStartValue: moment().startOf('year'),
        checkboxEndValue: moment().endOf('year'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD2',
        checkboxName: 'This Week',
        checkboxStartValue: this._utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this._utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD3',
        checkboxName: 'This Month',
        checkboxStartValue: this._utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this._utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD4',
        checkboxName: 'Next Month',
        checkboxStartValue: this._utilityService.getFormattedDate(
          moment().add(1, 'month').startOf('month'),
          moment(moment().add(1, 'month').startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this._utilityService.getFormattedDate(
          moment().add(1, 'month').endOf('month'),
          moment(moment().add(1, 'month').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD5',
        checkboxName: 'Upcoming 3 Months',
        checkboxStartValue: this._utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this._utilityService.getFormattedDate(
          moment().add(2, 'month').endOf('month'),
          moment(moment().add(2, 'month').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      }, {
        checkboxId: 'CDCRD6',
        checkboxName: 'All',
        checkboxStartValue: moment('1920-01-01'),
        checkboxEndValue: moment('2100-12-12'),
        isCheckboxDefaultSelected: false,
      }
    ];

    this._udrfService.udrfFunctions.constructCustomRangeData(
      6,
      'date',
      paymentExpectedDurationRanges
    );


    this.selectedItemId = 1;

    this._udrfService.udrfUiData.moreActionItemData = {
      moreActionItems: [{
        id: 1,
        name: 'Enter payment',
        icon: 'credit_card'
      },
      {
        id: 2,
        name: 'History',
        icon: 'history'
      },
      {
        id: 3,
        name: 'Watch invoice',
        icon: 'visibility'
      },
      {
        id: 4,
        name: 'Mark as complete',
        icon: 'done_all'
      },
      ]
    }

    this._udrfService.getNotifyReleasesUDRF();


    this.collectorItemDataCurrentIndex = 0;
    this._udrfService.udrfBodyData = []


    this._udrfService.udrfData.applicationId = this.applicationId;
    this._udrfService.udrfUiData.showNewReleasesButton = true;
    this._udrfService.udrfUiData.showItemDataCount = true
    this._udrfService.udrfUiData.itemDataType = ""
    this._udrfService.udrfUiData.totalItemDataCount = 0
    this._udrfService.udrfUiData.showSearchBar = true
    this._udrfService.udrfUiData.showActionButtons = true
    this._udrfService.udrfUiData.showUdrfModalButton = true
    this._udrfService.udrfUiData.showSettingsModalButton = false
    this._udrfService.udrfUiData.isReportDownloading = false
    this._udrfService.udrfUiData.showReportDownloadButton = true;
    this._udrfService.udrfUiData.showColumnConfigButton = true
    this._udrfService.udrfUiData.udrfItemStatusColor = this.udrfItemStatusColor
    this._udrfService.udrfUiData.itemcardSelected = this.itemCardClicked.bind(this)
    this._udrfService.udrfUiData.summaryCardsItem = {}
    this._udrfService.udrfUiData.showHierarchyData = {}
    this._udrfService.udrfUiData.inlineEditData = {}
    this._udrfService.udrfUiData.downloadItemDataReport = () => { }
    this._udrfService.udrfUiData.openComments = this.openComments.bind(this)
    this._udrfService.udrfUiData.openCommentsData = {}
    this._udrfService.udrfUiData.itemDataScrollDown = this.onCollectionItemDataScrollDown.bind(this)
    this._udrfService.udrfUiData.downloadItemDataReport = this.downloadItemDataReport.bind(this);
    this._udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColumns
    this._udrfService.udrfUiData.udrfVisibleBodyColumns = this._udrfService.udrfUiData.udrfVisibleBodyColumns
    this._udrfService.udrfUiData.udrfInvisibleBodyColumns = this._udrfService.udrfUiData.udrfInvisibleBodyColumns
    this._udrfService.udrfUiData.variant = 0;
    this._udrfService.udrfUiData.itemHasQuickCta = false
    this._udrfService.udrfUiData.itemHasComments = true
    this._udrfService.udrfUiData.itemHasHierarchyView = false;
    this._udrfService.udrfUiData.itemHasInfoButton = false;
    this._udrfService.udrfUiData.itemHasMoreActions = false;
    this._udrfService.udrfUiData.showCollapseButton = true;
    this._udrfService.udrfUiData.horizontalScroll = true;
    this._udrfService.udrfUiData.openHelpData = {}
    this._udrfService.udrfUiData.openHelp = this.openInvoiceInfoPopupComponent.bind(this)
    this._udrfService.udrfUiData.moreActionItemClicked = this.moreActionItemClicked.bind(this)
    this._udrfService.udrfUiData.moreActionItemClickedData = {}

    //group by declarations
    this._udrfService.udrfUiData.showGroupByButton = true;
    this._udrfService.udrfUiData.callgroupByData = this.callgroupByData.bind(this);
    this._udrfService.udrfUiData.groupByUiArray = [];
    this._udrfService.udrfUiData.groupByResponseData = [];
    this._udrfService.udrfUiData.gbyCategoryWiseViewData = true

    this._udrfService.getAppUdrfConfig(this.applicationId, this.initReport.bind(this));
    this._udrfService.udrfUiData.switchFunction = this.initReport.bind(this);

    this._udrfService.getNotifyReleasesUDRF();

    this._udrfService.udrfUiData.ghostButtonUI = false;

  }

  // Collector Role access
  collectorRoleAccess() {
    return new Promise((resolve, reject) => {
      this._billedInvoicesServices.getCollectorRoleAccess().subscribe(res => {

        if (res["messType"] == "E") {
          this.snackBar.open(res["messData"], "Dismiss", { duration: 2000 })
        }

        resolve(res);

      }, err => {
        console.error(err);
        reject(err);

      })
    })

  }

  /**
   * Init UDRF report
   */
  async initReport() {

    this.$onAppApiCalled.next();
    this.collectorItemDataCurrentIndex = 0;
    this._udrfService.udrfBodyData = [];
    this.selectedItemId = 1;
    this._udrfService.udrfUiData.resolveColumnConfig();

    if (this._udrfService.udrfData.appliedConfig["activeView"].length > 0) {
      if (this._udrfService.udrfData.appliedConfig["activeView"][0].groupByView) {
        this._udrfService.groupBy = true;
        this.callgroupByData();
      }
    }
    else {
      this.getCollectorList();
    }
    this.getSideNavData();
  }

  /**
   * For groupby data view - api call for number of count and 
   * category for the selected group by category
   */
  async callgroupByData() {
    this.spinner.show();
    this.collectorItemDataCurrentIndex = 0;
    let selectedNavItem = _.findWhere(this.sideNavData, { id: this.selectedItemId });
    //console.log('gby ',this._udrfService.udrfUiData.groupByUiArray)

    for (let i = 0; i < this._udrfService.udrfUiData.groupByUiArray.length; i++) {
      if (this._udrfService.udrfUiData.groupByUiArray[i].groupByItemValues.length > 0) {
        this._udrfService.udrfUiData.groupByUiArray[i].groupByItemValues = [];
      }
    }

    let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));

    let filterConfig = {
      startIndex: this.collectorItemDataCurrentIndex,
      startDate: this._udrfService.udrfData.mainApiDateRangeStart,
      endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
      mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails,
      groupByDetails: this._udrfService.udrfData.appliedConfig["activeView"][0].groupByViewArray[0],
      groupByActive: this._udrfService.groupBy,
    };

    this._billedInvoicesServices
      .getBilledInvoicesGroupByCount(filterConfig, this.collectorRoleAccessData, selectedNavItem)
      .pipe(takeUntil(this.$onDestroy))
      .pipe(takeUntil(this.$onAppApiCalled))
      .subscribe(
        async (res) => {
          if (
            res['messType'] == 'S' &&
            res['messData'] &&
            res['messData'].length > 0
          ) {

            this.filterId = this._udrfService.udrfData.appliedConfig["activeView"][0].groupByViewArray[0].filterId;

            for (let i = 0; i < this._udrfService.udrfUiData.groupByUiArray.length; i++) {
              if (this._udrfService.udrfUiData.groupByUiArray[i].groupByItemId == this.filterId) {
                for (let j = 0; j < res['messData'].length; j++) {
                  this._udrfService.udrfUiData.groupByUiArray[i].groupByItemValues.push(
                    {
                      categoryName: res['messData'][j].customer,
                      categoryId: res['messData'][j].customer_id,
                      categoryCount: res['messData'][j].groupByCount,
                      categoryItems: [],
                      expandAll: true,
                    }
                  )
                }
              }
            }
            this.getBilledInvoiceCount()
          }
          console.log(this._udrfService.udrfUiData.groupByUiArray);
          this.callgroupByItemData();
        });
       
  }


  /**
   * Retriving groupby data on selected category basis
   */
  callgroupByItemData () {
    this.spinner.show();
    let selectedNavItem = _.findWhere(this.sideNavData,{id: this.selectedItemId});

    let stat = false;
    let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));
    let tempArray = [], statArray = [];
    for (let mainFilterArrayItem of mainFilterArray) {
      if (mainFilterArrayItem.filterId != this.filterId) {
        stat = true;
      }
      else if (mainFilterArrayItem.filterId == this.filterId) {
        stat = false;
        statArray = mainFilterArrayItem.multiOptionSelectSearchValues;
      }
    }
    if (stat) {
      tempArray = JSON.parse(JSON.stringify(_.where(this._udrfService.udrfData.filterTypeArray, { filterId: this.filterId })));
      mainFilterArray.push(tempArray[0]);

    }
    if(this.filterId==undefined){
      this.filterId=0;
      this.spinner.hide();
    }
    let iterateData = _.where(this._udrfService.udrfUiData.groupByUiArray,{groupByItemId: this.filterId});
    console.log(this.filterId)
    console.log(this._udrfService.udrfUiData.groupByUiArray)
    console.log(iterateData)

    if (this._udrfService.udrfUiData.scrolled) {
      if (mainFilterArray.length > 0) {
        for (let mainFilterArrayItem of mainFilterArray) {
          if (mainFilterArrayItem.filterId == this.filterId) {
            mainFilterArrayItem.multiOptionSelectSearchValues =
              [iterateData[0].groupByItemValues[this._udrfService.udrfUiData.scrollIndex].categoryId];
          }
        }
      }
      else {
        mainFilterArray = JSON.parse(JSON.stringify(_.where(this._udrfService.udrfData.filterTypeArray, { filterId: this.filterId })));
        mainFilterArray[0].multiOptionSelectSearchValues = [iterateData[0].groupByItemValues[this._udrfService.udrfUiData.scrollIndex].categoryId];
      }

      let filterConfig = {
        startIndex: this.collectorItemDataCurrentIndex,
        startDate: this._udrfService.udrfData.mainApiDateRangeStart,
        endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
        mainFilterArray: mainFilterArray,
        txTableDetails: this._udrfService.udrfData.txTableDetails,
        mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
        searchTableDetails: this._udrfService.udrfData.searchTableDetails,
        isItemLevelApi: false
      };

      this.$billedInvoicesSubscription = this._billedInvoicesServices
        .getBilledInvoicesList(filterConfig, this.collectorRoleAccessData, selectedNavItem)
        .pipe(takeUntil(this.$onDestroy))
        .pipe(takeUntil(this.$onAppApiCalled))
        .subscribe(
          async (res) => {

            if (
              res['messType'] == 'S' &&
              res['messData'] &&
              res['messData'].length > 0
            ) {

              this._udrfService.udrfBodyData = this._udrfService.udrfBodyData.concat(res['messData'])

              for (let j = 0; j < this._udrfService.udrfUiData.groupByUiArray.length; j++) {
                if (this._udrfService.udrfUiData.groupByUiArray[j].groupByItemId == this.filterId) {
                  this._udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[this._udrfService.udrfUiData.scrollIndex].categoryItems
                    = this._udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[this._udrfService.udrfUiData.scrollIndex].categoryItems.concat(res['messData']);

                  // let customerWiseArValue = 0

                  // _.each(res['messData'],(item) => {
                  //   customerWiseArValue += item.ar_value_in_crs
                  // })

                  // this._udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[this._udrfService.udrfUiData.scrollIndex].categoryValue 
                  // = customerWiseArValue


                  // let customerWiseArValue = 0

                  // _.each(res['messData'],(item) => {customerWiseArValue += item.ar_value_in_crs})

                  // this._udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[i].categoryValue 
                  // = `${(customerWiseArValue).toFixed(2).toString()} Cr`

                // this._udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[i].categoryValueToolTip
                // = `AR value ${(customerWiseArValue).toFixed(4).toString()} Cr`
              }
            }
           
          } else {

            //this._udrfService.udrfBodyData = this._udrfService.udrfBodyData.concat([]);
            // this._udrfService.udrfUiData.totalItemDataCount = 0
            //this._udrfService.udrfData.noItemDataFound = true;
            
          }
          this.spinner.hide();
          this._udrfService.udrfData.isItemDataLoading = false;
 
        },  (err) => {
          this.spinner.hide();
          this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Invoice data", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        });

    }
    else {
      for (let i = 0; i < (statArray.length > 0 ? statArray.length : iterateData[0].groupByItemValues.length); i++) {
        if (mainFilterArray.length > 0) {
          for (let mainFilterArrayItem of mainFilterArray) {
            if (mainFilterArrayItem.filterId == this.filterId) {
              // console.log(mainFilterArrayItem.multiOptionSelectSearchValues);
              // console.log(mainFilterArrayItem.multiOptionSelectSearchValues.length)
              mainFilterArrayItem.multiOptionSelectSearchValues = [(statArray.length > 0 ? statArray[i] : iterateData[0].groupByItemValues[i].categoryId)];
            }
          }
        }
        else {
          mainFilterArray = JSON.parse(JSON.stringify(_.where(this._udrfService.udrfData.filterTypeArray, { filterId: this.filterId })));
          mainFilterArray[0].multiOptionSelectSearchValues = [iterateData[0].groupByItemValues[i].categoryId];
        }

        let filterConfig = {
          startIndex: this.collectorItemDataCurrentIndex,
          startDate: this._udrfService.udrfData.mainApiDateRangeStart,
          endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
          mainFilterArray: mainFilterArray,
          txTableDetails: this._udrfService.udrfData.txTableDetails,
          mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
          searchTableDetails: this._udrfService.udrfData.searchTableDetails,
          isItemLevelApi: false
        };

        this.$billedInvoicesSubscription = this._billedInvoicesServices
          .getBilledInvoicesList(filterConfig, this.collectorRoleAccessData, selectedNavItem)
          .pipe(takeUntil(this.$onDestroy))
          .pipe(takeUntil(this.$onAppApiCalled))
          .subscribe(
            async (res) => {

              if (
                res['messType'] == 'S' &&
                res['messData'] &&
                res['messData'].length > 0
              ) {

                this._udrfService.udrfBodyData = this._udrfService.udrfBodyData.concat(res['messData'])

                for (let j = 0; j < this._udrfService.udrfUiData.groupByUiArray.length; j++) {
                  if (this._udrfService.udrfUiData.groupByUiArray[j].groupByItemId == this.filterId) {
                    this._udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[i].categoryItems =
                      this._udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[i].categoryItems.concat(res['messData']);

                    let customerWiseArValue = 0

                    _.each(res['messData'], (item) => { customerWiseArValue += item.default_currency_ar })

                    this._udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[i].categoryValue
                      = `${(customerWiseArValue).toFixed(2).toString()} ${res['currency_code']}`

                    this._udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[i].categoryValueToolTip
                      = `AR value ${(customerWiseArValue).toFixed(4).toString()} ${res['currency_code']}`

                  }
                }

                // console.log('count2')
                // console.log(this._udrfService.udrfUiData.totalItemDataCount)

              } else {

            this._udrfService.udrfBodyData = this._udrfService.udrfBodyData.concat([]);
            //this._udrfService.udrfUiData.totalItemDataCount = 0
            this._udrfService.udrfData.noItemDataFound = true;
          }
          this.spinner.hide();
          this._udrfService.udrfData.isItemDataLoading = false;
 
        },  (err) => {
          this.spinner.hide();
          this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Invoice data", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        });

      }
      this.spinner.hide();
    }
        let itemArray = _.where(this._udrfService.udrfUiData.groupByUiArray, {groupByItemId: this.filterId});
        this._udrfService.udrfUiData.groupByResponseData = itemArray[0].groupByItemValues;
        console.log(this._udrfService.udrfUiData.groupByResponseData);
        
  }

  /**
   * Get Total count
   */
  async getBilledInvoiceCount() {
    let selectedNavItem = _.findWhere(this.sideNavData, { id: this.selectedItemId });
    let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));

    let filterConfig = {
      startIndex: this.collectorItemDataCurrentIndex,
      startDate: this._udrfService.udrfData.mainApiDateRangeStart,
      endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
      mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails
    };

    this._billedInvoicesServices.getBilledInvoiceTotalCount(filterConfig, selectedNavItem, this.collectorRoleAccessData)
      .pipe(takeUntil(this.$onDestroy))
      .subscribe(async (res: any) => {
        if (res.messType == 'S')
          this._udrfService.udrfUiData.totalItemDataCount = res.count
        console.log('count')
        console.log(this._udrfService.udrfUiData.totalItemDataCount)
      }, (err) => {
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Invoice data", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
      })
  }


  /**
   * Billed Invoice list api call (Main function)
   */
  async getCollectorList() {
    this.spinner.show();
    let selectedNavItem = _.findWhere(this.sideNavData,{id: this.selectedItemId});
    let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));

    let filterConfig = {
      startIndex: this.collectorItemDataCurrentIndex,
      startDate: this._udrfService.udrfData.mainApiDateRangeStart,
      endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
      mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails,
      isItemLevelApi: true
    };

    if (this.$billedInvoicesSubscription)
      this.$billedInvoicesSubscription.unsubscribe();

    this.$billedInvoicesSubscription = this._billedInvoicesServices
      .getBilledInvoicesList(filterConfig, this.collectorRoleAccessData, selectedNavItem)
      .pipe(takeUntil(this.$onDestroy))
      .pipe(takeUntil(this.$onAppApiCalled))
      .subscribe(
        async (res) => {
          if (
            res['messType'] == 'S' &&
            res['messData'] &&
            res['messData'].length > 0
          ) {
            this._udrfService.udrfBodyData = this._udrfService.udrfBodyData.concat(res['messData'])
            this.getBilledInvoiceCount()
            //this._udrfService.udrfUiData.totalItemDataCount = res['messData'].length
          } else {
            this._udrfService.udrfBodyData = this._udrfService.udrfBodyData.concat([]);
            this._udrfService.udrfUiData.totalItemDataCount = 0
            this._udrfService.udrfData.noItemDataFound = true;
          }

          this._udrfService.udrfData.isItemDataLoading = false;
          this.spinner.hide();
        },  (err) => {
          this.spinner.hide();
          this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Invoice data", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        });

  }

  async getCollectorListOnScrollDown() {

    let selectedNavItem = _.findWhere(this.sideNavData, { id: this.selectedItemId });
    let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));

    let filterConfig = {
      startIndex: this.collectorItemDataCurrentIndex,
      startDate: this._udrfService.udrfData.mainApiDateRangeStart,
      endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
      mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails,
      isItemLevelApi: true
    };

    if (this.$billedInvoicesSubscription)
      this.$billedInvoicesSubscription.unsubscribe();

    this.$billedInvoicesSubscription = this._billedInvoicesServices
      .getBilledInvoicesList(filterConfig, this.collectorRoleAccessData, selectedNavItem)
      .pipe(takeUntil(this.$onDestroy))
      .pipe(takeUntil(this.$onAppApiCalled))
      .subscribe(
        async (res) => {
          if (
            res['messType'] == 'S' &&
            res['messData'] &&
            res['messData'].length > 0
          ) {
            this._udrfService.udrfBodyData = this._udrfService.udrfBodyData.concat(res['messData'])
            this.getBilledInvoiceCount()
            //this._udrfService.udrfUiData.totalItemDataCount = res['messData'].length
          } else {
            this._udrfService.udrfBodyData = this._udrfService.udrfBodyData.concat([]);
            this._udrfService.udrfData.noItemDataFound = true;
          }

          this._udrfService.udrfData.isItemDataLoading = false;

        }, (err) => {
          this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Invoice data", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        });

  }

  /**
   * Invoice category bar (Right side bar) and count
   */
  getSideNavData() {

    this.isSideNavLoading = true
    let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));

    let filterConfig = {
      startIndex: this.collectorItemDataCurrentIndex,
      startDate: this._udrfService.udrfData.mainApiDateRangeStart,
      endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
      mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails
    };

    if (this.$billedInvoicesTotalSubscription)
      this.$billedInvoicesTotalSubscription.unsubscribe();

      this.sideNavData = [];

    this.$billedInvoicesTotalSubscription = this._billedInvoicesServices
      .getBilledInvoiceSideNavData(filterConfig, this.collectorRoleAccessData)
      .pipe(takeUntil(this.$onDestroy))
      .pipe(takeUntil(this.$onAppApiCalled))
      .subscribe(
        async (res) => {

          if (
            res['messType'] == 'S' &&
            res['messData'] &&
            res['messData'].length > 0
          ) {
            //this._udrfService.udrfUiData.totalItemDataCount = res['total'];
            this.sideNavData = res['cpList']
          } else {
            //this._udrfService.udrfUiData.totalItemDataCount = 0
          }
          this.isSideNavLoading = false
        }, (err) => {

          this.isSideNavLoading = false
          this.errorService.
            userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL',
              "Error while retrieving WFH Approvals",
              (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        });
  }


  /**
   * API call while scrolling UDRF item data
   */
  async onCollectionItemDataScrollDown() {
    console.log('scrolleedddd')
    console.log('noItemFound', this._udrfService.udrfData.noItemDataFound)
    console.log('isItemLoading', this._udrfService.udrfData.isItemDataLoading)

    if (!this._udrfService.udrfData.noItemDataFound) {
      if (!this._udrfService.udrfData.isItemDataLoading) {
        this.collectorItemDataCurrentIndex += this._udrfService.udrfData.defaultRecordsPerFetch;
        this._udrfService.udrfData.isItemDataLoading = true;

        if (this._udrfService.udrfData.appliedConfig["activeView"].length > 0) {
          if (this._udrfService.udrfData.appliedConfig["activeView"][0].groupByView) {
            this._udrfService.groupBy = true;
            console.log('scrolleedddd gby')
            this.callgroupByData();
          }
        }
        else {
          console.log('scrolleedddd normal')
          this.getCollectorListOnScrollDown();
        }
      }
    }
  }

  /**
   * UDRF item click - redirect to BilledInvoiceDetailLandingPage
   */
  itemCardClicked() {
    let projectName = this._udrfService.udrfUiData.itemCardSelecteditem['project_name']
    let billingId = this._udrfService.udrfUiData.itemCardSelecteditem['billing_id']

    this.router
      .navigateByUrl('/main/collector/billedInvoices/'
        + billingId +
        '/' +
        this.encodeURIComponent(projectName))
  }

  /**
   * Invoice info popUp
   */
  openInvoiceInfoPopupComponent() {

    let invoiceHelp = this._udrfService.udrfUiData.openHelpData['invoiceInfo']
    let invoiceData = this._udrfService.udrfUiData.openHelpData['data']
    this._billedInvoicesServices.setBilledInvoicePopupData(invoiceData)
    let viewContainerRef: ViewContainerRef = this._udrfService.udrfUiData.openHelpData['viewContainerRef']

    if (!this.overlayRef?.hasAttached()) {
      const positionStrategyBuilder = this.overlay.position();
      const positionStrategy = positionStrategyBuilder
        .flexibleConnectedTo(invoiceHelp).withFlexibleDimensions(true).withPush(true)
        .withViewportMargin(25).withGrowAfterOpen(true)
        .withPositions([
          {
            originX: 'center',
            originY: 'bottom',
            overlayX: 'center',
            overlayY: 'top'
          }
        ]);

      const scrollStrategy = this.overlay.scrollStrategies.close();
      this.overlayRef = this.overlay.create({
        positionStrategy,
        scrollStrategy,
        hasBackdrop: true
      });

      import('../../../../shared-components/invoice-course-of-action-popup/invoice-course-of-action-popup.component')
        .then(
          (infoView) => {

            const ngModuleFactory = this.compiler.compileModuleSync(infoView.InvoiceHelpPopupModule);
            const ngModule = ngModuleFactory.create(viewContainerRef.injector);
            const containerPortal = new ComponentPortal(infoView.InvoiceCourseOfActionPopupComponent, null, null, ngModule.componentFactoryResolver);
            this.overlayRef.attach(containerPortal);
            this.overlayRef.backdropClick().subscribe(() => {
              this.overlayRef?.dispose();
            });
            this._billedInvoicesServices.setCourseOfActionPopupSubject(this.overlayRef);
          });

    }

  }

  /**
   * Comments dialog
   */
  async openComments() {

    let inputData
    let itemData = this._udrfService.udrfUiData.openCommentsData['data']
    if (itemData) {
      inputData = {
        application_id: 221,
        unique_id_1: itemData.billing_id,
        unique_id_2: '',
        application_name: 'Collection management - Billed invoices tab'
      }
      inputData.title = itemData.item_name ? itemData.item_name : '';
      let modalParams = {
        inputData: inputData,
        context:
        {
          'Project Name': itemData.project_name ? itemData.project_name : '',
          'Item Name': itemData.item_name ? itemData.item_name : '',
          'Milestone Name': itemData.milestone_name ? itemData.milestone_name : itemData.milestone_name,
          'Invoice Status': itemData.invoice_status ? itemData.invoice_status : '',
          'Billed by': itemData.billed_by ? itemData.billed_by : ''
        },
        commentBoxHeight: '100vh',
        commentBoxScrollHeight: '80%'
      };

      const { ChatCommentContextModalComponent } = await
        import('src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component');

      const openChatCommentContextModalComponent = this.dialog.open(ChatCommentContextModalComponent, {
        height: '100%',
        width: '65%',
        position: { right: '0px' },
        data: { modalParams: modalParams }
      });
    }

  }

  /**
   * 
   * @param item 
   * Invoice category bar click function
   */
  filterBodyData(item) {

    this.selectedItemId = item.id;
    //Reset Data 
    this.collectorItemDataCurrentIndex = 0;
    this.$onAppApiCalled.next();
    this._udrfService.udrfBodyData = [];
    this._udrfService.udrfUiData.resolveColumnConfig();
    this._udrfService.udrfData.isItemDataLoading = true;
    this._udrfService.udrfData.noItemDataFound = false;

    if (this._udrfService.udrfData.appliedConfig["activeView"].length > 0) {
      if (this._udrfService.udrfData.appliedConfig["activeView"][0].groupByView) {
        this._udrfService.groupBy = true;
        this.callgroupByData();
      }
    }
    else {
      this.getCollectorList();
    }
  }

  /**
   * MoreActions in UDRF actions
   */
  async moreActionItemClicked() {

    console.log('item clicked', this._udrfService.udrfUiData.moreActionItemClickedData)
    let menuSelected = this._udrfService.udrfUiData.moreActionItemClickedData['selectedMenu']
    let itemData = this._udrfService.udrfUiData.moreActionItemClickedData['itemData']

    //Enter paymnet
    if (menuSelected.id == 1) {

      let modalParams
      // const { PaymentEntryModalComponent } = await import('../../../../shared-components/payment-entry-modal/payment-entry-modal.component')
      await this._billedInvoicesServices.getInvoiceInformation(itemData.billing_id)
        .pipe(takeUntil(this.$onDestroy))
        .subscribe(res => {
          modalParams = {
            itemData: itemData,
            invoiceInfo: res
          }
          console.log('here', res)
          // const openPaymentEntryModalComponent = this.dialog.open(PaymentEntryModalComponent, {
          //   height: '65%',
          //   width: '65%',
          //   // position: { right: '0px' },
          //   data: { modalParams: modalParams }
          // })
        }, (err) => {
          this.snackBar.open("Error while getting invoice data !", "Dismiss", { duration: 2000, })
          console.log(err)
        })
    }
  }

  /**
   * 
   * @param str 
   * @returns String
   * Encode URI
   */
  encodeURIComponent(str) {
    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {
      return "%" + c.charCodeAt(0).toString(16);
    });
  }

  /**
   * UDRF report download
   */
  downloadItemDataReport() {

    let selectedNavItem = _.findWhere(this.sideNavData, { id: this.selectedItemId });
    this._udrfService.udrfUiData.isReportDownloading = true;
    let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));
    let filterConfig = {
      startIndex: "D",
      startDate: this._udrfService.udrfData.mainApiDateRangeStart,
      endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
      mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails,
      isItemLevelApi: false
    };

    this._billedInvoicesServices.
      getBilledInvoicesList(filterConfig,this.collectorRoleAccessData, selectedNavItem)
      .pipe(takeUntil(this.$onDestroy))
      .pipe(takeUntil(this.$onAppApiCalled)).subscribe(async res => {

        this._udrfService.udrfUiData.isReportDownloading = false;

        if (res["messType"] == "S") {
          let fileName = `Billed invoices ${moment().format('DD-MM-YY,h:mm:ss a')}`
          let excelData = res["messData"]
          this.finalExcelData=[]
          for(let l of excelData){  
            if((l.ar).length != 0){
              if(l.BillingCurrency1 != " "){
                var arInCurrency1 = _.where( l.ar,{"currency_code":l.BillingCurrency1})[0]?.value
                arInCurrency1=arInCurrency1 + " " + l.BillingCurrency1
              }
              if(l.BillingCurrency1 != l.BillingCurrency2 &&  l.BillingCurrency2 != " "){
              var arInCurrency2 = _.where( l.ar,{"currency_code":l.BillingCurrency2})[0]?.value
              arInCurrency2=arInCurrency2 + " " + l.BillingCurrency2
              }
            }
            if(!this.showInvoiceV2)  {
              this.finalExcelData.push({
                "Project Name" : l.project_name,
                "Milestone Id": l.milestone_id,
                "Item Name": l.item_name,
                "Item Id" : l.item_id,
                "Entity Name": l.entity_name,
                "PL" : l.pl,
                "Milestone Name" : l.milestone_name,
                "Billing Id" : l.billing_id,
                "AR" : JSON.stringify(l.ar),
                "AR In INR" : ((l.ar).length != 0)?_.where( l.ar,{"currency_code":"INR"})[0]?.value + " " + "INR" :"-",
                "AR In BillingCurrency1": arInCurrency1?arInCurrency1:"-",
                "AR In BillingCurrency2" : arInCurrency2?arInCurrency2:"-",
                "Total Billed Value" : JSON.stringify(l.total_billed_value),
                "Total Received Value" :l.total_received_value == 1 ? "-" : JSON.stringify(l.total_received_value),
                "CP" : l.CP,
                "CP Days" : l.cp_x_days,
                "Billed On" : l.billed_on ,
                "Aging Days" : l.aging_days ,
                "Cost Center" : l.cost_center,
                "Billed By" : l.billed_by,
                "Customer Id": l.customer_id,
                "Customer Name" : l.customer_name,
                "Planned On" : l.planned_on,
                "Default Currency AR": l.default_currency_ar,
                "Course Of Action" : l.course_of_action,
                "Service Type" : l.service_type,
                "Payment Expected On" : l.payment_expected_on,
                "Invoice Status" : l.invoice_status,
                "Customer Invoice No" : l.customer_invoice_no,
                "TDS Value": l.tds_converted_value !== '' ? JSON.stringify(l.tds_converted_value) : "-"
              })
            } 
            else{
              this.finalExcelData.push({
                "Portfolio Name": l.project_name,
                "Milestone Id": l.milestone_id,
                "Project Name": l.item_name,
                "Project Id": l.item_id,
                "Entity Name": l.entity_name,
                "PL": l.pl,
                "Milestone Name": l.milestone_name,
                "Customer Name": l.customer_name,
                "Billing Id": l.billing_id,
                "Customer Invoice No": l.customer_invoice_no,
                "Invoice Status": l.invoice_status,
                "AR": JSON.stringify(l.ar),
                "Project Currency AR": l.ar_in_project_currency ? l.ar_in_project_currency : "-",
                "Standard Currency AR": l.ar_in_standard_currency ? l.ar_in_standard_currency : "-",
                "Common Currency AR": l.ar_in_common_currency ? l.ar_in_common_currency : "-",
                "Total Billed Value": JSON.stringify(l.total_billed_value),
                "Billed Value in INR": Array.isArray(l.total_billed_value) && l.total_billed_value.length !== 0
                  ? (_.find(l.total_billed_value, { "currency_code": "INR" })?.value
                    ? new Intl.NumberFormat('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(_.find(l.total_billed_value, { "currency_code": "INR" }).value)) + " INR"
                    : "-")
                  : "-",
                "Billed Value in USD": Array.isArray(l.total_billed_value) && l.total_billed_value.length !== 0
                  ? (_.find(l.total_billed_value, { "currency_code": "USD" })?.value
                    ? new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(_.find(l.total_billed_value, { "currency_code": "USD" }).value)) + " USD"
                    : "-")
                  : "-",
                "Total Received Value": l.total_received_value == 1
                  ? "-"
                  : JSON.stringify(l.total_received_value),
                "Received Value in INR": Array.isArray(l.total_received_value) && l.total_received_value.length !== 0
                  ? (_.find(l.total_received_value, { "currency_code": "INR" })?.value
                    ? new Intl.NumberFormat('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(_.find(l.total_received_value, { "currency_code": "INR" }).value)) + " INR"
                    : "-")
                  : "-",
                "Received Value in USD": Array.isArray(l.total_received_value) && l.total_received_value.length !== 0
                  ? (_.find(l.total_received_value, { "currency_code": "USD" })?.value
                    ? new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(_.find(l.total_received_value, { "currency_code": "USD" }).value)) + " USD"
                    : "-")
                  : "-",
                "CP": l.CP,
                "CP Days": l.cp_x_days,
                "Billed On": l.billed_on,
                "Aging Days": l.aging_days,
                "Cost Center": l.cost_center,
                "Billed By": l.billed_by,
                "Customer Id": l.customer_id,
                "Planned On": l.planned_on,
                "Default Currency AR": l.default_currency_ar,
                "Course Of Action": l.course_of_action,
                "Service Type": l.service_type,
                "Payment Expected On": l.payment_expected_on,
                "TDS Value": l.tds_converted_value !== '' ? JSON.stringify(l.tds_converted_value) : "-"
              })
            }     
          }
          this._excelService.exportAsExcelFile(this.finalExcelData, fileName);
          this._utilityService.showToastMessage("Billed invoices list downloaded successfully!");
        }

        else {
          let errReportingTeams = "KEBS";
          this._utilityService.showErrorMessage(errReportingTeams, res["data"]);
        }

      }, err => {
        this._udrfService.udrfUiData.isReportDownloading = false;
        let errReportingTeams = "KEBS";
        this._utilityService.showErrorMessage(err, errReportingTeams);
      });

  }

  
  /** 
* @description getTenantInfo
*/
async getTenantInfo() {
  try {
    const tenantInfo: any = await this.tenantService.getTenantInfo();
    const invoiceTenantDetails = await this.getInvoiceTenantRoleCheckDetail(tenantInfo.tenant_name, 'Role');
    const data = invoiceTenantDetails['data'];
    if (data.hasOwnProperty('is_to_show_invoice_version') && data['is_to_show_invoice_version'] == 1) {
      this.showInvoiceV2 = true;
    }
    return "Success";
  } catch (err) {
    console.error(err);
    throw err; // Rethrow the error to handle it outside this function if needed
  }
}
/** 
* @description getInvoiceTenantRoleCheckDetails
*/
 async getInvoiceTenantRoleCheckDetail(tenantName, checkType)  {
  return new Promise((resolve, reject) => {
    this.invoiceCommonService.getInvoiceTenantCheckDetail(tenantName, checkType).subscribe(res => {
      resolve(res);
    },
      (err) => {
        console.error(err);
        reject(err);
      }
    );
  });
};

  ngOnDestroy() {
    this.$onDestroy.next();
    this.$onDestroy.complete();
    this._udrfService.resetUdrfData();

    if (this.$billedInvoicesSubscription)
      this.$billedInvoicesSubscription.unsubscribe();

    if (this.$billedInvoicesTotalSubscription)
      this.$billedInvoicesTotalSubscription.unsubscribe();
  }

}
