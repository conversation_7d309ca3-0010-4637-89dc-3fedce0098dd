(window.webpackJsonp=window.webpackJsonp||[]).push([[1017,535,631,634,858],{IRv6:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("fXoL");let r=(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-setting-header-overall"]],decls:5,vars:0,consts:[[1,"header-container"],[1,"header-title"],[1,"header-sub-title"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275text"](2,"Overall Setup"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"div",2),i["\u0275\u0275text"](4," Empower Your Experience: Customize Your Preferences for Effortless Management "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())},styles:[".header-container[_ngcontent-%COMP%]{background:#fff;padding:10px 10px 10px 15px}.header-container[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;line-height:20.83px;text-align:left;color:#111434;height:21px}.header-container[_ngcontent-%COMP%]   .header-sub-title[_ngcontent-%COMP%]{color:#8b95a5;font-family:var(--atsfontFamily);font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;text-align:left;height:16px}"]}),e})()},IeBn:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var i=n("fXoL"),r=n("3Pt+"),a=n("ofXK");const s=["inputField"];function o(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275namespaceSVG"](),i["\u0275\u0275elementStart"](0,"svg",9),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().onEnterKeyPressed()})),i["\u0275\u0275element"](1,"path",10),i["\u0275\u0275elementEnd"]()}}function l(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275namespaceSVG"](),i["\u0275\u0275elementStart"](0,"svg",11),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]();return t.searchText="",t.onEnterKeyPressed()})),i["\u0275\u0275elementStart"](1,"g",12),i["\u0275\u0275element"](2,"path",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"defs"),i["\u0275\u0275elementStart"](4,"clipPath",14),i["\u0275\u0275element"](5,"rect",15),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function c(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",19),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index;return i["\u0275\u0275nextContext"](2).onSelectRecentSearch(n)})),i["\u0275\u0275elementStart"](1,"div",20),i["\u0275\u0275namespaceSVG"](),i["\u0275\u0275elementStart"](2,"svg",21),i["\u0275\u0275elementStart"](3,"mask",22),i["\u0275\u0275element"](4,"rect",23),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"g",24),i["\u0275\u0275element"](6,"path",25),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275namespaceHTML"](),i["\u0275\u0275element"](7,"div",26),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](7),i["\u0275\u0275property"]("innerHTML",n.highlightSearch(e),i["\u0275\u0275sanitizeHtml"])}}function d(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275element"](1,"div",16),i["\u0275\u0275elementStart"](2,"span",17),i["\u0275\u0275text"](3,"Recently Searched"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](4,c,8,1,"div",18),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngForOf",e.recentSearch)}}let p=(()=>{class e{constructor(){this.onEnter=new i.EventEmitter,this.searchText=""}ngOnInit(){this.currentSearchText&&""!=this.currentSearchText&&(this.searchText=this.currentSearchText)}ngAfterViewInit(){this.inputField&&this.inputField.nativeElement&&this.inputField.nativeElement.focus()}highlightSearch(e){if(!this.searchText)return e;let t=this.searchText.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");const n=new RegExp(t,"gi");return e.replace(n,e=>`<b>${e}</b>`)}onEnterKeyPressed(){this.onEnter.emit(this.searchText.trim())}onSelectRecentSearch(e){this.searchText=this.recentSearch[e],this.onEnterKeyPressed()}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-overlay"]],viewQuery:function(e,t){if(1&e&&i["\u0275\u0275viewQuery"](s,!0),2&e){let e;i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.inputField=e.first)}},inputs:{recentSearch:"recentSearch",currentSearchText:"currentSearchText"},outputs:{onEnter:"onEnter"},decls:9,vars:4,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between"],[1,"search-bar"],["type","text","placeholder","Search Here...",3,"ngModel","ngModelChange","keydown.enter"],["inputField",""],[2,"cursor","pointer"],["width","18","height","18","viewBox","0 0 18 18","fill","none",3,"click",4,"ngIf"],["style","cursor: pointer","width","18","height","18","viewBox","0 0 18 18","fill","none",3,"click",4,"ngIf"],[4,"ngIf"],["width","18","height","18","viewBox","0 0 18 18","fill","none",3,"click"],["d","M7.01916 12.6153C5.31148 12.6153 3.86533 12.023 2.68073 10.8384C1.49612 9.65383 0.903809 8.20768 0.903809 6.49999C0.903809 4.79231 1.49612 3.34616 2.68073 2.16154C3.86533 0.976943 5.31148 0.384644 7.01916 0.384644C8.72684 0.384644 10.173 0.976943 11.3576 2.16154C12.5422 3.34616 13.1345 4.79231 13.1345 6.49999C13.1345 7.21409 13.0147 7.89614 12.7749 8.54614C12.5352 9.19614 12.2153 9.76153 11.8153 10.2423L17.5692 15.9961C17.7076 16.1346 17.7784 16.3087 17.7816 16.5183C17.7849 16.7279 17.714 16.9051 17.5692 17.05C17.4243 17.1948 17.2486 17.2673 17.0422 17.2673C16.8358 17.2673 16.6602 17.1948 16.5153 17.05L10.7615 11.2961C10.2615 11.7089 9.68646 12.032 9.03646 12.2653C8.38646 12.4987 7.71403 12.6153 7.01916 12.6153ZM7.01916 11.1154C8.30763 11.1154 9.39897 10.6683 10.2932 9.77404C11.1874 8.87981 11.6346 7.78846 11.6346 6.49999C11.6346 5.21153 11.1874 4.12018 10.2932 3.22594C9.39897 2.33171 8.30763 1.88459 7.01916 1.88459C5.73069 1.88459 4.63934 2.33171 3.74511 3.22594C2.85089 4.12018 2.40378 5.21153 2.40378 6.49999C2.40378 7.78846 2.85089 8.87981 3.74511 9.77404C4.63934 10.6683 5.73069 11.1154 7.01916 11.1154Z","fill","#45546E"],["width","18","height","18","viewBox","0 0 18 18","fill","none",2,"cursor","pointer",3,"click"],["clip-path","url(#clip0_22386_12410)"],["d","M8.00048 7.05781L11.3005 3.75781L12.2431 4.70048L8.94315 8.00048L12.2431 11.3005L11.3005 12.2431L8.00048 8.94315L4.70048 12.2431L3.75781 11.3005L7.05781 8.00048L3.75781 4.70048L4.70048 3.75781L8.00048 7.05781Z","fill","#6E7B8F"],["id","clip0_22386_12410"],["width","16","height","16","fill","white"],[1,"divider"],[1,"recent-search-title"],["class","d-flex align-items-center search-text-list",3,"click",4,"ngFor","ngForOf"],[1,"d-flex","align-items-center","search-text-list",3,"click"],[2,"margin-bottom","1px"],["width","12","height","12","viewBox","0 0 12 12","fill","none"],["id","mask0_9594_64568","maskUnits","userSpaceOnUse","x","0","y","0","width","12","height","12",2,"mask-type","alpha"],["width","12","height","12","fill","#D9D9D9"],["mask","url(#mask0_9594_64568)"],["d","M5.99166 10.25C4.90897 10.25 3.96538 9.89357 3.1609 9.18075C2.35641 8.46793 1.89328 7.57434 1.77148 6.49999H2.53685C2.66313 7.36345 3.05175 8.07931 3.70271 8.64759C4.35368 9.21586 5.11666 9.49999 5.99166 9.49999C6.96666 9.49999 7.79374 9.16041 8.47291 8.48124C9.15208 7.80207 9.49166 6.97499 9.49166 5.99999C9.49166 5.02499 9.15208 4.1979 8.47291 3.51874C7.79374 2.83957 6.96666 2.49999 5.99166 2.49999C5.44551 2.49999 4.93365 2.6213 4.45608 2.86393C3.97853 3.10655 3.56731 3.44036 3.22243 3.86536H4.53012V4.61535H1.9917V2.07691H2.74167V3.26154C3.14744 2.7827 3.63333 2.41106 4.19936 2.14664C4.76538 1.88221 5.36282 1.75 5.99166 1.75C6.5814 1.75 7.13396 1.86154 7.64935 2.08463C8.16472 2.3077 8.61408 2.6109 8.99741 2.99423C9.38074 3.37756 9.68395 3.82692 9.90702 4.3423C10.1301 4.85768 10.2416 5.41025 10.2416 5.99999C10.2416 6.58973 10.1301 7.14229 9.90702 7.65768C9.68395 8.17306 9.38074 8.62242 8.99741 9.00575C8.61408 9.38908 8.16472 9.69228 7.64935 9.91535C7.13396 10.1384 6.5814 10.25 5.99166 10.25ZM7.49262 8.01344L5.63108 6.15191V3.49999H6.38107V5.84806L8.01953 7.48654L7.49262 8.01344Z","fill","#8B95A5"],[1,"recent-search-text",3,"innerHTML"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"input",3,4),i["\u0275\u0275listener"]("ngModelChange",(function(e){return t.searchText=e}))("keydown.enter",(function(){return t.onEnterKeyPressed()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",5),i["\u0275\u0275template"](6,o,2,0,"svg",6),i["\u0275\u0275template"](7,l,6,0,"svg",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](8,d,5,1,"ng-container",8),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngModel",t.searchText),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",""==t.searchText),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.searchText&&""!=t.searchText),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.recentSearch&&t.recentSearch.length>0))},directives:[r.e,r.v,r.y,a.NgIf,a.NgForOf],styles:[".bg-container[_ngcontent-%COMP%]{width:350px;padding:8px;border:2px solid #b9c0ca;border-radius:8px;box-shadow:0 4px 8px 0 rgba(0,0,0,.25098039215686274);background-color:#fff}.bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;outline:none;border:none}.bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.bg-container[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{height:1px;width:100%;background:#dadce2;margin-bottom:8px;margin-top:8px}.bg-container[_ngcontent-%COMP%]   .search-text-list[_ngcontent-%COMP%]{cursor:pointer;gap:8px;width:-moz-fit-content;width:fit-content;margin-bottom:4px}.bg-container[_ngcontent-%COMP%]   .recent-search-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-style:italic;font-weight:400;color:#b9c0ca}.bg-container[_ngcontent-%COMP%]   .recent-search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;color:#8b95a5}"]}),e})()},Jzeh:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var i=n("yuIm"),r=n("fXoL"),a=n("ofXK");function s(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",4),r["\u0275\u0275text"](1," Oops! You're not authorized to view this content. Contact your administrator for access. "),r["\u0275\u0275elementEnd"]())}function o(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",4),r["\u0275\u0275text"](1," KEBS System is unavailable, kindly try after sometime! "),r["\u0275\u0275elementEnd"]())}let l=(()=>{class e{constructor(){this.isRDSPeak=!1}ngOnInit(){this.isRDSPeak=null==i?void 0:i.is_rds_peak}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-access-denied"]],decls:5,vars:2,consts:[[1,"bg-container"],[1,"contents"],["src","https://assets.kebs.app/ATS-noAccess.png",1,"image-styles"],["class","message",4,"ngIf"],[1,"message"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275element"](2,"img",2),r["\u0275\u0275template"](3,s,2,0,"div",3),r["\u0275\u0275template"](4,o,2,0,"div",3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",!t.isRDSPeak),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.isRDSPeak))},directives:[a.NgIf],styles:[".bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px;align-items:center;justify-content:center;background-color:#fff;height:var(--dynamicAccessDeniedHeight)}.bg-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434;text-align:center}.bg-container[_ngcontent-%COMP%]   .image-styles[_ngcontent-%COMP%]{height:200px;width:200px}.bg-container[_ngcontent-%COMP%]   .contents[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}"]}),e})()},NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},T1tr:function(e,t,n){"use strict";n.r(t),n.d(t,"UserModule",(function(){return Fe}));var i=n("ofXK"),r=n("tyNb"),a=n("mrSG"),s=n("XNiG"),o=n("1G5W"),l=n("3Pt+"),c=n("+rOU"),d=n("yuIm"),p=n("fXoL"),g=n("rDax"),u=n("rQiX"),h=n("XNFG"),m=n("XQl4"),f=n("d3UM"),v=n("FKr1"),C=n("NFeN"),x=n("kmnG"),y=n("qFsG"),w=n("TmG/"),_=n("su5B"),b=n("Jzeh"),S=n("pEYl"),O=n("/rGH");const E=["triggerDialogOverlayTemplateRef"],M=["dragDropFileInput"];function D(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",34),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const t=p["\u0275\u0275nextContext"](3);return t.openReuploadOverlay(t.triggerDialogOverlayField)})),p["\u0275\u0275text"](1," Change "),p["\u0275\u0275elementEnd"]()}}function I(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"mat-option",37),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275property"]("value",e.id),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function P(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"mat-select",35),p["\u0275\u0275listener"]("ngModelChange",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).selectedStatus=t})),p["\u0275\u0275template"](1,I,2,2,"mat-option",36),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275property"]("ngModel",e.selectedStatus),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",e.statusListDetail)}}function k(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",38),p["\u0275\u0275text"](1),p["\u0275\u0275pipe"](2,"masterData"),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275property"]("ngStyle",e.getStatusStyle(e.selectedStatus)),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind2"](2,2,e.selectedStatus,e.statusListDetail)," ")}}function F(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",39),p["\u0275\u0275elementStart"](1,"div",40),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).onClickCancel()})),p["\u0275\u0275text"](2," Cancel "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",41),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).onClickSave()})),p["\u0275\u0275text"](4," Save "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function V(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",42),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",43),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).onClickEdit()})),p["\u0275\u0275element"](2,"path",44),p["\u0275\u0275element"](3,"path",45),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function L(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",46),p["\u0275\u0275elementStart"](1,"div",47),p["\u0275\u0275elementStart"](2,"mat-icon",48),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).closeOverlay()})),p["\u0275\u0275text"](3," close "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",49),p["\u0275\u0275listener"]("fileDropped",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).dragDropHandler(t)})),p["\u0275\u0275elementStart"](5,"input",50,51),p["\u0275\u0275listener"]("change",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).selectFromBrowseHandler(t.target.files)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"div"),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](8,"svg",52),p["\u0275\u0275element"](9,"ellipse",53),p["\u0275\u0275element"](10,"ellipse",54),p["\u0275\u0275element"](11,"ellipse",55),p["\u0275\u0275element"](12,"ellipse",56),p["\u0275\u0275element"](13,"ellipse",57),p["\u0275\u0275element"](14,"path",58),p["\u0275\u0275element"](15,"path",59),p["\u0275\u0275element"](16,"path",60),p["\u0275\u0275element"](17,"path",61),p["\u0275\u0275element"](18,"path",62),p["\u0275\u0275element"](19,"path",63),p["\u0275\u0275element"](20,"path",64),p["\u0275\u0275element"](21,"path",62),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](22,"div",65),p["\u0275\u0275elementStart"](23,"span",66),p["\u0275\u0275text"](24,"Drag And Drop File Here Or"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](25,"span",67),p["\u0275\u0275text"](26,"Choose File"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](27,"div",68),p["\u0275\u0275elementStart"](28,"div",69),p["\u0275\u0275text"](29," Supported formats: image/jpeg, image/png, image/gif "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](30,"div",69),p["\u0275\u0275text"](31,"Maximum Size: 100KB"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function T(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"mat-form-field",74),p["\u0275\u0275element"](2,"input",75),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2).$implicit;p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}const U=function(){return[]};function j(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275element"](1,"app-input-search",76),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2).$implicit,t=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("list","user_role"===e.key?t.roleListDetail:p["\u0275\u0275pureFunction0"](5,U))("disableNone",!0)("placeholder",e.placeholder)("formControlName",e.key)("hideMatLabel",!0)}}const B=function(e){return{fieldKey:e}};function A(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"app-multi-select-chip",77),p["\u0275\u0275listener"]("onValueChange",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](5).onMultiSelectChipChanges(t)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2).$implicit,t=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("type",2)("placeholder",e.placeholder)("masterData","user_role"===e.key?t.roleListDetail:p["\u0275\u0275pureFunction0"](5,U))("selectedValues",t.userDetailForm.get(e.key).value)("data",p["\u0275\u0275pureFunction1"](6,B,e.key))}}function R(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",72),p["\u0275\u0275elementStart"](1,"div",32),p["\u0275\u0275text"](2),p["\u0275\u0275elementStart"](3,"span"),p["\u0275\u0275elementStart"](4,"sup",73),p["\u0275\u0275text"](5,"*"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](6,T,3,2,"div",0),p["\u0275\u0275template"](7,j,2,6,"div",0),p["\u0275\u0275template"](8,A,2,8,"div",0),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.label," "),p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("ngIf","text"===e.fieldType||"email"===e.fieldType||"phonenumber"===e.fieldType),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","single-select"===e.fieldType),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","multi-select"===e.fieldType)}}function z(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275text"](1),p["\u0275\u0275pipe"](2,"masterData"),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2).$implicit,t=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind2"](2,1,t.userDetailValue[e.key],t.roleListDetail)," ")}}function H(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2).$implicit,t=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",t.userDetailValue[e.key]," ")}}function N(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",78),p["\u0275\u0275elementStart"](1,"div",79),p["\u0275\u0275elementStart"](2,"div",32),p["\u0275\u0275text"](3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](4,z,3,4,"div",0),p["\u0275\u0275template"](5,H,2,1,"div",0),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate"](e.label),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","user_role"==e.key),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!("user_role"==e.key))}}function G(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div"),p["\u0275\u0275template"](2,R,9,4,"div",70),p["\u0275\u0275template"](3,N,6,3,"div",71),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275classMapInterpolate1"]("col-",e.col," over-all-filed "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",n.isEditMode),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!n.isEditMode)}}function X(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",80),p["\u0275\u0275elementStart"](1,"mat-form-field",74),p["\u0275\u0275element"](2,"input",81),p["\u0275\u0275elementStart"](3,"mat-icon",82),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).togglePasswordVisibility()})),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("type",e.showPassword?"text":"password")("readonly",!0),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.showPassword?"visibility":"visibility_off"," ")}}function Z(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",84),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](4).togglePasswordVisibilityForView()})),p["\u0275\u0275text"](1," View Password "),p["\u0275\u0275elementEnd"]()}}function K(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",84),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](4).togglePasswordVisibilityForView()})),p["\u0275\u0275text"](1," Hide Password "),p["\u0275\u0275elementEnd"]()}}function Q(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div"),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](3,Z,2,0,"div",83),p["\u0275\u0275template"](4,K,2,0,"div",83),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.showPasswordForView?e.userDetailForm.get("password").value:"*".repeat(e.userDetailForm.get("password").value.length)," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.showPasswordForView),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.showPasswordForView)}}const $=function(e,t,n){return[e,t,n,0,"E"]};function q(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",3),p["\u0275\u0275elementStart"](1,"div",4),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](2,"svg",5),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).goBack()})),p["\u0275\u0275element"](3,"path",6),p["\u0275\u0275element"](4,"path",7),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](5,"div",8),p["\u0275\u0275elementStart"](6,"div",9),p["\u0275\u0275elementStart"](7,"div",10),p["\u0275\u0275elementStart"](8,"div",11),p["\u0275\u0275elementStart"](9,"div",12),p["\u0275\u0275element"](10,"img",13),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](11,D,2,0,"div",14),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](12,"div",15),p["\u0275\u0275elementStart"](13,"div"),p["\u0275\u0275template"](14,P,2,2,"mat-select",16),p["\u0275\u0275template"](15,k,3,5,"div",17),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](16,"div",18),p["\u0275\u0275text"](17),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](18,"div",19),p["\u0275\u0275text"](19),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](20,"div",20),p["\u0275\u0275template"](21,F,5,0,"div",21),p["\u0275\u0275template"](22,V,4,0,"div",22),p["\u0275\u0275pipe"](23,"access"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](24,L,32,0,"ng-template",23,24,p["\u0275\u0275templateRefExtractor"]),p["\u0275\u0275elementStart"](26,"div",25),p["\u0275\u0275text"](27,"User Information"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](28,"div",26),p["\u0275\u0275elementStart"](29,"form",27),p["\u0275\u0275elementStart"](30,"div",28),p["\u0275\u0275template"](31,G,4,5,"ng-container",29),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](32,"div"),p["\u0275\u0275elementStart"](33,"div",30),p["\u0275\u0275text"](34,"Password management"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](35,"div",31),p["\u0275\u0275elementStart"](36,"div",32),p["\u0275\u0275text"](37,"Password"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](38,X,5,3,"div",33),p["\u0275\u0275template"](39,Q,5,3,"div",0),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](10),p["\u0275\u0275property"]("src",e.image,p["\u0275\u0275sanitizeUrl"]),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.isEditMode),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf",e.isEditMode),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.isEditMode),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.userDetailValue.user_name," "),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.userDetailValue.primary_email," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",e.isEditMode),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.isEditMode&&p["\u0275\u0275pipeBindV"](23,13,p["\u0275\u0275pureFunction3"](19,$,e.access.moduleId.settings,e.access.subModuleId.userSettings,e.access.subModuleId.userSettingsDetailView))),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e.triggerDialogOverlay),p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("formGroup",e.userDetailForm),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngForOf",e.formFields),p["\u0275\u0275advance"](7),p["\u0275\u0275property"]("ngIf",e.isEditMode),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.isEditMode)}}function W(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",85),p["\u0275\u0275elementStart"](2,"div",86),p["\u0275\u0275element"](3,"img",87),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",88),p["\u0275\u0275elementStart"](5,"div",89),p["\u0275\u0275text"](6,"Loading..."),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],p["\u0275\u0275sanitizeUrl"])}}function Y(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275template"](1,q,40,23,"div",2),p["\u0275\u0275template"](2,W,7,1,"ng-container",0),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.isLoading)}}function J(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",90),p["\u0275\u0275element"](1,"app-access-denied"),p["\u0275\u0275elementEnd"]())}const ee=function(e,t,n){return[e,t,n,0,"V"]};let te=(()=>{class e{constructor(e,t,n,i,r,a,o,l){this._overlay=e,this._viewContainerRef=t,this._atsMasterService=n,this._toaster=i,this._fb=r,this._settingService=a,this._activatedRoute=o,this._router=l,this._onDestroy=new s.b,this.isEditMode=!1,this.access=d,this.formFields=[],this.selectedStatus=1,this.image="assets/images/User.png",this.userDetailValue={primary_email:"",user_role:0,phone_number:"",user_name:"",password:""},this.statusListDetail=[],this.isLoading=!0,this.spinnerText="Loading...",this.uiTextConfig={},this.showPassword=!1,this.showPasswordForView=!1}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){yield this.initializePage(),yield this.getAtsMasterUiConfig("usersettingconfig"),this.userDetailForm=this._fb.group({}),yield this.getAtsFormsConfig("userSetting"),this._activatedRoute.params.subscribe(e=>{this.userId=e.userId}),yield this.getUserDetailsByUserId(this.userId),yield this.getUserMasterData(),yield this.getAllRole(),this.createForm(),this.isLoading=!1}))}onMultiSelectChipChanges(e){var t,n;null===(n=null===(t=this.userDetailForm)||void 0===t?void 0:t.get(e.data.fieldKey))||void 0===n||n.setValue(e.val)}initializePage(){return Object(a.c)(this,void 0,void 0,(function*(){this.calculateDynamicContentHeight()}))}createForm(){this.userDetailForm.addControl("password",this._fb.control(this.userDetailValue.password)),this.formFields.forEach(e=>{this.userDetailForm.addControl(e.key,this._fb.control(this.userDetailValue[e.key],[e.isMandatory?l.H.required:null,"user_name"===e.key?l.H.pattern(/^[a-zA-Z ]+$/):null,"phonenumber"===e.fieldType?l.H.pattern(/^\d{10}$/):null,"email"==e.fieldType?l.H.pattern(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/):null].filter(e=>null!==e)))})}togglePasswordVisibility(){this.showPassword=!this.showPassword}goBack(){this._router.navigate(["/main/ats/settings/user"])}togglePasswordVisibilityForView(){this.showPasswordForView=!this.showPasswordForView}getAtsMasterUiConfig(e){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsMasterUiConfig(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"usersettingconfig"==e&&(this.uiTextConfig=n.data.uiTextConfig):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}openReuploadOverlay(e){var t;if(!(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const e=this._overlay.position().global().centerHorizontally().centerVertically(),t=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:e,scrollStrategy:t,hasBackdrop:!0,panelClass:["pop-up"]});const n=new c.h(this.triggerDialogOverlayTemplateRef,this._viewContainerRef);this.overlayRef.attach(n),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}onClickSave(){return Object(a.c)(this,void 0,void 0,(function*(){this.userDetailForm.valid?(this.isLoading=!0,this.userDetailValue=this.userDetailForm.value,yield this.updateUserDetail(this.userDetailValue,this.selectedStatus,this.image,this.userId),this.isLoading=!1):Object.keys(this.userDetailForm.controls).forEach(e=>{const t=(e.charAt(0).toUpperCase()+e.slice(1)).replace(/_/g," "),n=this.userDetailForm.get(e);n.errors&&(n.errors.required?this._toaster.showError("Warning",t+" is Mandatory",7e3):n.errors.pattern&&this._toaster.showError("Warning","Invalid "+t,7e3))})}))}onClickCancel(){this.isEditMode=!1,this.image=this.currentImage,this.selectedStatus=this.currentStatus}onClickEdit(){this.userDetailForm=this._fb.group({}),this.createForm(),this.currentImage=this.image,this.currentStatus=this.selectedStatus,this.userDetailForm=this._fb.group({}),this.createForm(),this.isEditMode=!0}getUserDetailsByUserId(e){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._settingService.getUserDetailsByUserId(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?e.data?(this.userDetailValue=e.data.userDetailValue,this.status=e.data.status,this.selectedStatus=parseInt(this.status.status_id,10),this.image=null!=e.data.image?e.data.image:"assets/images/User.png",this.createForm(),t(!0)):t(!1):(this._toaster.showError("Error",e.msg,7e3),t(!1))},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS User Detail by User ID Retrieval Failed!",7e3),n()}}))}))}getUserMasterData(){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._settingService.getUserMasterData().pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?t.data?(this.statusListDetail=t.data.status_list_detail,e(!0)):e(!1):(this._toaster.showError("Error",t.msg,7e3),e(!1))},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS User Master Data Retrieval Failed!",7e3),t()}}))}))}getAtsFormsConfig(e){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsFormsConfig(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"userSetting"==e&&(this.formFields=n.data.form[0].formFields):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}dragDropHandler(e){return Object(a.c)(this,void 0,void 0,(function*(){yield this.prepareTemplateFileItem(e)}))}selectFromBrowseHandler(e){return Object(a.c)(this,void 0,void 0,(function*(){yield this.prepareTemplateFileItem(e)}))}updateImage(e){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._settingService.updateImage(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.image=e.data.fileKey,this._toaster.showSuccess("Success",e.msg,3e3)):this._toaster.showError("Error",e.msg,3e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Update Image Failed!",3e3),n()}}))}))}getAllRole(){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAllRole().pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.roleListDetail=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),t()}}))}))}updateUserDetail(e,t,n,i){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((r,a)=>this._settingService.updateUserDetail(e,t,n,i).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.isEditMode=!1,this._toaster.showSuccess("Success",e.msg,3e3)):(this.isEditMode=!1,this._toaster.showError("Error",e.msg,3e3)),r(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Update User Detail Failed!",3e3),a()}}))}))}getStatusStyle(e){if(this.statusListDetail.length>0){const t=this.statusListDetail.find(t=>t.id==e);return{color:t?null==t?void 0:t.statusColor:"","background-color":t?null==t?void 0:t.statusBgColor:""}}return{color:"","background-color":""}}prepareTemplateFileItem(e){var t;return Object(a.c)(this,void 0,void 0,(function*(){if(null!=this.showFileSizeInBytes(e[0].size)){const n=new FormData;n.append("file",e[0]),yield this.updateImage(n),null===(t=this.overlayRef)||void 0===t||t.dispose()}else this._toaster.showWarning("Warning \u26a0\ufe0f","File size is greater than 100KB",7e3)}))}showFileSizeInBytes(e,t=2){if(e>1e5)return null;if(0===e)return"0 Bytes";const n=t<=0?0:t,i=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,i)).toFixed(n))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][i]}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){return Object(a.c)(this,void 0,void 0,(function*(){this.dynamicHeight=window.innerHeight-57-56+58+"px",document.documentElement.style.setProperty("--dynamicHeight",this.dynamicHeight),this.dynamicSubHeight=window.innerHeight-57-63-128+165+"px",document.documentElement.style.setProperty("--dynamicSubHeight",this.dynamicSubHeight)}))}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](g.e),p["\u0275\u0275directiveInject"](p.ViewContainerRef),p["\u0275\u0275directiveInject"](u.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](l.i),p["\u0275\u0275directiveInject"](m.a),p["\u0275\u0275directiveInject"](r.a),p["\u0275\u0275directiveInject"](r.g))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-detail-page"]],viewQuery:function(e,t){if(1&e&&(p["\u0275\u0275viewQuery"](E,!0),p["\u0275\u0275viewQuery"](M,!0)),2&e){let e;p["\u0275\u0275queryRefresh"](e=p["\u0275\u0275loadQuery"]())&&(t.triggerDialogOverlayTemplateRef=e.first),p["\u0275\u0275queryRefresh"](e=p["\u0275\u0275loadQuery"]())&&(t.dragDropFileInput=e.first)}},hostBindings:function(e,t){1&e&&p["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,p["\u0275\u0275resolveWindow"])},decls:4,vars:22,consts:[[4,"ngIf"],["style","margin: 0px 24px",4,"ngIf"],["class","user-detail",4,"ngIf"],[1,"user-detail"],[1,"user-detail-view"],["width","20","height","25","viewBox","0 0 16 16","fill","none",1,"arrow-svg",3,"click"],["d","M6.00065 14.6668H10.0007C13.334 14.6668 14.6673 13.3335 14.6673 10.0002V6.00016C14.6673 2.66683 13.334 1.3335 10.0007 1.3335H6.00065C2.66732 1.3335 1.33398 2.66683 1.33398 6.00016V10.0002C1.33398 13.3335 2.66732 14.6668 6.00065 14.6668Z","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],["d","M8.84081 10.3532L6.49414 7.99982L8.84081 5.64648","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],[1,"profile-container"],[1,"cont-profile"],[1,"d-flex","columns"],[1,"column-one"],[1,"user-profile-photo"],[1,"profile-image",3,"src"],["class","change-profile",3,"click",4,"ngIf"],[1,"column-two"],["class","user-profile-status",3,"ngModel","ngModelChange",4,"ngIf"],["class","user-profile-status-view",3,"ngStyle",4,"ngIf"],[1,"user-profile-name"],[1,"user-profile-website"],[1,"container-two"],["class","buttons",4,"ngIf"],["class","edit-button",4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerDialogOverlayTemplateRef",""],[1,"title-user-information"],[1,"userdetail-container"],[3,"formGroup"],[1,"row"],[4,"ngFor","ngForOf"],[1,"title-user-password"],[1,"d-flex","flex-column","password-overall"],[1,"form-label"],["class","col-4 password-input-wrapper",4,"ngIf"],[1,"change-profile",3,"click"],[1,"user-profile-status",3,"ngModel","ngModelChange"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],[1,"user-profile-status-view",3,"ngStyle"],[1,"buttons"],["type","button",1,"cancel-button",3,"click"],["type","submit",1,"save-button",3,"click"],[1,"edit-button"],["width","24","height","24","viewBox","0 0 24 24","fill","none",3,"click"],["d","M11.187 4.91345L4.25979 12.2456C3.99823 12.5241 3.7451 13.0725 3.69448 13.4522L3.38229 16.1859C3.2726 17.1731 3.98135 17.8481 4.9601 17.6794L7.67698 17.2153C8.05667 17.1478 8.58823 16.8694 8.84979 16.5825L15.777 9.25032C16.9751 7.9847 17.5151 6.54188 15.6504 4.77845C13.7942 3.03188 12.3851 3.64782 11.187 4.91345Z","stroke","#8B95A5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M10.0312 6.13672C10.3941 8.46547 12.2841 10.2458 14.6297 10.482","stroke","#8B95A5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],[1,"reupload-overlay"],[1,"close-btn"],[1,"material-symbols-outlined","close-icon",3,"click"],["appDnd","",1,"d-flex","flex-column","align-items-center","justify-content-center","drop-file",3,"fileDropped"],["type","file","accept","image/*","id","dragDropFileInput",3,"change"],["dragDropFileInput",""],["width","161","height","50","viewBox","0 0 161 122","fill","none"],["cx","25.4433","cy","23.2989","rx","3.91991","ry","3.91996","fill","#EFEFEF"],["cx","126.829","cy","7.79756","rx","7.30529","ry","7.30538","fill","#EFEFEF"],["cx","26.695","cy","105.797","rx","6.94894","ry","6.94902","fill","#EFEFEF"],["cx","151.062","cy","99.0262","rx","6.94894","ry","6.94902","fill","#EFEFEF"],["cx","137.514","cy","106.866","rx","3.38538","ry","3.38542","fill","#EFEFEF"],["d","M94.0522 5.67206C93.8456 5.62812 93.8456 5.33326 94.0522 5.28932L97.1759 4.62505C97.2512 4.60904 97.3101 4.55037 97.3265 4.47514L97.999 1.37342C98.0436 1.16784 98.3369 1.16784 98.3814 1.37342L99.054 4.47514C99.0703 4.55037 99.1292 4.60904 99.2045 4.62505L102.328 5.28932C102.535 5.33326 102.535 5.62812 102.328 5.67206L99.2045 6.33633C99.1292 6.35234 99.0703 6.41101 99.054 6.48623L98.3814 9.58795C98.3369 9.79353 98.0436 9.79353 97.999 9.58795L97.3265 6.48623C97.3101 6.41101 97.2512 6.35234 97.1759 6.33633L94.0522 5.67206Z","fill","#1B2140"],["d","M9.13197 74.8365C9.21171 74.4598 9.74949 74.4598 9.82924 74.8365L11.0205 80.4644C11.0495 80.6015 11.1563 80.7089 11.2932 80.7388L16.8629 81.9521C17.2369 82.0336 17.2369 82.567 16.8629 82.6485L11.2932 83.8619C11.1563 83.8917 11.0495 83.9991 11.0205 84.1363L9.82924 89.7641C9.74949 90.1408 9.21171 90.1408 9.13197 89.7641L7.94074 84.1363C7.91171 83.9991 7.80495 83.8917 7.66796 83.8619L2.09829 82.6485C1.72429 82.567 1.72429 82.0336 2.09829 81.9521L7.66796 80.7388C7.80495 80.7089 7.91171 80.6015 7.94074 80.4644L9.13197 74.8365Z","fill","#1B2140"],["d","M139.953 24.9459C140.033 24.5692 140.571 24.5692 140.651 24.9459L141.346 28.2291C141.375 28.3662 141.481 28.4736 141.618 28.5035L144.877 29.2133C145.251 29.2948 145.251 29.8282 144.877 29.9097L141.618 30.6196C141.481 30.6494 141.375 30.7568 141.346 30.894L140.651 34.1771C140.571 34.5539 140.033 34.5539 139.953 34.1771L139.258 30.894C139.229 30.7568 139.123 30.6494 138.986 30.6196L135.727 29.9097C135.353 29.8282 135.353 29.2948 135.727 29.2133L138.986 28.5035C139.123 28.4736 139.229 28.3662 139.258 28.2291L139.953 24.9459Z","fill","#1B2140"],["d","M154.325 77.584L155.417 82.7396L160.499 83.8467L155.417 84.9538L154.325 90.1094L153.234 84.9538L148.152 83.8467L153.234 82.7396L154.325 77.584Z","fill","white"],["d","M101.335 88.8411L85.0013 72.5078L68.668 88.8411","stroke","black","stroke-opacity","0.4","stroke-width","2","stroke-linecap","round","stroke-linejoin","round"],["d","M85 72.5078V109.258","stroke","black","stroke-opacity","0.4","stroke-width","2","stroke-linecap","round","stroke-linejoin","round"],["d","M119.26 98.6C123.243 96.4288 126.389 92.9931 128.202 88.8353C130.015 84.6774 130.392 80.0341 129.273 75.6382C128.154 71.2423 125.604 67.3442 122.023 64.5591C118.443 61.774 114.037 60.2605 109.501 60.2575H104.356C103.12 55.4769 100.816 51.0387 97.6182 47.2766C94.4201 43.5145 90.4107 40.5263 85.8915 38.5367C81.3723 36.5471 76.4608 35.6079 71.5264 35.7897C66.592 35.9715 61.763 37.2696 57.4024 39.5863C53.0419 41.9031 49.2632 45.1782 46.3506 49.1655C43.438 53.1528 41.4673 57.7485 40.5864 62.6071C39.7056 67.4657 39.9377 72.4607 41.2652 77.2167C42.5927 81.9727 44.9812 86.3658 48.2509 90.0659","stroke","black","stroke-opacity","0.4","stroke-width","2","stroke-linecap","round","stroke-linejoin","round"],[1,"d-flex","align-items-center",2,"gap","4px"],[1,"text-1"],["for","dragDropFileInput",1,"text-2"],[1,"d-flex","align-items-center","justify-content-between","mini-content"],[1,"text"],["class","d-flex flex-column",4,"ngIf"],["class","view",4,"ngIf"],[1,"d-flex","flex-column"],[1,"required-field"],["appearance","outline","color","warn",1,"mat-form-field"],["matInput","",3,"placeholder","formControlName"],[1,"userDetailDropDown",3,"list","disableNone","placeholder","formControlName","hideMatLabel"],[3,"type","placeholder","masterData","selectedValues","data","onValueChange"],[1,"view"],[1,"label-value-container"],[1,"col-4","password-input-wrapper"],["matInput","","formControlName","password",3,"type","readonly"],[1,"visibility",3,"click"],["class","view-password",3,"click",4,"ngIf"],[1,"view-password",3,"click"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"],[2,"margin","0px 24px"]],template:function(e,t){1&e&&(p["\u0275\u0275template"](0,Y,3,2,"div",0),p["\u0275\u0275pipe"](1,"access"),p["\u0275\u0275template"](2,J,2,0,"div",1),p["\u0275\u0275pipe"](3,"access")),2&e&&(p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBindV"](1,2,p["\u0275\u0275pureFunction3"](14,ee,t.access.moduleId.settings,t.access.subModuleId.userSettings,t.access.subModuleId.userSettingsDetailView))),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",!p["\u0275\u0275pipeBindV"](3,8,p["\u0275\u0275pureFunction3"](18,ee,t.access.moduleId.settings,t.access.subModuleId.userSettings,t.access.subModuleId.userSettingsDetailView))))},directives:[i.NgIf,g.a,l.J,l.w,l.n,i.NgForOf,f.c,l.v,l.y,v.p,i.NgStyle,C.a,x.c,y.b,l.e,l.l,w.a,_.a,b.a],pipes:[S.a,O.a],styles:['.over-all-filed[_ngcontent-%COMP%]{padding:10px}.userdetail-container[_ngcontent-%COMP%]{padding:8px 20px 20px}.mat-form-field[_ngcontent-%COMP%]{width:100%}.form-label[_ngcontent-%COMP%]{font-size:11px;font-weight:400;color:#5f6c81}.form-label[_ngcontent-%COMP%], input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);line-height:16px;letter-spacing:.02em}input[_ngcontent-%COMP%]::placeholder{font-size:12px;font-weight:200;color:#45546e}.title-user-information[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;line-height:18.23px;color:var(--atsprimaryColor);padding-left:29px;padding-top:15px}.label-value-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.required-field[_ngcontent-%COMP%]{color:red;font-size:15px;top:0}.loading-img[_ngcontent-%COMP%]{height:var(--dynamicHeight);flex-direction:column;background-color:#fff}.loading-img[_ngcontent-%COMP%], .loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{vertical-align:center}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}.buttons[_ngcontent-%COMP%]{gap:20px}.buttons[_ngcontent-%COMP%], .edit-button[_ngcontent-%COMP%]{display:flex;position:relative;align-items:center;justify-content:center}.edit-button[_ngcontent-%COMP%]{cursor:pointer}.cancel-button[_ngcontent-%COMP%]{text-align:left;color:#45546e;border-radius:5px;border:1px solid #45546e}.cancel-button[_ngcontent-%COMP%], .save-button[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;line-height:16px;letter-spacing:-.02em;height:40px;padding:10px;justify-content:center;align-items:center;display:flex;width:64px;cursor:pointer}.save-button[_ngcontent-%COMP%]{border-radius:5px;color:#fff;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%)}.profile-container[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);padding:20px 20px 0;position:relative;display:flex;text-wrap:nowrap}.cont-profile[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%}.columns[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);padding:0 10px 10px 0}.column-one[_ngcontent-%COMP%]{display:grid;gap:3px}.column-two[_ngcontent-%COMP%]{margin-left:20px;display:grid}.change-profile[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);align-items:center;display:flex;cursor:pointer;color:#1890ff;justify-content:center;font-size:11px;font-weight:400;letter-spacing:.02em}.user-profile-name[_ngcontent-%COMP%]{color:#111434;font-size:16px;font-weight:700;line-height:20.83px}.user-profile-name[_ngcontent-%COMP%], .user-profile-status[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);text-align:left}.user-profile-status[_ngcontent-%COMP%]{background-color:#eef9e8;color:#52c41a!important;width:80px}.user-profile-status[_ngcontent-%COMP%], .user-profile-status-view[_ngcontent-%COMP%]{font-size:12px;font-weight:500;line-height:16.8px;letter-spacing:.02em;height:21px;padding:2px 6px;gap:10px;border-radius:4px}.user-profile-status-view[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);text-align:left;width:-moz-fit-content;width:fit-content}a[_ngcontent-%COMP%]{color:#1890ff;text-decoration:underline;font-size:14px;font-weight:400;line-height:18.23px}.mat-option[_ngcontent-%COMP%]{outline:none!important;border-bottom:none!important;max-height:none!important}.green-option.mat-selected[_ngcontent-%COMP%]{color:#52c41a!important}.red-option.mat-selected[_ngcontent-%COMP%]{color:red}[_nghost-%COMP%]     .green-option.mat-select-value-text{color:#52c41a!important}[_nghost-%COMP%]     .red-option.mat-select-value-text{color:red!important}.reupload-overlay[_ngcontent-%COMP%]{background:#fff;padding:10px 32px 32px;border-radius:8px;width:600px}.reupload-overlay[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]{text-align:end;padding-bottom:10px}.reupload-overlay[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{cursor:pointer}.mini-content[_ngcontent-%COMP%]{width:98%}.mini-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--fontFamily);font-size:12px;font-weight:400;color:#7d838b;padding-top:7px}.drop-file[_ngcontent-%COMP%]{width:98%;height:150px;border:2px dashed #7d838b;border-radius:12px;gap:10px;cursor:pointer;padding:15px;position:relative}.drop-file[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{cursor:pointer;opacity:0;position:absolute;z-index:2;width:100%;height:100%;top:0;left:0}.drop-file[_ngcontent-%COMP%]   .text-1[_ngcontent-%COMP%]{font-weight:400;color:#7d838b}.drop-file[_ngcontent-%COMP%]   .text-1[_ngcontent-%COMP%], .drop-file[_ngcontent-%COMP%]   .text-2[_ngcontent-%COMP%]{font-family:var(--fontFamily);font-size:12px}.drop-file[_ngcontent-%COMP%]   .text-2[_ngcontent-%COMP%]{font-weight:500;color:#111434;text-decoration:underline}.profile-image[_ngcontent-%COMP%]{height:60px;width:60px;border-radius:50%}.title-user-password[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;line-height:18.23px;color:var(--atsprimaryColor);padding-left:10px;padding-top:15px}.user-detail[_ngcontent-%COMP%]{padding:15px;background:#f1f3f8;height:var(--dynamicHeight);overflow:hidden}.user-detail-view[_ngcontent-%COMP%]{width:100%;height:var(--dynamicSubHeight);background-color:#fff;overflow:auto;padding:15px}.password-overall[_ngcontent-%COMP%]{padding:15px}.password-input-wrapper[_ngcontent-%COMP%]{position:relative;padding:0}.mat-icon.visibility[_ngcontent-%COMP%]{cursor:pointer;position:absolute;right:10px;top:50%;transform:translateY(-50%);color:rgba(0,0,0,.54)}.mat-icon.visibility[_ngcontent-%COMP%]:hover{color:rgba(0,0,0,.87)}.arrow-svg[_ngcontent-%COMP%]{margin-top:2px;cursor:pointer;position:absolute;z-index:1}.view-password[_ngcontent-%COMP%]{color:#1890ff;cursor:pointer;font-family:var(--atsfontFamily);font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;text-decoration:underline;-webkit-text-decoration-color:#1890ff;text-decoration-color:#1890ff;width:-moz-fit-content;width:fit-content}']}),e})();var ne=n("1A3m"),ie=n("0IaG"),re=n("IRv6"),ae=n("vzmP"),se=n("IeBn");const oe=["triggerSearchBarTemplateRef"],le=["triggerSearchBar"];function ce(e,t){if(1&e&&(p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](0,"div",18),p["\u0275\u0275elementStart"](1,"div",19),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",20),p["\u0275\u0275text"](4," Update your personal information such as name, email, and contact details. "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"]("",e.userList.length," Active users")}}function de(e,t){1&e&&(p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](0,"div",18),p["\u0275\u0275elementStart"](1,"div",19),p["\u0275\u0275text"](2,"No user has been added..!"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",20),p["\u0275\u0275text"](4," Add a user by clicking on the '+ New User' button "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function pe(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",21,22),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const t=p["\u0275\u0275reference"](2);return p["\u0275\u0275nextContext"](3).openSearchBarOverlay(t,-37,!1)})),p["\u0275\u0275elementStart"](3,"div",23),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"div",24),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](6,"svg",25),p["\u0275\u0275element"](7,"path",26),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate"](e.searchParams)}}function ge(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",27,22),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const t=p["\u0275\u0275reference"](2),n=p["\u0275\u0275nextContext"](3);return n.openSearchBarOverlay(t,-30,n.isCreateAccessAvailable?-325:25)})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](3,"svg",25),p["\u0275\u0275element"](4,"path",26),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function ue(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",28),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).openCreateUser()})),p["\u0275\u0275text"](1," + New User "),p["\u0275\u0275elementEnd"]()}}function he(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",29),p["\u0275\u0275elementStart"](1,"app-list-view",30),p["\u0275\u0275listener"]("onSort",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).onSortColumn(t)}))("onClick",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).onClickRowData(t)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("list",e.userList)("fieldConfig",e.fieldConfig)("variant",e.variant)("totalCount",e.userList.length)("isCheckboxActive",!1)}}function me(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",35),p["\u0275\u0275text"](1," Start By Creating A New User. "),p["\u0275\u0275elementEnd"]())}function fe(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",31),p["\u0275\u0275element"](1,"img",32),p["\u0275\u0275elementStart"](2,"div",33),p["\u0275\u0275text"](3,"No User Found"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](4,me,2,0,"div",34),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("ngIf",""==e.searchParams)}}function ve(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",31),p["\u0275\u0275element"](1,"img",36),p["\u0275\u0275elementStart"](2,"div"),p["\u0275\u0275text"](3,"No Result Found"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div"),p["\u0275\u0275text"](5,"Sorry, We Couldn't Find Any Matches For Your Search."),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}const Ce=function(){return[]};function xe(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"app-search-overlay",37),p["\u0275\u0275listener"]("onEnter",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).onEnterSearch(t)})),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275property"]("currentSearchText",e.searchParams)("recentSearch",p["\u0275\u0275pureFunction0"](2,Ce))}}const ye=function(e,t){return[e,t,0,0,"C"]};function we(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275element"](1,"app-setting-header-overall"),p["\u0275\u0275elementStart"](2,"div",2),p["\u0275\u0275elementStart"](3,"div",3),p["\u0275\u0275elementStart"](4,"div",4),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](5,"svg",5),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).goBack()})),p["\u0275\u0275element"](6,"path",6),p["\u0275\u0275element"](7,"path",7),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](8,ce,5,1,"div",8),p["\u0275\u0275template"](9,de,5,0,"div",8),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](10,"div",9),p["\u0275\u0275elementStart"](11,"div",10),p["\u0275\u0275template"](12,pe,8,1,"div",11),p["\u0275\u0275template"](13,ge,5,0,"div",12),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](14,ue,2,0,"div",13),p["\u0275\u0275pipe"](15,"access"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](16,he,2,5,"div",14),p["\u0275\u0275template"](17,fe,5,1,"div",15),p["\u0275\u0275template"](18,ve,6,0,"div",15),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](19,xe,1,3,"ng-template",16,17,p["\u0275\u0275templateRefExtractor"]),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](8),p["\u0275\u0275property"]("ngIf",e.userList.length>0||""!=e.searchParams),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",0==e.userList.length&&""==e.searchParams),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf",""!=e.searchParams&&null!=e.searchParams),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",""==e.searchParams||null==e.searchParams),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBindV"](15,9,p["\u0275\u0275pureFunction2"](15,ye,e.access.moduleId.settings,e.access.subModuleId.userSettings))),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",e.userList.length>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",0==e.userList.length&&""==e.searchParams),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",0==e.userList.length&&""!=e.searchParams),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e.triggerSearchBar)}}function _e(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",38),p["\u0275\u0275elementStart"](2,"div",39),p["\u0275\u0275element"](3,"img",40),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",41),p["\u0275\u0275elementStart"](5,"div",42),p["\u0275\u0275text"](6,"Loading..."),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],p["\u0275\u0275sanitizeUrl"])}}function be(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275template"](1,we,21,18,"div",0),p["\u0275\u0275template"](2,_e,7,1,"ng-container",0),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.isLoading)}}function Se(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",43),p["\u0275\u0275element"](1,"app-access-denied"),p["\u0275\u0275elementEnd"]())}const Oe=function(e,t){return[e,t,0,0,"V"]},Ee=[{path:"",component:(()=>{class e{constructor(e,t,n,i,r,a,o,l,c,p){this._settingService=e,this._toaster=t,this._overlay=n,this._viewContainerRef=i,this._atsMasterService=r,this._dialog=a,this._route=o,this._router=l,this._settingsService=c,this.cdr=p,this._onDestroy=new s.b,this.count=0,this.access=d,this.isLoading=!0,this.searchParams="",this.userList=[],this.fieldConfig=[],this.variant=1,this.uiTextConfig={},this.selectedViewType=1,this.sort=[],this.spinnerText="Loading...",this.isCreateAccessAvailable=!1}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){yield this.initializePage(),yield this.getAtsMasterUiConfig("usersettingconfig"),yield this.getAllUserDetails(this.sort,this.searchParams),this.isLoading=!1,this.cdr.detectChanges(),this.isCreateAccessAvailable=d.checkAccessForGeneralRole(d.moduleId.settings,d.subModuleId.userSettings,0,0,"C")}))}toggleSelectedViewType(e){return Object(a.c)(this,void 0,void 0,(function*(){this.selectedViewType=e.value}))}initializePage(){return Object(a.c)(this,void 0,void 0,(function*(){this.calculateDynamicContentHeight()}))}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}onEnterSearch(e){return Object(a.c)(this,void 0,void 0,(function*(){this.searchParams=e,this.closeOverlay(),this.isLoading=!0,yield this.getAllUserDetails(this.sort,this.searchParams),this.isLoading=!1}))}openCreateUser(){return Object(a.c)(this,void 0,void 0,(function*(){const{CreateUserComponent:e}=yield n.e(930).then(n.bind(null,"U1l8"));this._dialog.open(e,{width:"760px",height:"540px",disableClose:!0,data:""}).afterClosed().subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){e&&(this.isLoading=!0,yield this.getAllUserDetails(this.sort,this.searchParams),this.isLoading=!1)})))}))}openSearchBarOverlay(e,t,n){var i;if(!(null===(i=this.overlayRef)||void 0===i?void 0:i.hasAttached())){const i=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"}]);i.withDefaultOffsetY(t),n&&i.withDefaultOffsetX(n);const r=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:i,scrollStrategy:r,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const a=new c.h(this.triggerSearchBarTemplateRef,this._viewContainerRef);this.overlayRef.attach(a),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}goBack(){this._router.navigate(["/main/ats/settings/"])}onResize(){this.calculateDynamicContentHeight()}onClickRowData(e){return Object(a.c)(this,void 0,void 0,(function*(){e.functionName&&""!=e.functionName&&(yield this[e.functionName](e.data))}))}openUserDetailView(e){return Object(a.c)(this,void 0,void 0,(function*(){if(!d.checkAccessForGeneralRole(d.moduleId.settings,d.subModuleId.userSettings,d.sectionId.userSettingsDetailView,0,"V"))return this._toaster.showInfo("Access Restricted!","Oops! You're not authorized to view this content. Contact your administrator for access.",7e3);this._router.navigate([e.userId],{relativeTo:this._route})}))}getAllUserDetails(e,t){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((n,i)=>this._settingService.getAllUserDetails(e,t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.userList=e.data:this._toaster.showError("Error",e.msg,7e3),n(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS User Data Retrieval Failed!",7e3),this.isLoading=!1,i()}}))}))}onSortColumn(e){return Object(a.c)(this,void 0,void 0,(function*(){let t=this.fieldConfig[e.fieldConfigIndex].sortOrder,n=e.sortOrder;this.fieldConfig.forEach(e=>{e.sortOrder=0}),this.sort=[],t==n?this.fieldConfig[e.fieldConfigIndex].sortOrder=0:(this.fieldConfig[e.fieldConfigIndex].sortOrder=n,this.sort.push(this.fieldConfig[e.fieldConfigIndex])),this.userList=[],this.isLoading=!0,yield this.getAllUserDetails(this.sort,this.searchParams),this.isLoading=!1}))}getAtsMasterUiConfig(e){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsMasterUiConfig(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"usersettingconfig"==e&&(this.variant=n.data.variant,this.fieldConfig=n.data.fieldConfig,this.uiTextConfig=n.data.uiTextConfig):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}calculateDynamicContentHeight(){this.dynamicHeight=window.innerHeight-57-56+"px",document.documentElement.style.setProperty("--dynamicHeight",this.dynamicHeight),this.dynamicSubHeight=window.innerHeight-57-63-90+"px",document.documentElement.style.setProperty("--dynamicSubHeight",this.dynamicSubHeight)}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](m.a),p["\u0275\u0275directiveInject"](ne.a),p["\u0275\u0275directiveInject"](g.e),p["\u0275\u0275directiveInject"](p.ViewContainerRef),p["\u0275\u0275directiveInject"](u.a),p["\u0275\u0275directiveInject"](ie.b),p["\u0275\u0275directiveInject"](r.a),p["\u0275\u0275directiveInject"](r.g),p["\u0275\u0275directiveInject"](m.a),p["\u0275\u0275directiveInject"](p.ChangeDetectorRef))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],viewQuery:function(e,t){if(1&e&&(p["\u0275\u0275viewQuery"](oe,!0),p["\u0275\u0275viewQuery"](le,!0)),2&e){let e;p["\u0275\u0275queryRefresh"](e=p["\u0275\u0275loadQuery"]())&&(t.triggerSearchBarTemplateRef=e.first),p["\u0275\u0275queryRefresh"](e=p["\u0275\u0275loadQuery"]())&&(t.triggerSearchBar=e.first)}},hostBindings:function(e,t){1&e&&p["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,p["\u0275\u0275resolveWindow"])},decls:4,vars:20,consts:[[4,"ngIf"],["style","margin: 0px 24px",4,"ngIf"],[1,"users"],[1,"container-box"],[1,"active-user"],["width","16","height","16","viewBox","0 0 16 16","fill","none",1,"arrow-svg",3,"click"],["d","M6.00065 14.6668H10.0007C13.334 14.6668 14.6673 13.3335 14.6673 10.0002V6.00016C14.6673 2.66683 13.334 1.3335 10.0007 1.3335H6.00065C2.66732 1.3335 1.33398 2.66683 1.33398 6.00016V10.0002C1.33398 13.3335 2.66732 14.6668 6.00065 14.6668Z","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],["d","M8.84081 10.3532L6.49414 7.99982L8.84081 5.64648","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],["class","active-user-lines",4,"ngIf"],[1,"d-flex","search-and-button"],[1,"d-flex","align-items-center"],["class","d-flex align-items-center justify-content-between search-ui","cdkOverlayOrigin","",3,"click",4,"ngIf"],["class","header-icon","cdkOverlayOrigin","",3,"click",4,"ngIf"],["class","new-user",3,"click",4,"ngIf"],["class","main-container-list-view",4,"ngIf"],["class","no-user-container",4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerSearchBarTemplateRef",""],[1,"active-user-lines"],[1,"line1"],[1,"line2"],["cdkOverlayOrigin","",1,"d-flex","align-items-center","justify-content-between","search-ui",3,"click"],["triggerSearchBar","cdkOverlayOrigin","triggerSearchField",""],[1,"search-text"],[1,"header-icon"],["width","18","height","18","viewBox","0 0 18 18","fill","none"],["d","M7.01916 12.6153C5.31148 12.6153 3.86533 12.023 2.68073 10.8384C1.49612 9.65383 0.903809 8.20768 0.903809 6.49999C0.903809 4.79231 1.49612 3.34616 2.68073 2.16154C3.86533 0.976943 5.31148 0.384644 7.01916 0.384644C8.72684 0.384644 10.173 0.976943 11.3576 2.16154C12.5422 3.34616 13.1345 4.79231 13.1345 6.49999C13.1345 7.21409 13.0147 7.89614 12.7749 8.54614C12.5352 9.19614 12.2153 9.76153 11.8153 10.2423L17.5692 15.9961C17.7076 16.1346 17.7784 16.3087 17.7816 16.5183C17.7849 16.7279 17.714 16.9051 17.5692 17.05C17.4243 17.1948 17.2486 17.2673 17.0422 17.2673C16.8358 17.2673 16.6602 17.1948 16.5153 17.05L10.7615 11.2961C10.2615 11.7089 9.68646 12.032 9.03646 12.2653C8.38646 12.4987 7.71403 12.6153 7.01916 12.6153ZM7.01916 11.1154C8.30763 11.1154 9.39897 10.6683 10.2932 9.77404C11.1874 8.87981 11.6346 7.78846 11.6346 6.49999C11.6346 5.21153 11.1874 4.12018 10.2932 3.22594C9.39897 2.33171 8.30763 1.88459 7.01916 1.88459C5.73069 1.88459 4.63934 2.33171 3.74511 3.22594C2.85089 4.12018 2.40378 5.21153 2.40378 6.49999C2.40378 7.78846 2.85089 8.87981 3.74511 9.77404C4.63934 10.6683 5.73069 11.1154 7.01916 11.1154Z","fill","#45546E"],["cdkOverlayOrigin","",1,"header-icon",3,"click"],[1,"new-user",3,"click"],[1,"main-container-list-view"],[3,"list","fieldConfig","variant","totalCount","isCheckboxActive","onSort","onClick"],[1,"no-user-container"],["src","https://assets.kebs.app/ATS-Empty-State.png"],[1,"no-vendor-title"],["class","no-vendor-description",4,"ngIf"],[1,"no-vendor-description"],["src","https://assets.kebs.app/ats-no-search-results-found.png","alt",""],[3,"currentSearchText","recentSearch","onEnter"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"],[2,"margin","0px 24px"]],template:function(e,t){1&e&&(p["\u0275\u0275template"](0,be,3,2,"div",0),p["\u0275\u0275pipe"](1,"access"),p["\u0275\u0275template"](2,Se,2,0,"div",1),p["\u0275\u0275pipe"](3,"access")),2&e&&(p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBindV"](1,2,p["\u0275\u0275pureFunction2"](14,Oe,t.access.moduleId.settings,t.access.subModuleId.userSettings))),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",!p["\u0275\u0275pipeBindV"](3,8,p["\u0275\u0275pureFunction2"](17,Oe,t.access.moduleId.settings,t.access.subModuleId.userSettings))))},directives:[i.NgIf,re.a,g.a,g.b,ae.a,se.a,b.a],pipes:[S.a],styles:['.users[_ngcontent-%COMP%]{padding:15px;background:#f1f3f8;height:var(--dynamicHeight);overflow:hidden}.active-user[_ngcontent-%COMP%]{display:flex}.active-user-lines[_ngcontent-%COMP%]{margin-left:10px}.line1[_ngcontent-%COMP%]{white-space:nowrap;font-size:16px;font-weight:700;line-height:20.83px;color:#111434;width:150px;height:21px}.line1[_ngcontent-%COMP%], .line2[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);text-align:left}.line2[_ngcontent-%COMP%]{color:#8b95a5;font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;width:-moz-fit-content;width:fit-content;height:16px}.new-user[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#45546e;padding:8px 12px;gap:8px;border-radius:10px;margin-left:30px;border:1px solid #5f6c81;cursor:pointer}.search-ui[_ngcontent-%COMP%]{width:350px;height:36px;padding:0 8px;border:2px solid #dadce2;border-radius:8px;cursor:text}.search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#5f6c81;width:280px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.header-icon[_ngcontent-%COMP%]{cursor:pointer}.main-container-list-view[_ngcontent-%COMP%]{max-height:var(--dynamicSubHeight);display:flex;flex-direction:column}.container-box[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:15px;background:#f9fafc}.loading-img[_ngcontent-%COMP%]{height:var(--dynamicHeight);flex-direction:column;background-color:#fff}.loading-img[_ngcontent-%COMP%], .loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{vertical-align:center}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}.arrow-svg[_ngcontent-%COMP%]{margin-top:2px;cursor:pointer}.mat-dialog-container[_ngcontent-%COMP%]{position:relative}.no-user-container[_ngcontent-%COMP%]{height:var(--dynamicSubHeight);justify-content:center;width:100%;display:flex;align-items:center;font-family:var(--atsfontFamily);flex-direction:column}']}),e})()},{path:":userId",component:te}];let Me=(()=>{class e{}return e.\u0275mod=p["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=p["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(Ee)],r.k]}),e})();var De=n("Xi0T"),Ie=n("Qu3c"),Pe=n("vxfF"),ke=n("1+mW");let Fe=(()=>{class e{}return e.\u0275mod=p["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=p["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,Me,c.g,g.h,C.b,De.a,f.d,Ie.b,y.c,l.p,l.E,Pe.g,x.e,ke.ApplicantTrackingSystemModule]]}),e})()},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return C}));var i=n("fXoL"),r=n("3Pt+"),a=n("jtHE"),s=n("XNiG"),o=n("NJ67"),l=n("1G5W"),c=n("kmnG"),d=n("ofXK"),p=n("d3UM"),g=n("FKr1"),u=n("WJ5W"),h=n("Qu3c");function m(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function f(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function v(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let C=(()=>{class e extends o.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new r.j,this.fieldFilterCtrl=new r.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new a.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new s.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,m,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,f,2,2,"mat-option",5),i["\u0275\u0275template"](7,v,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[c.c,d.NgIf,p.c,r.v,r.k,r.F,g.p,u.a,d.NgForOf,c.g,h.a],pipes:[d.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},XQl4:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("fXoL"),r=n("tk/3");let a=(()=>{class e{constructor(e){this._http=e}createTemplate(e){return this._http.post("api/ats/candidate/createTemplate",{templateData:e})}updateTemplate(e){return this._http.post("api/ats/candidate/updateTemplate",{templateData:e})}deleteTemplate(e){return this._http.post("api/ats/candidate/deleteTemplates",{templateIds:[e]})}fetchTemplate(e){return this._http.post("api/ats/candidate/fetchTemplate",{templateId:e})}fetchAllTemplates(e){return this._http.post("api/ats/candidate/fetchAllTemplates",{searchParams:e})}getAllCollegeDetails(e,t){return this._http.post("api/ats/settings/getAllCollegeDetails",{sort:e,searchParams:t})}getCollegeCount(){return this._http.post("api/ats/settings/getCollegeCount",{})}getCollegeDetailsByCollegeId(e){return this._http.post("api/ats/settings/getCollegeDetailsByCollegeId",{collegeId:e})}createCollege(e){return this._http.post("api/ats/settings/createCollege",{collegeBasicDetails:e})}updateImage(e){return this._http.post("api/ats/settings/updateImage",e)}getCollegeMasterData(){return this._http.post("api/ats/settings/getCollegeMasterData",{})}updateCollegeDetail(e,t,n){return this._http.post("api/ats/settings/updateCollegeDetail",{collegeDetail:e,status:t,image:n})}getCollegeEligibilityDetails(e){return this._http.post("api/ats/settings/getCollegeEligibilityDetails",{collegeId:e})}updateAttachment(e){return this._http.post("api/ats/settings/updateAttachment",e)}updateEligibilityDetails(e){return this._http.post("api/ats/settings/updateEligibilityDetails",{eligibilityDetails:e})}getDegreeMasterForCollege(){return this._http.post("api/ats/settings/getDegreeMasterForCollege",{})}getCourseMasterForCollege(){return this._http.post("api/ats/settings/getCourseMasterForCollege",{})}getStageCategory(){return this._http.post("api/ats/masterService/getStageCategory",{})}getTemplateModuleType(){return this._http.post("api/ats/masterService/getTemplateModuleType",{})}fetchCollegeUserDetails(e){return this._http.post("api/ats/settings/fetchCollegeUserDetails",{collegeId:e})}assignUserToCollege(e,t){return this._http.post("api/ats/settings/assignUserToCollege",{collegeId:e,users:t})}fetchCollegeUserDetailById(e){return this._http.post("api/ats/settings/fetchCollegeUserDetailById",{userId:e})}updateCollegeUserDetails(e,t,n){return this._http.post("api/ats/settings/updateCollegeUserDetails",{collegeId:e,userId:t,user:n})}deleteUserById(e){return this._http.post("api/ats/settings/deleteUserById",{userId:e})}generatePassword(){return this._http.post("api/ats/settings/generatePassword",{})}getAllUserDetails(e,t){return this._http.post("api/ats/settings/getAllUserDetails",{sort:e,searchParams:t})}getUserCount(){return this._http.post("api/ats/settings/getUserCount",{})}getUserMasterData(){return this._http.post("api/ats/settings/getUserMasterData",{})}createUser(e,t,n){return this._http.post("api/ats/settings/createUser",{createUserValue:e,hashedPassword:t,domain:n})}getUserDetailsByUserId(e){return this._http.post("api/ats/settings/getUserDetailsByUserId",{userId:e})}updateUserDetail(e,t,n,i){return this._http.post("api/ats/settings/updateUserDetail",{UserDetail:e,status:t,image:n,UserId:i})}getAllVendorDetails(e,t){return this._http.post("api/ats/settings/getAllVendorDetails",{sort:e,searchParams:t})}createVendor(e){return this._http.post("api/ats/settings/createVendor",{vendor_basic_details:e})}getVendorById(e){return this._http.post("api/ats/settings/getVendorById",{vendor_id:e})}updateVendorDetail(e,t,n){return this._http.post("api/ats/settings/updateVendorDetail",{vendorDetailValue:e,image:t,status:n})}fetchVendorUserDetails(e){return this._http.post("api/ats/settings/fetchVendorUserDetails",{vendorId:e})}assignUserToVendor(e,t,n){return this._http.post("api/ats/settings/assignUserToVendor",{vendorId:e,users:t,domain:n})}updateVendorUserDetails(e,t,n){return this._http.post("api/ats/settings/updateVendorUserDetails",{vendorId:e,userId:t,user:n})}fetchVendorUserDetailById(e){return this._http.post("api/ats/settings/fetchVendorUserDetailById",{userId:e})}deleteVendorUserById(e,t){return this._http.post("api/ats/settings/deleteVendorUserById",{userId:e,vendorId:t})}getProfileById(){return this._http.post("api/ats/settings/getProfileById",{})}updateProfileDetail(e,t,n){return this._http.post("api/ats/settings/updateProfileDetail",{generalDetailValue:e,image:t,status:n})}integrateOutlook(e,t){return this._http.post("api/ats/settings/integrateOutlook",{code:e,redirectUri:t})}integrateGoogle(e,t){return this._http.post("api/ats/settings/integrateGoogle",{code:e,redirectUri:t})}connectOutlookCalendar(e,t){return this._http.post("api/ats/settings/connectOutlookCalendar",{code:e,redirectUri:t})}getOrganizationDetail(){return this._http.post("api/ats/settings/getOrganizationDetail",{})}updateOrganizationDetail(e,t){return this._http.post("api/ats/settings/updateOrganizationDetail",{companyDetailValue:e,image:t})}getUserCalendarDetail(){return this._http.post("api/ats/settings/getUserCalendarDetail",{})}getOrganizationDetailsForPreview(){return this._http.post("api/ats/settings/getOrganizationDetailsForPreview",{})}createDegreeDetails(e){return this._http.post("api/ats/settings/createDegreeDetails",{degreeDetails:e})}updateDegreeDetails(e){return this._http.post("api/ats/settings/updateDegreeDetails",{degreeDetails:e})}createCourseDetails(e){return this._http.post("api/ats/settings/createCourseDetails",{courseDetails:e})}updateCourseDetails(e){return this._http.post("api/ats/settings/updateCourseDetails",{courseDetails:e})}createCourseDegreeMappingDetails(e){return this._http.post("api/ats/settings/createCourseDegreeMappingDetails",{insertionValues:e})}updateMappingDetails(e){return this._http.post("api/ats/settings/updateMappingDetails",{updateValues:e})}createStateDetails(e){return this._http.post("api/ats/settings/createStateDetails",{stateDetails:e})}updateStateDetails(e){return this._http.post("api/ats/settings/updateStateDetails",{stateDetails:e})}createCountryDetails(e){return this._http.post("api/ats/settings/createCountryDetails",{countryDetails:e})}updateCountryDetails(e){return this._http.post("api/ats/settings/updateCountryDetails",{countryDetails:e})}createRegionDetails(e){return this._http.post("api/ats/settings/createRegionDetails",{regionDetails:e})}updateRegionDetails(e){return this._http.post("api/ats/settings/updateRegionDetails",{regionDetails:e})}createCityDetails(e){return this._http.post("api/ats/settings/createCityDetails",{cityDetails:e})}updateCityDetails(e){return this._http.post("api/ats/settings/updateCityDetails",{cityDetails:e})}getDegreeCourseMasterForCollege(){return this._http.post("api/ats/settings/getDegreeCourseMasterForCollege",{})}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275inject"](r.c))},e.\u0275prov=i["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},pEYl:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("yuIm"),r=n("fXoL");let a=(()=>{class e{transform(e=0,t=0,n=0,r=0,a=""){if(!a||""==a)return!1;let s={module_id:e,sub_module_id:t,section_id:n,sub_section_id:r};"V"==a&&(s.view_permission=1),"C"==a&&(s.create_permission=1),"E"==a&&(s.edit_permission=1),"DE"==a&&(s.delete_permission=1),"DO"==a&&(s.download_permission=1),"U"==a&&(s.upload_permission=1),"B"==a&&(s.bulk_operation=1);const o=Object.keys(s);return i.roleAccessList.find(e=>o.every(t=>e[t]===s[t]))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=r["\u0275\u0275definePipe"]({name:"access",type:e,pure:!0}),e})()}}]);