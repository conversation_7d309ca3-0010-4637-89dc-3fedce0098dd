import { Component, HostListener, Inject, InjectionToken, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { Router } from '@angular/router';
import moment from 'moment';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { PmExternalStakeholderServiceService } from '../../services/pm-external-stakeholder-service.service';
import { ToastrService } from 'ngx-toastr';
import * as _ from 'underscore';
import { SubSink } from 'subsink';
import { PmInternalStakeholderService } from '../../../project-internal-stakeholders/services/pm-internal-stakeholder.service';
import { GetNameByIdPipe } from 'src/app/modules/project-management/shared-lazy-loaded/pipes/get-name-by-id.pipe';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
export const TOASTER_EXT_MESSAGE_SERVICE_TOKEN = new InjectionToken<ToasterMessageService>('TOASTER_EXT_MESSAGE_SERVICE_TOKEN');

@Component({
  selector: 'app-pm-add-external-member',
  templateUrl: './pm-add-external-member.component.html',
  styleUrls: ['./pm-add-external-member.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        },
        useUtc: true

      },

    },
    { provide: TOASTER_EXT_MESSAGE_SERVICE_TOKEN, useClass: ToasterMessageService },
    GetNameByIdPipe
  ]
})
export class PmAddExternalMemberComponent implements OnInit {

  addExternalMemberFormGroup = this.formBuilder.group({
    employee_name: [''],
    oid:[''],
    email:['',[Validators.email,Validators.pattern('^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}$')]],
    endDate: [''],
    startDate: [''],
    projectRole: [''],
    external_name:[''],
    communication:['']
  });
  isComponentLoading: boolean =  false;
  saveDisabled:boolean = false;
  projectRoleList : any = [];
  formConfig:any = [];
  itemId:number;
  projectId:number;
  color:string;
  retrieveMessages:any = [];
  subs = new SubSink();
  projectStartDate:any;
  projectEndDate:any;
  type:string;
  isSearchDisabled:boolean = false;
  isEmailDisabled:boolean = false;
  button: any;
  scrollColor: any;
  fieldOutline: any;
  oldFormValue: any;
  oldData: any;
  newData: any;

  constructor(@Inject(MAT_DIALOG_DATA) public dialogData:any, public dialogRef: MatDialogRef<PmAddExternalMemberComponent>,private _utilityService: UtilityService, public dialog: MatDialog, private router: Router, private toaster: ToasterService, private formBuilder: FormBuilder, private _masterService: PmMasterService, private _pmExternalService:  PmExternalStakeholderServiceService, private PmInternalStakeholderService: PmInternalStakeholderService,private getNameByIdPipe: GetNameByIdPipe,
  @Inject(TOASTER_EXT_MESSAGE_SERVICE_TOKEN)  private _toasterService: ToasterMessageService) { }
  @HostListener('window:keyup.esc') onKeyUp() {
    this.onCloseClick()
  }

  async ngOnInit() {
    this.color = this.dialogData?.color ? this.dialogData?.color : 'EE4961';
    document.documentElement.style.setProperty('--memberButton', this.color)
    this.isComponentLoading = true;
    this.itemId = parseInt(this.router.url.split("/")[5]);
    this.projectId = parseInt(this.router.url.split("/")[3]);
    

    await this._masterService.getProjectRoleMaster().then((res)=>{
      this.projectRoleList = _.where(res, {role_type:"External"})
    })
    await this.getCustomConfig();
    await this.getProjectDetails();
    if(this.dialogData.mode == 'Edit'){
      await this.prePatchDetails(this.dialogData.data);
      this.oldFormValue = this.addExternalMemberFormGroup.value;
      this.oldData = {
        employee_name: this.oldFormValue.employee_name ? this.oldFormValue.employee_name : null,
        email: this.oldFormValue.email ? this.oldFormValue.email : null,
        external_name: this.oldFormValue.external_name ? this.oldFormValue.external_name : null,
        projectRole: this.oldFormValue.projectRole ? this.getNameByIdPipe.transform(this.oldFormValue.projectRole, this.projectRoleList) : null,
        startDate: this.oldFormValue.startDate ? moment(this.oldFormValue.startDate).format('DD-MMM-YYYY') : null,
        endDate: this.oldFormValue.endDate ? moment(this.oldFormValue.endDate).format('DD-MMM-YYYY') : null,
        communication :this.oldFormValue.communication ? 'Yes' : 'No'
      }
    }
    this.handleFieldChanges();
    this.isComponentLoading = false;
  }

  onCloseClick() {
    if (this.addExternalMemberFormGroup.dirty) {
      this._utilityService.openConfirmationSweetAlertWithCustom("Are you sure", "You want to Close without saving").then((result) => {
        if (result) {
          this.dialogRef.close({ messType: "C" })
        }
      });
    }
    else {
      this.dialogRef.close({ messType: "C" })
    }

  }

  /**
   * @description Getting Form Customization values
   */
  getCustomConfig(){
    this._masterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
        const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
        this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
        document.documentElement.style.setProperty('--extButton', this.button)
        this.scrollColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.scroll_color ? retrieveStyles[0].data.scroll_color : "#90ee90" : "#90ee90";
        document.documentElement.style.setProperty('--project2externalScroll', this.scrollColor)
        this.fieldOutline = retrieveStyles.length > 0 ? retrieveStyles[0].data.field_outline_color ? retrieveStyles[0].data.field_outline_color : "#808080" : "#808080";
        document.documentElement.style.setProperty('--externalStakeField', this.fieldOutline);
      }
    });
  }

  /**
   * @description Adding External Stakeholder
   */
  async addExternalStakeholder() {
    this.saveDisabled = true;
  
    console.log(this.addExternalMemberFormGroup)
    if(this.addExternalMemberFormGroup.get('email').valid){
      let mandate = this.checkMandateNotEmpty(this.addExternalMemberFormGroup.value)
    if (mandate) {
      this.addExternalMemberFormGroup.patchValue({
        start_date: moment(this.addExternalMemberFormGroup.get('startDate').value).format("YYYY-MM-DD"),
        end_date: moment(this.addExternalMemberFormGroup.get('endDate').value).format("YYYY-MM-DD")
      })
      let formValue = this.addExternalMemberFormGroup.value
      formValue['oid'] = formValue['employee_name'] ? formValue['employee_name'] : null;

      console.log('FormValue Submitted:',formValue);

      if(this.dialogData.mode == 'Add'){
        let params = {
          project_id: this.projectId,
          project_item_id: this.itemId,
          start_date:formValue.startDate,
          end_date:formValue.endDate,
          associate_id:formValue.oid,
          email_id:formValue.email,
          external_name:formValue.external_name,
          oid:formValue.oid,
          project_role_id:formValue.projectRole,
          communication:formValue.communication
  
        }
        
        return new Promise((resolve, reject) => {
          this.subs.sink = this._pmExternalService.addExternalStakeholder(params).subscribe(
            (res: any) => {
              if (res['messType'] == "E") {
                // this.toastr.error("Employee onboarding fails", 'Error');
                this._toasterService.showWarning("Employee onboarding fails",10000);
                this.saveDisabled = false
              }
              else if(res['messType']=="W"){
                this._toasterService.showWarning(res['message'], 10000);
                this.saveDisabled = false
              }
              else if (res['messType'] == "R") {
                // this.toastr.warning("Duplicate Entry", 'Warning');
                this._toasterService.showWarning("Duplicate Entry",10000);
                this.saveDisabled = false
              }
              else {
                this.dialogRef.close({ messType: "S" });
                this._toasterService.showSuccess("Employee successfully onboarded", 10000);
                // this.toastr.success("Employee successfully onboarded", 'Success');
              }
              resolve(null)
            },
            (err) => {
              this._toasterService.showWarning("Employee onboarding fails",10000);
              // this.toastr.error("Employee onboarding fails", 'Error');
              this.saveDisabled = false
              resolve(null)
            }
          );
        });
      }
      else if(this.dialogData.mode == 'Edit'){
        this.newData = {
          employee_name: formValue.employee_name ? formValue.employee_name : null,
          email: formValue.email ? formValue.email : null,
          external_name: formValue.external_name ? formValue.external_name : null,
          projectRole: formValue.projectRole ? this.getNameByIdPipe.transform(formValue.projectRole, this.projectRoleList) : null,
          startDate: formValue.startDate ? moment(formValue.startDate).format('DD-MMM-YYYY') : null,
          endDate: formValue.endDate ? moment(formValue.endDate).format('DD-MMM-YYYY') : null,
          communication : formValue.communication ? 'Yes' : 'No'
        }
        let params = {
          project_id: this.projectId,
          project_item_id: this.itemId,
          id:this.dialogData.data.id,
          start_date:formValue.startDate,
          end_date:formValue.endDate,
          project_role_id:formValue.projectRole,
          communication:formValue.communication,
          oldData: this.oldData,
          newData: this.newData
        }
        
        return new Promise((resolve, reject) => {
          this.subs.sink = this._pmExternalService.editExternalStakeholder(params).subscribe(
            (res: any) => {
              if (res['messType'] == "S") {
                this.dialogRef.close({ messType: "S" });
                // this.toastr.success("Employee Edited Successfully!", 'Success');
                this._toasterService.showSuccess("Employee Edited Successfully!", 10000);
                this.saveDisabled = false
              }
              else if(res['messType']=="W"){
                this.saveDisabled = false
                this._toasterService.showWarning(res['message'], 10000);
                
              }
              else {
                this.saveDisabled = false
                // this.toastr.error("Employee Editing Failed", 'Error');
                this._toasterService.showWarning("Employee Editing Failed",10000);
              }
              resolve(null)
            },
            (err) => {
              // this.toastr.error("Employee Editing fails", 'Error');
              this._toasterService.showWarning("Employee Editing fails",10000);
              this.saveDisabled = false
              resolve(null)
            }
          );
        });
      }
      
    }
    else {
      this.saveDisabled = false
    }
    }
    else{
      this.saveDisabled = false;
      // this.toastr.error("Invalid Employee Email Id", 'Error');
      this._toasterService.showWarning("Invalid Employee Email Id",10000);
    }
  }
  /**
   * @description Checking Is Mandatory
   */
  isMandate(field: any) {
    const mandate = _.where(this.formConfig, { type: "add-external-member", field_name: field, is_active: true });
    if (mandate.length > 0) {
      const isMandate = mandate[0].is_mandant;
      return isMandate;
    }
  }
  /**
   * @description Checking Mandatory Fields
   */
  checkMandateNotEmpty(data: any) {
    console.log(data);
    let errorOccurred = false;

    if(this.dialogData.mode != 'Edit')
    {
      if ((data.employee_name === null || data.employee_name === undefined || data.employee_name === '')  && (data.email === null || data.email === undefined || data.email === '')) {
          const name_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.name_empty ? this.retrieveMessages[0].errors.name_empty : 'Employee Email / Search Employee is Mandatory' : 'Employee Email / Search Employee is Mandatory';
          // this.toaster.showWarning('Warning', name_empty);
          this._toasterService.showWarning(name_empty,10000);
          errorOccurred = true;
      }
      else if ((data.employee_name === null || data.employee_name === undefined || data.employee_name === '') && (data.external_name === null || data.external_name === undefined || data.external_name === '')) {
        const name_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.name_empty ? this.retrieveMessages[0].errors.name_empty : 'Employee name is Mandatory' : 'Employee Name is Mandatory';
        // this.toaster.showWarning('Warning', name_empty);
        this._toasterService.showWarning(name_empty,10000);
        errorOccurred = true;
      }
      else if ((data.projectRole === null || data.projectRole === undefined || data.projectRole === '') && this.isMandate('projectRole')) {
        // const industryEmptymsg = 'Milestone Value is Mandatory.';
        // this.toastr.error(industryEmptymsg, 'Error');
        console.log('Project Role')
        const projectRole_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.projectRole_empty ? this.retrieveMessages[0].errors.projectRole_empty : 'Project Role is Mandatory' : 'Project Role is Mandatory';
        // this.toaster.showWarning('Warning', projectRole_empty);
        this._toasterService.showWarning(projectRole_empty,10000);
        errorOccurred = true;
      }
    }
    else if ((data.startDate === null || data.startDate === undefined || data.startDate === '' || data.startDate == 'Invalid date') && this.isMandate('startDate')) {
      // const nameEmptymsg = 'Kindly choose when is start date?';
      // this.toastr.error(nameEmptymsg, 'Error');
      const startDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty ? this.retrieveMessages[0].errors.startDate_empty : 'Start Date is Mandatory' : 'Start Date is Mandatory';
      // this.toaster.showWarning('Warning', startDate_empty);
      this._toasterService.showWarning(startDate_empty,10000);
      errorOccurred = true;
    }

    else if ((data.endDate === null || data.endDate === undefined || data.endDate === '' || data.endDate == 'Invalid date') && this.isMandate('endDate')) {
      // const nameEmptymsg = 'Kindly choose When Is It due?';
      // this.toastr.error(nameEmptymsg, 'Error');
      const endDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.endDate_empty ? this.retrieveMessages[0].errors.endDate_empty : 'End Date is Mandatory' : 'End Date is Mandatory';
      // this.toaster.showWarning('Warning', endDate_empty);
      this._toasterService.showWarning(endDate_empty,10000);
      errorOccurred = true;
    }

    else if ((data.projectRole === null || data.projectRole === undefined || data.projectRole === '') && this.isMandate('projectRole')) {
      // const industryEmptymsg = 'Milestone Value is Mandatory.';
      // this.toastr.error(industryEmptymsg, 'Error');
      console.log('Project Role')
      const projectRole_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.projectRole_empty ? this.retrieveMessages[0].errors.projectRole_empty : 'Project Role is Mandatory' : 'Project Role is Mandatory';
      // this.toaster.showWarning('Warning', projectRole_empty);
      this._toasterService.showWarning(projectRole_empty,10000);
      errorOccurred = true;
    }

    else if ((data.startDate != null || data.startDate != undefined || data.startDate != '' || data.startDate != 'Invalid date') || (data.endDate != null || data.endDate != undefined || data.endDate != '' || data.endDate != 'Invalid date')) {
      data.startDate = moment(data.startDate).format('YYYY-MM-DD');
      data.endDate = moment(data.endDate).format('YYYY-MM-DD');
      if(data.startDate > data.endDate){
        // this.toaster.showWarning('Warning', 'Start Date must not be greater than End Date');
        this._toasterService.showWarning('Start Date must not be greater than End Date',10000);
        errorOccurred = true;
      }
      else if(data.startDate < this.projectStartDate || data.startDate > this.projectEndDate){
          // this.toaster.showWarning('Warning', 'Start Date should be within Project Start Date and End Date');
          this._toasterService.showWarning('Start Date should be within Start Date and End Date',10000);
          errorOccurred = true;
      }
      else if(data.endDate < data.startDate){
        // this.toaster.showWarning('Warning', 'End Date must not be less than Start Date');
        this._toasterService.showWarning('End Date must not be less than Start Date',10000);
        errorOccurred = true;
      }
      else if(data.endDate < this.projectStartDate || data.endDate > this.projectEndDate){
          // this.toaster.showWarning('Warning', 'End Date should be within Project Start Date and End Date');
          this._toasterService.showWarning('End Date should be within Start Date and End Date',10000);
          errorOccurred = true;
      }

      else{
        errorOccurred = false;
      }
    }

 
    if (errorOccurred) {
      console.log('Return Called')
      // this.toastr.warning('Kindly fill all mandatory!', 'Warning')
      return false; // Return false if any error occurred
    } else {
      console.log('Return Called True')
      return true; // Return true if no errors occurred
    }
  }

  /**
   * @description Getting Project Details
   */
  async getProjectDetails(){
    await this.PmInternalStakeholderService.getProjectQuote(this.projectId, this.itemId).then((res) => {
      if (res['messType'] == 'S') {
        console.log('Project Data:',res);
        this.projectEndDate = res['data'][0].show_planned_end_date
        this.projectStartDate = res['data'][0].show_planned_start_date
        this.addExternalMemberFormGroup.get('startDate').patchValue(this.projectStartDate);
        this.addExternalMemberFormGroup.get('endDate').patchValue(this.projectEndDate);
      }
    })
  }

  get getstartdate(){
    return this.addExternalMemberFormGroup.get('startDate').value;
  }
  get getenddate(){
    return this.addExternalMemberFormGroup.get('endDate').value;
  }

  prePatchDetails(val: any){
    console.log('Edited Value:',val);
    let start_date = moment(val.start_date).format('YYYY-MM-DD');
    let end_date = moment(val.end_date).format('YYYY-MM-DD');

    this.addExternalMemberFormGroup.get('startDate').patchValue(start_date);
    this.addExternalMemberFormGroup.get('endDate').patchValue(end_date);
    this.addExternalMemberFormGroup.get('employee_name').patchValue(val.oid);
    this.addExternalMemberFormGroup.get('oid').patchValue(val.oid);
    this.addExternalMemberFormGroup.get('email').patchValue(val.email_id);
    this.addExternalMemberFormGroup.get('projectRole').patchValue(val.project_role_id);
    this.addExternalMemberFormGroup.get('external_name').patchValue(val.employee_name);
    this.addExternalMemberFormGroup.get('communication').patchValue(val.communication);
    this.type = val.associate_id ? 'I' : 'E';
  }

  handleFieldChanges(){
    this.addExternalMemberFormGroup.get('employee_name').valueChanges
    .subscribe(async (res) => {
      if(res){
        this.isEmailDisabled = true;
      }
      else{
        this.isEmailDisabled = false;
      }
    })
    this.addExternalMemberFormGroup.get('email').valueChanges
    .subscribe(async (res) => {
      let val = this.addExternalMemberFormGroup.get('external_name').value;
      if(res){
        this.isSearchDisabled = true;
      }
      else{
        if((val != '' && val != null && val != undefined)){
          this.isSearchDisabled = true;
        }
        else{
          this.isSearchDisabled = false;
        }
      }
      // console.log('isSearchDisabled',this.isSearchDisabled)
    })
    this.addExternalMemberFormGroup.get('external_name').valueChanges
    .subscribe(async (res) => {
      let val = this.addExternalMemberFormGroup.get('email').value;
      if(res){
        this.isSearchDisabled = true;
      }
      else{
        if((val != '' && val != null && val != undefined)){
          this.isSearchDisabled = true;
        }
        else{
          this.isSearchDisabled = false;
        }
      }
      // console.log('isSearchDisabled',this.isSearchDisabled)
    })
  }

  ngOnDestroy () {
    this.subs.unsubscribe();
  }

}
