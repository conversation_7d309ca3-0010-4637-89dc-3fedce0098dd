const axios = require("axios");
const config0=require("config");
module.exports.getTokenForApplication = async (db_name, tenant_id) => {
  try {
    let bodyParams = {
      client_id: config0.client_id,
      client_secret: config0.client_secret,
      db_name: db_name,
      tenant_id: tenant_id,
    };

    let config = {
      url: `http://${process.env.AUTH_SERVICE||'localhost:3800'}/api/auth/authenticate/getTokenForApp`,
      method: "POST",
      data: bodyParams,
    };

    let response_data = await axios(config).catch((err) => {
      console.log(err);
      return Promise.reject(err);
    });
    return Promise.resolve(response_data.data);
  } catch (err) {
    console.error(err);
    return Promise.reject(err);
  }
};
