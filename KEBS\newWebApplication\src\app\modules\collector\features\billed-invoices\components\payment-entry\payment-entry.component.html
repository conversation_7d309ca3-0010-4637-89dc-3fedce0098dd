<form class="container-fluid payment-entry-component" [formGroup]="paymentForm">

    <div class="row py-1" *ngIf="isDataReceived">
        <div class="col-12 d-flex">
            <div class="raised-circle my-auto" mat-icon-button>
                <mat-icon class="logo-icon">
                    credit_card
                </mat-icon>
            </div>
            <span class="heading my-auto ml-3 mr-2">
                Payment entry for
            </span>
            <span class="customer-name my-auto">
                {{itemData?.project_name ? itemData.project_name : ''}}
            </span>
        </div>
        
    </div>

    <div class="row pt-4 justify-content-center" *ngIf="!isDataReceived">

        <mat-spinner role="progressbar" mode="indeterminate" diameter="30" 
         style="width: 30px; height: 30px;">
        </mat-spinner>

    </div>

    <mat-progress-bar class="mt-1" *ngIf="isSubmitted" mode="indeterminate"></mat-progress-bar>

    <div class="row pt-2 mt-3 mb-0" *ngIf="isDataReceived">
        <div class="col-lg-8 px-0">
            <div class="row my-3 slide-in-top">
                <div class="col-1 pl-3">
                    <mat-icon class="icon-btn mt-1">attach_money</mat-icon>
                </div>
                <div class="col-lg-5 col-sm-12 pl-0 light-txt-bold my-auto">
                    Amount To Be Collected
                </div>
                <div class="col-lg-6 col-sm-12 my-auto">
                    <app-currency-converter *ngIf="invoiceDetails?.amount_to_be_Collected !=0"
                    [currencyList]="invoiceDetails?.Balance_amount 
                    ? 
                    invoiceDetails.Balance_amount
                    :
                    ''"
                    class="flex-1 cp" 
                    type="medium">
                    </app-currency-converter>

                    <div *ngIf="invoiceDetails?.amount_to_be_Collected == 0" class="dark-txt-bold">
                        {{ invoiceDetails.amount_to_be_Collected }} 
                        {{ invoiceDetails.currency }}  
                    </div>
                </div>
            </div>
            <div class="row slide-in-top">
                <div class="col-1 pl-3 my-auto">
                    <mat-icon class="icon-btn pt-1">price_change</mat-icon>
                </div>
                <div class="col-lg-5 col-sm-12 light-txt-bold my-auto pl-0">
                    Amount Received
                </div>
                <div class="col-lg-6 col-sm-12 my-auto">
                    <mat-form-field appearance="outline" style="width: 235px;">
                        <mat-label>Amount Received</mat-label>
                        <input
                          matInput
                          type="number"
                          placeholder="Amount received"
                          formControlName="amountReceived"
                          required="true"
                          min="1"
                          onkeydown="return event.keyCode !== 69" 
                        />
                        <mat-error class="pt-2" *ngIf="!paymentForm.controls.amountReceived?.errors?.validAmount">
                            Amount Received can't be 0 or -ve
                         </mat-error>
                        <span matSuffix>
                          {{
                            invoiceDetails?.currency
                          }}
                        </span>
                      </mat-form-field>
                </div>
            </div>
            <div class="row slide-in-top">
                <div class="col-1 pl-3 my-auto">
                    <mat-icon class="icon-btn pt-1">event</mat-icon>
                </div>
                <div class="col-lg-5 col-sm-12 light-txt-bold my-auto pl-0">
                    Received On
                </div>
                <div class="col-lg-6 col-sm-12 my-auto">
                    <mat-form-field appearance="outline">
                        <mat-label>Received On</mat-label>
                        <input
                          matInput
                          [matDatepicker]="picker1"
                          name="endDate"
                          [max]="this.todayDate"
                          required
                          placeholder="Received Date"
                          formControlName="receivedOn"
                          required="true"
        
                        />
                        <mat-datepicker-toggle
                          matSuffix
                          [for]="picker1"
                        ></mat-datepicker-toggle>
                        <mat-datepicker #picker1></mat-datepicker>
                      </mat-form-field>
                </div>
            </div>
            <div class="row slide-in-top">
                <div class="col-1 pl-3 my-auto">
                    <mat-icon class="icon-btn pt-1">tag</mat-icon>
                </div>
                <div class="col-lg-5 col-sm-12 light-txt-bold my-auto pl-0">
                    Reference Number
                </div>
                <div class="col-lg-6 col-sm-12 my-auto">
                    <mat-form-field appearance="outline" style="width: 235px;">
                        <mat-label>
                            Reference Number</mat-label>
                        <input
                          matInput
                          type="text"
                          placeholder="Reference Number"
                          formControlName="tallyRefNo"
                          required="true"
                        />
                      </mat-form-field>
                </div>
            </div>
            <div class="row slide-in-top" *ngIf="showBankPaymentDetails">
                <div class="col-1 pl-3 my-auto">
                    <mat-icon class="icon-btn pt-1">tag</mat-icon>
                </div>
                <div class="col-lg-5 col-sm-12 light-txt-bold my-auto pl-0">
                    Bank Reference Number
                </div>
                <div class="col-lg-6 col-sm-12 my-auto">
                    <mat-form-field appearance="outline" style="width: 235px;">
                        <mat-label>
                            Bank Reference Number</mat-label>
                        <input
                          matInput
                          type="text"
                          placeholder="Bank Reference Number"
                          formControlName="bankRefNo"
                        />
                      </mat-form-field>
                </div>
            </div>
            <div class="row slide-in-top" *ngIf="showBankPaymentDetails">
                <div class="col-1 pl-3 my-auto">
                    <mat-icon class="icon-btn pt-1">price_change</mat-icon>
                </div>
                <div class="col-lg-5 col-sm-12 light-txt-bold my-auto pl-0">
                    Payment Received Bank
                </div>
                <div class="col-lg-6 col-sm-12 my-auto">
                    <mat-form-field appearance="outline" style="width: 235px;">
                        <mat-label>Payment Received Bank</mat-label>
                        <mat-select formControlName="paymentReceivedBank">
                          <mat-option *ngFor="let bank of bankDetails"
                            [value]="bank.bank_name">{{bank.bank_name}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
            <div class="row d-flex justify-content-center my-4 slide-from-down">
                    <button (click)="onSubmit()" [disabled]="isSubmitted" mat-raised-button class='submit-btn'>Submit</button>
            </div>
        </div>



        <div class="col-lg-4 mt-auto px-0">
            <span>
                <img _ngcontent-mla-c256="" src="https://assets.kebs.app/images/coinstack.png" height="250" width="250">
            </span>
        </div>
    </div>

</form>