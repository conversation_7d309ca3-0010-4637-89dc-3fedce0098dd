import { Component, OnInit, ViewChild, ViewContainerRef, Compiler, Output, EventEmitter,OnDestroy, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from "@angular/material/dialog";
import { MatSnackBar } from '@angular/material/snack-bar';
import { DocViewerComponent } from '../../../../../shared-lazy-loaded-components/attachment-mgmt/components/doc-viewer/doc-viewer.component';
import * as am4core from "@amcharts/amcharts4/core";
import * as am4charts from "@amcharts/amcharts4/charts";
import am4themes_animated from "@amcharts/amcharts4/themes/animated";
import am4themes_dataviz from '@amcharts/amcharts4/themes/dataviz'

import { BilledInvoicesService } from 'src/app/modules/collector/services/billed-invoice-service/billed-invoices.service';
import { pipe, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TenantService } from 'src/app/services/tenant-service/tenant.service';
import { InvoiceCommonService } from "src/app/modules/invoice/common-services/invoice-common.service";
import { SharedLazyLoadedComponentsService } from '../../../../../shared-lazy-loaded-components/services/shared-lazy-loaded-components.service';

@Component({
  selector: 'app-billed-invoice-detail-landing-page',
  templateUrl: './billed-invoice-detail-landing-page.component.html',
  styleUrls: ['./billed-invoice-detail-landing-page.component.scss']
})
export class BilledInvoiceDetailLandingPageComponent implements OnInit,OnDestroy {

  @ViewChild('contentContainer', { read: ViewContainerRef })
  private contentContainerRef: ViewContainerRef;
  tabs = [
    {
      id    :0,
      label : 'Invoice Info',
      disabled: false
    },
    {
      id    : 1,
      label : 'Payment Entry',
      disabled: false
    },
    // {
    //   id    : 2,
    //   label : 'Tasks',
    //   disabled: false
    // },
    // {
    //   id    : 3,
    //   label : 'Notes',
    //   disabled: false
    // },
    // {
    //   id    : 4,
    //   label : 'Activities',
    //   disabled: false
    // },
    {
      id    : 5,
      label : 'Documents',
      disabled: false
    }
  ]

  //ChartVariable
  outstandingsChart : any

  //Chart data
  outstandingsChartData : any
 
  selectedToggleId : any = this.tabs[0].id
 
  isComponentLoading: boolean = false;
  isInvoiceDialogOpen:boolean = false;
  generateAndStorePDFInS3: boolean = false;
  taskListArray = []
  billedInvoiceDetails : any
  billingId : any
  isLoading : boolean = true;
  protected $onDestroy = new Subject<void>();
  isModalLoading : boolean = false
  
  legalCount : any 
  legalArDetails : any
  arDetails : any

  activityTypesList : any
  invoiceTenantDetails: any;
  paymentTermsList: any;
  showInvoiceV2:boolean = false
  constructor(
    private compiler : Compiler,
    private _billedInvoiceService : BilledInvoicesService,
    private dialog : MatDialog,
    private router : Router,
    private snackBar : MatSnackBar,
    private route: ActivatedRoute,
    private tenantService: TenantService,
    private invoiceCommonService: InvoiceCommonService,
    private _sharedService: SharedLazyLoadedComponentsService
    ) { }
 

  async ngOnInit() {

    this.billedInvoiceDetails = await this.getInvoiceDetails();
    await this.getTenantInfo();
    await this.getInvoiceConfig();
    this.getCustomerOutstandings()
    this.loadTabContent()
    this.getPaymentTerms();

  }

  /**
   * Customer outstandings (chart data)
   */
  getCustomerOutstandings () {
    this._billedInvoiceService.viewCustomerOutstandings(this.billedInvoiceDetails.customer_id)
    .pipe(takeUntil(this.$onDestroy)).subscribe(async (res : any)=>{
      console.log('graph data',res)
      if(res.messType == 'S') {

        this.legalCount = res.legal_count
        this.outstandingsChartData = res.chartData
        this.legalArDetails = res.legal_ar_details
        this.arDetails = res.ar_details
        this.initOutstandingsChart()

      }
      else {
        this.snackBar.open("No data found for outstandings","Dismiss",{duration:3000})
      }
    }, (err) => {
      console.log(err)
    })
  }

  /**
   * pie chart init
   */
  async initOutstandingsChart() {

    //Chart Initialization
    //animation import
    am4core.useTheme(am4themes_animated);
    am4core.useTheme(am4themes_dataviz);

    this.outstandingsChart = am4core.create("chartDiv",am4charts.PieChart)
 
    this.outstandingsChart.data = this.outstandingsChartData

    // Add and configure Series
    let pieSeries = this.outstandingsChart.series.push(new am4charts.PieSeries());

      // Setting up color
    pieSeries.colors.list = [
      am4core.color('#7395FB'),
      am4core.color("#B2DF8A"),
      am4core.color("#FE7070")
    ]

    pieSeries.dataFields.value = "value_formatted";
    pieSeries.dataFields.value_formatted = "value";
    pieSeries.dataFields.category = "label";
  
    // Let's cut a hole in our Pie chart the size of 40% the radius
    this.outstandingsChart.innerRadius = am4core.percent(55);

    this.outstandingsChart.numberFormatter.numberFormat = "#.##  " + `${this.outstandingsChartData[0].value_symbol}`

    //animation
    pieSeries.hiddenState.properties.endAngle = -90;
  
    // Disable ticks and labels
    pieSeries.labels.template.disabled = true;
    pieSeries.ticks.template.disabled = true;
  
    pieSeries.slices.template.tooltipText = "{category}: {value_formatted}";
  
    // Add a legend
    this.outstandingsChart.legend = new am4charts.Legend();
    this.outstandingsChart.legend.position = "top";
    this.outstandingsChart.legend.showTooltipOn = "always"
    this.outstandingsChart.legend.labels.template.fill = am4core.color("#000000")
    this.outstandingsChart.legend.valueLabels.template.text = "{value.value}";

    let marker = this.outstandingsChart.legend.markers.template.children.getIndex(0);
    marker.cornerRadius(0, 0, 0, 0);
  
    let markerTemplate = this.outstandingsChart.legend.markers.template;
    markerTemplate.width = 25;
    markerTemplate.height = 15;

  }
 
  /**
   * On toogle button pressed
   * @param event 
   */
  selectToggle(event) {
    this.selectedToggleId = event.value;
 
    // If payment entry tab is selected, navigate to invoice v1.2 billed tab
    if (this.selectedToggleId == 1) {
      this.navigateToInvoiceV12BilledTab();
      return;
    }
 
    this.loadTabContent();
  }
 
  
  // navigateToInvoiceV12BilledTab() {
  //   const projectId = this.billedInvoiceDetails.project_id;
  //   const milestoneId = this.billedInvoiceDetails.milestone_id;
  //   const itemId = this.billedInvoiceDetails.item_id;
  //   const billingId = this.billedInvoiceDetails.billing_id;
 
  //   // Determine the item type based on payment status
  //   let itemType = 'billed'; // default
 
  //   // Check if it's partial payment or payment received
  //    if (this.billedInvoiceDetails.amount_to_be_Collected > 0 && this.billedInvoiceDetails.total_received_value == 0) {
  //     itemType = 'billed'; // Partial Payment
  //   } 
  //   else if (this.billedInvoiceDetails.amount_to_be_Collected > 0 && this.billedInvoiceDetails.total_received_value > 0) {
  //     itemType = 'PP'; // Partial Payment
  //   } else if (this.billedInvoiceDetails.amount_to_be_Collected == 0) {
  //     itemType = 'PR'; // Payment Received
  //   }
 
 
  //   const routeUrl = `/main/invoice-v1.2/invoicelist/1/invoiceBilled/${projectId}/${milestoneId}/${itemId}/${itemType}/${billingId}`;
 
  //   this.router.navigateByUrl(routeUrl);
  // }
 

  navigateToInvoiceV12BilledTab() {
    const projectId = this.billedInvoiceDetails.project_id;
    const milestoneId = this.billedInvoiceDetails.milestone_id;
    const itemId = this.billedInvoiceDetails.item_id;
    const billingId = this.billedInvoiceDetails.billing_id;
  
    let itemType = 'billed'; // default
  
    if (this.billedInvoiceDetails.amount_to_be_Collected > 0 && this.billedInvoiceDetails.total_received_value == 0) {
      itemType = 'billed';
    } else if (this.billedInvoiceDetails.amount_to_be_Collected > 0 && this.billedInvoiceDetails.total_received_value > 0) {
      itemType = 'PP';
    } else if (this.billedInvoiceDetails.amount_to_be_Collected == 0) {
      itemType = 'PR';
    }
  
    const routePath = `/main/invoice-v1.2/invoicelist/1/invoiceBilled/${projectId}/${milestoneId}/${itemId}/${itemType}/${billingId}`;
    const fullUrl = window.location.origin + routePath;
  
    // Open in new tab
    window.open(fullUrl, '_blank');
  }
  
 
  /**
   * On back pressed
   */
  goBack() {
    this.router.navigateByUrl("/main/collector/billedInvoices/billedInvoiceList")
  }
 
  loadTabContent() {
    this.isComponentLoading = true;
 
    if (this.contentContainerRef)
      this.contentContainerRef.clear();
 
    if (this.selectedToggleId == 0)
      this.loadInvoiceInfoContainer();
 
    else if (this.selectedToggleId == 1)
      this.paymentEntryContainer();
 
    else if (this.selectedToggleId == 2)
      this.tasksContainer();
 
    else if (this.selectedToggleId == 3)
      this.notesContainer();
 
    else if (this.selectedToggleId == 4)
      this.activitiesContainer();
 
    else if (this.selectedToggleId == 5)
      this.documentsContainer();
  }
 
  /**
   * Invoice info tab
   */
  loadInvoiceInfoContainer() {
      import('../../components/invoice-info/invoice-info.component').then(
        (invoiceInfoView) => {
          const ngModuleFactory = this.compiler.compileModuleSync(invoiceInfoView.InvoiceInfoModule);
          const ngModule = ngModuleFactory.create(this.contentContainerRef.injector);
          const component = ngModule.componentFactoryResolver.resolveComponentFactory(invoiceInfoView.InvoiceInfoComponent);
          const componentRef = this.contentContainerRef.createComponent(component);
          this.isComponentLoading = false;
          let compVar = componentRef.instance;
          compVar.billedInvoiceDetails = this.billedInvoiceDetails;
          compVar.invoiceVersion2 = this.showInvoiceV2
        });
  }
 
  /**
   * Payment tab
   */
  paymentEntryContainer() {
    import('../../components/payment-entry/payment-entry.component').then(
      (paymentEntryView) => {
        const ngModuleFactory = this.compiler.compileModuleSync(paymentEntryView.PaymentEntryModule);
        const ngModule = ngModuleFactory.create(this.contentContainerRef.injector);
        const component = ngModule.componentFactoryResolver.resolveComponentFactory(paymentEntryView.PaymentEntryComponent);
        const componentRef = this.contentContainerRef.createComponent(component);
        this.isComponentLoading = false;
        let compVar = componentRef.instance;
        compVar.itemData = this.billedInvoiceDetails 
      });
  }
 
  /**
   * For Task tab
   */
  tasksContainer() {
    import('../../components/tasks/tasks.component').then(
      (taskView) => {
        const ngModuleFactory = this.compiler.compileModuleSync(taskView.TasksModule);
        const ngModule = ngModuleFactory.create(this.contentContainerRef.injector);
        const component = ngModule.componentFactoryResolver.resolveComponentFactory(taskView.TasksComponent);
        const componentRef = this.contentContainerRef.createComponent(component);
        this.isComponentLoading = false;
        let compVar = componentRef.instance;

        compVar.billedInvoiceDetails = this.billedInvoiceDetails
 
        compVar.taskListOutput
        .pipe(takeUntil(this.$onDestroy)).subscribe(res => {
          if (res)
            this.taskListArray = res;
        }, err => {
          console.log(err)
        });
 
      });
 
  }
 
  /**
   * Notes tab
   */
  notesContainer() {
    import('../../components/notes/notes.component').then(
      (notesView) => {
        const ngModuleFactory = this.compiler.compileModuleSync(notesView.NotesModule);
        const ngModule = ngModuleFactory.create(this.contentContainerRef.injector);
        const component = ngModule.componentFactoryResolver.resolveComponentFactory(notesView.NotesComponent);
        const componentRef = this.contentContainerRef.createComponent(component);
        this.isComponentLoading = false;
        let compVar = componentRef.instance;
        compVar.billedInvoiceDetails = this.billedInvoiceDetails
 
      });
 
  }
  
  /**
   * Activities tab
   */
  activitiesContainer() {
    import('../../components/collector-activities/collector-activities.component').then(
      (activitiesView) => {
        const ngModuleFactory = this.compiler.compileModuleSync(activitiesView.CollectorActivitiesModule);
        const ngModule = ngModuleFactory.create(this.contentContainerRef.injector);
        const component = ngModule.componentFactoryResolver.resolveComponentFactory(activitiesView.CollectorActivitiesComponent);
        const componentRef = this.contentContainerRef.createComponent(component);
        this.isComponentLoading = false;
        let compVar = componentRef.instance;
        compVar.billedInvoiceDetails = this.billedInvoiceDetails

        // componentRef.instance.activityTypes.subscribe(res => {
        //   if (res) 
        //     this.activityTypesList = res;
        // });

        // setTimeout(() => { this.scrollToBottom(); }, 1000);

 
      });
 
  }
  
  /**
   * Documents tab
   */
  documentsContainer() {
    import('../../components/documents/documents.component').then(
      (documentsView) => {
 
        const ngModuleFactory = this.compiler.compileModuleSync(documentsView.DocumentsModule);
        const ngModule = ngModuleFactory.create(this.contentContainerRef.injector);
        const component = ngModule.componentFactoryResolver.resolveComponentFactory(documentsView.DocumentsComponent);
        const componentRef = this.contentContainerRef.createComponent(component);
        this.isComponentLoading = false;
        let compVar = componentRef.instance;
        compVar.billedInvoiceDetails = this.billedInvoiceDetails

      });
 
  }

  /**
   * Open Invoice modal
   */
    async viewInvoice(billingId?, milestoneId?) {

    // Use values from billedInvoiceDetails if parameters are not provided
    const actualBillingId = billingId || this.billedInvoiceDetails?.billing_id;
    const actualMilestoneId = milestoneId || this.billedInvoiceDetails?.milestone_id;

    // Validate required data
    if (!actualBillingId && !actualMilestoneId) {
      console.error('No billing ID or milestone ID available');
      this.snackBar.open('Invoice data not available. Please refresh the page.', 'Close', {
        duration: 3000
      });
      return;
    }

    if (this.isInvoiceDialogOpen) {
      this.isInvoiceDialogOpen = false; // Reset the flag and continue
    }
  
    this.isInvoiceDialogOpen = true;
    console.log(this.paymentTermsList)
    if(this.generateAndStorePDFInS3){
      let bucket = 'kebs-invoices';
      try {
        let res = await this._billedInvoiceService.retrieveUploadedObjects(bucket, actualMilestoneId).toPromise();
        console.log('S3 objects retrieved:', res);
      if (res && res["data"]?.length > 0) {
        this.viewFile(res["data"][0], true); // Pass true to indicate called from viewInvoice
      }
      else{
          console.error('No S3 files found for milestoneId:', actualMilestoneId);
       this.snackBar.open('No invoice file found. Please contact KEBS team to resolve.', 'Close', {
            duration: 3000
          });
          this.isInvoiceDialogOpen = false;
        }
      } catch (error) {
        console.error('Error retrieving S3 objects:', error);
        this.snackBar.open('Error loading invoice. Please try again.', 'Close', {
          duration: 3000
        });
        this.isInvoiceDialogOpen = false;
      }
    }
    else{
    this.isModalLoading = true
    await this._billedInvoiceService.viewInvoice(actualBillingId)
    .pipe(takeUntil(this.$onDestroy))
    .subscribe(
      async res => {
        this.isModalLoading = false
        console.log(res);
        if(this.showInvoiceV2){
          const { ViewInvoiceComponent } = await import('../../../../../invoice-v1.2/lazy-loaded-components/view-invoice/view-invoice.component')
          const openInvoice = this.dialog.open(ViewInvoiceComponent, {
            width: "65%",
            height: "100%",
            autoFocus: false,
            maxWidth: "90vw",
            data: {
              pdfData: res,
              billingId: actualBillingId,
              invoiceTenantDetails: this.invoiceTenantDetails,
              paymentTermsList: this.paymentTermsList
            }
            });
            openInvoice.afterClosed().subscribe(() => {
            this.isInvoiceDialogOpen = false;
          });
        }
        else{
          const { ViewInvoiceComponent } = await import('../../../../.././invoice/lazy-loaded-components/view-invoice/view-invoice.component')
          const openInvoice = this.dialog.open(ViewInvoiceComponent, {
            width: "65%",
            height: "100%",
            autoFocus: false,
            maxWidth: "90vw",
            data: {
              pdfData: res,
              billingId: actualBillingId,
              invoiceTenantDetails: this.invoiceTenantDetails,
              paymentTermsList: this.paymentTermsList
            }
          });
            openInvoice.afterClosed().subscribe(() => {
              this.isInvoiceDialogOpen = false;
          });
        }
      },
      err => {
        this.isModalLoading = false
          this.isInvoiceDialogOpen = false;
          console.error('Error loading invoice:', err);
          this.snackBar.open('Error loading invoice. Please try again.', 'Close', {
            duration: 3000
          });
        }
      );
    }
  }

async getInvoiceConfig(){
  let invoiceConfig = await this._billedInvoiceService.getInvoiceConfig();
  if(invoiceConfig['data'][0] && invoiceConfig['data'][0].config!=null){
    let invoiceConfigDetails = JSON.parse(invoiceConfig['data'][0].config);
    this.generateAndStorePDFInS3 = invoiceConfigDetails.hasOwnProperty('generate_store_pdf_in_s3') && invoiceConfigDetails['generate_store_pdf_in_s3'] ? invoiceConfigDetails['generate_store_pdf_in_s3'] : false;
  }   
}
viewFile(file: { cdn_link: string; file_format: string }, calledFromViewInvoice: boolean = false): void {
    console.log('ViewFile called with:', file, 'calledFromViewInvoice:', calledFromViewInvoice);

    // Prevent multiple dialogs from opening, unless called from viewInvoice
    if (this.isInvoiceDialogOpen && !calledFromViewInvoice) {
      console.log('Dialog already open, exiting');
      return;
    }

    // Only set dialog state if not called from viewInvoice (since viewInvoice manages it)
    if (!calledFromViewInvoice) {
      this.isInvoiceDialogOpen = true;
    }
    const cdn_link = file.cdn_link;
    let uploadPopUpDialogRef: any = null;

    this._sharedService.getDownloadUrl(cdn_link).subscribe({
      next: (res: { base64Url?: string; data?: string }) => {

        // Always try to create blob URL for better performance, regardless of tenant setting
        if (res.base64Url) {
          try {
            let base64Data = res.base64Url;

            // Check if it's a data URL format and extract base64 part
            if (base64Data.startsWith('data:')) {
              console.log('Detected data URL format, extracting base64 data');
              const parts = base64Data.split(',');
              if (parts.length > 1) {
                base64Data = parts[1]; // Get the base64 part after the comma
                console.log('Extracted base64 data length:', base64Data.length);
              }
            }

            // Always try to create blob URL for PDF data
            console.log('Creating blob URL from base64 data');
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);

            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }

            const byteArray = new Uint8Array(byteNumbers);
            const pdfFile = new Blob([byteArray], { type: 'application/pdf' });
            const fileURL = URL.createObjectURL(pdfFile);

            console.log('Successfully created blob URL:', fileURL);
            window.open(fileURL, '_blank');
            setTimeout(() => {
              console.log('Revoking blob URL:', fileURL);
              URL.revokeObjectURL(fileURL);
            }, 3000); 
          } catch (error) {
            console.error('Error creating blob URL, falling back to direct URL:', error);
            console.log('Fallback: Opening original base64 URL');
            window.open(res.base64Url, '_blank');
          } finally {
            if (!calledFromViewInvoice) {
              this.isInvoiceDialogOpen = false;
            }
          }
        }
        //  Fallback to component viewer
        else if (res.data) {
          console.log('Using DocViewer component with URL:', res.data);
          uploadPopUpDialogRef = this.dialog.open(DocViewerComponent, {
            width: '58%',
            height: '100%',
            data: {
              selectedFileUrl: res.data,
              fileFormat: file.file_format,
              expHeaderId: ""
            },
          });

          uploadPopUpDialogRef.afterClosed().subscribe(() => {
            // Only reset dialog state if we're managing it
            if (!calledFromViewInvoice) {
              this.isInvoiceDialogOpen = false;
            }
          });
        }
        else {
          console.error('No valid URL received from getDownloadUrl:', res);
          this.snackBar.open('Unable to load invoice. Please contact KEBS team.', 'Close', {
            duration: 3000
          });
          // Only reset dialog state if we're managing it
          if (!calledFromViewInvoice) {
            this.isInvoiceDialogOpen = false;
          }
        }
      },
      error: (error) => {
        console.error('Error fetching file:', error);
        this.snackBar.open('Error loading file. Please try again.', 'Close', {
          duration: 3000
        });
        // Only reset dialog state if we're managing it
        if (!calledFromViewInvoice) {
          this.isInvoiceDialogOpen = false;
        }
      },
    });
}

  /**
   * Comments modal
   */
  async openComments() {
    this.isModalLoading = true
    let inputData
    let itemData = this.billedInvoiceDetails

    if(itemData) {
      inputData = {
        application_id: 221,
        unique_id_1: itemData.billing_id,
        unique_id_2: '',
        application_name: 'Collection management - Billed invoices tab'
      }
  
      inputData.title = itemData.item_name ? itemData.item_name : '';

      let modalParams = {
        inputData: inputData,
        context:
        {
          'Project Name': itemData.project_name ? itemData.project_name : '',
          'Item Name': itemData.item_name ? itemData.item_name : '',
          'Milestone Name':itemData.milestone_name ? itemData.milestone_name : itemData.milestone_name,
          'Invoice Status': itemData.invoice_status ? itemData.invoice_status : '',
          'Billed by': itemData.billed_by ? itemData.billed_by : ''
        },
        commentBoxHeight: '100vh',
        commentBoxScrollHeight: '80%'
      };
  
      const { ChatCommentContextModalComponent } = await import('src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component');
  
      const openChatCommentContextModalComponent = this.dialog.open(ChatCommentContextModalComponent, {
        height: '100%',
        width: '65%',
        position: { right: '0px' },
        data: { modalParams: modalParams }
      });
      this.isModalLoading = false
    }
   
  }

  /**
   * View Outstandings modal
   */
  async viewOutstandings () {
    this.isModalLoading = true
    let customer_id = this.billedInvoiceDetails.customer_id
    await this._billedInvoiceService.customerInvoices(customer_id)
    .pipe(takeUntil(this.$onDestroy))
    .subscribe(
      async res => {
        this.isModalLoading = false

        let params = {
          tableData :  res,
          invoiceData : this.billedInvoiceDetails
        }

        const { ViewOutstandingsModalComponent } = await import('../../components/view-outstandings-modal/view-outstandings-modal.component')
        const openViewOutstandings = this.dialog.open(ViewOutstandingsModalComponent, {
          height: '80%',
          width: '80%',
          data: { modalParams: params }
        });
      },
      err => {
        this.isModalLoading = false
        console.error(err);
      }
    );
  }

  /**
   * 
   * Invoice detail API (main)
   */
  async getInvoiceDetails () {
    this.isLoading = true;
    return new Promise(async (resolve,reject) => {

      this.route.params.subscribe(async (res) => {
        this.billingId = res['billingId'];
      });
      
      await this._billedInvoiceService.getBillingDetails(this.billingId)
      .pipe(takeUntil(this.$onDestroy))
      .subscribe((res : any) => {
        if(res.messType = "S") {
          this.isLoading = false
          resolve(res.data)
        }
      }, (err) => {
        reject(err)
        this.snackBar.open("Something went wrong!","Dismiss",{duration:3000})
        console.log(err)
      })

    })

  }

  /**
   * Create button clicked
   * @param selectedToggleId 
   * 
   */
  async createItem(selectedToggleId) {
    this._billedInvoiceService.setActivityObservable(selectedToggleId)
  }

  /** 
* @description getTenantInfo
*/
async getTenantInfo() {
  try {
    const tenantInfo: any = await this.tenantService.getTenantInfo();
    this.invoiceTenantDetails = await this.getInvoiceTenantRoleCheckDetail(tenantInfo.tenant_name, 'Role');
    const data = this.invoiceTenantDetails['data'];
    if (data.hasOwnProperty('is_to_show_invoice_version') && data['is_to_show_invoice_version'] == 1) {
      this.showInvoiceV2 = true;
    }
    return "Success";
  } catch (err) {
    console.error(err);
    throw err; // Rethrow the error to handle it outside this function if needed
  }
}
/** 
* @description getInvoiceTenantRoleCheckDetails
*/
 async getInvoiceTenantRoleCheckDetail(tenantName, checkType)  {
  return new Promise((resolve, reject) => {
    this.invoiceCommonService.getInvoiceTenantCheckDetail(tenantName, checkType).subscribe(res => {
      resolve(res);
    },
      (err) => {
        console.error(err);
        reject(err);
      }
    );
  });
};

      //Get Payment Terms
  getPaymentTerms() {
    this.invoiceCommonService.getPaymentTerms().then((res: any) => {
      for (let r of res) {
        r['id'] = r['name']
      }
      this.paymentTermsList = res
    }, (err) => {
      console.log(err)
    })
  }

  ngOnDestroy() {
    this.$onDestroy.next();
    this.$onDestroy.complete();

    if(this.contentContainerRef)
      this.contentContainerRef.clear()
  }



}
