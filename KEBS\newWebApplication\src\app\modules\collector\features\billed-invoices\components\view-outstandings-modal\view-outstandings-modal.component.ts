import { Component, OnInit, Input, Inject,ViewChild,AfterViewInit } from '@angular/core';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";

import * as moment from 'moment';

@Component({
  selector: 'app-view-outstandings-modal',
  templateUrl: './view-outstandings-modal.component.html',
  styleUrls: ['./view-outstandings-modal.component.scss']
})
export class ViewOutstandingsModalComponent implements OnInit,AfterViewInit {

  modalParams : any;

  //Table declarations
  tableData : outstandingsData[]
  tableDataSource : any
  displayedColumns = []

  @ViewChild(MatSort) sort: MatSort;

  @ViewChild('paginator') paginator: MatPaginator;
  itemsPerPage = [5,10,15,20]


  //Table declarations
  columns = [
    {
      colKey:'milestone_name',
      colName:'Milestone Name',
      className:'milestone-name',
      isToolTip: true,
      cell: (element : outstandingsData) => `${element.milestone_name ? element.milestone_name : ''}`,
      toolTipVal: (element : outstandingsData) => `${element.milestone_name ? element.milestone_name : ''}`
    }, 
    {
      colKey:'billed_value_formatted',
      colName:'Billed Value',
      className:'dark-txt',
      cell: (element : outstandingsData) => `${element.billed_value_formatted}`,
      isToolTip: true,
      toolTipVal: (element : outstandingsData) => `${element.billed_value}`
    }, 
    {
      colKey: 'ar_value_formatted', 
      colName: 'AR Value',
      className:'dark-txt',
      cell: (element : outstandingsData) => `${element.ar_value_formatted}`,
      isToolTip: true,
      toolTipVal: (element : outstandingsData) => `${element.ar_value}`
    },
    {
      colKey: 'milestone_status_name', 
      colName: 'Milestone Status',
      classNameByStatus:
      (element) => element.milestone_status_name == 'Legal' 
      ? 'is-legal'
      : element.milestone_status_name == 'Partial Payment'
        ? 'is-partial-payment'
        : element.milestone_status_name == 'Billed'        
          ? 'is-billed-invoice'
          : '',
      isToolTip: false,
      cell: (element : outstandingsData) => `${element.milestone_status_name}`
    },
    {
      colKey: 'billed_on',
      colName: 'Billed On',
      className:'dark-txt',
      isToolTip: false,
      cell: (element : outstandingsData) => `${element.billed_on ? moment(element.billed_on).format("DD-MMM-YY") : ''}`
    },
    {
      colKey: 'aging_days', 
      colName: 'Aging Days',
      className:'dark-txt',
      isToolTip: false,
      cell: (element : outstandingsData) => `${element.aging_days}`
    },
  ];

  constructor(
    public dialogRef: MatDialogRef<ViewOutstandingsModalComponent>, 
    @Inject(MAT_DIALOG_DATA) public outstandingData: any,
    
  ) { }

  ngOnInit() {

    this.modalParams = this.outstandingData.modalParams;

    this.tableData = this.modalParams.tableData.data

    this.displayedColumns = this.columns.map(c => c.colKey);

    console.log(this.modalParams)

  }

  ngAfterViewInit () {

    this.tableDataSource = new MatTableDataSource(this.tableData)

    this.tableDataSource.paginator = this.paginator;

  }

  sortData(){
    this.tableDataSource.sort = this.sort;
  }



  closeOutstandingsModal () {
    this.dialogRef.close({ event: "Close" });
  }

}


export interface outstandingsData {
  aging_days: number;
  billed_on: string;
  ar_value: string;
  billed_value: string;
  // is_under_legal_notice: any;
  billed_value_formatted : string;
  ar_value_formatted : string;
  milestone_name: string;
  milestone_status_name: string
}


import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import {MatTableModule} from '@angular/material/table';
import {MatPaginatorModule} from '@angular/material/paginator';
import {MatIconModule} from '@angular/material/icon';
import {MatSortModule} from '@angular/material/sort';
import {MatTooltipModule} from '@angular/material/tooltip';
import {CdkTableModule} from '@angular/cdk/table';
import {MatButtonModule} from '@angular/material/button';

@NgModule({
  declarations: [
    ViewOutstandingsModalComponent
  ],
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatIconModule,
    MatSortModule,
    MatTooltipModule,
    CdkTableModule,
    MatButtonModule
  ],
  providers: [],
  exports:[
    ViewOutstandingsModalComponent
  ]

})

class ViewOutstandingsModalModule { }