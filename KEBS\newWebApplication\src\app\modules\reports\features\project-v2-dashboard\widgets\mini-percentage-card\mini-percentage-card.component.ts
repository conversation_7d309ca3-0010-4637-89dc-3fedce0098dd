import { Component, Input, OnInit } from '@angular/core';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DecimalPipe } from '@angular/common';

import { WidgetsService } from './../../services/widgets/widgets.service';
import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';
import { MatDialog } from '@angular/material/dialog';
import { SummaryDialogComponent } from 'src/app/modules/reports/features/project-v2-dashboard/widgets/summary-dialog/summary-dialog.component';
@Component({
  selector: 'app-mini-percentage-card',
  templateUrl: './mini-percentage-card.component.html',
  styleUrls: ['./mini-percentage-card.component.scss'],
  providers: [DecimalPipe]
})
export class MiniPercentageCardComponent implements OnInit {

  @Input() aid: any = null;
  @Input() oid: any = null;
  @Input() widgetConfig: any = {};
  @Input() widgetType: string = '';
  @Input() startDate: any = null;
  @Input() endDate: any = null;
  @Input() filterData: any = null;
  @Input() filterQuery: any = '';
summaryData: any = null; // Add this in the class
  protected _onDestroy = new Subject<void>();

  calculatedWidgetHeight: string = '';
  calculatedChartWidgetHeight: number = null;

  isLoading: boolean = true;
  centerLabel: any = 'Total';

  spinnerSize: number = 0;
  radius: number = 0;
  circumference: number = 0;
  strokeDashoffset: number = 0;

  data: any = {
    percentage: 0,
    color: "#52C41A"
  };
  constructor(
    private _widgetService: WidgetsService,
    private _toaster: ToasterService,
    private _decimalPipe: DecimalPipe,
       private _dialog: MatDialog
  ) { }

  async ngOnInit() {
    this.calculateHeight();
    await this.getChartData();
    await this.updateCircle();
    this.isLoading = false;
  }

  /**
 * @description To calculate height dynamically
 */
  calculateHeight() {
    if (this.widgetConfig && this.widgetConfig?.height) {
      this.calculatedWidgetHeight = `calc(${this.widgetConfig.height} - 55px)`;
      this.calculatedChartWidgetHeight =
        parseInt(this.widgetConfig.height) - 75;
    }
  }

  /**
* @description Get list view menu data
*/
  async getChartData() {
    if (
      !this.widgetConfig?.widget_config?.api ||
      this.widgetConfig?.widget_config?.api == ''
    ) {
      return;
    }

    let apiUrl = this.widgetConfig?.widget_config?.api;
    let payload = {
      aid: this.aid,
      oid: this.oid,
      startDate: this.startDate,
      endDate: this.endDate,
      filterData: this.filterData,
      filterQuery: this.filterQuery
    };

    this.data = [];
    this.isLoading = true;

    return new Promise((resolve, reject) => {
      this._widgetService
        .getDataDynamically(apiUrl, payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              this.data =
                res['data'] ? res['data'] : [];
                this.summaryData = res['summary_data'] || null;
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            this.isLoading = false;
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching Widget Data',
              7000
            );
            this.isLoading = false;
            reject();
          },
        });
    });
  }
  openSummaryForm() {
  if (this.summaryData) {
    this._dialog.open(SummaryDialogComponent, {
      width: '90vw',
      data: {
        widgetData: this.summaryData
      }
    });
  } else {
    this._toaster.showWarning("Warning ⚠️", "Summary data is not available", 7000);
  }
}


  async updateCircle() {
    // Calculate spinner size (80% of chart height)
    this.spinnerSize = 70;
    this.radius = this.spinnerSize / 2 - 5; // Adjust for stroke width
    this.circumference = 2 * Math.PI * this.radius;

    // Calculate stroke-dashoffset for progress
    if (this.data['percentage'] !== undefined) {
      this.strokeDashoffset =
        this.circumference - (this.data['percentage'] / 100) * this.circumference;
    }
  }
}

