<div class="timesheet-upload-styles container-fluid pt-3">
  <div class="col-12">
    <div class="row">
      <div class="col-8"></div>
      <div
        class="col-2 pt-2"
        (click)="downloadTemplate()"
        style="cursor: pointer"
      >
        <span style="font-weight: bold; text-decoration: underline"
          >Download Template</span
        >
      </div>
      <div class="col-2">
        <button
          mat-icon-button
          matTooltip="Log History"
          class="icon-tray-button ml-auto"
          (click)="openLogsDialog()"
        >
          <mat-icon class="smallCardIcon">history</mat-icon>
          Logs
        </button>
      </div>
    </div>
  </div>
  <mat-horizontal-stepper [linear]="isLinear" #stepper>
    <mat-step>
      <ng-template matStepLabel>Upload Leave Data</ng-template>
      <mat-card class="timesheet-upload-card">
        <div class="row">
          <div class="col-4"></div>
          <div class="col-4">
            <div class="text-header">Upload Leave Data</div>
            <div style="text-align: center">
              <button mat-icon-button>
                <mat-icon (click)="fileInput7.click()">upload_file</mat-icon>
              </button>
              <input
                hidden
                type="file"
                #fileInput7
                accept=".csv, .xls,.xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                (change)="leaveUpload($event)"
              />
            </div>
          </div>
        </div>
      </mat-card>
      <div class="pt-3">
        <button
          *ngIf="enableButtonCheck"
          class="upload-btn"
          mat-button
          matStepperNext
        >
          Next
        </button>
      </div>
    </mat-step>
    <mat-step label="Leave Upload Validations">
      <div
        class="col-12 pt-2 pl-0"
        *ngIf="exceluploadedData.length > 0"
        style="overflow-y: scroll"
      >
        <table>
          <thead>
            <tr>
              <th
                *ngFor="let columnName of tableColumnName"
                class="table-title"
              >
                {{ columnName.column_name }}
              </th>
              <th>Message</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="let values of exceluploadedData; let i = index"
              [ngStyle]="values.error != '' ? { background: 'red' } : ''"
            >
              <td>{{ values["Leave Record Id"] }}</td>
              <td>{{ values["Employee Id"] }}</td>
              <td>{{ values["Employee Name"] }}</td>
              <td>{{ values["Leave Start Date"] }}</td>
              <td>{{ values["Leave End Date"] }}</td>
              <td>{{ values["From"] }}</td>
              <td>{{ values["To"] }}</td>
              <td>{{ values["Leave Type"] }}</td>
              <td>{{ values["No Of Calendar Days"] }}</td>
              <td>{{ values["No Of Absent Days"] }}</td>
              <td>{{ values["Status"] }}</td>
              <td>{{ values["Leave Requested On"] }}</td>
              <td>{{ values["Leave Approved On"] }}</td>
              <td>{{ values["Leave Comments"] }}</td>
              <td>{{ values.error }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="pt-3">
        <button class="back-btn" mat-raised-button matStepperPrevious>
          Back
        </button>
        <button
          mat-raised-button
          class="ml-3 btn-upload"
          (click)="uploadStagingDataToLeave()"
          [disabled]="isUploading"
        >
          <span>Upload Leave Data</span>
        </button>
      </div>
    </mat-step>
    <mat-step>
      <ng-template matStepLabel>Leave Upload Status</ng-template>
      <p>You are now done.</p>
      <div>
        <button class="back-btn" mat-raised-button matStepperPrevious>
          Back
        </button>
        <button class="upload-btn" mat-button (click)="stepper.reset()">
          Reset
        </button>
      </div>
    </mat-step>
  </mat-horizontal-stepper>
</div>
