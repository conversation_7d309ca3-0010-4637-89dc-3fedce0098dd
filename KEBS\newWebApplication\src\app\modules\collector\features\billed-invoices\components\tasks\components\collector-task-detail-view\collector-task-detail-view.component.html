<div class="container-fluid pt-2 pb-2 task-detail-styles">
    <div style="background-color: #F9F9F9;">
        <div class="row">
            <div class="col-11 pt-2 headingBold">
                {{taskItem?.task_name}}
            </div>
            <div class="col-1 mt-0 d-flex">
                <button mat-icon-button class="ml-auto close-button mt-1" (click)="closeTaskDetailModal();">
                  <mat-icon matTooltip="Close" class="close-Icon">close</mat-icon>
                </button>
            </div>
        </div>
        <div class="row pt-2 pb-2">
            <div class="col-1 headerFont"  matTooltip="Status">
                Status
            </div>
            <div class="col-3 normalFont">
                <div>
                    <span class="status-dot" [ngStyle]="{'background': taskItem?.status_name | taskStatusPipe}"></span>
                    <span class="pl-2">{{taskItem?.status_name}}</span>
                </div>
            </div>
            <div class="col-1 headerFont" matTooltip="Assigned to">
                Assigned to
            </div>
            <div class="col-3 normalFont">
                <div>
                    <app-user-profile type="small" [oid]="taskItem?.assigned_to_oid"
                    imgHeight="28px" imgWidth="28px">
                    </app-user-profile>
                </div>
            </div>
        </div>

        <div class="row pt-2 pb-2">
            <div class="col-1 headerFont" matTooltip="Due On">
                Due on 
            </div>
            <div class="col-3 normalFont">
                {{taskItem?.end_date | ddmmmyy}}
            </div>
            <div class="col-1 headerFont"  matTooltip="Created by">
                Created by
            </div>
            <div class="col-3 normalFont">
                <app-user-profile type="small" [oid]="taskItem?.created_by"
                imgHeight="28px" imgWidth="28px">
                </app-user-profile>
            </div>
        </div>

        <div class="row pt-2 pb-2">
            <!-- <div class="col-1 headerFont">
                Location
            </div>
            <div class="col-3 normalFont" style="cursor: pointer;">
                {{taskItem?.location}}
            </div> -->
            <div class="col-1 headerFont"  matTooltip="Created on">
                Created on
            </div>
            <div class="col-3 normalFont">
                {{taskItem?.created_on | ddmmmyy}}
            </div>
            <div class="col-1 headerFont" matTooltip="Planned hours">
                Planned hours
            </div>
            <div class="col-3 normalFont">
                {{taskItem?.planned_hours ? taskItem.planned_hours : '00'}}  Hours
            </div>
        </div>
    </div>

    <div class="row pt-2">
        <div class="col-8 pl-0">
            <div class="row pt-2 pb-2">
                <div class="col-12 headerFont">
                    Task Description
                </div>
            </div>
            <div class="row pt-2">
                <div class="col-12 pl-4" [innerHtml]="taskItem?.task_description | safeHTML">
                </div>
                <div *ngIf="!taskItem?.task_description" class="pl-4"> - </div>
            </div>

            <div class="row pt-2 pb-2">
                <div class="col-11 headerFont">
                    Attachment
                </div>
                <div class="col-1">
                    <button mat-icon-button matTooltip="Add files"
                        class="view-button-inactive" (click)="moreFileInput.click()">
                        <mat-icon class="iconButton">cloud_upload</mat-icon>
                    </button>
                    <input hidden type="file" ng2FileSelect
                        [uploader]="uploader" [accept]="allowedMimeType"
                        #moreFileInput (change)="onFileSelected($event)"/>
                </div>
            </div>

            <ng-container *ngIf="taskItem.attachments && taskItem.attachments.length > 0">
                <div class="row pt-2 header" style="border-bottom: solid 1px #cacaca">
                    <div class="col-1">
                        Type
                    </div>
                    <div class="col-4">
                        File name
                    </div>
                    <div class="col-2">
                        Created on
                    </div>
                    <div class="col-2">
                        Created by
                    </div>
                    <div class="col-2">
                        Size
                    </div>
                </div>
                <ng-container *ngFor="let fileItem of taskItem?.attachments; let fileIndex = index">
                    <div class="row" style="border-bottom: solid 1px #cacaca">
                        <div class="col-1 pl-2">
                            <button mat-icon-button class="ic-size"><i [class]="fileType[(fileItem?.type)] || 'ms-Icon ms-Icon--FileTemplate'" aria-hidden="true"></i></button>
                        </div>
                        <div class="col-4 fileFont d-flex my-auto">
                            {{fileItem.fileName ? fileItem.fileName : fileItem.file_name}}
                        </div>
                        <div class="col-2 fileFont d-flex my-auto">
                            {{fileItem?.created_on | date: "dd-MMM-yy"}}
                        </div>
                        <div class="col-2 fileFont d-flex my-auto">
                            {{fileItem?.created_by}}
                        </div>
                        <div class="col-2 fileFont d-flex my-auto">
                            {{fileItem?.displaySize}}
                        </div>
                        <div class="col-1 d-flex my-auto">
                            <button mat-icon-button matTooltip="Download" class="icon-tray-button mr-2"
                                (click)="downloadFile(fileIndex)">
                                <mat-icon class="smallCardIcon">file_download</mat-icon>
                            </button>
                        </div>
                    </div>
                </ng-container>
            </ng-container>

            <div *ngIf="!taskItem?.attachments || taskItem?.attachments.length == 0" style="text-align: center;padding-top: 3rem;">
                <div class="d-flex pb-2 justify-content-center align-items-center slide-in-top">
                    <span style="font-size: 16px;font-weight: normal;">No Attachment found ! </span>
                </div>
                <div class="d-flex justify-content-center align-items-center slide-from-down pt-2 pb-2">
                    <img src="https://assets.kebs.app/images/noAccounts.png" class="mt-2 mb-2" height="200" width="200" />
                </div>
            </div>
        </div>

        <div class="col-4 pl-1 pr-3" style="border-left: solid 1px #cacaca">
            <div class="row pt-2">
                <div class="col-11 headerFont">
                    Comments
                </div>
            </div>
            <app-collector-task-comment [comments]="taskItem?.comments" [commentBoxHeight]="'64vh'"
            (sendComments)="getComments($event)"></app-collector-task-comment>
        </div>
    </div>
    
</div>