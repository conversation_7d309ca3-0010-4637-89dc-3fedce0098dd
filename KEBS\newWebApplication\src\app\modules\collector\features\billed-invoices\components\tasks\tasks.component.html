<div class="container-fluid tasks-component">

    <div class="row pt-4 justify-content-center" *ngIf="isLoading">
        <mat-spinner role="progressbar" mode="indeterminate" diameter="30" 
        style="width: 30px; height: 30px;">
       </mat-spinner>
    </div>

    <div *ngIf="!isLoading && taskList.length>0">
        <div class="row pb-0">
            <div class="pt-2 col-11 title">
                Total :
                <span class="heading pl-2">{{taskList.length}}</span>
            </div>
        </div>
        <div class="row pt-2 pb-1 header">
            <div class="col-3 pl-4 d-flex">
                Task
            </div>
            <div class="col-2">
                Status
            </div>
            <div class="col-2">
                Due date
            </div>
            <div class="col-2">
                Assigned to
            </div>
            <div class="col-1 pr-0 pl-1">
                Planned hours
            </div>
        </div>

        <ng-container *ngFor="let item of taskList; let taskIndex = index">

            <ng-template
                [ngTemplateOutlet]="recTemplateRef"
                [ngTemplateOutletContext]="{ $implicit: item, index: taskIndex}">
            </ng-template>

        </ng-container>

        <ng-template #recTemplateRef let-item let-index="index">

            <ng-template
                [ngTemplateOutlet]="cardTemplate"
                [ngTemplateOutletContext]="{ $implicit: item, index: index }">
            </ng-template>

            <div *ngIf="item.children" class="children-class">

                <ng-template
                    ngFor
                    [ngForOf]="item.children"
                    [ngForTemplate]="recTemplateRef">
                </ng-template>

            </div>
        
        </ng-template>

        <ng-template #cardTemplate let-taskItem let-index="index">
            <div *ngIf="taskItem?.showTask" class="card listcard" style="border-left-width: 4px !important;"
                [ngStyle]="{'border-left-color':taskItem?.color ? taskItem?.color : ''}"
                >
                <!-- [ngClass]="['taskStatusBorder_'+(taskItem?.status_name ? taskItem?.status_name : '')]" -->
                <div class="card-body" style="padding: 2px !important;cursor: pointer;">
                    <div class="row">
                        <div class="col-3 pl-0 pr-0 normalFont d-flex">

                            <button *ngIf="taskItem?.children?.length" class="arrow-button" mat-icon-button
                            [matTooltip]="taskItem.loadChildTask ? 'Collapse' : 'Expand'" (click)="showChildTask(taskItem)">
                                <mat-icon class="iconButton">
                                    {{taskItem.loadChildTask ? 'keyboard_arrow_down' : 'keyboard_arrow_right'}}
                                </mat-icon>
                            </button>

                            <div inlineEdit
                            (click)="activateInlineEdit('Task name', 'simple-text',[], taskItem)">
                                <ng-template [ngTemplateOutlet]="taskNameContent"></ng-template>
                            </div>
                        
                            <ng-template #taskNameContent>
                                <div [matTooltip]="taskItem?.task_name" class="pt-2" style="font-weight: 500;color: #cf0001;"
                                [ngClass]="taskItem?.children?.length == 0 ? 'pl-3' : ''">
                                    {{taskItem?.task_name}}
                                </div>
                            </ng-template>

                        </div>
                        <!-- <div class="col-3 pr-0 pt-2 pl-3 normalFont" [matTooltip]="taskItem?.task_name"
                            style="font-weight: 500;color: #cf0001;">
                            <div>
                                <ng-template [ngTemplateOutlet]="taskNameContent"></ng-template>
                            </div>
                            <ng-template #taskNameContent>
                                {{taskItem?.task_name}}
                            </ng-template>
                        </div> -->
                        <div class="col-2 pt-2 pl-0 normalFont">
                            <div inlineEdit
                            (click)="activateInlineEdit('Status', 'minimal-dropdown', statusTypes, taskItem)">
                                <ng-template [ngTemplateOutlet]="taskStatusContent"></ng-template>
                            </div>
                            <ng-template #taskStatusContent>
                                <span class="status-dot mb-1"
                                    [ngStyle]="{'background': taskItem?.status_name | taskStatusPipe}"></span>
                                <span class="pl-2">{{taskItem?.status_name}}</span>
                            </ng-template>

                        </div>
                        <div class="col-2 pt-2 normalFont">
                            <div inlineEdit
                            (click)="activateInlineEdit('Due date', 'date-picker',[], taskItem)">
                                <ng-template [ngTemplateOutlet]="taskDueContent"></ng-template>
                            </div>

                            <ng-template #taskDueContent>
                                {{taskItem?.end_date ? (taskItem?.end_date | ddmmmyy) : '-'}}
                            </ng-template>

                        </div>
                        <div class="col-2 pt-2 normalFont">

                            <div inlineEdit
                            (click)="activateInlineEdit('Assigned to', 'search-dropdown',[], taskItem)">
                                <ng-template [ngTemplateOutlet]="taskAssignedContent"></ng-template>
                            </div>

                            <ng-template #taskAssignedContent>
                                <app-user-profile *ngIf="taskItem?.assigned_to_oid" type="small"
                                imgHeight="28px" imgWidth="28px"
                                    [oid]="taskItem?.assigned_to_oid">
                                </app-user-profile>
                                <span *ngIf="taskItem?.assigned_to_oid?.length == 0">Not Assigned</span>
                            </ng-template>

                        </div>

                        <div class="col-1 pt-2 normalFont d-flex justify-content-center">

                            <div inlineEdit
                            (click)="activateInlineEdit('Planned hours', 'simple-text',[], taskItem)">
                                <ng-template [ngTemplateOutlet]="taskPlHrContent"></ng-template>
                            </div>

                            <ng-template #taskPlHrContent>
                                {{taskItem?.planned_hours ? taskItem?.planned_hours : '0'}} Hrs
                            </ng-template>

                        </div>

                        <div class="col-1 d-flex justify-content-center">
                            <button mat-icon-button matTooltip="Detail view" class="icon-tray-button"
                            [ngStyle]="{ visibility: moreOptionMenu.menuOpen ? 'visible' : 'hidden'}"
                                (click)="openTaskDetail(taskItem)">
                                <mat-icon class="smallCardIcon">open_in_full</mat-icon>
                            </button>
                            <button mat-icon-button class="icon-tray-button" matTooltip="More Options"
                            [ngStyle]="{ visibility: moreOptionMenu.menuOpen ? 'visible' : 'hidden'}"
                                [matMenuTriggerFor]="moreActions" #moreOptionMenu="matMenuTrigger">
                                    <mat-icon class="iconButton">more_horiz</mat-icon>
                            </button>
                            <mat-menu #moreActions="matMenu">
                                <mat-selection-list>
                                    <mat-list-option class="request-check" (click)="openTaskDetail(taskItem)">
                                        <div style="display:flex; align-items:center">
                                            <div>
                                                <mat-icon class="request-icon-btn">open_in_full</mat-icon>
                                            </div>
                                            <div style="display:flex;align-items:center">
                                            <div style="display:flex; flex-direction:column; padding-left: 10px;">
                                                View details
                                            </div>
                                            </div>  
                                        </div>
                                    </mat-list-option>
                                    <mat-list-option class="request-check" (click)="createSubTask(taskItem)">
                                        <div style="display:flex; align-items:center">
                                            <div>
                                                <mat-icon class="request-icon-btn">add_circle_outline</mat-icon>
                                            </div>
                                            <div style="display:flex;align-items:center">
                                            <div style="display:flex; flex-direction:column; padding-left: 10px;">
                                                Add sub task
                                            </div>
                                            </div>  
                                        </div>
                                    </mat-list-option>
                                    <!-- <mat-list-option class="request-check" (click)="openMeetingInviteModal(taskItem)">
                                        <div style="display:flex; align-items:center">
                                            <div>
                                                <mat-icon class="request-icon-btn">event</mat-icon>
                                            </div>
                                            <div style="display:flex;align-items:center">
                                            <div style="display:flex; flex-direction:column; padding-left: 10px;">
                                                Teams Meeting
                                            </div>
                                            </div> 
                                        </div>
                                    </mat-list-option> -->
                                    <mat-list-option *ngIf="taskItem?.status_name == 'Open'&& taskItem?.attachments.length == 0"
                                     class="request-check" (click)="deleteTask(taskItem)">
                                        <div style="display:flex; align-items:center">
                                            <div>
                                                <mat-icon class="request-icon-btn">delete</mat-icon>
                                            </div>
                                            <div style="display:flex;align-items:center">
                                            <div style="display:flex; flex-direction:column; padding-left: 10px;">
                                                Delete
                                            </div>
                                            </div>  
                                        </div>
                                    </mat-list-option>
                                </mat-selection-list>
                            </mat-menu>     

                        </div>
                    </div>
                </div>
            </div>
        </ng-template>
    </div>

    <div *ngIf="!isLoading && taskList.length==0"
        style="padding-top: 3rem;">
        <div class="d-flex pb-2 justify-content-center align-items-center slide-in-top">
            <span class="extra-dark-txt">No task found ! Let's create one</span>
        </div>
    
        <div class="d-flex justify-content-center align-items-center slide-from-down pt-2 pb-2">
            <img src="https://assets.kebs.app/images/nomilestone.png" height="170" width="200" class="mt-2 mb-2">
        </div>
    
        <div class="d-flex justify-content-center slign-items-center slide-from-down">
          <button (click)="createNewTask()" mat-raised-button class="create-btn mt-2">
            Create Tasks
          </button>
        </div>
    </div>


</div>