const passport = require("passport");
const JwtStrategy = require("passport-jwt").Strategy;
const ExtractJwt = require("passport-jwt").ExtractJwt;

const passportOpts = {
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
  secretOrKey: process.env.JWT_SECRET,
};

module.exports = (passport) => {
  passport.use(
    new JwtStrategy(passportOpts, function (jwtPayload, done) {
      const expirationDate = new Date(jwtPayload.exp * 1000);
      if (expirationDate < new Date()) {
        return done(null, false);
      }
      done(null, jwtPayload);
    })
  );
  passport.serializeUser(function (user, done) {
    done(null, user);
  });
};







// const pool = require("./databaseCon").pool;
// const BearerStrategy = require("passport-azure-ad").BearerStrategy;
// const logger = require("./logger").logger;

// const opts = {
//   identityMetadata: process.env.tenantEndPoint,
//   clientID: process.env.clientId,
//   passReqToCallback: true,
//   isB2C: false
// };

// module.exports = passport => {
//   passport.use(
//     new BearerStrategy(opts, (req, token, done) => {
//       try {
//         let user = token;
//         return done(null, user);
//       } catch (err) {
//         return done(null, false);
//       }
//     })
//   );
// };
