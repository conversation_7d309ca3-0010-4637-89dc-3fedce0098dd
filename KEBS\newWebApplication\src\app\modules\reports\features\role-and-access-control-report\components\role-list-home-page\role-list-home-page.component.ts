import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { RoleAccessServiceService } from '../../service/role-access-service.service';
import { Subject } from 'rxjs';
import moment from 'moment';
import { DxDataGridComponent } from 'devextreme-angular';
import { MatSnackBar } from '@angular/material/snack-bar';
import * as _ from 'underscore';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { saveAs } from 'file-saver';
import * as ExcelJS from 'exceljs';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { RoleDetailsPageComponent } from '../role-details-page/role-details-page.component'
@Component({
  selector: 'app-role-list-home-page',
  templateUrl: './role-list-home-page.component.html',
  styleUrls: ['./role-list-home-page.component.scss']
})
export class RoleListHomePageComponent implements OnInit {

  isDataLoading = false
  isDeleteRoleLoading = false
  protected $onDestroy = new Subject<void>();
  protected $onAppApiCalled = new Subject<void>();
  tableData: any
  allMode = "allPages";
  checkBoxesMode = 'always';
  selectedRowKeys: any[] = [];
  allSelected: boolean = false;
  dataGridInstance: any;

  dataGridInstanceForBand: any;

  roleVisible = [
    {
      ID: 1,
      Name: "True",
    },
    {
      ID: 0,
      Name: "False",
    }
  ]
  @ViewChild(DxDataGridComponent, { static: false }) dataGrid: DxDataGridComponent;
  @ViewChild("roleGroupDataGrid", { static: false }) roleGroupDataGrid: DxDataGridComponent;

  isPermissionListDownloaded: boolean = false;

  constructor(private dialog: MatDialog, private api: RoleAccessServiceService, private snackBar: MatSnackBar,
    private _toaster: ToasterService) { }

  async ngOnInit() {
    await this.loadRoleList()

  }

  async loadRoleList() {
    this.isDataLoading = false
    this.tableData = []
    await this.api.getRoleList().pipe()
      .subscribe((res: any) => {
        if (res.messType == "S") {
          this.isDataLoading = true
          this.tableData = res["data"]
        }
        else {
          this.tableData = []
          this.isDataLoading = true

        }
      }, err => {
        console.error(err);
        this.isDataLoading = true
        this.tableData = []
      })
  }

  onGridReady(e: any) {
    this.dataGridInstance = e.component;
    this.dataGridInstanceForBand = e.component
  }

  onExporting(e: any): void {

    let current_time = new Date();

    let final = moment().format("DD-MM-YYYY");
    let report_name = `Role List Report - ${final}`
    let exportFileName = `${report_name}`; // Set your custom file name here
    e.component.beginUpdate();
    e.fileName = exportFileName;
    e.component.endUpdate();
  }

  openColumnChooser() {
    this.roleGroupDataGrid.instance.showColumnChooser();
  }

  exportGridToExcel() {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Main sheet');

    // Hide the action column
    this.dataGrid.instance.columnOption('actionColumn', 'view_role_details', false);

    exportDataGrid({
      component: this.dataGrid.instance,
      worksheet: worksheet,
      autoFilterEnabled: true
    }).then(() => {
      // Restore visibility
      this.dataGrid.instance.columnOption('actionColumn', 'view_role_details', true);

      workbook.xlsx.writeBuffer().then((buffer) => {
        saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'Role List Report.xlsx');
      });
    });
  }

  viewRoleDetails(data) {
    console.log("Clicked Role Details  : ",data)
    // this.getSummaryData(data.category_name)
    let roleGroupDetailsPopUp = this.dialog.open(RoleDetailsPageComponent, {
      width: '90%',
      height: '90%',
      autoFocus: false,
      data: data,
    });

    roleGroupDetailsPopUp.afterClosed().subscribe((res: any) => {
      if (res.messType == 'S') {
        this.loadRoleList()
      }
    });
  }

  async downloadPermissionList(){
    this.isPermissionListDownloaded = true
    this.api.downloadPermissionList().subscribe(async (res: any) => {
      if (res.messType == "S" && res["data"]?.length > 0) {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Permission List');

        // Get column headers from keys of the first object
        const columns = Object.keys(res.data[0]).map((key) => ({
          header: key,
          key: key,
          width: 20,
        }));
        worksheet.columns = columns;

        // Add rows
        res.data.forEach((item: any) => {
          worksheet.addRow(item);
        });

        const headerRow = worksheet.getRow(1);
        const lastCol = worksheet.columns.length;
        const lastColLetter = worksheet.getColumn(lastCol).letter;

        worksheet.autoFilter = {
          from: 'A1',
          to: `${lastColLetter}1`,
        };

        // Generate and download Excel file
        const buffer = await workbook.xlsx.writeBuffer();
        saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'Permission List Report.xlsx');
        this.isPermissionListDownloaded = false;
        return this._toaster.showSuccess("Success", "Permission List Report downloaded successfully", 3000);
      } else if (res.messType == "S" && res["data"]?.length == 0) {
        this.isPermissionListDownloaded = false;
        return this._toaster.showWarning("Warning", "No data available for download", 3000);
      }
    }, err => {
      console.error(err);
      this.isPermissionListDownloaded = false;
      return this._toaster.showError("Error", "Failed to download Permission List Report", 3000);
    });
  }
}
