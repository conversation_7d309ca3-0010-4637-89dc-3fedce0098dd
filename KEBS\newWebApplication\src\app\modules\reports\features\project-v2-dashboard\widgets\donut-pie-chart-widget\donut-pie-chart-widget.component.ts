import { Component, Input, OnInit, ViewContainerRef, TemplateRef, ViewChild  } from '@angular/core';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DecimalPipe } from '@angular/common';
import { Overlay, OverlayConfig, OverlayRef } from "@angular/cdk/overlay";
import { TemplatePortal } from "@angular/cdk/portal";

import { WidgetsService } from './../../services/widgets/widgets.service';
import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';
import { MatDialog } from '@angular/material/dialog';
import { SummaryDialogComponent } from 'src/app/modules/reports/features/project-v2-dashboard/widgets/summary-dialog/summary-dialog.component';
@Component({
  selector: 'app-donut-pie-chart-widget',
  templateUrl: './donut-pie-chart-widget.component.html',
  styleUrls: ['./donut-pie-chart-widget.component.scss'],
  providers:[DecimalPipe]
})
export class DonutPieChartWidgetComponent implements OnInit {

  @Input() aid: any = null;
  @Input() oid: any = null;
  @Input() widgetConfig: any = {};
  @Input() widgetType: string = '';
  @Input() startDate: any = null;
  @Input() endDate: any = null;
  @Input() filterData: any = null;
  @Input() filterQuery: any = '';
summaryData: any = null; // Add this in the class
  protected _onDestroy = new Subject<void>();

  calculatedWidgetHeight: string = '';
  calculatedChartWidgetHeight: number = null;

  isLoading: boolean = true;
  centerLabel: any = 'Total';

  private overlayRef: OverlayRef | null;

  @ViewChild("triggerInlineFilterTemplateRef", { static: false })
  triggerInlineFilterTemplateRef!: TemplateRef<HTMLElement>;

  data = [];
  constructor(
    private _widgetService: WidgetsService,
    private _toaster: ToasterService,
    private _decimalPipe: DecimalPipe,
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
       private _dialog: MatDialog
  ) { }

  async ngOnInit() {
    this.calculateHeight();
    await this.getChartData();
    this.isLoading = false;
  }

    /**
   * @description To calculate height dynamically
   */
    calculateHeight() {
      if (this.widgetConfig && this.widgetConfig?.height) {
        this.calculatedWidgetHeight = `calc(${this.widgetConfig.height} - 55px)`;
        this.calculatedChartWidgetHeight =
          parseInt(this.widgetConfig.height) - 75;
      }
    }

      /**
   * @description Get list view menu data
   */
  async getChartData() {
    if (
      !this.widgetConfig?.widget_config?.api ||
      this.widgetConfig?.widget_config?.api == ''
    ) {
      return;
    }

    let apiUrl = this.widgetConfig?.widget_config?.api;
    let payload = {
      aid: this.aid,
      oid: this.oid,
      startDate: this.startDate,
      endDate: this.endDate,
      filterData: this.filterData,
      filterQuery: this.filterQuery
    };

    this.data = [];
    this.isLoading = true;

    return new Promise((resolve, reject) => {
      this._widgetService
        .getDataDynamically(apiUrl, payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              const palette = this.widgetConfig?.widget_config?.palette || [];
              this.data =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
                this.data.forEach((item, index) => {
                  if (!item.color) {
                    item.color = palette[index % palette.length] || '#cccccc'; // Default to a gray color if palette is missing
                  }
                });
                this.summaryData = res['summary_data'] || null;
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            this.isLoading = false;
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching Widget Data',
              7000
            );
            this.isLoading = false;
            reject();
          },
        });
    });
  }
  openSummaryForm() {
  if (this.summaryData) {
    this._dialog.open(SummaryDialogComponent, {
      width: '90vw',
      data: {
        widgetData: this.summaryData
      }
    });
  } else {
    this._toaster.showWarning("Warning ⚠️", "Summary data is not available", 7000);
  }
}


    /**
   * @description Calculate total count
   */
    calculateTotal(pieChart) {
      const totalValue = pieChart
        .getAllSeries()[0]
        .getVisiblePoints()
        .reduce((s, p) => s + p.originalValue, 0);
      return this._decimalPipe.transform(totalValue, '1.0-0');
    }

      /**
   * @description Customize color of chart
   */
  customizePoint = (arg) => {
    return { color: arg?.data?.color };
  };

  /**
   * @description Customize legend text
   */
  customizeText = (arg) => {
    let count =
      this.data[arg.pointIndex][this.widgetConfig?.widget_config?.value_field];
    return arg.pointName + ` (${count})`;
  };

  openFilterOverlay(event: MouseEvent, filter: any) {
    event.stopPropagation();

    // Close the overlay if it is already open
    if (this.overlayRef) {
      this.closeOverlay();
    } else {
      const target = event.currentTarget as HTMLElement;

      // Create overlay config
      const config = new OverlayConfig({
        hasBackdrop: true,
        backdropClass: "cdk-overlay-transparent-backdrop",
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(target)
          .withPositions([
            {
              originX: "end",
              originY: "bottom",
              overlayX: "end",
              overlayY: "top",
            },
          ]),
      });

      // Create overlay reference
      this.overlayRef = this.overlay.create(config);

      // Attach the template to the overlay
      this.overlayRef.attach(
        new TemplatePortal(this.triggerInlineFilterTemplateRef, this.viewContainerRef, {
          $implicit: filter // Ensure 'filter' is of the correct type
        } as any) // Optionally, use 'as any' if the type is complex or not directly inferred
      );

      // Close overlay on backdrop click
      this.overlayRef.backdropClick().subscribe(() => this.closeOverlay());
    }
  }

  closeOverlay() {
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
    }
  }

  async updateFilterData(filter_id, filter){
    filter.default_filter_id = filter_id;
    this.closeOverlay();
    await this.getChartData();
  }

  
  getDefaultFilterLabel(filter: any): string {
    if (!filter || !filter.filter_data || !filter.default_filter_id) {
      return '';
    }
  
    const defaultFilter = filter.filter_data.find(
      (item: any) => item.id === filter.default_filter_id
    );
    return defaultFilter ? defaultFilter.label : '';
  }
}
