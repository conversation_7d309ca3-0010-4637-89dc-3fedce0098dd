pipelines:
  custom:
    refresh-branches:
      - step:
          name: Refresh Dev1, Development and Quality Branches with Preprd
          image: atlassian/default-image:3
          script:
            - git config --global user.email "<EMAIL>"
            - git config --global user.name "<PERSON><PERSON> DevOps"
            - git clone https://$BB_USERNAME:$<EMAIL>/dinesh1294/kebs-new.git repo
            - cd repo
            - git fetch origin
            - for branch in dev1 development quality-assurance; do
                git checkout $branch || git checkout -b $branch origin/$branch;
                git reset --hard origin/preprd;
                git push --force origin $branch;
              done