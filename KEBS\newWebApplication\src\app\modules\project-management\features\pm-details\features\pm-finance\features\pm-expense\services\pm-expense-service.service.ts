import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PmExpenseServiceService {


getExpenseData(project_id, item_id){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/financial/getExpenseData",{project_id, item_id}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }

  constructor(private http: HttpClient) { }
}
