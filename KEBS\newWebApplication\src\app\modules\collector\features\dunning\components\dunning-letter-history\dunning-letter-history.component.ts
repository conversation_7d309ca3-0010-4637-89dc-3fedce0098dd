import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA} from '@angular/material/dialog';
import * as _ from "underscore";
import {
  MatDialog
} from "@angular/material/dialog";
import { DunningService } from '../../../../services/dunning-service/dunning.service';
import * as moment from "moment";

@Component({
  selector: 'app-dunning-letter-history',
  templateUrl: './dunning-letter-history.component.html',
  styleUrls: ['./dunning-letter-history.component.scss']
})
export class DunningLetterHistoryComponent implements OnInit {

  constructor(private Dialog: MatDialog,
    public dialogRef: MatDialogRef<DunningLetterHistoryComponent>,
    @Inject(MAT_DIALOG_DATA) public inData: any,
    private dunningService: DunningService) { }

    customer_id_input: any;
    dunningHistoryData: any = [];
    entireResponse: any;
    customerName: any;
    paymentDaysColumn: boolean = false;
    DunningFromMailID: any;

  ngOnInit() {

  //Customer ID Input from Card Component
  this.customer_id_input = this.inData.customer_id;

  //Dunning History Data
  this.dunningService.dunningHistoryDetails(this.customer_id_input).then(
    res => {  
      this.entireResponse = res;
      this.dunningHistoryData = JSON.parse(this.entireResponse[0].dunning_logs);

     
     //Customer Name 
      if(this.dunningHistoryData != null){
        this.customerName = this.dunningHistoryData[0].customer_name;   
      }
      else{
        this.customerName = "";
      }
  
    },
  err => {
    console.log(err);
  })
      
   }

//Close the Dialog
 closeDialog(){
  this.dialogRef.close();
}

//Moment for Dates
getDateFormat(date){
  return moment(date).format('DD-MMM-YYYY');
}

getDate(date) {
  if (date === null || date === undefined) {
    return "-";
  } else {
    let localTime = new Date(date);
    let timezoneOffset = localTime.getTimezoneOffset() * 60000;
    localTime.setTime(localTime.getTime() - timezoneOffset);
    return moment(localTime).format('DD-MMM-YYYY hh:mm:ss A');
  }
}

}
