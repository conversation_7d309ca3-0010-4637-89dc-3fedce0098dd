import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import * as moment from 'moment'
import * as _ from 'underscore'
import { RolesService } from 'src/app/services/acl/roles.service';
import { MailUtilityService } from 'src/app/app-shared/app-shared-components/mail-box-modal/services/mail-utility.service';
@Injectable({
  providedIn: 'root'
})
export class DunningService {

  constructor(
    private $http: HttpClient,
    private roleService: RolesService,
    public mailUtilityService: MailUtilityService,
  ) { }


  // Collector Role Access
  getCollectorRoleAccess = () => {
    return this.$http.post('api/collector/getCollectorRoleAccess', {
    })
  }

  getDunningList = (filterConfig,collectorRoleAccessData) => {
    return this.$http.post('api/collector/getDunningList', {
      filterConfig: filterConfig,
      collectorRoleAccessData : collectorRoleAccessData
    })
  }

  dunningHistoryDetails = (customer_id) => {
    return new Promise((resolve, reject) => {
      this.$http.post("/api/invoice/dunningHistoryData", { customer_id }).subscribe(
        res => {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    });
  }

  dunningAgeingPayments = (customer_id) => {
    return new Promise((resolve, reject) => {
      this.$http.post("/api/collector/customerDunningDetails", { customer_id })
        .subscribe((res) => {
          return resolve(res);
        }, (err) => {
          return reject(err)
        })
    })
  }

  dunningAgeingPaymentsForSingle = (customer_id) =>{
    return this.$http.post("/api/collector/customerDunningDetails", { customer_id })
  }

  getDunningTemplate = () => {
    return new Promise((resolve, reject) => {
      this.$http.post("/api/collector/getDunningTemplate", {})
        .subscribe((res) => {
          return resolve(res);
        }, (err) => {
          return reject(err)
        })
    })
  }

  saveDunningRecipientEmail = (type, emailIds, customerId) => {
    return this.$http.post("/api/collector/saveDunningRecipientEmail", {
      type: type,
      emailIds: emailIds,
      customerId: customerId
    })
  }

  formatDunningDataForMailComponent = (data: any, dunningTemplate: any) => {

    let result = {}
    let fromMailIds
    let toMailIds
    let ccMailIds
    let bccMailIds
    let alertFlag
    let customerName
    let currency
    let totalValueFinal
    let paymentTermDays
    let paymentDaysColumn
    let differentPaymentDays
    let outstandingValue: any = []
    let finalOutstandingValueArray: any = []
    let isOutstandingValueZero
    let finalTodaysDate = moment().format('DD-MM-YYYY');
    let isTaxAmountColumn: boolean = false;
    let taxTotal = 0;

    let bodyText = dunningTemplate[0].template.body_text;
    let text = ''
    let finalText = dunningTemplate[0].template.final_text
    let subjectTxt = 'Outstanding details as on ' + finalTodaysDate + ' for your reference.';

    fromMailIds = _.pluck(data.from_mail_id, 'from_mail_ID');

    toMailIds = data.to_mail_id ? data.to_mail_id : [];

    ccMailIds = data.cc ? data.cc : [];


    bccMailIds = data.bcc ? data.bcc : [];

    alertFlag = data.alertFlag;

    //-----check if tax amount column needed to be added or not


    for (let item of data.result) {
      if (item.tax_amount && item.tax_amount > 0) {
        isTaxAmountColumn = true;
        taxTotal += item.tax_amount;
        break
      }
    }

    //--------------------------------------------------------

    customerName = _.uniq(_.pluck(data.result, 'customer_name'));

    currency = _.uniq(_.pluck(data.result, 'currency'));

    totalValueFinal = data.totalOutstandingValue

    paymentTermDays = data.creditPeriodDays ? data.creditPeriodDays : 30;

    paymentDaysColumn = _.every(
      data.result,
      (val) =>
        val.Payment_term_days ==
        data.result[0].Payment_term_days
    );

    differentPaymentDays = _.uniq(_.pluck(data.result, 'Payment_term_days'));

    for (let j = 0; j < differentPaymentDays.length; j++) {
      let temp = 0;

      for (let i = 0; i < data.result.length; i++) {
        if (
          data.result[i].Credit_period_days ==
          (this.getLabelForCM(221, 1) == "" ? data.result[i].Credit_period_days : (this.getLabelForCM(221, 1))) + ' Payment'
        ) {
          if (
            differentPaymentDays[j] ==
            data.result[i].Payment_term_days
          ) {
            temp += Number(data.result[i].Value);
          }
        }
      }

      outstandingValue.push(temp);
    }

    for (let i = 0; i < differentPaymentDays.length; i++) {
      let tempArray = {
        days: differentPaymentDays[i],
        value: outstandingValue[i],
      };
      finalOutstandingValueArray.push(tempArray);
    }

    if (outstandingValue == 0) {
      isOutstandingValueZero = true;
    }


    bodyText = bodyText.replace("[CURRENTDATE]", finalTodaysDate);
    bodyText = bodyText.replace("[CURRENCY]", currency);
    bodyText = bodyText.replace("[TOTALVALUE]", totalValueFinal);


    if (isOutstandingValueZero == false) {
      let outstanding_text = "";
      for (let i = 0; i < finalOutstandingValueArray.length; i++) {
        outstanding_text +=
          "<li style='color:red'> More than " +
          finalOutstandingValueArray[i].days +
          ' days outstanding value ' +
          currency +
          ' ' +
          finalOutstandingValueArray[i].value +
          '</li>';
      }

      bodyText = bodyText.replace("[OUTSTANDINGTEXT]", outstanding_text);
    } else {
      bodyText = bodyText.replace("[OUTSTANDINGTEXT]", "");
    }

    text +=
      "<table border='1' style='width:1000px !important; border-collapse: collapse !important;'>";
    text += "<tr>";
    text += "<th width='200' style='text-align:center;'>Project</th>";
    text +=
      "<th width='200' style='text-align:center;'>Milestone Name</th>";
    text +=
      "<th width='100' style='text-align:center;'>Invoice Date</th>";
    text +=
      "<th width='100' style='text-align:center;'>Invoice Number</th>";
    text +=
      "<th width='100' style='text-align:center;'>Value in " +
      currency +
      '</th>';

    //----------Tax amount column to be included based on condition---------  
    if (isTaxAmountColumn) {
      text += "<th width='100' style='text-align:center;'>Tax Amount in " + currency + "</th>";
    }
    //----------------------------------------------------------------------

    text += "<th width='100' style='text-align:center;'>Aging</th>";
    if (paymentDaysColumn == false) {
      text +=
        "<th width='100' style='text-align:center;'>Payment Term Days</th>";
      text +=
        "<th width='100' style='text-align:center;'>Payment Status</th>";
    }
    if (paymentDaysColumn == true) {
      text +=
        "<th width='100' style='text-align:center;'>" +
        paymentTermDays +
        ' days credit period</th>';
    }
    text += '</tr>';

    _.each(data.result, (val) => {
      text += "<tr>";
      text +=
        "<td width='200' style='text-align:center; font-weight:bold;'>" +
        val.project_name +
        '</td>';
      text +=
        "<td width='200' style='text-align:center; font-weight:bold;'>" +
        val.milestone_name +
        '</td>';
      text +=
        "<td width='100' style='text-align:center; font-weight:bold;'>" +
        val.invoice_date +
        '</td>';
      text +=
        "<td width='100' style='text-align:center; font-weight:bold;'>" +
        val.invoice_no +
        '</td>';
      text +=
        "<td width='100' style='text-align:center; font-weight:bold;'>" +
        val.Value +
        '</td>';

      if (isTaxAmountColumn) {
        text +=
          "<td width='100' style='text-align:center; font-weight:bold;'>" +
          val.tax_amount +
          '</td>';
      }

      text +=
        "<td width='100' style='text-align:center; font-weight:bold;'>" +
        val.Ageing +
        '</td>';

      if (paymentDaysColumn == false) {
        text +=
          "<td width='100' style='text-align:center; font-weight:bold;'>" +
          val.Payment_term_days +
          '</td>';
      }

      if (
        val.Credit_period_days == (this.getLabelForCM(221, 1) == "" ? val.Credit_period_days : (this.getLabelForCM(221, 1))) + ' Payment' &&
        alertFlag == false
      ) {
        text +=
          "<td width='100' style='text-align:center; font-weight:bold; color:#cf0001;'>" +
          val.Credit_period_days +
          '</td>';
      } else {
        text +=
          "<td width='100' style='text-align:center; font-weight:bold;'>" +
          val.Credit_period_days +
          '</td>';
      }
      text += '</tr>';
    });

    text += "<tr>";
    text +=
      "<td width='200' style='text-align:center; font-weight:bold;'>Total Outstanding</td>";
    text +=
      "<td width='200' style='text-align:center; font-weight:bold;'></td>";
    text +=
      "<td width='100' style='text-align:center; font-weight:bold;'></td>";
    text +=
      "<td width='100' style='text-align:center; font-weight:bold;'></td>";
    text +=
      "<td width='100' style='text-align:center; font-weight:bold;'>" + totalValueFinal + '</td>';

    if (isTaxAmountColumn) {
      text +=
        "<td width='100' style='text-align:center; font-weight:bold;'>" + taxTotal + '</td>';
    }
    if (paymentDaysColumn == false) {
      text +=
        "<td width='100' style='text-align:center; font-weight:bold;'></td>";
    }
    text +=
      "<td width='100' style='text-align:center; font-weight:bold;'></td>";
    text +=
      "<td width='100' style='text-align:center; font-weight:bold;'></td>";
    text += '</tr>';

    text += '</table><br/>';

    let mailBody = bodyText + text + finalText
    result = {

      fromMailIds: fromMailIds,
      toMailIds: toMailIds,
      ccMailIds: ccMailIds,
      authorizedMailSenders: fromMailIds,
      uniqueId: data.result[0].customer_id,
      uniqueName: customerName,
      bccMailIds: bccMailIds,
      subjectTxt: subjectTxt,
      mailBody: mailBody

    }

    return result

  }

  formatDunningTemplatesDataForMailComponent= (data: any, dunningTemplate: any, dateFormats: any) => {
    console.log(dunningTemplate)
    console.log(data)
    let result = {};
    let fromMailIds;
    let toMailIds;
    let ccMailIds;
    let bccMailIds;
    let alertFlag;
    let customerName;
    let currency;
    let currencySymbol;
    let totalValueFinal;
    let currentMonth = moment().format("MMM 'YY");
    let TodaysDate = moment().format('DD-MM-YYYY');
    let config = JSON.parse(dunningTemplate.table_config)
    let bodyText = dunningTemplate.template.body_text;
    let text = '';
    let finalText = dunningTemplate.template.final_text;
    let mailContentDateFormat = dateFormats?.invoice_mail_date_format ? dateFormats.invoice_mail_date_format : 'DD-MM-YYYY';
    let finalTodaysDate =  moment().format(mailContentDateFormat);
    let subjectTxt = dunningTemplate.template.subject_text ? dunningTemplate.template.subject_text : 'Outstanding details as on ' + finalTodaysDate + ' for your reference.';
  
    fromMailIds = _.pluck(data.from_mail_id, 'from_mail_ID');
    toMailIds = data.to_mail_id ? data.to_mail_id : [];
    ccMailIds = data.cc ? data.cc : [];
    bccMailIds = data.bcc ? data.bcc : [];
    alertFlag = data.alertFlag;
    let spocName = data.spoc_name
    let defaultSpocName = dunningTemplate?.default_spoc_name
  
    customerName = _.uniq(_.pluck(data.result, 'customer_name'));
    currency = _.uniq(_.pluck(data.result, 'currency'));
    currencySymbol = _.uniq(_.pluck(data.result, 'currency_symbol'));
    totalValueFinal = data.totalOutstandingValue;
    let currentDate = moment().format(mailContentDateFormat);

    let res = data.result
    totalValueFinal = res?.reduce((accumulator, currentValue) => {
      const value = parseFloat(currentValue.Value.replace(/,/g, ''));
      if (!isNaN(value)) {
          return accumulator + value;
      }
      return accumulator;
  }, 0);
  
    if (bodyText?.includes('[CURRENTDATE]')) {
      bodyText = bodyText.replace('[CURRENTDATE]', TodaysDate);
    }
    if (bodyText?.includes('[CURRENTDATEFORMAT]')) {
      bodyText = bodyText.replace('[CURRENTDATEFORMAT]', currentDate);
    }
    if (bodyText?.includes('[CURRENTMONTH]')) {
      bodyText = bodyText.replace('[CURRENTMONTH]', currentMonth);
    }
    
    if (bodyText?.includes('[CURRENCY]')) {
      bodyText = bodyText.replace('[CURRENCY]', currency);
    }
    
    if (bodyText?.includes('[TOTALVALUE]')) {
      bodyText = bodyText.replace('[TOTALVALUE]', totalValueFinal);
    }

    if (subjectTxt?.includes('[CURRENTDATE]')) {
      subjectTxt = subjectTxt.replace('[CURRENTDATE]', TodaysDate);
    }
    if (subjectTxt?.includes('[CURRENTDATEFORMAT]')) {
      subjectTxt = subjectTxt.replace('[CURRENTDATEFORMAT]', currentDate);
    }

    if (subjectTxt?.includes('[CURRENTMONTH]')) {
      subjectTxt = subjectTxt.replace('[CURRENTMONTH]', currentMonth);
    }
    spocName = this.mailUtilityService.mailUiData.mailInputFields?.value?.spocName ? this.mailUtilityService.mailUiData.mailInputFields?.value?.spocName : spocName
    if (bodyText?.includes('[SPOC]')) {
      if(spocName != null){
        bodyText = bodyText.replace('[SPOC]', spocName);
      }
      else{
        bodyText = bodyText.replace('[SPOC]', defaultSpocName);
      }
    }

    const usd_formatter = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2, maximumFractionDigits: 2});

    const inr_formatter = new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: 2, maximumFractionDigits: 2});


    if(currency == "INR"){
      totalValueFinal = inr_formatter.format(totalValueFinal);
    }
    else{
      totalValueFinal = usd_formatter.format(totalValueFinal);
    }

  
    text +=
      "<table border='1' style='border-collapse: collapse !important; width: 100% !important; table-layout: auto !important;'>";
    text += '<tr>';


  
    // Generate table header based on the configuration
    config.forEach((column) => {
      if (column.is_active && column.key != 'Value') {
        const width = `width='${column.width}'`;
        text += `<th ${width} style='text-align:center;'>${column.description}</th>`;
      }
      else if(column.is_active && column.key == 'Value'){
        const width = `width='${column.width}'`;
        text += `<th ${width} style='text-align:center;'>${column.description} - ${currencySymbol || currency}</th>`;
      }
    });
  
    text += '</tr>';

  
    // Generate table data based on the configuration
    _.each(data.result, (val) => {
      text += '<tr>';
      config.forEach((column) => {
        if (column.is_active && column.is_to_format == 0) {
          const width = `width='${column.width}'`;
          text += `<td ${width} style='text-align:center; font-weight:bold;'>${val[column.key]}</td>`;
        }
        if(column.is_active && column.is_to_format == 1){
        const width = `width='${column.width}'`;
        text += `<td ${width} style='text-align:center; font-weight:bold;'>${column.format ? moment(val[column.key]).format(column.format) : val[column.key]}</td>`;
        }
      });
  
      text += '</tr>';
    });


    text += "<tr>";
    text +=
      "<td width='150' style='text-align:center; font-weight:bold;'></td>";
    text +=
      "<td width='100' style='text-align:center; font-weight:bold;'></td>";
    text +=
      "<td width='200' style='text-align:center; font-weight:bold;'>Total</td>";
    text +=
      "<td width='100' style='text-align:center; font-weight:bold;'>" + totalValueFinal + '</td>';
    text +=
      "<td width='100' style='text-align:center; font-weight:bold;'></td>";

    text += "</tr>";

    text += '</table><br/>';


  
    let mailBody = bodyText + text + finalText;
    result = {
      fromMailIds: fromMailIds,
      toMailIds: toMailIds,
      ccMailIds: ccMailIds,
      authorizedMailSenders: fromMailIds,
      uniqueId: data.result[0].customer_id,
      uniqueName: customerName,
      bccMailIds: bccMailIds,
      subjectTxt: subjectTxt,
      mailBody: mailBody,
      spocName : spocName

    }

    return result

  }


  getLabelForCM = (application_id, id) => {

    let val = _.where(this.roleService.label, { application_id: application_id, id: id })
    return val.length > 0 ? val[0]['label_name'] : ""
  }

  getAllDunningTemplate = () => {
    return new Promise((resolve, reject) => {
      this.$http.post("/api/collector/getAllDunningTemplate", {})
        .subscribe((res) => {
          return resolve(res);
        }, (err) => {
          return reject(err)
        })
    })
  }

  saveDunningSpocName = (spocName, customerId) => {
    return this.$http.post("/api/collector/saveDunningSpocName", {
      spocName: spocName,
      customerId: customerId
    })
  }
  getTenantDateFormats = () =>{
    return this.$http.post("/api/invoice/v2/getTenantDateFormats",{});
  }

  /** 
  * @description get tenant role details
  */
  getInvoiceTenantCheckDetail(tenantName,checkType) {
    return this.$http.post("/api/invoice/getInvoiceTenantApplicationCheck", {
      tenantName : tenantName,
      checkType : checkType
    });
  }

  getSendMailAuthorization = () =>{
    return this.$http.post("/api/invoice/v2/getSendMailAuthorization",{});
  }
  

}
