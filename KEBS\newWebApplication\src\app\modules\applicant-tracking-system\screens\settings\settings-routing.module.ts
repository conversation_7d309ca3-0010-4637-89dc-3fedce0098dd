import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'template',
  },
  {
    path: 'template',
    loadChildren: () =>
      import(`./templates/templates.module`).then((m) => m.TemplatesModule),
    data: { breadcrumb: 'Templates' },
  },
  {
    path: 'organization',
    loadChildren: () =>
      import(`./company/company.module`).then((m) => m.CompanyModule),
    data: { breadcrumb: 'Organization' },
  },
  {
    path: 'activity-logs',
    loadChildren: () =>
      import(`./activity-logs/activity-logs.module`).then(
        (m) => m.ActivityLogsModule
      ),
    data: { breadcrumb: 'Activity Logs' },
  },
  {
    path: 'profile',
    loadChildren: () =>
      import(`./profile/profile.module`).then((m) => m.ProfileModule),
    data: { breadcrumb: 'Profile' },
  },
  {
    path: 'vendor',
    loadChildren: () =>
      import(`./vendor/vendor.module`).then((m) => m.VendorModule),
    data: { breadcrumb: 'Vendor Management' },
  },
  {
    path: 'user',
    loadChildren: () => import(`./user/user.module`).then((m) => m.UserModule),
    data: { breadcrumb: 'User' },
  },
  {
    path: 'role',
    loadChildren: () =>
      import(`./roles/roles.module`).then((m) => m.RolesModule),
    data: { breadcrumb: 'Roles & Permissions' },
  },
  {
    path: 'college',
    loadChildren: () =>
      import(`./college/college.module`).then((m) => m.CollegeModule),
    data: { breadcrumb: 'Colleges' },
  },
  {
    path: 'new-hire-tasks',
    loadChildren: () =>
      import(`./onboarding-tasks/onboarding-tasks.module`).then(
        (m) => m.NewHireTasksModule
      ),
    data: { breadcrumb: 'Onboarding Setting' },
  },
  {
    path: 'new-hire-checklists',
    loadChildren: () =>
      import(`./onboarding-checklists/onboarding-checklists.module`).then(
        (m) => m.NewHireChecklistsModule
      ),
    data: { breadcrumb: 'Onboarding Setting' },
  },
  {
    path: 'course-degree',
    loadChildren: () =>
      import(`./course-degree/course-degree.module`).then(
        (m) => m.courseDegreeModule
      ),
    data: { breadcrumb: 'Degree & Course' },
  },
  {
    path: 'location',
    loadChildren: () =>
      import(`./location/location.module`).then((m) => m.LocationModule),
    data: { breadcrumb: 'Location' },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SettingsRoutingModule {}
