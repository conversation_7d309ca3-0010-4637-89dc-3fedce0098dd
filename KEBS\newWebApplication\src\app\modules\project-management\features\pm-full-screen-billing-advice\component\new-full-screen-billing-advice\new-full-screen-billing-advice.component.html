<div class="loader-class"  *ngIf="loading">
  <div class="loader-container">
    <mat-spinner class="green-spinner" diameter="30"></mat-spinner>
  </div>
</div>
<div class="billing-advice" *ngIf="!loading">
  <div class="row pl-3 pr-3 header" *ngIf = "!approverView">
    <div class="header-name-section">
      <div class="header-name-section-1">
          <div class="back-action" (click)="returnback()">
        <mat-icon class="back-icn-class">keyboard_arrow_left</mat-icon>
      </div>


      <div class="item-container" *ngIf="!isToShowProjectName" [tooltip]="(pmNameService.profitCenter ? ('#' + pmNameService.profitCenter) : '') +
                        ' ' +
                        (data.label ? data.label : '')" placement="top" showDelay="500">
          <span class="item-label" *ngIf="pmNameService.profitCenter">
            #{{ pmNameService.profitCenter | maxellipsis:50 }}
          </span>
        
          <span class="item-name" *ngIf="data.label">
            {{ data.label | maxellipsis:100 }}
          </span>
        </div>

      <div class="item-container" *ngIf="isToShowProjectName" [tooltip]="(pmNameService.projectName && data.label) ? (pmNameService.projectName + ' - ' + data.label) : ''" placement="top" showDelay="500">

        <span class="item-label" *ngIf="pmNameService.projectName">
            {{ pmNameService.projectName | maxellipsis:50 }}
        </span>
          <span class="item-name" *ngIf="data.label">
            {{ data.label | maxellipsis:100 }}
          </span>
        </div>
      </div>
    
<div class="header-name-section-2">
<div class="status-class" [ngStyle]="{ border: data.border, background: data.background }">
  <span class="text-status" [ngStyle]="{ color: data.color }">{{
    data?.status_name
    }}</span>
</div>
</div>

      
    </div>
    <div class="header-action-section">
      <div class="order-value-class" *ngIf="'order-value' | checkActive : this.formConfig : 'new-billing-advice' ">
        <span class="order-label">{{
          "order-value"
            | checkLabel : this.formConfig : "new-billing-advice" : "Order Value"
        }}</span>
        <span class="order-text">
          <app-currency
            [currencyList]="item_value"
            [code]="code"
            [font_size]="currency_font_size"
            class="flex-1"
            type="big"
          >
          </app-currency>
        </span>
      </div>
      <!-- <div class="sync-class" *ngIf="roundOffSyncAccess && (matrixConfig?.billing_advice_stepper) && ((employeeBased && items.length > 0) ||
        (positionBased && PositionData.length > 0))">
        <span *ngIf="!roundoff_sync" (click)="syncRoundoff()" tooltip="Sync Now"
          ><mat-icon class="sync-icn">sync</mat-icon></span
        >
        <span *ngIf="roundoff_sync" tooltip="Sync In-Progress!">
          <img
            src="https://assets.kebs.app/round_off_sync_gif.gif"
            class="gif-class"
          />
        </span>
      </div> -->
      <div
      class="comments-class"
      (click)="addNotes()"
      *ngIf="
        'milestone_notes'
          | checkActive : this.formConfig : 'milestone-landing'
      "
      >
      <svg *ngIf="!is_comment_present" width="13" height="13" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" >
        <path d="M0.333984 13.6654V1.66536C0.333984 1.2987 0.46454 0.984809 0.725651 0.723698C0.986762 0.462587 1.30065 0.332031 1.66732 0.332031H12.334C12.7007 0.332031 13.0145 0.462587 13.2757 0.723698C13.5368 0.984809 13.6673 1.2987 13.6673 1.66536V9.66536C13.6673 10.032 13.5368 10.3459 13.2757 10.607C13.0145 10.8681 12.7007 10.9987 12.334 10.9987H3.00065L0.333984 13.6654ZM2.43398 9.66536H12.334V1.66536H1.66732V10.4154L2.43398 9.66536Z" fill="#6E7B8F"/>
      </svg>
      <svg *ngIf="is_comment_present" width="13" height="13" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" >
        <path d="M0.333984 13.6654V1.66536C0.333984 1.2987 0.46454 0.984809 0.725651 0.723698C0.986762 0.462587 1.30065 0.332031 1.66732 0.332031H12.334C12.7007 0.332031 13.0145 0.462587 13.2757 0.723698C13.5368 0.984809 13.6673 1.2987 13.6673 1.66536V9.66536C13.6673 10.032 13.5368 10.3459 13.2757 10.607C13.0145 10.8681 12.7007 10.9987 12.334 10.9987H3.00065L0.333984 13.6654ZM2.43398 9.66536H12.334V1.66536H1.66732V10.4154L2.43398 9.66536Z" fill="#EE4961"/>
      </svg>
     
     
      </div>
      <div
      class="attachment-class"
      (click)="addAttachment(data.Mid, data.gantt_id)"
      
      >
      <svg
        width="15"
        height="20"
        viewBox="0 0 15 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        *ngIf="!is_attachment_present"
      >
        <path
          d="M5.85529 19.5C4.23129 19.5 2.85262 18.93 1.71929 17.79C0.585953 16.6502 0.0192871 15.2683 0.0192871 13.6443V4.65375C0.0192871 3.49992 0.419954 2.51917 1.22129 1.7115C2.02245 0.903834 2.99995 0.5 4.15379 0.5C5.30762 0.5 6.2852 0.903834 7.08654 1.7115C7.88787 2.51917 8.28854 3.49992 8.28854 4.65375V11.25C8.28854 11.4625 8.21662 11.6407 8.07279 11.7845C7.92895 11.9282 7.75079 12 7.53829 12C7.32562 12 7.14745 11.9282 7.00379 11.7845C6.86029 11.6407 6.78854 11.4625 6.78854 11.25V4.64425C6.7782 3.90575 6.5217 3.28042 6.01904 2.76825C5.51637 2.25608 4.89462 2 4.15379 2C3.41612 2 2.79262 2.25767 2.28329 2.773C1.77395 3.2885 1.51929 3.91542 1.51929 4.65375V13.6443C1.50895 14.8533 1.92687 15.8814 2.77304 16.7288C3.6192 17.5763 4.64679 18 5.85579 18C6.13912 18 6.4122 17.9708 6.67504 17.9125C6.93787 17.8542 7.18912 17.7802 7.42879 17.6905C7.62495 17.616 7.8187 17.6186 8.01004 17.6982C8.2012 17.7779 8.33504 17.9157 8.41154 18.1115C8.48587 18.3077 8.4817 18.5019 8.39904 18.6943C8.31637 18.8866 8.17695 19.0199 7.98079 19.0943C7.64995 19.2276 7.3067 19.3285 6.95104 19.397C6.5952 19.4657 6.22995 19.5 5.85529 19.5ZM10.9228 18.5C10.7101 18.5 10.532 18.4281 10.3885 18.2843C10.2449 18.1406 10.173 17.9625 10.173 17.75V15.6923H8.11554C7.90304 15.6923 7.72487 15.6203 7.58104 15.4765C7.43737 15.3327 7.36554 15.1545 7.36554 14.942C7.36554 14.7293 7.43737 14.5513 7.58104 14.4078C7.72487 14.2641 7.90304 14.1923 8.11554 14.1923H10.173V12.1348C10.173 11.9223 10.245 11.7441 10.3888 11.6003C10.5326 11.4566 10.7109 11.3848 10.9235 11.3848C11.136 11.3848 11.3141 11.4566 11.4578 11.6003C11.6013 11.7441 11.673 11.9223 11.673 12.1348V14.1923H13.7308C13.9433 14.1923 14.1214 14.2642 14.265 14.408C14.4089 14.5518 14.4808 14.7301 14.4808 14.9427C14.4808 15.1552 14.4089 15.3333 14.265 15.477C14.1214 15.6205 13.9433 15.6923 13.7308 15.6923H11.673V17.75C11.673 17.9625 11.6011 18.1406 11.4573 18.2843C11.3135 18.4281 11.1353 18.5 10.9228 18.5ZM4.15354 14.9423C3.94104 14.9423 3.76295 14.8704 3.61929 14.7268C3.47579 14.5829 3.40404 14.4047 3.40404 14.1923V5.13475C3.40404 4.92225 3.47587 4.74408 3.61954 4.60025C3.76337 4.45658 3.94162 4.38475 4.15429 4.38475C4.36679 4.38475 4.54487 4.45658 4.68854 4.60025C4.83204 4.74408 4.90379 4.92225 4.90379 5.13475V14.1923C4.90379 14.4047 4.83187 14.5829 4.68804 14.7268C4.54437 14.8704 4.3662 14.9423 4.15354 14.9423ZM10.9228 9.19225C10.7101 9.19225 10.532 9.12042 10.3885 8.97675C10.2449 8.83292 10.173 8.65475 10.173 8.44225V5.13475C10.173 4.92225 10.245 4.74408 10.3888 4.60025C10.5326 4.45658 10.7109 4.38475 10.9235 4.38475C11.136 4.38475 11.3141 4.45658 11.4578 4.60025C11.6013 4.74408 11.673 4.92225 11.673 5.13475V8.44225C11.673 8.65475 11.6011 8.83292 11.4573 8.97675C11.3135 9.12042 11.1353 9.19225 10.9228 9.19225Z"
          fill="#6E7B8F"
        />
      </svg>
      <svg
        width="15"
        height="20"
        viewBox="0 0 15 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        *ngIf="is_attachment_present"
      >
        <path
          d="M5.85529 19.5C4.23129 19.5 2.85262 18.93 1.71929 17.79C0.585953 16.6502 0.0192871 15.2683 0.0192871 13.6443V4.65375C0.0192871 3.49992 0.419954 2.51917 1.22129 1.7115C2.02245 0.903834 2.99995 0.5 4.15379 0.5C5.30762 0.5 6.2852 0.903834 7.08654 1.7115C7.88787 2.51917 8.28854 3.49992 8.28854 4.65375V11.25C8.28854 11.4625 8.21662 11.6407 8.07279 11.7845C7.92895 11.9282 7.75079 12 7.53829 12C7.32562 12 7.14745 11.9282 7.00379 11.7845C6.86029 11.6407 6.78854 11.4625 6.78854 11.25V4.64425C6.7782 3.90575 6.5217 3.28042 6.01904 2.76825C5.51637 2.25608 4.89462 2 4.15379 2C3.41612 2 2.79262 2.25767 2.28329 2.773C1.77395 3.2885 1.51929 3.91542 1.51929 4.65375V13.6443C1.50895 14.8533 1.92687 15.8814 2.77304 16.7288C3.6192 17.5763 4.64679 18 5.85579 18C6.13912 18 6.4122 17.9708 6.67504 17.9125C6.93787 17.8542 7.18912 17.7802 7.42879 17.6905C7.62495 17.616 7.8187 17.6186 8.01004 17.6982C8.2012 17.7779 8.33504 17.9157 8.41154 18.1115C8.48587 18.3077 8.4817 18.5019 8.39904 18.6943C8.31637 18.8866 8.17695 19.0199 7.98079 19.0943C7.64995 19.2276 7.3067 19.3285 6.95104 19.397C6.5952 19.4657 6.22995 19.5 5.85529 19.5ZM10.9228 18.5C10.7101 18.5 10.532 18.4281 10.3885 18.2843C10.2449 18.1406 10.173 17.9625 10.173 17.75V15.6923H8.11554C7.90304 15.6923 7.72487 15.6203 7.58104 15.4765C7.43737 15.3327 7.36554 15.1545 7.36554 14.942C7.36554 14.7293 7.43737 14.5513 7.58104 14.4078C7.72487 14.2641 7.90304 14.1923 8.11554 14.1923H10.173V12.1348C10.173 11.9223 10.245 11.7441 10.3888 11.6003C10.5326 11.4566 10.7109 11.3848 10.9235 11.3848C11.136 11.3848 11.3141 11.4566 11.4578 11.6003C11.6013 11.7441 11.673 11.9223 11.673 12.1348V14.1923H13.7308C13.9433 14.1923 14.1214 14.2642 14.265 14.408C14.4089 14.5518 14.4808 14.7301 14.4808 14.9427C14.4808 15.1552 14.4089 15.3333 14.265 15.477C14.1214 15.6205 13.9433 15.6923 13.7308 15.6923H11.673V17.75C11.673 17.9625 11.6011 18.1406 11.4573 18.2843C11.3135 18.4281 11.1353 18.5 10.9228 18.5ZM4.15354 14.9423C3.94104 14.9423 3.76295 14.8704 3.61929 14.7268C3.47579 14.5829 3.40404 14.4047 3.40404 14.1923V5.13475C3.40404 4.92225 3.47587 4.74408 3.61954 4.60025C3.76337 4.45658 3.94162 4.38475 4.15429 4.38475C4.36679 4.38475 4.54487 4.45658 4.68854 4.60025C4.83204 4.74408 4.90379 4.92225 4.90379 5.13475V14.1923C4.90379 14.4047 4.83187 14.5829 4.68804 14.7268C4.54437 14.8704 4.3662 14.9423 4.15354 14.9423ZM10.9228 9.19225C10.7101 9.19225 10.532 9.12042 10.3885 8.97675C10.2449 8.83292 10.173 8.65475 10.173 8.44225V5.13475C10.173 4.92225 10.245 4.74408 10.3888 4.60025C10.5326 4.45658 10.7109 4.38475 10.9235 4.38475C11.136 4.38475 11.3141 4.45658 11.4578 4.60025C11.6013 4.74408 11.673 4.92225 11.673 5.13475V8.44225C11.673 8.65475 11.6011 8.83292 11.4573 8.97675C11.3135 9.12042 11.1353 9.19225 10.9228 9.19225Z"
          fill="#EE4961"
        />
      </svg>
      </div>

      <div
      class="version-history"
      *ngIf="isVesrionHistory" 
      (click)="openDialog()"
      >
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg" 
      >
        <path
          d="M8.98084 17.5C6.97067 17.5 5.19892 16.8865 3.66559 15.6595C2.13226 14.4327 1.14951 12.8628 0.717339 10.95C0.663506 10.7513 0.695922 10.5687 0.814589 10.402C0.933089 10.2353 1.10101 10.1385 1.31834 10.1115C1.52284 10.0847 1.70617 10.1251 1.86834 10.2327C2.03051 10.3404 2.14367 10.4988 2.20784 10.7078C2.58851 12.2526 3.40867 13.5208 4.66834 14.5125C5.92801 15.5042 7.36551 16 8.98084 16C10.9308 16 12.585 15.3208 13.9433 13.9625C15.3017 12.6042 15.9808 10.95 15.9808 9C15.9808 7.05 15.3017 5.39583 13.9433 4.0375C12.585 2.67917 10.9308 2 8.98084 2C7.88851 2 6.86484 2.24267 5.90984 2.728C4.95467 3.21317 4.13217 3.88075 3.44234 4.73075H5.30784C5.52034 4.73075 5.69842 4.80267 5.84209 4.9465C5.98592 5.09033 6.05784 5.2685 6.05784 5.481C6.05784 5.69367 5.98592 5.87175 5.84209 6.01525C5.69842 6.15892 5.52034 6.23075 5.30784 6.23075H1.88484C1.62867 6.23075 1.41401 6.14408 1.24084 5.97075C1.06751 5.79758 0.980839 5.58292 0.980839 5.32675V1.90375C0.980839 1.69125 1.05276 1.51317 1.19659 1.3695C1.34042 1.22567 1.51867 1.15375 1.73134 1.15375C1.94384 1.15375 2.12192 1.22567 2.26559 1.3695C2.40909 1.51317 2.48084 1.69125 2.48084 1.90375V3.523C3.29234 2.56533 4.26417 1.82208 5.39634 1.29325C6.52834 0.764416 7.72317 0.5 8.98084 0.5C10.1608 0.5 11.2661 0.723084 12.2966 1.16925C13.3271 1.61542 14.2257 2.22183 14.9923 2.9885C15.759 3.75517 16.3654 4.65367 16.8116 5.684C17.2578 6.7145 17.4808 7.81967 17.4808 8.9995C17.4808 10.1793 17.2578 11.2846 16.8116 12.3152C16.3654 13.3461 15.759 14.2448 14.9923 15.0115C14.2257 15.7782 13.3271 16.3846 12.2966 16.8307C11.2661 17.2769 10.1608 17.5 8.98084 17.5ZM9.75959 8.69625L12.5096 11.4462C12.6481 11.5846 12.7189 11.7586 12.7221 11.9683C12.7254 12.1779 12.6546 12.3552 12.5096 12.5C12.3648 12.6448 12.1892 12.7172 11.9828 12.7172C11.7763 12.7172 11.6007 12.6448 11.4558 12.5L8.53084 9.575C8.43734 9.48133 8.36876 9.37933 8.32509 9.269C8.28159 9.15867 8.25984 9.04467 8.25984 8.927V4.75C8.25984 4.5375 8.33167 4.35933 8.47534 4.2155C8.61917 4.07183 8.79742 4 9.01009 4C9.22259 4 9.40067 4.07183 9.54434 4.2155C9.68784 4.35933 9.75959 4.5375 9.75959 4.75V8.69625Z"
          fill="#6E7B8F"
        />
      </svg>
      </div>
      
    
      
      <div
        class="save-btn-class"
        *ngIf="
          saveButtonEnabled &&
          !submitProgress &&
          ((employeeBased && items.length > 0) ||
            (positionBased && PositionData.length > 0))
        "
        (click)="saveData()"
      >
        <div class="save-icn-class">
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1.33333 1.33333V10.6667H10.6667V3.21867L8.78133 1.33333H1.33333ZM0.666667 0H9.33333L11.8047 2.47133C11.9297 2.59633 12 2.76587 12 2.94267V11.3333C12 11.5101 11.9298 11.6797 11.8047 11.8047C11.6797 11.9298 11.5101 12 11.3333 12H0.666667C0.489856 12 0.320286 11.9298 0.195262 11.8047C0.0702379 11.6797 0 11.5101 0 11.3333V0.666667C0 0.489856 0.0702379 0.320286 0.195262 0.195262C0.320286 0.0702379 0.489856 0 0.666667 0ZM6 10C5.46957 10 4.96086 9.78929 4.58579 9.41421C4.21071 9.03914 4 8.53043 4 8C4 7.46957 4.21071 6.96086 4.58579 6.58579C4.96086 6.21071 5.46957 6 6 6C6.53043 6 7.03914 6.21071 7.41421 6.58579C7.78929 6.96086 8 7.46957 8 8C8 8.53043 7.78929 9.03914 7.41421 9.41421C7.03914 9.78929 6.53043 10 6 10ZM2 2H8V4.66667H2V2Z"
              fill="#45546E"
            />
          </svg>
        </div>
        <span class="save-text" *ngIf="('save-button' | checkInfoIcon : this.formConfig: 'new-billing-advice')">{{
          "save-button"
            | checkLabel : this.formConfig : "new-billing-advice" : "Save"
        }}</span>
      </div>
      
      
     

      <div
        class="download-billing"
        (click)="downloadCostingSheet()"
        *ngIf="downloadBillingAdvice"
      >
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <mask
            id="mask0_1218_21007"
            style="mask-type: alpha"
            maskUnits="userSpaceOnUse"
            x="0"
            y="0"
            width="24"
            height="24"
          >
            <rect width="24" height="24" fill="#D9D9D9" />
          </mask>
          <g mask="url(#mask0_1218_21007)">
            <path
              d="M12 15.4115C11.8795 15.4115 11.7673 15.3923 11.6635 15.3538C11.5597 15.3154 11.4609 15.2494 11.3672 15.1558L8.25775 12.0463C8.10908 11.8974 8.03567 11.7233 8.0375 11.524C8.0395 11.3247 8.11292 11.1474 8.25775 10.9922C8.41292 10.8372 8.59108 10.7572 8.79225 10.752C8.99358 10.7468 9.17183 10.8218 9.327 10.977L11.25 12.9V5.25C11.25 5.03717 11.3218 4.859 11.4655 4.7155C11.609 4.57183 11.7872 4.5 12 4.5C12.2128 4.5 12.391 4.57183 12.5345 4.7155C12.6782 4.859 12.75 5.03717 12.75 5.25V12.9L14.673 10.977C14.8218 10.8283 14.9984 10.7549 15.2028 10.7568C15.4073 10.7588 15.5871 10.8372 15.7423 10.9922C15.8871 11.1474 15.9621 11.3231 15.9672 11.5192C15.9724 11.7154 15.8974 11.8911 15.7423 12.0463L12.6328 15.1558C12.5391 15.2494 12.4403 15.3154 12.3365 15.3538C12.2327 15.3923 12.1205 15.4115 12 15.4115ZM6.30775 19.5C5.80258 19.5 5.375 19.325 5.025 18.975C4.675 18.625 4.5 18.1974 4.5 17.6923V15.7308C4.5 15.5179 4.57183 15.3398 4.7155 15.1962C4.859 15.0526 5.03717 14.9808 5.25 14.9808C5.46283 14.9808 5.641 15.0526 5.7845 15.1962C5.92817 15.3398 6 15.5179 6 15.7308V17.6923C6 17.7692 6.03208 17.8398 6.09625 17.9038C6.16025 17.9679 6.23075 18 6.30775 18H17.6923C17.7692 18 17.8398 17.9679 17.9038 17.9038C17.9679 17.8398 18 17.7692 18 17.6923V15.7308C18 15.5179 18.0718 15.3398 18.2155 15.1962C18.359 15.0526 18.5372 14.9808 18.75 14.9808C18.9628 14.9808 19.141 15.0526 19.2845 15.1962C19.4282 15.3398 19.5 15.5179 19.5 15.7308V17.6923C19.5 18.1974 19.325 18.625 18.975 18.975C18.625 19.325 18.1974 19.5 17.6923 19.5H6.30775Z"
              fill="#6E7B8F"
            />
          </g>
        </svg>
      </div>
    </div>
  </div>
  <div class="row pl-3 pr-3" *ngIf = "approverView">
    <div class="col-12 approver-header">
      <div style="padding: 6px;">
        <div class="back-action" (click)="returnback()">
          <mat-icon class="back-icn-class">keyboard_arrow_left</mat-icon>
        </div>
      </div>
      <div class="approver-details">
        <div class="col-12 details-header">
          <div class="col-2">
            Submitted By
          </div>
          <div class="custom-col">
            Source Name
          </div>
          <div class="custom-col">
            Source Id
          </div>
          <div class="custom-col">
            Submitted On
          </div>
          <div class="description-col">
            Description
          </div>
        </div>
        <div class="col-12 details-value">
          <div class="col-2 text d-flex">
            <app-people-icon-display [peopleList]="[workflowDetails?.submitted_by]"
            [count]="[workflowDetails?.submitted_by].length" [bgColor]="button">
            </app-people-icon-display>
            <span style="
                padding: 1px 5px 0px 5px;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 100%;
                overflow: hidden;
                cursor: pointer;"
                tooltip="{{workflowDetails?.employee_details[0]?.employee_name}}"
                >
              {{workflowDetails?.employee_details[0]?.employee_name}}
            </span>
          </div>
          <div class="custom-col">
            <div 
              [ngStyle]="{ 
                background: workflowDetails?.secondary_color, 
                color: workflowDetails?.primary_color,
                padding: '5px',
                'border-radius': '4px',
                width: 'fit-content' }">
              {{workflowDetails?.source_name}}
            </div>
          </div>      
          <div class="custom-col text">
            {{workflowDetails?.source_id}}
          </div>
          <div class="custom-col text">
            {{workflowDetails?.created_on}}
          </div>
          <div class="description-col text" tooltip="{{workflowDetails?.workflow_name}}">
            {{workflowDetails?.workflow_name}}
          </div>    
          <div class="actions">
            <div class="reject" (click)="updateWorkflowRequest('R')">
              Reject
            </div>
            <div class="approve" (click)="updateWorkflowRequest('A')">
              Approve
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row pl-3 pr-3 pt-1 stepper-class">
    <div class="card stepper-row">
      <div class="step-line" *ngFor="let section of sectionList; let i = index">
        <div
          class="section-label-class"
          (click)="navigateSection(i, 'current')"
          *ngIf="section.is_visible"
        >
          <div
            class="outer-circle-class"
            [ngClass]="{
              disabled: section.key_id != templateId && !section.is_completed,
              selected: section.key_id == templateId,
              completed: section.is_completed
            }"
          >
            <ng-container *ngIf="section.is_completed; else stepNumber">
              <mat-icon class="complete-icon-class">done</mat-icon>
            </ng-container>
            <ng-template #stepNumber>
              <span>{{ i + 1 }}</span>
            </ng-template>
          </div>
          <span
            class="section-text"
            [ngClass]="{
              disabled: section.key_id != templateId && !section.is_completed,
              selected: section.key_id == templateId,
              completed: section.is_completed && section.key_id != templateId
            }"
          >
            {{ section?.label }}
          </span>
        </div>
        <mat-divider
          class="divider-class"
          [ngClass]="{
            disabled: section.key_id != templateId && !section.is_completed,
            completed: section.is_completed || templateId > i + 1,
            selected: section.key_id == templateId
          }"
          *ngIf="sectionList.length != i + 1 && section.is_visible"
        ></mat-divider>
      </div>
    </div>
  </div>
  <div class="row pl-3 pr-3 pt-1 section-class">
    <div class="card flex-class-card">
      <ng-container
        [ngTemplateOutlet]="billingAdvice"
        *ngIf="templateId == 1"
      ></ng-container>
      <ng-container
        [ngTemplateOutlet]="billingPlanTemplate"
        *ngIf="templateId == 3"
      ></ng-container>
    </div>
  </div>
  <div class="footer">
    <div
      class="arrow-btn-class"
      *ngIf="stepId != 1 && this.milestone_status_id<8"
      (click)="navigateSection(stepId - 1, 'back')"
    >
      <mat-icon class="arrow-btn-icon">keyboard_arrow_left</mat-icon>
    </div>
    <div
      class="arrow-btn-class"
      *ngIf="stepId < this.sectionList.length && this.milestone_status_id<8"
      (click)="navigateSection(stepId - 1, 'next')"
    >
      <mat-icon class="arrow-btn-icon">keyboard_arrow_right</mat-icon>
    </div>
    <div class="footer-btns-class" *ngIf="stepId == this.sectionList.length && !approverView">
      <div
        class="button-class secondary"
        *ngIf="accruedButtonEnabled && !submitProgress && checkBillingStepper && !isComponentLoading"
        (click)="accuralData()"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clip-path="url(#clip0_15426_1077)">
            <path
              d="M14 12.0002H4C3.82319 12.0002 3.65362 12.0704 3.5286 12.1954C3.40357 12.3204 3.33333 12.49 3.33333 12.6668C3.33333 12.8436 3.40357 13.0132 3.5286 13.1382C3.65362 13.2633 3.82319 13.3335 4 13.3335H14V14.6668H4C3.46957 14.6668 2.96086 14.4561 2.58579 14.081C2.21071 13.706 2 13.1973 2 12.6668V2.66683C2 2.31321 2.14048 1.97407 2.39052 1.72402C2.64057 1.47397 2.97971 1.3335 3.33333 1.3335H14V12.0002ZM3.33333 10.7002C3.44133 10.6782 3.55267 10.6668 3.66667 10.6668H12.6667V2.66683H3.33333V10.7002ZM10.6667 6.00016H5.33333V4.66683H10.6667V6.00016Z"
              fill="#45546E"
            />
          </g>
          <defs>
            <clipPath id="clip0_15426_1077">
              <rect width="16" height="16" fill="white" />
            </clipPath>
          </defs>
        </svg>
        <span class="button-text secondary">Forecast & Accrue</span>
      </div>
      <div
        class="button-class primary"
        *ngIf="invoiceButtonEnabled && !submitProgress && checkBillingStepper"
        (click)="confirmData()"
      >
        <svg
          width="12"
          height="14"
          viewBox="0 0 12 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 11.0002H2C1.82319 11.0002 1.65362 11.0704 1.5286 11.1954C1.40357 11.3204 1.33333 11.49 1.33333 11.6668C1.33333 11.8436 1.40357 12.0132 1.5286 12.1382C1.65362 12.2633 1.82319 12.3335 2 12.3335H12V13.6668H2C1.46957 13.6668 0.960859 13.4561 0.585787 13.081C0.210714 12.706 0 12.1973 0 11.6668V1.66683C0 1.31321 0.140476 0.974069 0.390524 0.72402C0.640573 0.473972 0.979711 0.333496 1.33333 0.333496H12V11.0002ZM1.33333 9.70016C1.44133 9.67816 1.55267 9.66683 1.66667 9.66683H10.6667V1.66683H1.33333V9.70016ZM8.66667 5.00016H3.33333V3.66683H8.66667V5.00016Z"
            fill="white"
          />
        </svg>
        <span class="button-text primary">Invoice</span>
      </div>
      <div
        class="button-class primary"
        *ngIf="creditNoteButtonEnabled && !submitProgress && checkBillingStepper"
        (click)="creditNoteSubmitData()"
      >
        <svg
          width="12"
          height="14"
          viewBox="0 0 12 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 11.0002H2C1.82319 11.0002 1.65362 11.0704 1.5286 11.1954C1.40357 11.3204 1.33333 11.49 1.33333 11.6668C1.33333 11.8436 1.40357 12.0132 1.5286 12.1382C1.65362 12.2633 1.82319 12.3335 2 12.3335H12V13.6668H2C1.46957 13.6668 0.960859 13.4561 0.585787 13.081C0.210714 12.706 0 12.1973 0 11.6668V1.66683C0 1.31321 0.140476 0.974069 0.390524 0.72402C0.640573 0.473972 0.979711 0.333496 1.33333 0.333496H12V11.0002ZM1.33333 9.70016C1.44133 9.67816 1.55267 9.66683 1.66667 9.66683H10.6667V1.66683H1.33333V9.70016ZM8.66667 5.00016H3.33333V3.66683H8.66667V5.00016Z"
            fill="white"
          />
        </svg>
        <span class="button-text primary">Credit Note</span>
      </div>
      <div
        class="button-class primary"
        *ngIf="partialInvoiceButtonEnabled && !submitProgress"
        (click)="partialInvoiceData()"
      >
        <svg
          width="12"
          height="14"
          viewBox="0 0 12 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 11.0002H2C1.82319 11.0002 1.65362 11.0704 1.5286 11.1954C1.40357 11.3204 1.33333 11.49 1.33333 11.6668C1.33333 11.8436 1.40357 12.0132 1.5286 12.1382C1.65362 12.2633 1.82319 12.3335 2 12.3335H12V13.6668H2C1.46957 13.6668 0.960859 13.4561 0.585787 13.081C0.210714 12.706 0 12.1973 0 11.6668V1.66683C0 1.31321 0.140476 0.974069 0.390524 0.72402C0.640573 0.473972 0.979711 0.333496 1.33333 0.333496H12V11.0002ZM1.33333 9.70016C1.44133 9.67816 1.55267 9.66683 1.66667 9.66683H10.6667V1.66683H1.33333V9.70016ZM8.66667 5.00016H3.33333V3.66683H8.66667V5.00016Z"
            fill="white"
          />
        </svg>
        <span class="button-text primary">Partial Invoice</span>
      </div>
      <div
        class="button-class primary"
        *ngIf="cancelButtonEnabled && !submitProgress"
        (click)="cancelData()"
      >
        <svg
          width="12"
          height="14"
          viewBox="0 0 12 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 11.0002H2C1.82319 11.0002 1.65362 11.0704 1.5286 11.1954C1.40357 11.3204 1.33333 11.49 1.33333 11.6668C1.33333 11.8436 1.40357 12.0132 1.5286 12.1382C1.65362 12.2633 1.82319 12.3335 2 12.3335H12V13.6668H2C1.46957 13.6668 0.960859 13.4561 0.585787 13.081C0.210714 12.706 0 12.1973 0 11.6668V1.66683C0 1.31321 0.140476 0.974069 0.390524 0.72402C0.640573 0.473972 0.979711 0.333496 1.33333 0.333496H12V11.0002ZM1.33333 9.70016C1.44133 9.67816 1.55267 9.66683 1.66667 9.66683H10.6667V1.66683H1.33333V9.70016ZM8.66667 5.00016H3.33333V3.66683H8.66667V5.00016Z"
            fill="white"
          />
        </svg>
        <span class="button-text primary">Cancel</span>
      </div>
      <div
        class="button-class primary"
        *ngIf="reversalButtonEnabled && !submitProgress"
        (click)="reversalData()"
      >
        <svg
          width="12"
          height="14"
          viewBox="0 0 12 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 11.0002H2C1.82319 11.0002 1.65362 11.0704 1.5286 11.1954C1.40357 11.3204 1.33333 11.49 1.33333 11.6668C1.33333 11.8436 1.40357 12.0132 1.5286 12.1382C1.65362 12.2633 1.82319 12.3335 2 12.3335H12V13.6668H2C1.46957 13.6668 0.960859 13.4561 0.585787 13.081C0.210714 12.706 0 12.1973 0 11.6668V1.66683C0 1.31321 0.140476 0.974069 0.390524 0.72402C0.640573 0.473972 0.979711 0.333496 1.33333 0.333496H12V11.0002ZM1.33333 9.70016C1.44133 9.67816 1.55267 9.66683 1.66667 9.66683H10.6667V1.66683H1.33333V9.70016ZM8.66667 5.00016H3.33333V3.66683H8.66667V5.00016Z"
            fill="white"
          />
        </svg>
        <span class="button-text primary">Revert</span>
      </div>
    </div>
  </div>
</div>

<!----------------------------------Billing Advice Screen----------------------------->
<ng-template #billingAdvice>
  <div class="content-header-class">
    <div class="content-text">
      <span class="header-text">{{
        "date" | checkLabel : this.formConfig : "new-billing-advice" : "Duration"
      }}</span>
      <span class="header-value"
        >{{displayMilestoneStartDate}} - {{displayMilestoneEndDate}}</span
      >
    </div>
    <mat-divider [vertical]="true"></mat-divider>
    <div class="content-text" *ngIf="withOpportunity == 1">
      <span class="header-text">{{
        "quote_id"
          | checkLabel : this.formConfig : "new-billing-advice" : "Quote ID"
      }}</span>
      <span class="header-value">{{ quote_id ? quote_id : "-" }}</span>
    </div>
    <mat-divider [vertical]="true" *ngIf="withOpportunity == 1"></mat-divider>
    <div  *ngIf="!isFinancialValuesHidden" class="content-text">
      <span class="header-text">{{
        "po_number"
          | checkLabel : this.formConfig : "new-billing-advice" : "PO Number"
      }}
      <span
      *ngIf="('po_number' | checkInfoIcon : this.formConfig: 'new-billing-advice')">
      <mat-icon class="info-icon"
          tooltip="{{('po_number' | checkTooltip : this.formConfig: 'new-billing-advice': 'PO Number')}}"
          >
          info_outline
      </mat-icon>
  </span></span>

      <div class="header-value">
        <span>{{ po_number ? po_number : "-" }}</span>

      </div>
    </div>
    <mat-divider [vertical]="true" *ngIf="('po_value' | checkActive : this.formConfig : 'new-billing-advice')  && !isFinancialValuesHidden"></mat-divider>
    <div class="content-text" *ngIf="('po_value' | checkActive : this.formConfig : 'new-billing-advice')  && !isFinancialValuesHidden">
      <span class="header-text">{{
        "po_value"
          | checkLabel : this.formConfig : "new-billing-advice" : "PO Value"
      }}</span>
      <span class="header-value">{{ po_value ? po_value : "-" }}</span>
    </div>
    <mat-divider  *ngIf="!isFinancialValuesHidden" [vertical]="true"></mat-divider>
    <div *ngIf="!isFinancialValuesHidden" class="content-text">
      <span class="header-text">{{
        "milestone_value"
          | checkLabel : this.formConfig : "new-billing-advice" : "Milestone Value"
      }}</span>
      <span class="header-value"
        ><app-currency
          [currencyList]="data.value"
          [code]="code"
          [font_size]="currency_font_size"
          class="flex-1"
          type="big"
        >
        </app-currency
      ></span>
    </div>
    <mat-divider  *ngIf="!isFinancialValuesHidden" [vertical]="true"></mat-divider>
    <div  *ngIf="!isFinancialValuesHidden" class="content-text">
      <span class="header-text">{{
        "billing_value"
          | checkLabel : this.formConfig : "new-billing-advice" : "Billing Advice Value"
      }}</span>
      <span class="header-value">
        {{code}} {{(this.getTotalBillingValue() || 0)    | decimal : decimalValue }}
      </span>
    </div>
    <mat-divider  [vertical]="true"></mat-divider>
    <div   class="content-text">
      <span class="header-text">{{
        "current_month_progress"
          | checkLabel : this.formConfig : "new-billing-advice" : "Current Month Progress"
      }}</span>
      <span class="header-value">
        {{(this.getCurrentMonthProgress() || 0)    | decimal : decimalValue }} %
      </span>
    </div>
    <mat-divider   [vertical]="true"></mat-divider>
    <div   class="content-text">
      <span class="header-text">{{
        "cummulative_month_progress"
          | checkLabel : this.formConfig : "new-billing-advice" : "Cummulative Progress"
      }}</span>
      <span class="header-value">
       {{(this.getCummulativeMonthProgress() || 0)    | decimal : decimalValue }} %
      </span>
    </div>
    <div class="content-text end-align" *ngIf="roundOffSyncAccess && ((employeeBased && items.length > 0) || (positionBased && PositionData.length > 0)) && this.billingStepper">
      <span class="header-text">{{
        "sync_button_header"
          | checkLabel : this.formConfig : "new-billing-advice" : "Sync"
      }}</span>
      <span class="header-value">
        <div class="sync-class">
          <span *ngIf="!roundoff_sync" (click)="syncRoundoff()" tooltip="Sync Now"
          ><mat-icon class="sync-icn">sync</mat-icon></span
        >
        <span *ngIf="roundoff_sync" tooltip="Sync In-Progress!">
          <img
            src="https://assets.kebs.app/round_off_sync_gif.gif"
            class="gif-class"
          />
        </span>
        </div>
      </span>
    </div>
  </div>

  <ng-container [ngTemplateOutlet]="billingAdviceGridView"></ng-container>
</ng-template>

<!----------------------------------------Billing Advice Employee & Position Based Grid View-------------->
<ng-template #billingAdviceGridView>
  <!------------------Employee Based View--------------->
  <div class="employee-view" *ngIf="employeeBased">
    <div class="body-table" *ngIf="items?.length > 0; else noData">
      <div class="header-table">
        <div class="header-row">
          <div
            class="header-col-class emp-class"
            *ngIf="'id' | checkActive : this.formConfig : 'new-billing-advice'"
          >
            <span  class="overflow-class center emp-class">{{
              "id" | checkLabel : this.formConfig : "new-billing-advice" : "ID"
            }}</span>
          </div>
          <div
            class="header-col-class name-class"   [ngStyle]="{ 'min-width': bill_enable ? '16rem' : ''}"
            *ngIf="'name' | checkActive : this.formConfig : 'new-billing-advice'"
          >
            <span class="overflow-class center name-class"   [ngStyle]="{ 'min-width': bill_enable ? '16rem' : ''}">{{
              "name" | checkLabel : this.formConfig : "new-billing-advice" : "Name"
            }}</span>
          </div>
          <div
            class="header-col-class date-class"
            *ngIf="'start_date' | checkActive : this.formConfig : 'new-billing-advice'"
          >
            <span class="overflow-class center date-class">{{
              "start_date"
                | checkLabel : this.formConfig : "new-billing-advice" : "Start Date"
            }}</span>
          </div>
          <div
            class="header-col-class date-class"
            *ngIf="
              'end_date' | checkActive : this.formConfig : 'new-billing-advice'
            "
          >
            <span class="overflow-class center date-class">{{
              "end_date"
                | checkLabel : this.formConfig : "new-billing-advice" : "End Date"
            }}</span>
          </div>

          <div class="header-col-class-span" *ngIf="'timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice'">
            <div class="header-main">
              <span class="overflow-class center ">{{
                "timesheet_hours_header"
                  | checkLabel
                    : this.formConfig
                    : "new-billing-advice"
                    : "Timesheet
                Hrs"
              }}</span>
            </div>
            <div class="sub-header">
              <div
                class="sub-header-col"
                *ngIf="
                  'billable_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "billable_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Billable"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'non_billable_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "non_billable_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Non Billable"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'non_billable_hours_growth'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "non_billable_hours_growth"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Non Billable Growth"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'non_billable_hours_flexi'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "non_billable_hours_flexi"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Non Billable Flexi"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'total_timesheet_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "total_timesheet_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Total"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'zifo_timesheet_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "zifo_timesheet_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Timesheet"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'after_zifo_timesheet_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "after_zifo_timesheet_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Zifo Timesheet"
                }}</span>
              </div>
            </div>
          </div>

          <div class="header-col-class-span" *ngIf="'prev_timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice'">
            <div class="header-main">
              <span class="overflow-class center ">{{
                "prev_timesheet_hours_header"
                  | checkLabel
                    : this.formConfig
                    : "new-billing-advice"
                    : "Previous
                Timesheet Hrs"
              }}</span>
            </div>
            <div class="sub-header">
              <div
                class="sub-header-col"
                *ngIf="
                  'previous_billable_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "previous_billable_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Billable"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'previous_non_billable_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "previous_non_billable_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Non Billable"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'previous_non_billable_hours_growth'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "previous_non_billable_hours_growth"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Non Billable Growth"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'previous_non_billable_hours_flexi'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "previous_non_billable_hours_flexi"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Non Billable Flexi"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'previous_total_timesheet_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "previous_total_timesheet_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Total Hours"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'zifo_previous_timesheet_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "zifo_previous_timesheet_hours"
                    | checkLabel : this.formConfig : "new-billing-advice" : "Zifo"
                }}</span>
              </div>
            </div>
          </div>

          <div class="header-col-class-span" *ngIf="'round_off_header' | checkActive : this.formConfig : 'new-billing-advice'">
            <div class="header-main">
              <span class="overflow-class center ">{{
                "round_off_header"
                  | checkLabel
                    : this.formConfig
                    : "new-billing-advice"
                    : "Round Off Hrs"
              }}</span>
            </div>
            <div class="sub-header">
              <div
                class="sub-header-col"
                *ngIf="
                  'rounded_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "rounded_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Rounded off Hours"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'after_rounded_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "after_rounded_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Zifo Rounded off Hours"
                }}</span>
              </div>
            </div>
          </div>

          <div class="header-col-class-span" *ngIf="'billable_header' | checkActive : this.formConfig : 'new-billing-advice'">
            <div class="header-main">
              <span class="overflow-class center ">{{
                "billable_header"
                  | checkLabel : this.formConfig : "new-billing-advice" : "Emp Type"
              }}</span>
            </div>
            <div class="sub-header">
              <div
                class="sub-header-col" [ngClass]="{ 'bill-class': bill_enable }"
                *ngIf="
                  'planned_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "planned_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Planned Hrs"
                }}</span>
              </div>
              <div
                class="sub-header-col" [ngClass]="{ 'bill-class': bill_enable }"
                *ngIf="
                  'actual_billable_hours'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "actual_billable_hours"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Actual Hrs"
                }}</span>
              </div>
              
              <div
                class="sub-header-col"
                *ngIf="
                  'actual_billable_days'
                    | checkActive : this.formConfig : 'new-billing-advice'
                "
              >
                <span class="overflow-class center ">{{
                  "actual_billable_days"
                    | checkLabel
                      : this.formConfig
                      : "new-billing-advice"
                      : "Actual Days"
                }}</span>
              </div>
            </div>
          </div>

          <div
            class="header-col-class hour-class"  [ngStyle]="{ 'min-width': bill_enable ? '10rem' : ''}"
            *ngIf="
              ('per_hour_rate' | checkActive : this.formConfig : 'new-billing-advice') && !isFinancialValuesHidden
            "
          >
            <span class="overflow-class center hour-class"  [ngStyle]="{ 'min-width': bill_enable ? '10rem' : ''}">{{
              "per_hour_rate"
                | checkLabel
                  : this.formConfig
                  : "new-billing-advice"
                  : "Per Hour Rate"
            }}</span>
          </div>
          <div
            class="header-col-class val-class"   [ngStyle]="{ 'min-width': bill_enable ? '10rem' : ''}"
            *ngIf="('value' | checkActive : this.formConfig : 'new-billing-advice') && !isFinancialValuesHidden"
          >
            <span class="overflow-class center val-class"   [ngStyle]="{ 'min-width': bill_enable ? '10rem' : ''}">{{
              "value"
                | checkLabel : this.formConfig : "new-billing-advice" : "Value"
            }}</span>
          </div>
        </div>
      </div>

      <div class="content-table">
        <div class="value-row" *ngFor="let item of items; let i = index"
          style="display: flex;"
          [ngClass]="{ 'value-row-child': !item.has_child }"
        >
          <div
            class="value-col-class emp-class"
            *ngIf="'id' | checkActive : this.formConfig : 'new-billing-advice'"
          >
          <span class="overflow-class center emp-class">{{ item.associate_id ? item.associate_id : "-" }}</span>
          </div>
          <div
            class="value-col-class name-class"   [ngStyle]="{ 'min-width': bill_enable ? '16rem' : ''}"
            *ngIf="'name' | checkActive : this.formConfig : 'new-billing-advice'"
          >
            <div class="d-flex flex-column">
              <span class="overflow-class left name-class" [ngStyle]="{ 'min-width': bill_enable ? '16rem' : '' }"
                    tooltip="{{item.has_child && item.id && item.name ? (item.id + ' - ' + item.name) : (item.name ? item.name : '-')}}">
                <span *ngIf="item.has_child && item.id" style="color: black;">{{ item.id }}</span><span *ngIf="item.has_child && item.id && item.name"> - </span><span>{{ item.name ? item.name : "-" }}</span>
            </span>
            </div>
          </div>
          <div
            class="value-col-class date-class"
            *ngIf="
              'start_date' | checkActive : this.formConfig : 'new-billing-advice'
            "
          >
            <span>{{ item.start_date ? item.start_date : "-" }}</span>
          </div>
          <div
            class="value-col-class date-class"
            *ngIf="
              'end_date' | checkActive : this.formConfig : 'new-billing-advice'
            "
          >
            <span>{{ item.end_date ? item.end_date : "-" }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('billable_hours'
                | checkActive : this.formConfig : 'new-billing-advice') && ('timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <div *ngIf="datasaved" class="div-width-align">
              <span class="right-overflow-class" tooltip="{{ item.billable_hours ? item.billable_hours : 0 }}">{{ item.billable_hours }}</span>
            </div>
            <div *ngIf="!datasaved">
              <mat-icon class="refresh-icon" tooltip="Refreshing"
                >autorenew</mat-icon
              >
            </div>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('non_billable_hours'
                | checkActive : this.formConfig : 'new-billing-advice') && ('timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <div *ngIf="datasaved" class="div-width-align">
              <span class="right-overflow-class"  tooltip="{{ item.non_billable_hours ? item.non_billable_hours : 0 }}">{{ item.non_billable_hours ? item.non_billable_hours : 0 }}</span>
            </div>
            <div *ngIf="!datasaved">
              <mat-icon class="refresh-icon" tooltip="Refreshing"
                >autorenew</mat-icon
              >
            </div>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              datasaved &&
              ('non_billable_hours_growth'
                | checkActive : this.formConfig : 'new-billing-advice') && ('timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.non_billable_hours_growth ? item.non_billable_hours_growth : 0 }}">{{ item.non_billable_hours_growth ? item.non_billable_hours_growth : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              datasaved &&
              ('non_billable_hours_flexi'
                | checkActive : this.formConfig : 'new-billing-advice') && ('timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.non_billable_hours_flexi ? item.non_billable_hours_flexi : 0 }}">{{ item.non_billable_hours_flexi ? item.non_billable_hours_flexi : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('timesheet_hours'
                | checkActive : this.formConfig : 'new-billing-advice') && ('timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class" *ngIf="!allowTimesheetInDecimal"  tooltip="{{ (item.timesheet_hours ? item.timesheet_hours : 0) | ceilPipe }}">{{ (item.timesheet_hours ? item.timesheet_hours : 0) | ceilPipe }}</span>
            <span class="right-overflow-class" *ngIf="allowTimesheetInDecimal"  tooltip="{{ (item.timesheet_hours ? item.timesheet_hours : 0) | decimal : decimalValue }}">{{ (item.timesheet_hours ? item.timesheet_hours : 0) | decimal : decimalValue }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('zifo_timesheet_hours'
                | checkActive : this.formConfig : 'new-billing-advice') && ('timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class" *ngIf="!allowTimesheetInDecimal"  tooltip="{{ (item.zifo_timesheet_hours ? item.zifo_timesheet_hours : 0) | ceilPipe }}">{{ (item.zifo_timesheet_hours ? item.zifo_timesheet_hours : 0) | ceilPipe }}</span>
            <span class="right-overflow-class" *ngIf="allowTimesheetInDecimal"  tooltip="{{ (item.zifo_timesheet_hours ? item.zifo_timesheet_hours : 0) | decimal : decimalValue }}">{{ (item.zifo_timesheet_hours ? item.zifo_timesheet_hours : 0) | decimal : decimalValue }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('after_zifo_timesheet_hours'
                | checkActive : this.formConfig : 'new-billing-advice') && ('timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class" *ngIf="!allowTimesheetInDecimal" tooltip="{{ (item.after_zifo_timesheet_hours ? item.after_zifo_timesheet_hours : 0) | ceilPipe }}">{{ (item.after_zifo_timesheet_hours ? item.after_zifo_timesheet_hours : 0) | ceilPipe }}</span>
            <span class="right-overflow-class" *ngIf="allowTimesheetInDecimal"  tooltip="{{ (item.after_zifo_timesheet_hours ? item.after_zifo_timesheet_hours : 0) | decimal : decimalValue }}">{{ (item.after_zifo_timesheet_hours ? item.after_zifo_timesheet_hours : 0) | decimal : decimalValue }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('previous_billable_hours' 
                | checkActive : this.formConfig : 'new-billing-advice') && ('prev_timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.previous_billable_hours ? item.previous_billable_hours : 0 }}">{{ item.previous_billable_hours ? item.previous_billable_hours : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('previous_non_billable_hours'
                | checkActive : this.formConfig : 'new-billing-advice') && ('prev_timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.previous_non_billable_hours ? item.previous_non_billable_hours : 0 }}">{{ item.previous_non_billable_hours ? item.previous_non_billable_hours : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('previous_non_billable_hours_growth'
                | checkActive : this.formConfig : 'new-billing-advice') && ('prev_timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.previous_non_billable_hours_growth ? item.previous_non_billable_hours_growth : 0 }}">{{ item.previous_non_billable_hours_growth ? item.previous_non_billable_hours_growth : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('previous_non_billable_hours_flexi'
                | checkActive : this.formConfig : 'new-billing-advice') && ('prev_timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.previous_non_billable_hours_flexi ? item.previous_non_billable_hours_flexi : 0 }}">{{ item.previous_non_billable_hours_flexi ? item.previous_non_billable_hours_flexi : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('previous_total_timesheet_hours'
                | checkActive : this.formConfig : 'new-billing-advice') && ('prev_timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class" *ngIf="!allowTimesheetInDecimal" tooltip="{{ (item.previous_timesheet_hours ? item.previous_timesheet_hour : 0) | ceilPipe  }}">{{ (item.previous_timesheet_hours ? item.previous_timesheet_hour : 0) | ceilPipe  }}</span>
            <span class="right-overflow-class" *ngIf="allowTimesheetInDecimal"  tooltip="{{ (item.previous_timesheet_hours ? item.previous_timesheet_hours : 0) | decimal : decimalValue }}">{{ (item.previous_timesheet_hours ? item.previous_timesheet_hours : 0) | decimal : decimalValue }}</span>
          </div>

          <div
            class="value-col-class"
            *ngIf="
              ('zifo_previous_timesheet_hours'
                | checkActive : this.formConfig : 'new-billing-advice') && ('prev_timesheet_hours_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class" *ngIf="!allowTimesheetInDecimal"   tooltip="{{ (item.zifo_previous_timesheet_hours ? item.zifo_previous_timesheet_hours : 0)  | ceilPipe }}">{{ (item.zifo_previous_timesheet_hours ? item.zifo_previous_timesheet_hours : 0)  | ceilPipe }}</span>
            <span class="right-overflow-class" *ngIf="allowTimesheetInDecimal"  tooltip="{{ (item.zifo_previous_timesheet_hours ? item.zifo_previous_timesheet_hours : 0) | decimal : decimalValue }}">{{ (item.zifo_previous_timesheet_hours ? item.zifo_previous_timesheet_hours : 0) | decimal : decimalValue }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              datasaved &&
              ('rounded_hours'
                | checkActive : this.formConfig : 'new-billing-advice') && ('round_off_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class"  tooltip="{{ (item.rounded_hours ? item.rounded_hours : 0) | ceilPipe }}">{{ (item.rounded_hours ? item.rounded_hours : 0) | ceilPipe }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              datasaved &&
              ('after_rounded_hours'
                | checkActive : this.formConfig : 'new-billing-advice') && ('round_off_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class"  tooltip="{{ (item.after_rounded_hours ? item.after_rounded_hours : 0) | ceilPipe }}">{{ (item.after_rounded_hours ? item.after_rounded_hours : 0) | ceilPipe }}</span>
          </div>
          <div
            class="value-col-class" [ngClass]="{ 'bill-class': bill_enable }"
            *ngIf="
              ('planned_hours' | checkActive : this.formConfig : 'new-billing-advice') && ('billable_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <div *ngIf="datasaved" class="div-width-align">
              <span class="right-overflow-class"  tooltip="{{ (item.planned_hours ? item.planned_hours : 0) | roundOff : decimalValue }}">{{ (item.planned_hours ? item.planned_hours : 0) | roundOff : decimalValue }}</span>
            </div>
            <div *ngIf="!datasaved">
              <mat-icon class="refresh-icon" tooltip="Refreshing"
                >autorenew</mat-icon
              >
            </div>
          </div>

          <div
            class="value-col-class" [ngClass]="{ 'bill-class': bill_enable }"
            *ngIf="
              ('actual_billable_hours'
                | checkActive : this.formConfig : 'new-billing-advice') && ('billable_header' | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <div *ngIf="datasaved && editMode; else displayEmployeeHours" class="div-width-align">
              <input
                  [ngClass]="{ disabled: isZeroTimesheetHours(item) }"
                  digitOnly
                  [isPercentage]="false"
                  [allowDecimal]="allowActualHrsDecimal"
                  [decimalsAllowed]="decimalValue"
                  type="text"
                  placeholder="Value"
                  [(ngModel)]="item.actual_billable_hours"
                  (ngModelChange)="getActualBillableHours($event, i, item.id)"
                  [required]="
                    'actual_billable_hours'
                      | checkMandatedField : formConfig : 'new-billing-advice'
                  "
                  [ngStyle]="highlightHoursDeviations ? {
                    color: getInputColor(item.billable_hours, item.actual_billable_hours),
                    'font-weight': '700'
                  } : { 'font-weight': '700' }"
                  [readonly]="isZeroTimesheetHours(item)"
                />
            </div>
            <ng-template #displayEmployeeHours>
              <span *ngIf="allowActualHrsDecimal"  class="right-overflow-class"  
              [ngStyle]="highlightHoursDeviations ? {
                color: getInputColor(item.billable_hours, item.actual_billable_hours),
                'font-weight': '700'
              } : { 'font-weight': '700' }"
              tooltip="{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)  | decimal : decimalValue}}">{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)  | decimal : decimalValue}}</span>
              <span *ngIf="!allowActualHrsDecimal"  class="right-overflow-class"  
              [ngStyle]="highlightHoursDeviations ? {
                color: getInputColor(item.billable_hours, item.actual_billable_hours),
                'font-weight': '700'
              } : { 'font-weight': '700' }"
              tooltip="{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)  | ceilPipe }}">{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)  | ceilPipe }}</span>
            </ng-template>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              datasaved &&
              ('actual_billable_days'
                | checkActive : this.formConfig : 'new-billing-advice')
            "
          >
            <span class="right-overflow-class"  tooltip="{{ (item.actual_billable_days ? item.actual_billable_days : 0)  | decimal : decimalValue }}">{{ (item.actual_billable_days ? item.actual_billable_days : 0)  | decimal : decimalValue }}</span>
          </div>

          <div
            class="value-col-class"
            *ngIf="
              ('per_hour_rate' | checkActive : this.formConfig : 'new-billing-advice') && !isFinancialValuesHidden
            "
          >
            <div *ngIf="datasaved && editPerhourrate" class="div-width-align">
              <input
                digitOnly
                [isPercentage]="false"
                [allowDecimal]="true"
                [decimalsAllowed]="decimalValue"
                type="text"
                placeholder="Value"
                [digitsAllowed]="noOFDigits"
                [(ngModel)]="item.per_hour_rate"
                (ngModelChange)="getPerHourRate($event, i)"
                [required]="
                  'non_billable_hours'
                    | checkMandatedField : this.formConfig : 'new-billing-advice'
                "
              />
            </div>
            <div *ngIf="datasaved && !editPerhourrate && enable_financial"  class="div-width-align">
              <span class="right-overflow-class"  tooltip="{{ (item.per_hour_rate ? item.per_hour_rate : 0)  }}">{{ (item.per_hour_rate ? item.per_hour_rate : 0)  }}</span>
            </div>
            <div *ngIf="datasaved && !editPerhourrate && !enable_financial"  class="div-width-align">
              <span></span>
            </div>
            <div *ngIf="!datasaved"  class="div-width-align">
              <mat-icon class="refresh-icon" tooltip="Refreshing"
                >autorenew</mat-icon
              >
            </div>
          </div>

          <div
            class="value-col-class"
            *ngIf="('value' | checkActive : this.formConfig : 'new-billing-advice') && !isFinancialValuesHidden"
          >
            <div *ngIf="datasaved && editPerhourrate" class="div-width-align">
              <input
                digitOnly
                [isPercentage]="false"
                [allowDecimal]="true"
                [decimalsAllowed]="decimalValue"
                type="text"
                placeholder="Value"
                [(ngModel)]="item.value"
                [required]="
                  'value'
                    | checkMandatedField : this.formConfig : 'new-billing-advice'
                "
              />
            </div>

            <div *ngIf="!datasaved"  class="div-width-align">
              <mat-icon class="refresh-icon" tooltip="Refreshing"
                >autorenew</mat-icon
              >
            </div>

            <div *ngIf="datasaved && !editPerhourrate && enable_financial"  class="div-width-align">
              <span class="right-overflow-class"  tooltip="{{ (item.value ? item.value : 0) | decimal : decimalValue }}">{{ (item.value ? item.value : 0) | decimal : decimalValue }}</span>
            </div>

            <div *ngIf="datasaved && !editPerhourrate && !enable_financial">
              <span></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-------------------Position Based View---------------->

  <div class="position-view" *ngIf="positionBased">
    <div class="body-table" *ngIf="PositionData?.length > 0; else noData">
      <div class="header-table">
        <div class="header-row">
          <div class="header-col-class checkbox-class" *ngIf="enableCheckBoxClass && !bill_enable">
            
          </div>
          <div class="header-col-class expand-class" *ngIf="!bill_enable">
            <div class="expand-collapse-all-container">
              <button
                class="toggle-all-btn"
                (click)="toggleAll()"
                [disabled]="!hasCollapsedItems() && !hasExpandedItems()"
              >
                <mat-icon>{{ hasExpandedItems() ? 'expand_more' : 'chevron_right' }}</mat-icon>
              </button>
            </div>
          </div>
          <div
            class="header-col-class emp-class" [ngStyle]="{ 'min-width': bill_enable ? '8rem' : '5rem'}"
            *ngIf="'id' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')"
          >
            <span class="overflow-class center emp-class" [ngStyle]="{ 'min-width': bill_enable ? '8rem' : '5rem'}">{{
              "id" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "ID"
            }}</span>
          </div>
          <div
            class="header-col-class emp-class" [ngStyle]="{ 'min-width': bill_enable ? '8rem' : '5rem'}"
            *ngIf="'rate_card_id' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')"
          >
            <span class="overflow-class center emp-class" [ngStyle]="{ 'min-width': bill_enable ? '8rem' : '5rem'}">{{
              "rate_card_id" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "rate_card_id"
            }}</span>
          </div>
          <div
            class="header-col-class name-class"   [ngStyle]="{ 'min-width': bill_enable ? '16rem' : ''}"
            *ngIf="'name' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')"
          >
            <span class="overflow-class center name-class"   [ngStyle]="{ 'min-width': bill_enable ? '16rem' : ''}">{{
              "name" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Name"
            }}</span>
          </div>
          <div
            class="header-col-class enable-class"
            *ngIf="
              ('action_type'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="overflow-class center enable-class">{{
              "action_type"
                | checkLabel
                  : this.formConfig
                  : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                  : "Action Type"
            }}</span>
          </div>
          <div
            class="header-col-class billable-class"
            *ngIf="
              'billable_flag' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
            "
          >
            <span class="overflow-class center billable-class">{{
              "billable_flag"
                | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Billable"
            }}</span>
          </div>
          <div
            class="header-col-class quote-position-class"
            *ngIf="
              'quote_position_location' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
            "
          >
            <span class="overflow-class center quote-position-class">{{
              "quote_position_location"
                | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Location"
            }}</span>
          </div>
          <div
            class="header-col-class date-class"
            *ngIf="
              'start_date' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
            "
          >
            <span class="overflow-class center date-class">{{
              "start_date"
                | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Start Date"
            }}</span>
          </div>
          <div
            class="header-col-class date-class"
            *ngIf="
              'end_date' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
            "
          >
            <span class="overflow-class center date-class">{{
              "end_date"
                | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "End Date"
            }}</span>
          </div>

          <div class="header-col-class-span" *ngIf="'timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')">
            <div class="header-main">
              <span class="overflow-center-class">{{
                "timesheet_hours_header"
                  | checkLabel
                    : this.formConfig
                    : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                    : "Timesheet
                Hrs"
              }}</span>
            </div>
            <div class="sub-header">
              <div
              class="sub-header-col"
              *ngIf="
                'after_zifo_timesheet_hours'
                  | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
              "
            >
              <span class="overflow-center-class">{{
                "after_zifo_timesheet_hours"
                  | checkLabel
                    : this.formConfig
                    : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                    : "Zifo Timesheet"
              }}</span>
            </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'billable_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "billable_hours"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Billable"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'non_billable_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "non_billable_hours"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Non Billable"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'non_billable_hours_growth'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "non_billable_hours_growth"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Non Billable Growth"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'non_billable_hours_flexi'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "non_billable_hours_flexi"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Non Billable Flexi"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'total_timesheet_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "total_timesheet_hours"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Total"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'zifo_timesheet_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "zifo_timesheet_hours"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Timesheet"
                }}</span>
              </div>
            </div>
          </div>

          <div class="header-col-class-span" *ngIf="'prev_timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')">
            <div class="header-main">
              <span class="overflow-center-class">{{
                "prev_timesheet_hours_header"
                  | checkLabel
                    : this.formConfig
                    : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                    : "Previous
                Timesheet Hrs"
              }}</span>
            </div>
            <div class="sub-header">
              <div
                class="sub-header-col"
                *ngIf="
                  'previous_billable_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "previous_billable_hours"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Billable"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'previous_non_billable_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "previous_non_billable_hours"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Non Billable"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'previous_non_billable_hours_growth'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "previous_non_billable_hours_growth"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Non Billable Growth"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'previous_non_billable_hours_flexi'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "previous_non_billable_hours_flexi"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Non Billable Flexi"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'previous_total_timesheet_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "previous_total_timesheet_hours"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Total Hours"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'zifo_previous_timesheet_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "zifo_previous_timesheet_hours"
                    | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Zifo"
                }}</span>
              </div>
            </div>
          </div>

          <div class="header-col-class-span" *ngIf="'round_off_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')">
            <div class="header-main">
              <span class="overflow-center-class">{{
                "round_off_header"
                  | checkLabel
                    : this.formConfig
                    : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                    : "Round Off Hrs"
              }}</span>
            </div>
            <div class="sub-header">
              <div
                class="sub-header-col"
                *ngIf="
                  'rounded_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "rounded_hours"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Rounded off Hours"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'after_rounded_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "after_rounded_hours"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Zifo Rounded off Hours"
                }}</span>
              </div>
            </div>
          </div>

          <div class="header-col-class-span" *ngIf="'billable_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')">
            <div class="header-main">
              <span class="overflow-center-class">{{
                "billable_header"
                  | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Billable"
              }}</span>
            </div>
            <div class="sub-header">
              <div
                class="sub-header-col" [ngClass]="{ 'bill-class': bill_enable }"
                *ngIf="
                  'planned_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "planned_hours"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Planned Hrs"
                }}</span>
              </div>
              <div
                class="sub-header-col" [ngClass]="{ 'bill-class': bill_enable }"
                *ngIf="
                  'actual_billable_hours'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "actual_billable_hours"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Actual Hrs"
                }}</span>
              </div>
              <div
                class="sub-header-col"
                *ngIf="
                  'actual_billable_days'
                    | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              >
                <span class="overflow-center-class">{{
                  "actual_billable_days"
                    | checkLabel
                      : this.formConfig
                      : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                      : "Actual Days"
                }}</span>
              </div>
            </div>
          </div>

          <div
            class="header-col-class hour-class" [ngStyle]="{ 'min-width': bill_enable ? '10rem' : ''}"
            *ngIf="
              ('per_hour_rate' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden
            "
          >
            <span class="overflow-class center hour-class"  [ngStyle]="{ 'min-width': bill_enable ? '10rem' : ''}">{{
              "per_hour_rate"
                | checkLabel
                  : this.formConfig
                  : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                  : "Per Hour Rate"
            }}</span>
          </div>
          <div
            class="header-col-class val-class"   [ngStyle]="{ 'min-width': bill_enable ? '10rem' : ''}"
            *ngIf="('value' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden"
          >
            <span class="overflow-class center val-class"   [ngStyle]="{ 'min-width': bill_enable ? '10rem' : ''}">{{
              "value"
                | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Value"
            }}</span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('actual_till_previous_date' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))">
            <span class="overflow-class center line-future-calci-class">
              {{"actual_till_previous_date" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Actual Effort Till Previous Date"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('actual_till_date' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))">
            <span class="overflow-class center line-future-calci-class">
              {{"actual_till_date" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Actual Effort Till Date"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('future_effort' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))">
            <span class="overflow-class center line-future-calci-class">
              {{"future_effort" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Future Effort"}}
            </span>
          </div>


          <div class="header-col-class line-future-calci-class"   
              *ngIf="('future_effort_required' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))">
            <span class="overflow-class center line-future-calci-class">
              {{"future_effort_required" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Additional Effort Required"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('total_quote_position_hours' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))">
            <span class="overflow-class center line-future-calci-class">
              {{"total_quote_position_hours" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Total Effort"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('total_quote_position_value' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))  && !isFinancialValuesHidden">
            <span class="overflow-class center line-future-calci-class">
              {{"total_quote_position_value" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Total Quote Position Value"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('revised_per_hour_rate' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))  && !isFinancialValuesHidden">
            <span class="overflow-class center line-future-calci-class">
              {{"revised_per_hour_rate" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Revised Per Hour Rate"}}
            </span>
           </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('baseline_planned_effort' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))">
            <span class="overflow-class center line-future-calci-class">
              {{"baseline_planned_effort" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Planned Effort"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('revenue_till_previous_month' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden">
            <span class="overflow-class center line-future-calci-class">
              {{"revenue_till_previous_month" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Revenue Till Previous Month"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('revenue_till_date' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden">
            <span class="overflow-class center line-future-calci-class">
              {{"revenue_till_date" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Revenue Till Date"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('future_revenue' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden">
            <span class="overflow-class center line-future-calci-class">
              {{"future_revenue" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Future Revenue"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('total_revenue' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden">
            <span class="overflow-class center line-future-calci-class">
              {{"total_revenue" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Total Revenue"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('current_month_revenue' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden">
            <span class="overflow-class center line-future-calci-class">
              {{"current_month_revenue" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Current Month Revenue"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('current_month_per_hour_rate' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden">
            <span class="overflow-class center line-future-calci-class">
              {{"current_month_per_hour_rate" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Current Month Per Hour Rate"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('current_month_progress' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden">
            <span class="overflow-class center line-future-calci-class">
              {{"current_month_progress" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Current Month Progress"}}
            </span>
          </div>

          <div class="header-col-class line-future-calci-class"   
              *ngIf="('cummulative_month_progress' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))">
            <span class="overflow-class center line-future-calci-class">
              {{"cummulative_month_progress" | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Cummulative Month Progress"}}
            </span>
          </div>

          <div
            class="header-col-class line-comments-class"
            *ngIf="('comments' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))"
          >
            <span class="overflow-class center line-comments-class">{{
              "comments"
                | checkLabel : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice') : "Comments"
            }}</span>
          </div>
          
        </div>
      </div>

      <div class="content-table">
        <div
          class="value-row"
          *ngFor="let item of PositionData; let i = index"
          [ngStyle]="{ display: item.isVisible ? 'flex' : 'none' }"
          [ngClass]="{ 'value-row-child': !item.has_child }"
        >
        <div class="value-col-class checkbox-class" *ngIf="enableCheckBoxClass && !bill_enable">
          <div class="checkbox">
          <mat-checkbox
            [(ngModel)]="item.is_selected"
            [indeterminate]="item.is_indeterminate"
            (ngModelChange)="onCheckBoxToggleChange($event, item)"
          ></mat-checkbox>
        </div>
        </div>
          <div class="value-col-class expand-class" *ngIf="!bill_enable">
            <div *ngIf="item.has_child">
              <mat-icon
                *ngIf="!item.expandEnable"
                class="expand-icon-class"
                (click)="showChild(item)"
              >
                chevron_right
              </mat-icon>
              <mat-icon
                *ngIf="item.expandEnable"
                class="expand-icon-class"
                (click)="showChild(item)"
              >
                expand_more
              </mat-icon>
            </div>
            <div *ngIf="!item.has_child">
              <span>-</span>
            </div>
          </div>
          <div
            class="value-col-class emp-class" [ngStyle]="{ 'min-width': bill_enable ? '8rem' : '5rem'}"
            *ngIf="'id' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')"
          >
            <span class="overflow-class center emp-class" [ngStyle]="{ 'min-width': bill_enable ? '8rem' : '5rem'}">{{ item.associate_id ? item.associate_id : "-" }}</span>
          </div>
          <div
            class="value-col-class emp-class" [ngStyle]="{ 'min-width': bill_enable ? '8rem' : '5rem'}"
            *ngIf="'rate_card_id' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')"
          >
            <span class="overflow-class center emp-class" [ngStyle]="{ 'min-width': bill_enable ? '8rem' : '5rem'}">{{ item.rate_card_id ? item.rate_card_id : "-" }}</span>
          </div>
          <div
            class="value-col-class name-class"   [ngStyle]="{ 'min-width': bill_enable ? '16rem' : ''}"
            *ngIf="'name' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')"
          >
            <div class="d-flex flex-column">
              <span class="overflow-class left name-class" [ngStyle]="{ 'min-width': bill_enable ? '16rem' : '' }"
                    tooltip="{{item.has_child && item.id && item.name ? (item.id + ' - ' + item.name) : (item.name ? item.name : '-')}}">
                <span *ngIf="item.has_child && item.id" style="color: black; font-weight: 100;">{{ item.id }}</span><span *ngIf="item.has_child && item.id && item.name"> - </span><span>{{ item.name ? item.name : "-" }}</span>
              </span>
            </div>
          </div>
          <div
            class="value-col-class enable-class"
            *ngIf="
              ('action_type'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <div *ngIf="item.has_child && datasaved; else displayNone">
              <mat-slide-toggle
                class="employee-billing-slide-toggle"
                [ngClass]="{ disabled: !editMode }"
                [disabled]="(!editMode || item?.is_disabled)"
                [checked]="!item.is_employee_level"
                (change)="toggleEmployeeLevel($event, item)"
              >
              </mat-slide-toggle>
            </div>
            <ng-template #displayNone>
              <span>-</span>
            </ng-template>
          </div>
          <div
            class="value-col-class billable-class"
            *ngIf="
              'billable_flag' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
            "
          >
            <span *ngIf="!item.has_child && item.billable == 1" tooltip ="Billable">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13.2203 5.80052L10.2003 2.78052C9.56698 2.14719 8.69364 1.80719 7.80031 1.85385L4.46698 2.01385C3.13364 2.07385 2.07364 3.13385 2.00698 4.46052L1.84698 7.79385C1.80698 8.68719 2.14031 9.56052 2.77364 10.1939L5.79364 13.2139C7.03364 14.4539 9.04698 14.4539 10.2936 13.2139L13.2203 10.2872C14.467 9.05385 14.467 7.04052 13.2203 5.80052ZM6.33364 8.25385C5.27364 8.25385 4.41364 7.39385 4.41364 6.33385C4.41364 5.27385 5.27364 4.41385 6.33364 4.41385C7.39364 4.41385 8.25364 5.27385 8.25364 6.33385C8.25364 7.39385 7.39364 8.25385 6.33364 8.25385Z" fill="#52C41A"/>
              </svg>
            </span>
            <span *ngIf="!item.has_child && item.billable == 0 && item.identity ==2" tooltip ="{{('shadow' | checkLabel : this.formConfig: 'new-billing-advice': 'Shadow')}}">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13.2203 5.80052L10.2003 2.78052C9.56698 2.14719 8.69364 1.80719 7.80031 1.85385L4.46698 2.01385C3.13364 2.07385 2.07364 3.13385 2.00698 4.46052L1.84698 7.79385C1.80698 8.68719 2.14031 9.56052 2.77364 10.1939L5.79364 13.2139C7.03364 14.4539 9.04698 14.4539 10.2936 13.2139L13.2203 10.2872C14.467 9.05385 14.467 7.04052 13.2203 5.80052ZM6.33364 8.25385C5.27364 8.25385 4.41364 7.39385 4.41364 6.33385C4.41364 5.27385 5.27364 4.41385 6.33364 4.41385C7.39364 4.41385 8.25364 5.27385 8.25364 6.33385C8.25364 7.39385 7.39364 8.25385 6.33364 8.25385Z" fill="#6E7B8F"/>
              </svg>
            </span>
            
          </div>
          <div
            class="value-col-class quote-position-class"
            *ngIf="
              'quote_position_location' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
            "
          >
            <span  *ngIf="item.has_child" class="overflow-class left quote-position-class" tooltip ="{{ item.quote_position_location ? item.quote_position_location : '-' }}">
              {{ item.quote_position_location ? item.quote_position_location : "-" }}
            </span>
            <span  *ngIf="!item.has_child">
              -
            </span>
          </div>
          
          <div
            class="value-col-class date-class"
            *ngIf="
              'start_date' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
            "
          >
            <span>{{ item.start_date ? item.start_date : "-" }}</span>
          </div>
          <div
            class="value-col-class date-class"
            *ngIf="
              'end_date' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
            "
          >
            <span>{{ item.end_date ? item.end_date : "-" }}</span>
          </div>
          <div
          class="value-col-class"
          *ngIf="
            ('after_zifo_timesheet_hours'
              | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
          "
        >
          <span class="right-overflow-class" *ngIf="!allowTimesheetInDecimal" tooltip="{{ (item.after_zifo_timesheet_hours ? item.after_zifo_timesheet_hours : 0) | ceilPipe }}">{{ (item.after_zifo_timesheet_hours ? item.after_zifo_timesheet_hours : 0) | ceilPipe }}</span>
          <span class="right-overflow-class" *ngIf="allowTimesheetInDecimal"  tooltip="{{ (item.after_zifo_timesheet_hours ? item.after_zifo_timesheet_hours : 0) | decimal : decimalValue }}">{{ (item.after_zifo_timesheet_hours ? item.after_zifo_timesheet_hours : 0) | decimal : decimalValue }}</span>
        </div>
          <div
            class="value-col-class"
            *ngIf="
              ('billable_hours'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            " 
          >
            <div *ngIf="datasaved" class="div-width-align">
              <span class="right-overflow-class" tooltip="{{ item.billable_hours ? item.billable_hours : 0 }}">{{ item.billable_hours }}</span>
            </div>
            <div *ngIf="!datasaved">
              <mat-icon class="refresh-icon" tooltip="Refreshing"
                >autorenew</mat-icon
              >
            </div>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('non_billable_hours'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <div *ngIf="datasaved" class="div-width-align">
              <span class="right-overflow-class"  tooltip="{{ item.non_billable_hours ? item.non_billable_hours : 0 }}">{{ item.non_billable_hours ? item.non_billable_hours : 0 }}</span>
            </div>
            <div *ngIf="!datasaved">
              <mat-icon class="refresh-icon" tooltip="Refreshing"
                >autorenew</mat-icon
              >
            </div>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              datasaved &&
              ('non_billable_hours_growth'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.non_billable_hours_growth ? item.non_billable_hours_growth : 0 }}">{{ item.non_billable_hours_growth ? item.non_billable_hours_growth : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              datasaved &&
              ('non_billable_hours_flexi'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.non_billable_hours_flexi ? item.non_billable_hours_flexi : 0 }}">{{ item.non_billable_hours_flexi ? item.non_billable_hours_flexi : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('timesheet_hours'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class" *ngIf="!allowTimesheetInDecimal"  tooltip="{{ (item.timesheet_hours ? item.timesheet_hours : 0) | ceilPipe }}">{{ (item.timesheet_hours ? item.timesheet_hours : 0) | ceilPipe }}</span>
            <span class="right-overflow-class" *ngIf="allowTimesheetInDecimal"  tooltip="{{ (item.timesheet_hours ? item.timesheet_hours : 0) | decimal : decimalValue }}">{{ (item.timesheet_hours ? item.timesheet_hours : 0) | decimal : decimalValue }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('zifo_timesheet_hours'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class" *ngIf="!allowTimesheetInDecimal"  tooltip="{{ (item.zifo_timesheet_hours ? item.zifo_timesheet_hours : 0) | ceilPipe }}">{{ (item.zifo_timesheet_hours ? item.zifo_timesheet_hours : 0) | ceilPipe }}</span>
            <span class="right-overflow-class" *ngIf="allowTimesheetInDecimal"  tooltip="{{ (item.zifo_timesheet_hours ? item.zifo_timesheet_hours : 0) | decimal : decimalValue }}">{{ (item.zifo_timesheet_hours ? item.zifo_timesheet_hours : 0) | decimal : decimalValue }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('previous_billable_hours' 
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('prev_timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.previous_billable_hours ? item.previous_billable_hours : 0 }}">{{ item.previous_billable_hours ? item.previous_billable_hours : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('previous_non_billable_hours'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('prev_timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.previous_non_billable_hours ? item.previous_non_billable_hours : 0 }}">{{ item.previous_non_billable_hours ? item.previous_non_billable_hours : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('previous_non_billable_hours_growth'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('prev_timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.previous_non_billable_hours_growth ? item.previous_non_billable_hours_growth : 0 }}">{{ item.previous_non_billable_hours_growth ? item.previous_non_billable_hours_growth : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('previous_non_billable_hours_flexi'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('prev_timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class"  tooltip="{{ item.previous_non_billable_hours_flexi ? item.previous_non_billable_hours_flexi : 0 }}">{{ item.previous_non_billable_hours_flexi ? item.previous_non_billable_hours_flexi : 0 }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              ('previous_total_timesheet_hours'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('prev_timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class" *ngIf="!allowTimesheetInDecimal" tooltip="{{ (item.previous_timesheet_hours ? item.previous_timesheet_hour : 0) | ceilPipe  }}">{{ (item.previous_timesheet_hours ? item.previous_timesheet_hour : 0) | ceilPipe  }}</span>
            <span class="right-overflow-class" *ngIf="allowTimesheetInDecimal"  tooltip="{{ (item.previous_timesheet_hours ? item.previous_timesheet_hours : 0) | decimal : decimalValue }}">{{ (item.previous_timesheet_hours ? item.previous_timesheet_hours : 0) | decimal : decimalValue }}</span>
          </div>

          <div
            class="value-col-class"
            *ngIf="
              ('zifo_previous_timesheet_hours'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('prev_timesheet_hours_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class" *ngIf="!allowTimesheetInDecimal"   tooltip="{{ (item.zifo_previous_timesheet_hours ? item.zifo_previous_timesheet_hours : 0)  | ceilPipe }}">{{ (item.zifo_previous_timesheet_hours ? item.zifo_previous_timesheet_hours : 0)  | ceilPipe }}</span>
            <span class="right-overflow-class" *ngIf="allowTimesheetInDecimal"  tooltip="{{ (item.zifo_previous_timesheet_hours ? item.zifo_previous_timesheet_hours : 0) | decimal : decimalValue }}">{{ (item.zifo_previous_timesheet_hours ? item.zifo_previous_timesheet_hours : 0) | decimal : decimalValue }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              datasaved &&
              ('rounded_hours'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('round_off_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class"  tooltip="{{ (item.rounded_hours ? item.rounded_hours : 0) | ceilPipe }}">{{ (item.rounded_hours ? item.rounded_hours : 0) | ceilPipe }}</span>
          </div>
          <div
            class="value-col-class"
            *ngIf="
              datasaved &&
              ('after_rounded_hours'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('round_off_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class"  tooltip="{{ (item.after_rounded_hours ? item.after_rounded_hours : 0) | ceilPipe }}">{{ (item.after_rounded_hours ? item.after_rounded_hours : 0) | ceilPipe }}</span>
          </div>
          <div
            class="value-col-class" [ngClass]="{ 'bill-class': bill_enable }"
            *ngIf="
              ('planned_hours' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('billable_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <div *ngIf="datasaved && !bill_enable" class="div-width-align">
              <span class="right-overflow-class"  tooltip="{{ (item.planned_hours ? item.planned_hours : 0) | roundOff : decimalValue }}">{{ (item.planned_hours ? item.planned_hours : 0) | roundOff : decimalValue }}</span>
            </div>
            <div *ngIf="datasaved && bill_enable" class="div-width-align">
              <span class="right-overflow-class"  tooltip="{{ (item.planned_hours ? item.planned_hours : 0)  | ceilPipe }}">{{ (item.planned_hours ? item.planned_hours : 0) | ceilPipe }}</span>
            </div>
            <div *ngIf="!datasaved">
              <mat-icon class="refresh-icon" tooltip="Refreshing"
                >autorenew</mat-icon
              >
            </div>
          </div>
          <div
            class="value-col-class" [ngClass]="{ 'bill-class': bill_enable }"
            *ngIf="
              ('actual_billable_hours'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && ('billable_header' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <div *ngIf="item.has_child && datasaved" class="div-width-align">
              <div
                *ngIf="!item.is_employee_level && editMode; else displayHours"
                class="div-width-align"
              >
                <input
                  [ngClass]="{ disabled: isZeroTimesheetHours(item) }"
                  digitOnly
                  [isPercentage]="false"
                  [allowDecimal]="(allowActualHrsDecimal && !bill_enable)"
                  [decimalsAllowed]="decimalValue"
                  type="text"
                  placeholder="Value"
                  [(ngModel)]="item.actual_billable_hours"
                  (ngModelChange)="getActualBillableHours($event, i, item.id)"
                  [required]="
                    'actual_billable_hours'
                      | checkMandatedField : formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                  "
                  [ngStyle]="highlightHoursDeviations ? {
                    color: getInputColor((bill_enable ? item.planned_hours : item.billable_hours), item.actual_billable_hours),
                    'font-weight': '700'
                  } : { 'font-weight': '700' }"
                  [readonly]="isZeroTimesheetHours(item)"
                />
              </div>

              <ng-template #displayHours>
                <span *ngIf="allowActualHrsDecimal && !bill_enable" class="right-overflow-class"  
                [ngStyle]="highlightHoursDeviations ? {
                  color: getInputColor((bill_enable ? item.planned_hours : item.billable_hours), item.actual_billable_hours),
                  'font-weight': '700'
                } : { 'font-weight': '700' }"
                tooltip="{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)   | decimal : decimalValue }}">{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)   | decimal : decimalValue }}</span>
                <span *ngIf="!allowActualHrsDecimal || bill_enable" class="right-overflow-class"  
                [ngStyle]="highlightHoursDeviations ? {
                  color: getInputColor((bill_enable ? item.planned_hours : item.billable_hours), item.actual_billable_hours),
                  'font-weight': '700'
                } : { 'font-weight': '700' }"
                tooltip="{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)  | ceilPipe }}">{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)  | ceilPipe }}</span>
              </ng-template>
            </div>

            <div *ngIf="!item.has_child" class="div-width-align">
              <div
                *ngIf="
                  item.is_employee_level && editMode;
                  else displayEmployeeHours
                "
                class="div-width-align"
              >
                <input
                  [ngClass]="{ disabled: isZeroTimesheetHours(item) }"
                  digitOnly
                  [isPercentage]="false"
                  [allowDecimal]="allowActualHrsDecimal"
                  [decimalsAllowed]="decimalValue"
                  type="text"
                  placeholder="Value"
                  [(ngModel)]="item.actual_billable_hours"
                  (ngModelChange)="getActualBillableHours($event, i, item.id)"
                  [required]="
                    'actual_billable_hours'
                      | checkMandatedField : formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                  "
                  [ngStyle]="highlightHoursDeviations ? {
                    color: getInputColor((bill_enable ? item.planned_hours : item.billable_hours), item.actual_billable_hours),
                    'font-weight': '700'
                  } : { 'font-weight': '700' }"
                  [readonly]="isZeroTimesheetHours(item)"
                />
              </div>

              <ng-template #displayEmployeeHours>
                <span *ngIf="allowActualHrsDecimal"  class="right-overflow-class"  
                [ngStyle]="highlightHoursDeviations ? {
                  color: getInputColor((bill_enable ? item.planned_hours : item.billable_hours), item.actual_billable_hours),
                  'font-weight': '700'
                } : { 'font-weight': '700' }"
                tooltip="{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)  | decimal : decimalValue}}">{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)  | decimal : decimalValue}}</span>
                <span *ngIf="!allowActualHrsDecimal"  class="right-overflow-class"  
                [ngStyle]="highlightHoursDeviations ? {
                  color: getInputColor((bill_enable ? item.planned_hours : item.billable_hours), item.actual_billable_hours),
                  'font-weight': '700'
                } : { 'font-weight': '700' }"
                tooltip="{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)  | ceilPipe }}">{{ (item.actual_billable_hours ? item.actual_billable_hours : 0)  | ceilPipe }}</span>
              </ng-template>
            </div>
            <!-- <div *ngIf="!item.has_child && !enable_financial"></div> -->
          </div>
        
          <div
            class="value-col-class"
            *ngIf="
              datasaved &&
              ('actual_billable_days'
                | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))
            "
          >
            <span class="right-overflow-class"  tooltip="{{ (item.actual_billable_days ? item.actual_billable_days : 0)  | decimal : decimalValue }}">{{ (item.actual_billable_days ? item.actual_billable_days : 0)  | decimal : decimalValue }}</span>
          </div>
          <div
            class="value-col-class hour-class"   [ngStyle]="{ 'min-width': bill_enable ? '10rem' : ''}"
            *ngIf="
              ('per_hour_rate' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden
            "
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div *ngIf="datasaved && editPerhourrate"  class="div-width-align">
                <input
                  digitOnly
                  [isPercentage]="false"
                  [allowDecimal]="true"
                  [decimalsAllowed]="decimalValue"
                  type="text"
                  placeholder="Value"
                  [(ngModel)]="item.per_hour_rate"
                  [digitsAllowed]="noOFDigits"
                  (ngModelChange)="getPerHourRate($event, i)"
                  [required]="
                    'non_billable_hours'
                      | checkMandatedField : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                  "
                />
              </div>
              <div *ngIf="!datasaved">
                <mat-icon class="refresh-icon" tooltip="{{ Refreshing }}"
                  >autorenew</mat-icon
                >
              </div>
              <div *ngIf="datasaved && !editPerhourrate" class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.per_hour_rate ? item.per_hour_rate : 0)  }}">{{ (item.per_hour_rate ? item.per_hour_rate : 0)  }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              <div *ngIf="enable_financial" class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.per_hour_rate ? item.per_hour_rate : 0)  }}">{{ (item.per_hour_rate ? item.per_hour_rate : 0)  }}</span>
              </div>
              <div *ngIf="!enable_financial"></div>
            </div>
          </div>

          <div
            class="value-col-class val-class"   [ngStyle]="{ 'min-width': bill_enable ? '10rem' : ''}"
            *ngIf="('value' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div *ngIf="datasaved && editPerhourrate">
                <input
                  digitOnly
                  [isPercentage]="false"
                  [allowDecimal]="true"
                  type="text"
                  [decimalsAllowed]="decimalValue"
                  placeholder="Value"
                  [(ngModel)]="item.value"
                  (ngModelChange)="getValue($event, item.id)"
                  [required]="
                    'value'
                      | checkMandatedField : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                  "
                />
              </div>
              <div *ngIf="!datasaved">
                <mat-icon class="refresh-icon" tooltip="{{ Refreshing }}"
                  >autorenew</mat-icon
                >
              </div>
              <div *ngIf="datasaved && !editPerhourrate" class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.value ? item.value : 0) | decimal : decimalValue }}">{{ (item.value ? item.value : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              <div *ngIf="enable_financial" class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.value ? item.value : 0) | decimal : decimalValue }}">{{ (item.value ? item.value : 0) | decimal : decimalValue }}</span>
              </div>
              <div *ngIf="!enable_financial"></div>
            </div>
          </div>

          <div
          class="value-col-class line-future-calci-class"   
          *ngIf="('actual_till_previous_date' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))"
        >
          <div *ngIf="item.has_child" class="div-width-align">
            <div class="div-width-align">
              <span class="right-overflow-class"  tooltip="{{ (item.display_actual_till_previous_date ? item.display_actual_till_previous_date : 0) | decimal : decimalValue }}">{{ (item.display_actual_till_previous_date ? item.display_actual_till_previous_date : 0) | decimal : decimalValue }}</span>
            </div>
          </div>
          <div *ngIf="!item.has_child" class="div-width-align">
            
           
          </div>
        </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('actual_till_date' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.display_actual_till_date ? item.display_actual_till_date : 0) | decimal : decimalValue }}">{{ (item.display_actual_till_date ? item.display_actual_till_date : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('future_effort' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.future_planned ? item.future_planned : 0) | decimal : decimalValue }}">{{ (item.future_planned ? item.future_planned : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>
          

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('future_effort_required' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))"
          >
          <div *ngIf="item.has_child" class="div-width-align">
            <div *ngIf="this.editMode">
              <input
                digitOnly
                [isPercentage]="false"
                [allowDecimal]="true"
                [isNegative]="true"
                type="text"
                [decimalsAllowed]="decimalValue"
                placeholder="Hours"
                [(ngModel)]="item.future_effort_required"
                (ngModelChange)="getFutureEffortRequired($event, i)"
                [required]="
                  'future_effort_required'
                    | checkMandatedField : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')
                "
              />
            </div>
            
            <div *ngIf="!this.editMode" class="div-width-align">
              <span class="right-overflow-class"  tooltip="{{ (item.future_effort_required ? item.future_effort_required : 0) | decimal : decimalValue }}">{{ (item.future_effort_required ? item.future_effort_required : 0) | decimal : decimalValue }}</span>
            </div>
          </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

       
          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('total_quote_position_hours' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.display_total_quote_position_hours? item.display_total_quote_position_hours: 0) | decimal : decimalValue }}">{{ (item.display_total_quote_position_hours? item.display_total_quote_position_hours: 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('total_quote_position_value' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))  && !isFinancialValuesHidden"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.total_quote_position_value ? item.total_quote_position_value : 0) | decimal : decimalValue }}">{{ (item.total_quote_position_value ? item.total_quote_position_value : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('revised_per_hour_rate' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))  && !isFinancialValuesHidden"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.revised_per_hour_rate ? item.revised_per_hour_rate : 0) | decimal : decimalValue }}">{{ (item.revised_per_hour_rate ? item.revised_per_hour_rate : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('baseline_planned_effort' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.baseline_planned_effort ? item.baseline_planned_effort : 0) | decimal : decimalValue }}">{{ (item.baseline_planned_effort ? item.baseline_planned_effort : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>


          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('revenue_till_previous_month' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.revenue_till_previous_month ? item.revenue_till_previous_month : 0) | decimal : decimalValue }}">{{ (item.revenue_till_previous_month ? item.revenue_till_previous_month : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('revenue_till_date' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.display_revenue_till_date ? item.display_revenue_till_date : 0) | decimal : decimalValue }}">{{ (item.display_revenue_till_date ? item.display_revenue_till_date : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('future_revenue' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.future_revenue ? item.future_revenue : 0) | decimal : decimalValue }}">{{ (item.future_revenue ? item.future_revenue : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('total_revenue' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.display_total_revenue ? item.display_total_revenue : 0) | decimal : decimalValue }}">{{ (item.display_total_revenue ? item.display_total_revenue : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('current_month_revenue' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.current_month_revenue ? item.current_month_revenue : 0) | decimal : decimalValue }}">{{ (item.current_month_revenue ? item.current_month_revenue : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('current_month_per_hour_rate' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice')) && !isFinancialValuesHidden"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.current_month_per_hour_rate ? item.current_month_per_hour_rate : 0) | decimal : decimalValue }}">{{ (item.current_month_per_hour_rate ? item.current_month_per_hour_rate : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('current_month_progress' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.current_month_progress ? item.current_month_progress : 0) | decimal : decimalValue }}">{{ (item.current_month_progress ? item.current_month_progress : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-future-calci-class"   
            *ngIf="('cummulative_month_progress' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              <div class="div-width-align">
                <span class="right-overflow-class"  tooltip="{{ (item.cummulative_month_progress ? item.cummulative_month_progress : 0) | decimal : decimalValue }}">{{ (item.cummulative_month_progress ? item.cummulative_month_progress : 0) | decimal : decimalValue }}</span>
              </div>
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              
             
            </div>
          </div>

          <div
            class="value-col-class line-comments-class"
            *ngIf="('comments' | checkActive : this.formConfig : (bill_enable ? 'bill_billing_advice' : 'new-billing-advice'))"
          >
            <div *ngIf="item.has_child" class="div-width-align">
              
            </div>
            <div *ngIf="!item.has_child" class="div-width-align">
              <div class="div-width-align">
                <div class="comment-btn-class" (click)="viewLineItemComment(item)" *ngIf="(item.comments=='' || item.comments==null || item.comments == 'null' || !item.comments) && editMode">
                
                    <div class="comment-icn-class" >
                      
                      <svg width="13" height="13" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" >
                        <path d="M0.333984 13.6654V1.66536C0.333984 1.2987 0.46454 0.984809 0.725651 0.723698C0.986762 0.462587 1.30065 0.332031 1.66732 0.332031H12.334C12.7007 0.332031 13.0145 0.462587 13.2757 0.723698C13.5368 0.984809 13.6673 1.2987 13.6673 1.66536V9.66536C13.6673 10.032 13.5368 10.3459 13.2757 10.607C13.0145 10.8681 12.7007 10.9987 12.334 10.9987H3.00065L0.333984 13.6654ZM2.43398 9.66536H12.334V1.66536H1.66732V10.4154L2.43398 9.66536Z" fill="#292D32"/>
                      </svg>
                      
                        
                    </div>
                    <span class="comment-text" >Add</span>
                </div>

                <div class="comment-btn-class" (click)="viewLineItemComment(item)" *ngIf="item.comments!='' && item.comments!=null && item.comments != 'null' &&  item.comments">
                  
                 
                    <div class="comment-icn-class" >
                      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 3.375C10.7594 3.375 11.375 2.75939 11.375 2C11.375 1.24061 10.7594 0.625 10 0.625C9.24061 0.625 8.625 1.24061 8.625 2C8.625 2.75939 9.24061 3.375 10 3.375Z" fill="#EE4961"/>
                        <path opacity="0.4" d="M10 4C8.895 4 8 3.105 8 2C8 1.635 8.105 1.295 8.28 1H3.5C2.12 1 1 2.115 1 3.49V6.48V6.98C1 8.355 2.12 9.47 3.5 9.47H4.25C4.385 9.47 4.565 9.56 4.65 9.67L5.4 10.665C5.73 11.105 6.27 11.105 6.6 10.665L7.35 9.67C7.445 9.545 7.595 9.47 7.75 9.47H8.5C9.88 9.47 11 8.355 11 6.98V3.72C10.705 3.895 10.365 4 10 4Z" fill="#EE4961"/>
                        <path d="M6 6C5.72 6 5.5 5.775 5.5 5.5C5.5 5.225 5.725 5 6 5C6.275 5 6.5 5.225 6.5 5.5C6.5 5.775 6.28 6 6 6Z" fill="#EE4961"/>
                        <path d="M8 6C7.72 6 7.5 5.775 7.5 5.5C7.5 5.225 7.725 5 8 5C8.275 5 8.5 5.225 8.5 5.5C8.5 5.775 8.28 6 8 6Z" fill="#EE4961"/>
                        <path d="M4 6C3.72 6 3.5 5.775 3.5 5.5C3.5 5.225 3.725 5 4 5C4.275 5 4.5 5.225 4.5 5.5C4.5 5.775 4.28 6 4 6Z" fill="#EE4961"/>
                      </svg>
                    </div>
                    <span class="comment-text" *ngIf="item.comments!='' && item.comments!=null && item.comments != 'null' &&  item.comments">View</span>
                </div>
                  
             
                
              </div>

            </div>
          
          </div>
      </div>
    </div>
    </div>
  </div>
</ng-template>

<!----------------------------------Invoice Screen----------------------------->
<ng-template #invoiceTemplate>
  <div class="content-header-class-invoice">
    <div class="content-text-invoice">
      <span class="header-text-invoice">{{
        "invoice_date"
          | checkLabel : this.formConfig : "new-billing-advice" : "Invoice Date"
      }}</span>
      <div class="header-value-invoice date-range-class" (click)="openDatePicker()">
        <!-- <div *ngIf="editMode">
          <input
            class="date-input"
            [(ngModel)]="data.invoice_date"
            [matDatepicker]="ivd"
            placeholder="DD-MMM-YYYY"
            [min]="minDate"
            [max]="maxDate"
            (click)="openDatePicker()"
          />
          <mat-datepicker #ivd></mat-datepicker>
        </div>
        <span *ngIf="!editMode">{{data.invoice_date ? data.invoice_date : '-'}}</span> -->
        <span>{{ data.invoice_date ? getDateWithFormat(data.invoice_date) : "-" }}</span>
        <mat-icon *ngIf="editMode" class="icon-med-class">date_range</mat-icon>
        <input
          matInput
          [matDatepicker]="ivd"
          (ngModelChange)="onInvoiceDateChange($event)"
          (dateChange)="onDateChange($event)"
          [(ngModel)]="data.invoice_date"
          [min]="minDate"
          [max]="maxDate"
          class="hidden-input"
        />
        <mat-datepicker #ivd></mat-datepicker>
      </div>
    </div>
    <mat-divider [vertical]="true"></mat-divider>
    <div class="content-text-invoice">
      <span class="header-text-invoice">{{
        "invoice_type"
          | checkLabel : this.formConfig : "new-billing-advice" : "Invoice Type"
      }}</span>
      <span class="header-value-invoice">{{
        data.milestone_type_name ? data.milestone_type_name : "-"
      }}</span>
    </div>
    <mat-divider [vertical]="true"></mat-divider>
    <div class="content-text-invoice">
      <span class="header-text-invoice">{{
        "currency"
          | checkLabel : this.formConfig : "new-billing-advice" : "Currency"
      }}</span>
      <span class="header-value-invoice">{{ code ? code : "-" }}</span>
    </div>
    <mat-divider [vertical]="true"></mat-divider>
    <div class="content-text-invoice">
      <span class="header-text-invoice" *ngIf="matrixConfig?.milestone_value_invoice_details && !isFinancialValuesHidden">{{
        "amount_milestone" | checkLabel : this.formConfig : "new-billing-advice" : "Milestone Value"
      }}</span>
      <span class="header-text-invoice" *ngIf="!matrixConfig?.milestone_value_invoice_details && !isFinancialValuesHidden">{{
        "amount_billing" | checkLabel : this.formConfig : "new-billing-advice" : "Billing Advice Value"
      }}</span>
      <span class="header-value-invoice" *ngIf="!matrixConfig?.milestone_value_invoice_details && !isFinancialValuesHidden">{{code ? code : ''}} {{ (getTotalBillingValue() || 0)  | decimal : decimalValue   }}</span>
      <span class="header-value-invoice" *ngIf="matrixConfig?.milestone_value_invoice_details && !isFinancialValuesHidden">
        <app-currency
          [currencyList]="data.value"
          [code]="code"
          [font_size]="currency_font_size"
          class="flex-1"
          type="big"
        >
        </app-currency
      ></span>
    </div>
  </div>
  <ng-container [ngTemplateOutlet]="invoiceGridView"></ng-container>
</ng-template>

<!----------------------------------Invoice Grid View ----------------------------->
<ng-template #invoiceGridView>
  <div class="invoice-grid" *ngIf="!invoiceLoader">
    <div
      class="body-table"
      *ngIf="((employeeBased && items?.length > 0) || 
            (positionBased && PositionData?.length > 0) || 
            (!matrixConfig?.billing_advice_stepper)); 
            else noData"
    >
      <div class="header-table scroll-content">
        <div class="header-row-invoice">
          <div *ngFor="let item of invoiceGridConfig">
            <div
              class="header-col-class-invoice"
              [ngStyle]="{ width: (item.width || 4) + 'rem' }"
              *ngIf="item['isVisible']"
            >
              <span class="overflow-class center ">{{ item?.label }}</span>
            </div>
          </div>
        </div>
        <div class="content-table">
          <div
            class="value-row-invoice"
            *ngFor="let data of invoiceList; let i = index"
          >
            <div
              *ngFor="let col of invoiceGridConfig"
              class="value-col-class-invoice"
              [ngStyle]="{
                width: (col.width || 4) + 'rem',
                display: col.isVisible ? 'flex' : 'none'
              }"
            >
              <div
                *ngIf="col.type === 'inputNumber' && col.isVisible"
                class="invoice-value-grid"
                [ngStyle]="{ width: (col.width || 4) + 'rem' }"
              >
                <input
                  class="text-grid"
                  digitOnly
                  [isPercentage]="false"
                  [allowDecimal]="false"
                  type="text"
                  [isNegative]="true"
                  [digitsAllowed]="noOFDigits"
                  [(ngModel)]="data[col.key]"
                  (ngModelChange)="getTotalAmount($event, i)"
                />
              </div>
              <div
                *ngIf="col.type === 'inputDecimal' && col.isVisible"
                class="invoice-value-grid"
                [ngStyle]="{ width: (col.width || 4) + 'rem' }"
              >
                <input
                  class="text-grid"
                  digitOnly
                  [isPercentage]="false"
                  [allowDecimal]="true"
                  type="text"
                  [isNegative]="true"
                  [digitsAllowed]="noOFDigits"
                  [(ngModel)]="data[col.key]"
                  (ngModelChange)="getTotalAmount($event, i)"
                  [decimalsAllowed]="col.decimalsAllowed ? col.decimalsAllowed : decimalValue"
                />
              </div>
              <div
                *ngIf="col.type === 'inputTextArea' && col.isVisible"
                class="invoice-value-grid"
                [ngStyle]="{ width: (col.width || 4) + 'rem' }"
              >
                <!-- <input
                  type="text"
                  [placeholder]="col.label"
                  class="text-grid"
                  [(ngModel)]="data[col.key]"
                /> -->
                <!-- <dx-html-editor
                 [(ngModel)]="data[col.key]" [placeholder]="col.label">
                <dxo-toolbar>
                  <dxi-item name="bold"></dxi-item>
                  <dxi-item name="italic"></dxi-item>
                  <dxi-item name="strike"></dxi-item>
                  <dxi-item name="underline"></dxi-item>
                  <dxi-item name="separator"></dxi-item>
                  <dxi-item name="alignLeft"></dxi-item>
                  <dxi-item name="alignCenter"></dxi-item>
                  <dxi-item name="alignRight"></dxi-item>
                  <dxi-item name="alignJustify"></dxi-item>
                  <dxi-item name="separator"></dxi-item>
                  <dxi-item name="orderedList"></dxi-item>
                  <dxi-item name="bulletList"></dxi-item>
                </dxo-toolbar>
              </dx-html-editor> -->
              <textarea
                [placeholder]="col.label"
                class="text-grid"
                [(ngModel)]="data[col.key]"
                rows="3"
              ></textarea>
              </div>
              <div
              *ngIf="col.type === 'inputText' && col.isVisible"
              class="invoice-value-grid"
              [ngStyle]="{ width: (col.width || 4) + 'rem' }"
            >
              <input
                type="text"
                [placeholder]="col.label"
                class="text-grid"
                [(ngModel)]="data[col.key]"
              />
       
            </div>
              <div
                *ngIf="col.type === 'dropdown-text' && col.isVisible"
                class="invoice-value-grid"
                [ngStyle]="{ width: (col.width || 4) + 'rem', background: editMode ? '#fff' : '#F6F6F6' }"
              >
              <div *ngIf="editMode" style="display: contents;">
                <app-input-search-name
                  [list]="getDropdownValue(col.dropdownList,data[col.key],true)"
                  class="text-grid dropdown"
                  [ngClass]="{ disabled: !editMode }"
                  [(ngModel)]="data[col.key]"
                  [disabled]="!editMode"
                  placeholder="Select One"
                  [showSelect]="false"
                ></app-input-search-name>
              </div>
              <div *ngIf="!editMode">
                <span>{{getDropdownValue(col.dropdownList,data[col.key]) ? getDropdownValue(col.dropdownList,data[col.key]) : '-'}}</span>
              </div> 
              </div>
              <div
                *ngIf="col.type == 'text' && col.isVisible"
                class="invoice-value-grid"
                [ngStyle]="{ width: (col.width || 4) + 'rem' }"
                style="background-color: #F6F6F6;"
              >
                <span class="overflow-class right" [ngStyle]="{ 'max-width': (col.width || '4') + 'rem' }" tooltip ="{{data[col?.key] ? data[col.key] : '-'}}">{{ data[col?.key] ? data[col.key] : "-" }}</span>
              </div>
              <div
                *ngIf="col.type == 'text-decimal' && col.isVisible"
                class="invoice-value-grid"
                [ngStyle]="{ width: (col.width || 4) + 'rem' }"
                style="background-color: #F6F6F6;"
              >
                <span class="overflow-class right" [ngStyle]="{ 'max-width': (col.width || '4') + 'rem' }" tooltip ="{{(data[col?.key] ? data[col.key] : 0)   | decimal : (col.decimalsAllowed ? col.decimalsAllowed : decimalValue) }}">{{ (data[col?.key] ? data[col.key] : 0)   | decimal : (col.decimalsAllowed ? col.decimalsAllowed : decimalValue)  }}</span>
              </div>
              <div
                *ngIf="col.type == 'text-overflow' && col.isVisible"
                class="invoice-value-grid"
                [ngStyle]="{ width: (col.width || 4) + 'rem' }"
                style="background-color: #F6F6F6; justify-content: flex-start;"
              >
                <span class="overflow-class left  " [ngStyle]="{ 'max-width': (col.width || '4') + 'rem' }" tooltip ="{{data[col?.key] ? data[col.key] : '-'}}">{{ data[col?.key] ? data[col.key] : '-'  }}</span>
              </div>
              <div
                *ngIf="col.type == 'action' && col.isVisible"
                class="invoice-value-grid action"
                [ngStyle]="{ width: (col.width || 4) + 'rem' }"
              >
                <div class="add" (click)="addInvoice()">
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 14 14"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6.25 7.75H1.25C1.0375 7.75 0.859417 7.67808 0.71575 7.53425C0.571917 7.39042 0.5 7.21225 0.5 6.99975C0.5 6.78708 0.571917 6.609 0.71575 6.4655C0.859417 6.32183 1.0375 6.25 1.25 6.25H6.25V1.25C6.25 1.0375 6.32192 0.859417 6.46575 0.71575C6.60958 0.571917 6.78775 0.5 7.00025 0.5C7.21292 0.5 7.391 0.571917 7.5345 0.71575C7.67817 0.859417 7.75 1.0375 7.75 1.25V6.25H12.75C12.9625 6.25 13.1406 6.32192 13.2843 6.46575C13.4281 6.60958 13.5 6.78775 13.5 7.00025C13.5 7.21292 13.4281 7.391 13.2843 7.5345C13.1406 7.67817 12.9625 7.75 12.75 7.75H7.75V12.75C7.75 12.9625 7.67808 13.1406 7.53425 13.2843C7.39042 13.4281 7.21225 13.5 6.99975 13.5C6.78708 13.5 6.609 13.4281 6.4655 13.2843C6.32183 13.1406 6.25 12.9625 6.25 12.75V7.75Z"
                      fill="#45546E"
                    />
                  </svg>
                </div>
                <div
                  class="remove"
                  (click)="removeInvoice(i)"
                  *ngIf="invoiceList.length > 1"
                >
                  <svg
                    width="10"
                    height="10"
                    viewBox="0 0 10 10"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.00014 6.06355L1.75389 9.30955C1.60906 9.45455 1.43347 9.52538 1.22714 9.52205C1.02064 9.51888 0.844973 9.44488 0.70014 9.30005C0.555306 9.15521 0.48289 8.97796 0.48289 8.7683C0.48289 8.55863 0.555306 8.38138 0.70014 8.23655L3.93664 5.00005L0.69064 1.7788C0.54564 1.63396 0.474806 1.45671 0.47814 1.24705C0.481306 1.03755 0.555306 0.860381 0.70014 0.715547C0.844973 0.570547 1.02222 0.498047 1.23189 0.498047C1.44156 0.498047 1.61881 0.570547 1.76364 0.715547L5.00014 3.96155L8.22139 0.715547C8.36622 0.570547 8.54181 0.498047 8.74814 0.498047C8.95464 0.498047 9.13031 0.570547 9.27514 0.715547C9.43031 0.870547 9.50789 1.0503 9.50789 1.2548C9.50789 1.4593 9.43031 1.63396 9.27514 1.7788L6.03864 5.00005L9.28464 8.2463C9.42964 8.39113 9.50214 8.56672 9.50214 8.77305C9.50214 8.97955 9.42964 9.15521 9.28464 9.30005C9.12964 9.45521 8.94989 9.5328 8.74539 9.5328C8.54089 9.5328 8.36622 9.45521 8.22139 9.30005L5.00014 6.06355Z"
                      fill="#F27A6C"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-row">
          <div *ngFor="let item of invoiceGridConfig">
            <div
              class="footer-col-class-invoice"
              [ngStyle]="{ width: (item.width || 4) + 'rem' }"
              [ngClass]="item.totalVisible ? 'visible' : ''"
              *ngIf="item['isVisible']"
            >
              <div *ngIf="item.totalVisible" class="total-value">
                <span>Total</span>
                <span class="text-bold-class-align" *ngIf="item?.type == 'text-decimal'" tooltip ="{{(getTotalValue(item?.key))   | decimal : decimalValue}}">{{
                  (getTotalValue(item?.key))   | decimal : decimalValue
                }}</span>
                <span class="text-bold-class-align" *ngIf="item?.type != 'text-decimal'" tooltip ="{{(getTotalValue(item?.key))}}">{{
                  getTotalValue(item?.key)
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="loader-container invoice" *ngIf="invoiceLoader">
    <mat-spinner class="green-spinner" diameter="30"></mat-spinner>
  </div>
</ng-template>

<!----------------------------------Billing Plan view----------------------------->
<ng-template #billingPlanTemplate>
  <app-resource-loading-landing-page #resourceLoadingComponent [height]="tableHeight" [deliverable_id]="quoteData?.deliverable_id" [start_date]="quoteData?.start_date" [end_date]="quoteData?.end_date" [projectId]="quoteData?.project_id" [itemId]="quoteData?.item_id" [quote_id]="quoteData?.quote_id" [area]="'billing-advice'" [deliverable_id]="quoteData?.deliverable_id" [PositionData]="this.PositionData" [billing_advice_month]="this.billing_advice_month" [billing_advice_year]="this.billing_advice_year" (triggerAccrual)="accuralData()" (displayForecast)="displayForecast()"></app-resource-loading-landing-page>
</ng-template>

<!-----------------------------------------No Data--------------------------------->
<ng-template #noData>
  <div class="no-data-section">
    <div class="no-data-img-class">
      <svg
        width="248"
        height="185"
        viewBox="0 0 248 185"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M247.798 162.281H0V162.405H247.798V162.281Z" fill="#EBEBEB" />
        <path
          d="M222.969 170.258H215.584V170.382H222.969V170.258Z"
          fill="#EBEBEB"
        />
        <path
          d="M164.151 171.602H159.843V171.726H164.151V171.602Z"
          fill="#EBEBEB"
        />
        <path
          d="M213.491 165.656H196.546V165.78H213.491V165.656Z"
          fill="#EBEBEB"
        />
        <path
          d="M54.3318 168.215H32.9258V168.339H54.3318V168.215Z"
          fill="#EBEBEB"
        />
        <path
          d="M66.4651 168.215H58.7451V168.339H66.4651V168.215Z"
          fill="#EBEBEB"
        />
        <path
          d="M126.255 166.551H79.8301V166.675H126.255V166.551Z"
          fill="#EBEBEB"
        />
        <path
          d="M117.463 140.178H21.7631C21.013 140.177 20.2939 139.879 19.7636 139.349C19.2332 138.818 18.9349 138.099 18.9341 137.349V2.828C18.9351 2.07812 19.2336 1.35927 19.7639 0.829121C20.2942 0.298968 21.0132 0.000793618 21.7631 0H117.463C118.213 0.00105816 118.931 0.299348 119.462 0.829472C119.992 1.3596 120.29 2.07829 120.291 2.828V137.347C120.291 138.097 119.993 138.817 119.463 139.347C118.932 139.878 118.213 140.177 117.463 140.178ZM21.7641 0.124001C21.0486 0.126111 20.3631 0.411253 19.8572 0.917145C19.3513 1.42304 19.0662 2.10857 19.0641 2.824V137.347C19.0662 138.062 19.3513 138.748 19.8572 139.254C20.3631 139.76 21.0486 140.045 21.7641 140.047H117.464C118.18 140.045 118.865 139.76 119.371 139.254C119.877 138.748 120.162 138.062 120.164 137.347V2.828C120.162 2.11256 119.877 1.42703 119.371 0.921143C118.865 0.415251 118.18 0.130113 117.464 0.128002L21.7641 0.124001Z"
          fill="#EBEBEB"
        />
        <path
          d="M224.659 140.178H128.959C128.209 140.177 127.49 139.879 126.96 139.348C126.43 138.818 126.132 138.099 126.131 137.349V2.828C126.132 2.07821 126.43 1.35936 126.96 0.829182C127.49 0.299001 128.209 0.000794128 128.959 0H224.659C225.409 0.000793618 226.128 0.298968 226.658 0.829121C227.188 1.35927 227.487 2.07812 227.488 2.828V137.347C227.488 138.097 227.19 138.817 226.659 139.348C226.129 139.879 225.409 140.177 224.659 140.178ZM128.959 0.124001C128.243 0.126111 127.558 0.411253 127.052 0.917145C126.546 1.42304 126.261 2.10857 126.259 2.824V137.347C126.261 138.062 126.546 138.748 127.052 139.254C127.558 139.76 128.243 140.045 128.959 140.047H224.659C225.375 140.046 226.062 139.762 226.569 139.256C227.076 138.75 227.362 138.063 227.364 137.347V2.828C227.362 2.1117 227.076 1.42542 226.569 0.919384C226.062 0.41335 225.375 0.128793 224.659 0.128002L128.959 0.124001Z"
          fill="#EBEBEB"
        />
        <path
          d="M106.293 162.277H115.522L115.522 98.9053H106.293V162.277Z"
          fill="#E6E6E6"
        />
        <path
          d="M109.542 162.278H106.293V150.238H112.948L109.542 162.278Z"
          fill="#F0F0F0"
        />
        <path
          d="M203.887 162.277H213.116V98.9053H203.887V162.277Z"
          fill="#E6E6E6"
        />
        <path
          d="M106.293 158.992H206.722V98.9072L106.293 98.9072L106.293 158.992Z"
          fill="#F0F0F0"
        />
        <path
          d="M203.477 162.278H206.726V150.238H200.067L203.477 162.278Z"
          fill="#F0F0F0"
        />
        <path
          d="M173.612 103.878V153.582H203.383V103.878H173.612Z"
          fill="#E6E6E6"
        />
        <path
          d="M185.553 105.628H191.443C193.936 105.653 196.348 104.747 198.207 103.086H178.79C180.649 104.746 183.061 105.653 185.553 105.628V105.628Z"
          fill="#F0F0F0"
        />
        <path
          d="M141.622 103.878V153.582H171.393V103.878H141.622Z"
          fill="#E6E6E6"
        />
        <path
          d="M153.262 105.628H159.152C161.644 105.653 164.056 104.746 165.915 103.086H146.498C148.357 104.747 150.769 105.653 153.262 105.628V105.628Z"
          fill="#F0F0F0"
        />
        <path
          d="M109.632 103.878V153.582H139.403V103.878H109.632Z"
          fill="#E6E6E6"
        />
        <path
          d="M121.272 105.628H127.162C129.654 105.653 132.067 104.747 133.926 103.086H114.508C116.367 104.747 118.779 105.653 121.272 105.628V105.628Z"
          fill="#F0F0F0"
        />
        <path
          d="M34.6823 162.277H97.6523L97.6523 20.7253H34.6823L34.6823 162.277Z"
          fill="#F0F0F0"
        />
        <path
          d="M34.6822 162.277H96.3232L96.3232 20.7253H34.6822L34.6822 162.277Z"
          fill="#F5F5F5"
        />
        <path
          d="M92.5669 159.066L92.5669 23.9375L38.4379 23.9375L38.4379 159.066H92.5669Z"
          fill="#F0F0F0"
        />
        <path
          d="M87.2483 49.0695C87.2483 47.1316 86.8666 45.2126 86.125 43.4222C85.3834 41.6318 84.2964 40.005 82.9261 38.6347C81.5558 37.2644 79.929 36.1774 78.1386 35.4358C76.3482 34.6942 74.4292 34.3125 72.4913 34.3125H58.5153C54.6015 34.3125 50.848 35.8673 48.0805 38.6347C45.3131 41.4022 43.7583 45.1557 43.7583 49.0695H87.2483Z"
          fill="#FAFAFA"
        />
        <path
          d="M87.248 150.669V118.387H43.758V150.669H87.248Z"
          fill="#F5F5F5"
        />
        <path
          d="M87.2479 58.1719H68.0649V114.221H87.2479V58.1719Z"
          fill="#F5F5F5"
        />
        <path
          d="M62.9413 58.1719H43.7583V114.221H62.9413V58.1719Z"
          fill="#F5F5F5"
        />
        <path
          d="M50.7152 85.9419H42.8582C42.5351 85.9411 42.2255 85.8124 41.9971 85.584C41.7686 85.3555 41.64 85.046 41.6392 84.7229V84.7229C41.6402 84.3999 41.769 84.0905 41.9974 83.8621C42.2257 83.6337 42.5352 83.505 42.8582 83.5039H50.7152C51.0381 83.505 51.3476 83.6337 51.576 83.8621C51.8043 84.0905 51.9331 84.3999 51.9342 84.7229V84.7229C51.9334 85.046 51.8047 85.3555 51.5762 85.584C51.3478 85.8124 51.0382 85.9411 50.7152 85.9419Z"
          fill="#E0E0E0"
        />
        <path
          d="M39.4131 159.066L39.4131 23.9375H38.4391L38.4391 159.066H39.4131Z"
          fill="#F0F0F0"
        />
        <path
          d="M137.482 83.3711L204.261 83.3711V20.7271L137.482 20.7271V83.3711Z"
          fill="#E6E6E6"
        />
        <path
          d="M135.799 83.3711L202.578 83.3711V20.7271L135.799 20.7271V83.3711Z"
          fill="#F5F5F5"
        />
        <path
          d="M131.9 86.9961L204.261 86.9961V83.3701L131.9 83.3701V86.9961Z"
          fill="#E6E6E6"
        />
        <path
          d="M130.005 86.9961L198.639 86.9961V83.3701L130.005 83.3701V86.9961Z"
          fill="#F5F5F5"
        />
        <path
          d="M199.556 80.1114V23.9844L138.82 23.9844V80.1114H199.556Z"
          fill="white"
        />
        <path
          d="M170.871 80.1114V23.9844H167.504V80.1114H170.871Z"
          fill="#F5F5F5"
        />
        <path
          d="M140.093 80.1114V23.9844H138.82V80.1114H140.093Z"
          fill="#E6E6E6"
        />
        <path
          d="M172.144 80.1114V23.9844H170.871V80.1114H172.144Z"
          fill="#E6E6E6"
        />
        <path
          d="M146.46 85.0322C145.232 72.3072 144.377 51.6582 145.041 44.7522C145.052 44.6335 145.089 44.5186 145.148 44.4152C145.207 44.3117 145.288 44.2221 145.385 44.1522C145.513 44.0582 145.665 44.0022 145.824 43.9906C145.983 43.9789 146.141 44.0121 146.282 44.0863C146.423 44.1605 146.54 44.2728 146.62 44.4104C146.7 44.5479 146.74 44.7051 146.735 44.8642C146.618 48.2862 146.483 56.8392 147.212 71.5942C147.575 78.9392 148.098 83.9692 147.739 85.0302L146.46 85.0322Z"
          fill="#E6E6E6"
        />
        <path
          d="M141.881 55.7004C138.381 51.8174 131.242 49.0564 123.964 49.0704C121.107 49.0764 132.657 53.5874 137.464 58.5704C141.306 62.7083 144.468 67.4284 146.833 72.5554C145.905 64.9504 145.378 59.5814 141.881 55.7004Z"
          fill="#E6E6E6"
        />
        <path
          d="M148.813 56.8837C151.713 52.8397 158.42 49.7298 165.673 49.3778C168.52 49.2398 157.687 54.3148 153.644 59.5168C150.419 63.8646 147.968 68.7359 146.397 73.9168C146.185 66.3038 145.909 60.9287 148.813 56.8837Z"
          fill="#E6E6E6"
        />
        <path
          d="M141.725 41.5203C138.764 38.2323 132.716 35.8943 126.553 35.9063C124.134 35.9063 133.914 39.7323 137.981 43.9473C141.234 47.4511 143.912 51.4479 145.915 55.7893C145.133 49.3553 144.684 44.8083 141.725 41.5203Z"
          fill="#E6E6E6"
        />
        <path
          d="M147.594 46.3818C150.053 42.9568 155.73 40.3238 161.871 40.0258C164.282 39.9088 155.109 44.2058 151.685 48.6108C148.953 52.2943 146.877 56.4215 145.548 60.8108C145.37 54.3588 145.135 49.8048 147.594 46.3818Z"
          fill="#E6E6E6"
        />
        <path
          d="M146.953 98.9082C145.108 98.9082 143.332 98.2025 141.99 96.9356C140.649 95.6687 139.842 93.9366 139.736 92.0943L139.098 81.0312H154.808L154.17 92.0943C154.064 93.9366 153.258 95.6687 151.916 96.9356C150.574 98.2025 148.799 98.9082 146.953 98.9082Z"
          fill="#E6E6E6"
        />
        <path
          d="M155.825 83.5453H138.084L137.576 78.9453H156.337L155.825 83.5453Z"
          fill="#E6E6E6"
        />
        <path
          d="M123.899 184.665C176.969 184.665 219.99 182.153 219.99 179.053C219.99 175.954 176.969 173.441 123.899 173.441C70.8295 173.441 27.8081 175.954 27.8081 179.053C27.8081 182.153 70.8295 184.665 123.899 184.665Z"
          fill="#F5F5F5"
        />
        <path
          d="M125.838 173.689L121.7 174.155L120.174 164.771L124.312 164.305L125.838 173.689Z"
          fill="#FFC3BD"
        />
        <path
          d="M144.364 174.592H140.232L139.203 165.023H143.335L144.364 174.592Z"
          fill="#FFC3BD"
        />
        <path
          d="M139.892 174.109H144.929C145.011 174.109 145.09 174.137 145.154 174.189C145.218 174.24 145.263 174.311 145.281 174.391L146.097 178.064C146.116 178.154 146.114 178.247 146.093 178.335C146.071 178.424 146.03 178.507 145.971 178.578C145.913 178.649 145.84 178.705 145.757 178.744C145.674 178.782 145.583 178.801 145.492 178.799C143.871 178.771 142.692 178.676 140.648 178.676C139.392 178.676 135.604 178.806 133.87 178.806C132.17 178.806 131.909 177.091 132.619 176.936C135.803 176.236 138.202 175.279 139.219 174.363C139.404 174.198 139.644 174.108 139.892 174.109Z"
          fill="#263238"
        />
        <path
          d="M121.632 173.444L126.116 172.936C126.197 172.927 126.28 172.945 126.349 172.989C126.418 173.032 126.471 173.098 126.498 173.176L127.723 176.733C127.754 176.819 127.764 176.911 127.754 177.002C127.744 177.093 127.714 177.18 127.665 177.257C127.616 177.335 127.55 177.4 127.472 177.447C127.394 177.495 127.306 177.524 127.215 177.533C125.595 177.689 123.247 177.859 121.215 178.09C118.838 178.36 118.442 178.996 115.638 178.931C113.938 178.891 113.512 177.166 114.225 177.031C117.473 176.4 118.101 175.909 120.682 173.883C120.951 173.651 121.28 173.499 121.632 173.444V173.444Z"
          fill="#263238"
        />
        <path
          d="M125.282 53.1746C123.894 54.3206 122.528 55.3166 121.115 56.3366C119.702 57.3566 118.259 58.3036 116.774 59.2226C115.289 60.1416 113.765 61.0226 112.167 61.8226C110.533 62.6689 108.832 63.3787 107.082 63.9446C106.619 64.0926 106.168 64.2146 105.648 64.3446C105.139 64.4548 104.625 64.5373 104.108 64.5916C103.177 64.6916 102.301 64.7366 101.428 64.7766C99.6876 64.8566 97.9806 64.8546 96.2756 64.8426C92.8656 64.8016 89.4846 64.6376 86.0886 64.3376L86.0376 61.2376C89.3726 60.8086 92.7226 60.4376 96.0376 60.0646C97.6946 59.8846 99.3476 59.6826 100.963 59.4716C101.763 59.3626 102.563 59.2346 103.301 59.0976C103.618 59.0416 103.932 58.9678 104.241 58.8766C104.532 58.7766 104.889 58.6626 105.219 58.5296C106.636 57.9542 108.011 57.2813 109.336 56.5156C110.707 55.7286 112.079 54.8836 113.427 53.9856C114.775 53.0876 116.117 52.1576 117.447 51.2086C118.777 50.2596 120.106 49.2546 121.354 48.3086L125.282 53.1746Z"
          fill="#FFC3BD"
        />
        <path
          d="M129.321 49.6424C128.727 53.5484 120.503 59.5234 120.503 59.5234L114.603 52.2004C117.188 49.4421 119.928 46.8342 122.811 44.3894C125.466 42.2194 129.967 45.3924 129.321 49.6424Z"
          fill="#526179"
        />
        <path
          d="M87.1758 61.5645L84.8298 59.8945L84.5728 65.1705C84.9274 65.1715 85.2768 65.0856 85.5906 64.9203C85.9044 64.7551 86.1729 64.5155 86.3728 64.2225L87.1758 61.5645Z"
          fill="#FFC3BD"
        />
        <path
          d="M82.711 58.3594L82.231 63.5304L84.575 65.1714L84.832 59.8954L82.711 58.3594Z"
          fill="#FFC3BD"
        />
        <path
          opacity="0.2"
          d="M120.176 164.775L120.962 169.612L125.101 169.146L124.315 164.309L120.176 164.775Z"
          fill="black"
        />
        <path
          opacity="0.2"
          d="M143.337 165.027H139.203L139.735 169.959H143.869L143.337 165.027Z"
          fill="black"
        />
        <path
          d="M143.396 44.5152C143.703 44.5842 143.994 44.7141 144.251 44.8972C144.508 45.0803 144.726 45.3129 144.891 45.5813C145.057 45.8498 145.167 46.1486 145.215 46.4603C145.264 46.772 145.249 47.0902 145.173 47.3962C144.276 51.1177 143.6 54.8889 143.149 58.6902C142.758 61.8902 142.524 64.9472 142.378 67.7182C142.035 74.2132 142.178 79.1642 141.984 80.8422C138.753 80.6182 126.577 79.7712 119.938 79.3072C117.697 61.7632 120.323 50.5672 121.798 46.0182C122.024 45.3094 122.446 44.6793 123.016 44.2013C123.586 43.7233 124.28 43.417 125.018 43.3182C125.864 43.2072 126.876 43.1002 127.944 43.0372C128.318 43.0132 128.697 43.0002 129.083 42.9922C132.217 43.0293 135.347 43.247 138.456 43.6442C139.046 43.7142 139.642 43.8012 140.222 43.8942C141.381 44.0882 142.483 44.3112 143.396 44.5152Z"
          fill="#6E7B8F"
        />
        <path
          d="M138.265 33.0312C137.465 36.0052 136.487 41.4892 138.457 43.6492C133.347 47.0862 130.085 51.9253 129.144 50.8743C128.651 50.3243 128.437 43.9593 129.085 42.9953C132.504 42.4273 132.591 39.9262 132.169 37.5422L138.265 33.0312Z"
          fill="#FFC3BD"
        />
        <path
          d="M138.061 42.418C138.061 42.418 129.734 46.103 128.63 54.343C130.924 51.687 135.991 48.797 135.991 48.797L133.756 48.155C135.177 47.6155 136.649 47.2196 138.149 46.973C137.901 45.835 139.223 43.565 139.223 43.565L138.061 42.418Z"
          fill="#526179"
        />
        <path
          d="M130.548 42.2852C130.548 42.2852 131.658 46.9912 128.631 54.3432C128.034 51.7771 127.215 49.2677 126.185 46.8432L128.224 47.3982C128.224 47.3982 128.124 46.4492 126.473 45.1872C127.161 44.1879 128.019 43.317 129.008 42.6142L130.548 42.2852Z"
          fill="#526179"
        />
        <path
          opacity="0.2"
          d="M135.78 34.875L132.172 37.543C132.279 38.0982 132.337 38.6617 132.345 39.227C133.645 39.127 135.505 37.844 135.736 36.509C135.849 35.9715 135.864 35.4178 135.78 34.875V34.875Z"
          fill="black"
        />
        <path
          d="M127.684 24.4191C125.669 24.5061 124.557 27.9801 126.602 30.8411C128.647 33.7021 130.399 24.3021 127.684 24.4191Z"
          fill="#263238"
        />
        <path
          d="M137.621 27.4779C137.86 31.5719 138.208 33.9549 136.406 36.2879C133.696 39.7969 128.456 38.5389 126.999 34.5989C125.688 31.0529 125.517 24.9539 129.299 22.8589C130.131 22.3925 131.068 22.1466 132.022 22.1445C132.976 22.1425 133.914 22.3844 134.748 22.8473C135.582 23.3101 136.284 23.9786 136.786 24.7891C137.289 25.5995 137.576 26.5251 137.621 27.4779V27.4779Z"
          fill="#FFC3BD"
        />
        <path
          d="M138.222 30.6231C137.127 29.9813 136.171 29.1282 135.409 28.1134C134.647 27.0986 134.095 25.9424 133.785 24.7121C132.585 24.9961 126.604 27.7481 124.765 24.7121C122.926 21.6761 125.24 19.8951 128.681 21.7641C127.159 19.0781 128.543 17.7641 133.325 17.6481C138.107 17.5321 137.464 20.3041 137.464 20.3041C137.464 20.3041 141.084 18.7281 142.364 21.5041C143.847 24.6941 141.111 30.1411 138.222 30.6231Z"
          fill="#263238"
        />
        <path
          d="M136.646 19.5693C136.69 19.5693 138.457 19.9943 140.493 17.9453C140.128 19.6933 136.966 20.8573 136.966 20.8573L136.646 19.5693Z"
          fill="#263238"
        />
        <path
          d="M139.022 25.5609C137.115 24.9019 134.806 27.7269 135.659 31.1379C136.512 34.5489 141.591 26.4489 139.022 25.5609Z"
          fill="#263238"
        />
        <path
          d="M140.101 30.8834C139.926 31.9001 139.356 32.8062 138.515 33.4034C137.394 34.1854 136.384 33.2914 136.315 31.9974C136.257 30.8334 136.769 29.0234 138.076 28.7564C138.363 28.7028 138.658 28.7217 138.936 28.8114C139.214 28.9012 139.465 29.0589 139.666 29.2702C139.867 29.4815 140.012 29.7398 140.088 30.0215C140.164 30.3033 140.169 30.5995 140.101 30.8834V30.8834Z"
          fill="#FFC3BD"
        />
        <path
          d="M126.152 125.863C127.145 113.777 134.429 80.3196 134.429 80.3196L119.935 79.3086C119.935 79.3086 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z"
          fill="#526179"
        />
        <path
          opacity="0.5"
          d="M126.152 125.863C127.145 113.777 134.429 80.3196 134.429 80.3196L119.935 79.3086C119.935 79.3086 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z"
          fill="white"
        />
        <path
          opacity="0.2"
          d="M128.969 91.6484C126.047 95.3844 125.995 108.505 127.125 118.199C128.202 111.206 129.899 102.325 131.386 94.9224L128.969 91.6484Z"
          fill="black"
        />
        <path
          d="M127.756 79.8516C127.756 79.8516 129.32 115.227 131.065 127.207C132.975 140.326 137.826 168.999 137.826 168.999H145.394C145.394 168.999 144.149 141.993 143.15 129.104C142.013 114.423 141.982 80.8436 141.982 80.8436L127.756 79.8516Z"
          fill="#526179"
        />
        <path
          opacity="0.5"
          d="M127.756 79.8516C127.756 79.8516 129.32 115.227 131.065 127.207C132.975 140.326 137.826 168.999 137.826 168.999H145.394C145.394 168.999 144.149 141.993 143.15 129.104C142.013 114.423 141.982 80.8436 141.982 80.8436L127.756 79.8516Z"
          fill="white"
        />
        <path
          d="M136.916 169.159H145.827V166.497L135.866 166.324L136.916 169.159Z"
          fill="#526179"
        />
        <path
          d="M118.44 168.136L126.724 167.197L126.424 164.52L117.147 165.429L118.44 168.136Z"
          fill="#526179"
        />
        <path
          d="M131.593 29.3547C131.633 29.6857 131.493 29.9747 131.273 30.0007C131.053 30.0267 130.849 29.7797 130.81 29.4487C130.771 29.1177 130.91 28.8287 131.13 28.8027C131.35 28.7767 131.554 29.0237 131.593 29.3547Z"
          fill="#263238"
        />
        <path
          d="M127.854 29.8039C127.894 30.1349 127.754 30.4238 127.534 30.4498C127.314 30.4758 127.11 30.2289 127.07 29.8979C127.03 29.5669 127.17 29.2779 127.39 29.2519C127.61 29.2259 127.814 29.4769 127.854 29.8039Z"
          fill="#263238"
        />
        <path
          d="M129.062 29.8242C128.789 30.8829 128.372 31.8991 127.822 32.8442C128.067 32.9786 128.339 33.0579 128.618 33.0763C128.897 33.0948 129.177 33.0521 129.438 32.9512L129.062 29.8242Z"
          fill="#ED847E"
        />
        <path
          d="M132.61 28.0068C132.575 28.0173 132.538 28.0181 132.503 28.0091C132.468 28.0001 132.436 27.9817 132.41 27.9558C132.245 27.7739 132.038 27.6338 131.809 27.5469C131.579 27.46 131.331 27.4288 131.087 27.4558C131.036 27.4644 130.984 27.4529 130.941 27.4239C130.898 27.3948 130.869 27.3503 130.858 27.2998C130.853 27.2745 130.853 27.2484 130.858 27.223C130.863 27.1977 130.873 27.1736 130.887 27.1523C130.902 27.1309 130.921 27.1127 130.942 27.0987C130.964 27.0847 130.988 27.0752 131.014 27.0708C131.322 27.0308 131.636 27.0656 131.928 27.1721C132.22 27.2786 132.483 27.4537 132.693 27.6828C132.729 27.72 132.749 27.7697 132.749 27.8213C132.749 27.8729 132.729 27.9225 132.693 27.9598C132.67 27.9821 132.641 27.9983 132.61 28.0068V28.0068Z"
          fill="#263238"
        />
        <path
          d="M131.173 34.2719C131.645 34.1727 132.086 33.9613 132.459 33.6557C132.832 33.35 133.126 32.9591 133.316 32.5159C133.325 32.4894 133.323 32.4603 133.311 32.435C133.299 32.4097 133.277 32.3902 133.251 32.3809C133.224 32.3717 133.195 32.3733 133.17 32.3855C133.144 32.3977 133.125 32.4194 133.116 32.4459C132.934 32.8536 132.658 33.2128 132.312 33.4945C131.966 33.7762 131.558 33.9727 131.122 34.0679C131.108 34.071 131.096 34.0767 131.084 34.0847C131.073 34.0927 131.063 34.1029 131.056 34.1146C131.049 34.1263 131.044 34.1393 131.042 34.1529C131.039 34.1665 131.04 34.1805 131.043 34.1939C131.046 34.2074 131.052 34.2201 131.06 34.2314C131.068 34.2426 131.078 34.2522 131.089 34.2595C131.101 34.2669 131.114 34.2718 131.128 34.2741C131.141 34.2764 131.155 34.276 131.169 34.2729L131.173 34.2719Z"
          fill="#263238"
        />
        <path
          d="M126.095 28.7073C126.056 28.7205 126.013 28.7205 125.974 28.7073C125.926 28.6895 125.886 28.6536 125.863 28.6072C125.841 28.5608 125.837 28.5074 125.853 28.4583C125.947 28.1617 126.111 27.8921 126.331 27.6724C126.551 27.4527 126.821 27.2894 127.118 27.1963C127.168 27.185 127.221 27.1934 127.265 27.2198C127.309 27.2463 127.341 27.2887 127.355 27.3383C127.366 27.3884 127.358 27.4409 127.332 27.485C127.306 27.5291 127.263 27.5615 127.214 27.5753V27.5753C126.981 27.6532 126.769 27.7853 126.597 27.9609C126.424 28.1365 126.296 28.3505 126.223 28.5853C126.212 28.6141 126.195 28.6401 126.173 28.6613C126.151 28.6824 126.124 28.6982 126.095 28.7073Z"
          fill="#263238"
        />
        <path
          d="M138.531 174.833C138.248 174.855 137.966 174.781 137.731 174.622C137.657 174.558 137.601 174.478 137.566 174.388C137.531 174.298 137.519 174.201 137.531 174.105C137.531 174.047 137.546 173.991 137.576 173.943C137.605 173.894 137.648 173.854 137.699 173.828C138.158 173.592 139.489 174.412 139.639 174.506C139.655 174.516 139.668 174.531 139.676 174.549C139.684 174.567 139.687 174.586 139.684 174.606C139.68 174.625 139.672 174.642 139.659 174.657C139.645 174.671 139.628 174.681 139.61 174.686C139.257 174.775 138.895 174.824 138.531 174.833V174.833ZM137.917 173.978C137.873 173.976 137.83 173.984 137.79 174.002C137.769 174.013 137.753 174.03 137.742 174.05C137.731 174.07 137.726 174.094 137.728 174.117C137.719 174.182 137.726 174.249 137.749 174.311C137.773 174.373 137.811 174.428 137.861 174.472C138.322 174.682 138.847 174.709 139.328 174.546C138.897 174.266 138.416 174.073 137.912 173.978H137.917Z"
          fill="#526179"
        />
        <path
          d="M139.584 174.689C139.567 174.689 139.551 174.685 139.537 174.677C139.11 174.445 138.283 173.539 138.37 173.077C138.384 173.005 138.423 172.94 138.479 172.893C138.536 172.847 138.607 172.821 138.681 172.82C138.755 172.811 138.831 172.818 138.902 172.84C138.974 172.861 139.041 172.898 139.098 172.946C139.578 173.339 139.677 174.531 139.68 174.581C139.681 174.599 139.677 174.616 139.67 174.632C139.662 174.648 139.65 174.662 139.636 174.671C139.62 174.682 139.602 174.688 139.584 174.689ZM138.75 173.014H138.705C138.578 173.03 138.567 173.088 138.562 173.114C138.51 173.386 139.037 174.07 139.462 174.393C139.434 173.921 139.263 173.468 138.974 173.093C138.911 173.041 138.831 173.013 138.75 173.014V173.014Z"
          fill="#526179"
        />
        <path
          d="M121.296 174.055L121.28 174.062C120.633 174.269 119.475 174.562 118.98 174.216C118.907 174.165 118.847 174.098 118.806 174.019C118.765 173.94 118.744 173.852 118.744 173.764C118.74 173.71 118.75 173.656 118.773 173.608C118.796 173.559 118.831 173.518 118.875 173.487C119.34 173.16 121.087 173.803 121.285 173.878C121.302 173.885 121.317 173.897 121.327 173.912C121.338 173.927 121.344 173.945 121.345 173.963C121.346 173.982 121.342 174 121.334 174.016C121.325 174.033 121.312 174.046 121.296 174.056V174.055ZM119.013 173.631L118.99 173.645C118.972 173.657 118.958 173.673 118.949 173.693C118.94 173.713 118.937 173.734 118.94 173.756C118.939 173.814 118.952 173.872 118.978 173.925C119.005 173.977 119.044 174.022 119.092 174.056C119.7 174.245 120.356 174.21 120.94 173.956C120.335 173.687 119.672 173.575 119.012 173.631H119.013Z"
          fill="#526179"
        />
        <path
          d="M121.296 174.056C121.272 174.068 121.245 174.07 121.22 174.062C120.7 173.896 119.62 173.119 119.64 172.647C119.645 172.536 119.708 172.391 119.972 172.334C120.067 172.312 120.166 172.311 120.262 172.329C120.358 172.347 120.449 172.385 120.53 172.44C120.954 172.845 121.239 173.373 121.346 173.95C121.349 173.966 121.348 173.984 121.343 174C121.338 174.016 121.328 174.031 121.316 174.043C121.31 174.048 121.303 174.052 121.296 174.056V174.056ZM119.905 172.566C119.838 172.602 119.836 172.644 119.835 172.66C119.821 172.944 120.567 173.566 121.108 173.812C121.008 173.351 120.769 172.932 120.423 172.612C120.364 172.571 120.297 172.543 120.226 172.529C120.156 172.515 120.083 172.517 120.013 172.533C119.976 172.539 119.939 172.55 119.905 172.566Z"
          fill="#526179"
        />
        <path
          d="M146.882 50.4757C146.777 52.0897 146.587 53.5927 146.39 55.1447C146.193 56.6967 145.928 58.2177 145.637 59.7527C145.061 62.8879 144.211 65.9664 143.096 68.9527L142.605 70.1097L142.481 70.3987L142.45 70.4707L142.399 70.5807L142.255 70.8657C142.064 71.212 141.836 71.536 141.574 71.8317C141.143 72.3168 140.639 72.7318 140.08 73.0617C139.627 73.3311 139.152 73.5616 138.661 73.7507C137.023 74.3415 135.31 74.699 133.573 74.8127C130.418 75.0483 127.247 74.9299 124.119 74.4597L124.232 71.3597L126.418 71.1127C127.151 71.0257 127.88 70.9127 128.605 70.8127C130.052 70.5997 131.484 70.3617 132.849 70.0447C134.111 69.7876 135.339 69.3807 136.505 68.8327C136.819 68.7005 137.094 68.4902 137.305 68.2217C137.318 68.1507 137.16 68.4617 137.346 67.9647L137.673 67.0087C138.474 64.268 139.086 61.4758 139.507 58.6517C139.742 57.2137 139.943 55.7627 140.137 54.3077C140.331 52.8527 140.491 51.3677 140.644 49.9727L146.882 50.4757Z"
          fill="#FFC3BD"
        />
        <path
          d="M147.242 47.4803C149.265 50.8733 147.542 61.5373 147.542 61.5373L136.942 59.5783C136.942 59.5783 136.368 53.4783 138.386 48.7843C140.577 43.6753 144.791 43.3693 147.242 47.4803Z"
          fill="#526179"
        />
        <path
          d="M121.264 80.9523L115.639 54.7883C115.369 53.671 114.743 52.6718 113.856 51.9413C112.968 51.2108 111.867 50.7885 110.719 50.7383H89.166C90.3145 50.7883 91.4156 51.2104 92.3033 51.941C93.1909 52.6715 93.817 53.6708 94.087 54.7883L99.712 80.9523C99.982 82.0696 100.608 83.0688 101.495 83.7993C102.383 84.5298 103.484 84.952 104.632 85.0023H126.184C125.036 84.952 123.935 84.5298 123.047 83.7993C122.16 83.0688 121.534 82.0696 121.264 80.9523V80.9523Z"
          fill="#263238"
        />
        <path
          opacity="0.7"
          d="M121.264 80.9523L115.639 54.7883C115.369 53.671 114.743 52.6718 113.856 51.9413C112.968 51.2108 111.867 50.7885 110.719 50.7383H89.166C90.3145 50.7883 91.4156 51.2104 92.3033 51.941C93.1909 52.6715 93.817 53.6708 94.087 54.7883L99.712 80.9523C99.982 82.0696 100.608 83.0688 101.495 83.7993C102.383 84.5298 103.484 84.952 104.632 85.0023H126.184C125.036 84.952 123.935 84.5298 123.047 83.7993C122.16 83.0688 121.534 82.0696 121.264 80.9523V80.9523Z"
          fill="#526179"
        />
        <path
          d="M89.1658 50.7383H110.718C109.614 50.7589 108.556 51.1819 107.743 51.9277C106.929 52.6735 106.416 53.6908 106.3 54.7883L100.709 115.588C100.592 116.686 100.079 117.703 99.2655 118.449C98.452 119.194 97.3942 119.617 96.2908 119.638H82.8388C82.3206 119.648 81.8064 119.547 81.3305 119.341C80.8546 119.136 80.4281 118.832 80.0795 118.448C79.7308 118.065 79.4682 117.611 79.309 117.118C79.1499 116.625 79.098 116.103 79.1568 115.588L84.7488 54.7883C84.8651 53.6909 85.3781 52.6738 86.1914 51.9281C87.0048 51.1823 88.0625 50.7592 89.1658 50.7383Z"
          fill="#526179"
        />
        <path
          d="M92.6088 115.586C92.55 116.101 92.6019 116.622 92.7611 117.116C92.9202 117.609 93.1829 118.062 93.5315 118.446C93.8801 118.829 94.3066 119.134 94.7825 119.339C95.2584 119.544 95.7726 119.646 96.2908 119.636H74.7388C74.2206 119.646 73.7063 119.545 73.2303 119.339C72.7544 119.134 72.3278 118.83 71.9791 118.446C71.6304 118.063 71.3678 117.609 71.2087 117.116C71.0497 116.622 70.9979 116.101 71.0568 115.586H92.6088Z"
          fill="#263238"
        />
        <path
          opacity="0.7"
          d="M92.6088 115.586C92.55 116.101 92.6019 116.622 92.7611 117.116C92.9202 117.609 93.1829 118.062 93.5315 118.446C93.8801 118.829 94.3066 119.134 94.7825 119.339C95.2584 119.544 95.7726 119.646 96.2908 119.636H74.7388C74.2206 119.646 73.7063 119.545 73.2303 119.339C72.7544 119.134 72.3278 118.83 71.9791 118.446C71.6304 118.063 71.3678 117.609 71.2087 117.116C71.0497 116.622 70.9979 116.101 71.0568 115.586H92.6088Z"
          fill="#526179"
        />
        <g opacity="0.5">
          <path
            opacity="0.5"
            d="M102.565 57.5492H87.9807C87.8681 57.5508 87.7564 57.5284 87.6531 57.4834C87.5499 57.4385 87.4573 57.3722 87.3817 57.2887C87.306 57.2053 87.2489 57.1068 87.2143 56.9996C87.1796 56.8925 87.1682 56.7792 87.1807 56.6672V56.6672C87.2066 56.4285 87.3185 56.2073 87.4955 56.045C87.6725 55.8827 87.9026 55.7904 88.1427 55.7852H102.725C102.837 55.7837 102.949 55.8061 103.052 55.851C103.155 55.8959 103.248 55.9623 103.324 56.0457C103.399 56.1291 103.456 56.2277 103.491 56.3349C103.526 56.442 103.537 56.5553 103.525 56.6672V56.6672C103.499 56.9057 103.387 57.1266 103.211 57.2888C103.034 57.4511 102.804 57.5436 102.565 57.5492V57.5492Z"
            fill="white"
          />
          <path
            opacity="0.5"
            d="M102.131 62.3188H87.5466C87.434 62.3203 87.3224 62.2979 87.2191 62.253C87.1158 62.208 87.0233 62.1417 86.9476 62.0583C86.8719 61.9749 86.8149 61.8763 86.7802 61.7691C86.7456 61.662 86.7341 61.5487 86.7466 61.4368C86.7725 61.198 86.8844 60.9768 87.0615 60.8145C87.2385 60.6522 87.4685 60.5599 87.7086 60.5548H102.293C102.405 60.5532 102.517 60.5756 102.62 60.6205C102.723 60.6655 102.816 60.7318 102.892 60.8152C102.967 60.8987 103.024 60.9972 103.059 61.1044C103.094 61.2115 103.105 61.3248 103.093 61.4368V61.4368C103.067 61.6756 102.955 61.8969 102.778 62.0592C102.601 62.2216 102.371 62.3138 102.131 62.3188V62.3188Z"
            fill="white"
          />
          <path
            opacity="0.5"
            d="M98.1215 67.0922H87.1125C86.9999 67.0937 86.8883 67.0713 86.785 67.0264C86.6817 66.9815 86.5892 66.9151 86.5135 66.8317C86.4378 66.7483 86.3808 66.6497 86.3461 66.5426C86.3115 66.4354 86.3 66.3221 86.3125 66.2102V66.2102C86.3384 65.9714 86.4504 65.7503 86.6274 65.588C86.8044 65.4257 87.0344 65.3334 87.2745 65.3282H98.2835C98.3961 65.3267 98.5078 65.3491 98.6111 65.394C98.7143 65.4389 98.8069 65.5053 98.8825 65.5887C98.9582 65.6721 99.0152 65.7707 99.0499 65.8778C99.0846 65.985 99.096 66.0983 99.0835 66.2102V66.2102C99.0576 66.4489 98.9457 66.6701 98.7687 66.8324C98.5916 66.9947 98.3616 67.087 98.1215 67.0922V67.0922Z"
            fill="white"
          />
          <path
            opacity="0.5"
            d="M90.4129 71.8617H86.6799C86.5673 71.8633 86.4557 71.8409 86.3524 71.7959C86.2491 71.751 86.1566 71.6847 86.0809 71.6012C86.0052 71.5178 85.9482 71.4193 85.9135 71.3121C85.8789 71.205 85.8674 71.0917 85.8799 70.9797V70.9797C85.9056 70.7411 86.0173 70.5199 86.1942 70.3576C86.371 70.1953 86.6009 70.1029 86.8409 70.0977H90.5749C90.6875 70.0962 90.7992 70.1186 90.9024 70.1635C91.0057 70.2084 91.0983 70.2748 91.1739 70.3582C91.2496 70.4416 91.3066 70.5402 91.3413 70.6474C91.3759 70.7545 91.3874 70.8678 91.3749 70.9797V70.9797C91.349 71.2185 91.2371 71.4396 91.06 71.6019C90.883 71.7642 90.653 71.8566 90.4129 71.8617V71.8617Z"
            fill="white"
          />
          <path
            opacity="0.5"
            d="M100.829 76.6313H86.2409C86.1283 76.6328 86.0167 76.6104 85.9134 76.5655C85.8101 76.5206 85.7176 76.4542 85.6419 76.3708C85.5663 76.2874 85.5092 76.1888 85.4746 76.0816C85.4399 75.9745 85.4284 75.8612 85.4409 75.7493V75.7493C85.4669 75.5105 85.5788 75.2893 85.7558 75.127C85.9328 74.9647 86.1628 74.8724 86.4029 74.8673H100.987C101.1 74.8657 101.211 74.8881 101.314 74.9331C101.418 74.978 101.51 75.0443 101.586 75.1277C101.662 75.2112 101.719 75.3097 101.753 75.4169C101.788 75.524 101.799 75.6373 101.787 75.7493V75.7493C101.761 75.9874 101.65 76.208 101.474 76.3702C101.297 76.5324 101.068 76.6251 100.829 76.6313V76.6313Z"
            fill="white"
          />
          <path
            opacity="0.5"
            d="M94.6517 81.4047H85.8117C85.6991 81.4063 85.5875 81.3838 85.4842 81.3389C85.3809 81.294 85.2884 81.2276 85.2127 81.1442C85.137 81.0608 85.08 80.9622 85.0454 80.8551C85.0107 80.7479 84.9992 80.6346 85.0117 80.5227V80.5227C85.0374 80.284 85.1491 80.0629 85.326 79.9006C85.5028 79.7383 85.7327 79.6459 85.9727 79.6407H94.8127C94.9253 79.6392 95.037 79.6616 95.1403 79.7065C95.2435 79.7514 95.3361 79.8178 95.4117 79.9012C95.4874 79.9846 95.5445 80.0832 95.5791 80.1903C95.6138 80.2975 95.6252 80.4108 95.6127 80.5227V80.5227C95.5868 80.7613 95.4751 80.9823 95.2983 81.1446C95.1214 81.3069 94.8917 81.3993 94.6517 81.4047V81.4047Z"
            fill="white"
          />
          <path
            opacity="0.5"
            d="M99.9617 86.1742H85.3777C85.265 86.1758 85.1534 86.1534 85.0501 86.1084C84.9468 86.0635 84.8543 85.9972 84.7786 85.9137C84.703 85.8303 84.6459 85.7318 84.6113 85.6246C84.5766 85.5175 84.5652 85.4042 84.5777 85.2922V85.2922C84.6036 85.0535 84.7155 84.8323 84.8925 84.67C85.0695 84.5077 85.2996 84.4154 85.5397 84.4102H100.125C100.237 84.4087 100.349 84.4311 100.452 84.476C100.555 84.5209 100.648 84.5873 100.724 84.6707C100.799 84.7541 100.856 84.8527 100.891 84.9599C100.926 85.067 100.937 85.1803 100.925 85.2922V85.2922C100.899 85.5312 100.787 85.7524 100.609 85.9148C100.432 86.0771 100.202 86.1693 99.9617 86.1742V86.1742Z"
            fill="white"
          />
          <path
            opacity="0.5"
            d="M95.9526 90.9438H84.9436C84.831 90.9453 84.7193 90.9229 84.616 90.878C84.5128 90.8331 84.4202 90.7667 84.3446 90.6833C84.2689 90.5999 84.2118 90.5013 84.1772 90.3941C84.1425 90.287 84.1311 90.1737 84.1436 90.0618V90.0618C84.1695 89.823 84.2814 89.6019 84.4584 89.4396C84.6355 89.2773 84.8655 89.1849 85.1056 89.1798H96.1146C96.2272 89.1782 96.3388 89.2006 96.4421 89.2456C96.5454 89.2905 96.6379 89.3568 96.7136 89.4403C96.7893 89.5237 96.8463 89.6222 96.881 89.7294C96.9156 89.8365 96.9271 89.9498 96.9146 90.0618V90.0618C96.8886 90.3005 96.7767 90.5217 96.5997 90.684C96.4227 90.8463 96.1927 90.9386 95.9526 90.9438V90.9438Z"
            fill="white"
          />
          <path
            opacity="0.5"
            d="M88.2435 95.7133H84.5075C84.3949 95.7148 84.2833 95.6924 84.18 95.6475C84.0767 95.6026 83.9842 95.5362 83.9085 95.4528C83.8329 95.3694 83.7758 95.2708 83.7412 95.1637C83.7065 95.0565 83.695 94.9432 83.7075 94.8313V94.8313C83.7335 94.5925 83.8454 94.3714 84.0224 94.2091C84.1994 94.0468 84.4294 93.9544 84.6695 93.9493H88.4075C88.5202 93.9477 88.6318 93.9702 88.7351 94.0151C88.8384 94.06 88.9309 94.1264 89.0066 94.2098C89.0822 94.2932 89.1393 94.3918 89.1739 94.4989C89.2086 94.6061 89.22 94.7194 89.2075 94.8313C89.1816 95.0704 89.0694 95.2918 88.8919 95.4541C88.7145 95.6165 88.484 95.7086 88.2435 95.7133V95.7133Z"
            fill="white"
          />
          <path
            opacity="0.5"
            d="M98.6599 100.487H84.0759C83.9633 100.488 83.8516 100.466 83.7484 100.421C83.6451 100.376 83.5525 100.31 83.4769 100.226C83.4012 100.143 83.3442 100.044 83.3095 99.9371C83.2749 99.83 83.2634 99.7167 83.2759 99.6047V99.6047C83.3018 99.366 83.4137 99.1448 83.5908 98.9825C83.7678 98.8202 83.9978 98.7279 84.2379 98.7227H98.8219C98.9345 98.7212 99.0462 98.7436 99.1494 98.7885C99.2527 98.8334 99.3453 98.8998 99.4209 98.9832C99.4966 99.0666 99.5536 99.1652 99.5883 99.2724C99.6229 99.3795 99.6344 99.4928 99.6219 99.6047V99.6047C99.596 99.8435 99.4841 100.065 99.307 100.227C99.13 100.389 98.9 100.482 98.6599 100.487V100.487Z"
            fill="white"
          />
          <path
            opacity="0.5"
            d="M92.4828 105.256H83.6428C83.5302 105.258 83.4185 105.235 83.3153 105.19C83.212 105.146 83.1194 105.079 83.0438 104.996C82.9681 104.912 82.9111 104.814 82.8764 104.707C82.8418 104.599 82.8303 104.486 82.8428 104.374V104.374C82.8685 104.135 82.9804 103.914 83.1574 103.752C83.3345 103.589 83.5646 103.497 83.8048 103.492H92.6438C92.7564 103.491 92.8681 103.513 92.9713 103.558C93.0746 103.603 93.1672 103.669 93.2428 103.753C93.3185 103.836 93.3755 103.935 93.4102 104.042C93.4448 104.149 93.4563 104.262 93.4438 104.374V104.374C93.4179 104.613 93.3061 104.834 93.1293 104.996C92.9525 105.158 92.7227 105.251 92.4828 105.256V105.256Z"
            fill="white"
          />
          <path
            opacity="0.5"
            d="M97.7917 110.026H83.2077C83.0951 110.027 82.9835 110.005 82.8802 109.96C82.7769 109.915 82.6844 109.849 82.6087 109.765C82.5331 109.682 82.476 109.583 82.4413 109.476C82.4067 109.369 82.3952 109.256 82.4077 109.144V109.144C82.4337 108.905 82.5456 108.684 82.7226 108.522C82.8996 108.359 83.1296 108.267 83.3697 108.262H97.9517C98.0643 108.26 98.176 108.283 98.2793 108.328C98.3825 108.373 98.4751 108.439 98.5508 108.522C98.6264 108.606 98.6835 108.704 98.7181 108.811C98.7528 108.919 98.7642 109.032 98.7517 109.144V109.144C98.7258 109.382 98.6142 109.603 98.4376 109.765C98.261 109.928 98.0315 110.02 97.7917 110.026Z"
            fill="white"
          />
        </g>
        <path
          d="M125.503 71.7002L122.337 68.9492L120.853 74.0132C120.853 74.0132 124.069 75.7572 125.183 74.1562L125.503 71.7002Z"
          fill="#FFC3BD"
        />
        <path
          d="M118.888 68.75L117.858 72.994L120.85 74.014L122.334 68.949L118.888 68.75Z"
          fill="#FFC3BD"
        />
      </svg>
    </div>
    <span class="nodata-text">No Data</span>
  </div>
</ng-template>
