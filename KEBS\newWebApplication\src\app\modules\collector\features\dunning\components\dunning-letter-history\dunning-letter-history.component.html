<div class="container-fluid dunning-history-component">
    <div class="row">
     <div class="col-12">

      <div *ngIf="dunningHistoryData == null">

        <div class="row pt-2 justify-content-end">
            <span><button mat-icon-button (click)="closeDialog()" matTooltip="Close">
              <mat-icon class="close-icon">close</mat-icon></button></span>
      </div>

        <div>
          <h4 class=" d-flex justify-content-center align-items-center mt-8">
            No Dunning History found !
          </h4>
        </div>
        <div class="d-flex justify-content-center align-items-center">
          <img src="https://assets.kebs.app/images/nomilestone.png" class="mt-4" height="350" width="400" />
        </div>

  </div>     
  
      <div *ngIf="dunningHistoryData != null">  

        <div class="row pt-2">
            <div class="col-11 heading-text  d-flex">
             <span class="my-auto mx-auto">Dunning History of {{customerName}}</span> 
            </div>
            <div class="col-1 d-flex">
                 <button mat-icon-button (click)="closeDialog()" matTooltip="Close" class="ml-auto">
                 <mat-icon class="close-icon">close</mat-icon></button>
              </div>
           </div>

    <mat-divider class="pb-2"></mat-divider>
    
     <!-- <div class="row pt-1" *ngFor="let item of dunningHistoryData">

      <div class="col-12"> -->
             
         <!-- <mat-accordion>
            <mat-expansion-panel>

                  <mat-expansion-panel-header>
             
                 <div class="col-1 pl-0">
                 <mat-icon class="green-tick">check</mat-icon>    
                 </div>
                 <div class="col-1 pt-1 pb-0 tileName">Sent On</div>
                 <div class="col-2 pt-1 pb-0 sent-on">{{item ? getDateFormat(item.sent_on) : '-'}}</div>
                 <div class="col-1 pt-1 pb-0 tileName">Sent By</div>
                 <div class="col-3 pt-1 pb-0 sent-by">{{item ? item.sent_by : '-'}}</div>

                  </mat-expansion-panel-header>                 
            
    Accordion Content Begins Here-->
            
       <!-- <div class="row pt-1 pb-1" style="min-height: 36px;">
           <div class="col-1 tileName">From:</div>
           <div class="col-11 subject-text">{{DunningFromMailID}}</div></div> 
           <mat-divider></mat-divider>

       <div class="row pt-1 pb-1" style="min-height: 36px;">
            <div class="col-1 tileName">To:</div>
            <div class="col-11 subject-text"> 
              <div *ngFor="let toMail of item.to_mail_id">
            {{toMail ? toMail.toMailId : '-'}}</div></div></div> 
            <mat-divider></mat-divider>
       
       <div class="row pt-1 pb-1" style="min-height: 36px;">
            <div class="col-1 tileName">Cc:</div>
            <div class="col-11 subject-text"> 
              <div *ngFor="let Cc of item.cc">
            {{Cc ? Cc.cc : '-'}}</div></div></div> 
            <mat-divider></mat-divider>     

       <div class="row pt-1 pb-1" style="min-height: 36px;">
            <div class="col-1 tileName">Subject:</div>
            <div class="col-11 subject-text">{{item ? item.subject : '-'}}</div></div> 
            <mat-divider></mat-divider>

    <div class="row content-row pt-2">
        <div class="col-12">
         <span [innerHTML] = 'item.email_body_text'></span> 
        </div>
    </div> -->

<!-- TABLE CONTENT -->
<!-- <div class="row pt-3">
    <div class="col-12 table-text">
      <div class="row">
       <div class="col-12 pl-5">
        <table border ="1" width="1200px" style="border-collapse:collapse;  text-align: center !important;">
          <tr style="background-color: #FBC531;">
            <th colspan="2">Project</th>
            <th colspan="2">Milestone Name</th>
            <th colspan="2">Invoice Date</th>
            <th colspan="2">Invoice Number</th>
            <th colspan="1">Value in {{item ? item.due_payments[0].currency : '-'}}</th>
            <th colspan="1">Aging</th>
            <th *ngIf="paymentDaysColumn==false" colspan="1">Payment Term Days</th>
            <th *ngIf="paymentDaysColumn==false" colspan="1">Payment Status</th>
            <th *ngIf="paymentDaysColumn==true" colspan="1">{{item ? item.credit_period_days : '-'}} Days Credit Period </th>
          </tr>  

          <tr *ngFor="let i of item.due_payments">
            <th colspan="2">{{i.project_name}}</th>
            <th colspan="2">{{i.milestone_name}}</th>
            <th colspan="2">{{i.invoice_date}}</th>
            <th colspan="2">{{i.invoice_no}}</th>
            <th colspan="1">{{i.Value}}</th>
            <th colspan="1">{{i.Ageing}}</th>
            <th *ngIf="paymentDaysColumn==false" colspan="1">{{i.Payment_term_days}}</th>
            <th colspan="1" [ngClass]="{'status1': i.Credit_period_days == 'Overdue Payment' ? 
            status1: '-', 'status2': i.Credit_period_days == 'Not Due' ? status2:'-'}">
              {{i.Credit_period_days}}</th>
          </tr>
    
          <tr style="background-color: #FBC531;">
            <th colspan="2">Total Outstanding</th>
            <th colspan="2"></th>
            <th colspan="2"></th>
            <th colspan="2"></th>
            <th colspan="1"> {{item ? item.total_outstanding_value : '-'}} {{item ? item.due_payments[0].currency : '-'}}</th>
            <th colspan="1"></th>
            <th *ngIf="paymentDaysColumn==false" colspan="1"></th>
            <th colspan="1"></th>
          </tr>
          
       </table> 
       </div>
       </div>
    </div>
  </div>              

        </mat-expansion-panel> 
         </mat-accordion>  -->
     <!-- </div> 

  </div>   -->
  <div class="timeline">
    <div *ngFor="let item of dunningHistoryData; let last=last">
      <div class="container">
        <div class="displayFlex mb-1 mt-1">
        <div class="image-container">
          <app-user-image
          [id]="item.oid"
          imgHeight="40px"
          imgWidth="40px"
        >
        </app-user-image>
  
          </div>
          <div style="display: flex; flex-direction: column; margin-top: 7px; padding-left: 10px;">
            <span>
            Sent By <strong>{{item.sent_by}}</strong>
            </span>
            <span>
              {{ getDate(item.sent_on) }}
            </span>
          </div>
        </div>
    <div class="line-container" *ngIf="!last">
        <span class="vertical-line"></span></div>
      </div>
    </div>
  </div>
        
</div>  

     </div>
    </div>
</div>    