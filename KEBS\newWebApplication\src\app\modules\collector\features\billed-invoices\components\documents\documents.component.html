<div class="container-fluid document-styles">

    <div *ngIf="isAttachmentLoading">
        <div class="row pt-4 justify-content-center">
        <mat-spinner diameter="30"> </mat-spinner>
        </div>
    </div>

    <div class="card" *ngIf="addItemVisible">
        <div class="card-body p-2">
            <ng-container *ngFor="let item of fileList;let fileIndex = index">
                <div class="row pt-2 pb-2" style="border-bottom: solid 1px #cacaca">
                    <div class="col-1 d-flex my-auto">
                        <mat-icon class="icon">done</mat-icon>
                    </div>
                    <div class="col-3 pt-2 normalFont pl-0" [matTooltip]="item?.fileTitle" style="font-weight: 600;">
                        {{item.fileTitle ? item.fileTitle : '-'}}
                    </div>
                    <div class="col-4 pt-2 normalFont pl-0">
                        Uploaded on {{item?.created_on | ddmmmyy}} by {{item?.created_by}}
                    </div>
                    <div class="col-3 pl-0 normalFont">
                        <button mat-icon-button 
                        (click)="downloadFile(fileIndex)" 
                        class="ic-size"><i [class]="fileType[(item?.type)] || 'ms-Icon ms-Icon--FileTemplate'" aria-hidden="true"></i></button>
                        <span class="pl-1" [matTooltip]="item?.fileName" style="cursor: pointer;" (click)="downloadFile(fileIndex)">{{item?.fileName}}</span>
                    </div>
                    <div class="col-1 pl-0">
                        <button *ngIf="!item?.isLoading && currentUser.oid == item?.created_by_oid" 
                        mat-icon-button class="ml-auto close-button" 
                        matTooltip="Delete File" 
                        (click)="deleteDocumentItem(fileIndex);">
                            <mat-icon class="close-Icon">close</mat-icon>
                        </button>
                        <mat-spinner *ngIf="item?.isLoading" matTooltip="Loading" class="spinner-align" diameter="18"></mat-spinner>
                    </div>
                </div>
            </ng-container>
            <form [formGroup]="attachmentForm">
                <ng-container formArrayName="attachmentValues">
                    <ng-container
                        *ngFor="let item of attachmentFormData.attachmentValues['controls'];let fileIndex = index"
                        [formGroupName]="fileIndex">
                        <div class="row pt-2">
                            <div class="col-4">
                                <mat-form-field class="form-field-class" appearance="outline">
                                    <mat-label>File title</mat-label>
                                    <input matInput formControlName="fileTitle" placeholder="File title">
                                </mat-form-field>
                            </div>
                            <div class="col-4"></div>
                            <div class="col-2">
                                <div *ngIf="!item['controls']['uploadInProgress'].value">
                                    <button mat-raised-button class="mt-2 btn-active slide-from-down" (click)="fileInput.click()">
                                        Upload
                                    </button>
                                    <input hidden type="file" #fileInput ng2FileSelect [uploader]="uploader"
                                    (change)="onFileAdd($event, fileIndex)" [accept]="allowedMimeType" />
                                </div>
                                <div class="d-flex justify-content-center" *ngIf="item['controls']['uploadInProgress'].value">
                                    <mat-spinner matTooltip="Uploading" class="spinner-align" diameter="18"></mat-spinner>
                                </div>
                            </div>
                            <div *ngIf="!item['controls']['uploadInProgress'].value" class="col-2 d-flex justify-content-center">
                                <button mat-icon-button class="close-button mt-1" matTooltip="Remove File" (click)="removeAttachmentItem(fileIndex)">
                                    <mat-icon class="close-Icon">remove_circle_outline</mat-icon>
                                </button>
                            </div>
                        </div>
                    </ng-container>
                </ng-container>
            </form>
            <div class="row pt-2">
                <div class="col-12 pl-0">
                    <button mat-icon-button class="ml-auto close-button mt-1" matTooltip="Add File"
                        (click)="addAttachmentItem()">
                        <mat-icon class="close-Icon">add</mat-icon>
                    </button>
                    <span class="normalFont pl-2" style="cursor: pointer;" (click)="addAttachmentItem()">Add File</span>
                </div>
            </div>
        </div>
    </div>


    <div *ngIf="fileList.length == 0 && !addItemVisible &&!isAttachmentLoading" style="text-align: center;padding-top: 3rem;">
        <div class="d-flex pb-2 justify-content-center align-items-center slide-in-top">
            <span class="extra-dark-txt">No Documents found ! Let's add one</span>
        </div>
        <div class="d-flex justify-content-center align-items-center slide-from-down pt-2 pb-2">
            <img src="https://assets.kebs.app/images/noAccounts.png" class="mt-2 mb-2" height="200" width="200" />
        </div>
        <button mat-raised-button class="mt-2 btn-active slide-from-down" (click)="createNewFileList()">
            Add files
        </button>
    </div>



</div>