<div style="display: flex; flex-direction: column; height: 100%; max-height: 80vh;">
  <h3 style="background-color: #FFF3F4;
      padding: 15px;
      max-height: 70px;
      color: #1B2140;
      letter-spacing: normal;
      margin: 0;
      font-family: 'DM Sans';
      font-weight: 500;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;">
      
      <div>
        <div>{{ data?.widgetData?.label || 'Summary' }}</div>
        <div style="font-size: 12px; color: #6B7280; margin-top: 4px;">
          Total: {{dataSource.filteredData.length}}
        </div>
      </div>
      
      <div style="display: flex; align-items: center;">
          <button style="border: none; background-color: #FFF3F4;" mat-icon-button (click)="downloadExcel()" matTooltip="Download Excel">
              <img style="height: 20px; width: 20px" src="https://assets.kebs.app/excel-icon-report-ai.png">
          </button>
          <button style="border: none; height: 24px; width: 24px; color:#515965; background-color: #FFF3F4;" mat-icon-button (click)="onClose()" matTooltip="Close">
              <mat-icon>close</mat-icon>
          </button>
      </div>
  </h3>

  <div style="flex: 1; overflow: auto; min-height: 0;margin: 15px;">
    <div class="summary-table" *ngIf="dataSource.data?.length; else noData">
      <div>
        <table mat-table [dataSource]="dataSource" class="mat-elevation-z2 summary-mat-table" matSort>
          
          <!-- Column Definitions with Filter Dropdowns -->
          <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column">
            <th mat-header-cell *matHeaderCellDef class="summary-header-cell" mat-sort-header>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ getColumnLabel(column) }}</span>
              
              </div>
              
              <!-- Filter Dropdown Menu -->
             
            </th>
            <td mat-cell *matCellDef="let element" class="summary-cell">{{ element[column] }}</td>
          </ng-container>

          <!-- Header Row -->
          <tr mat-header-row *matHeaderRowDef="displayedColumns" class="summary-header-row"></tr>
          
          <!-- Data Row -->
          <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="summary-data-row"></tr>
        </table>
      </div>
    </div>
  </div>

  <ng-template #noData>
    <div style="flex: 1; display: flex; align-items: center; justify-content: center;">
      <p style="text-align: center; padding: 2rem;">No summary data available.</p>
    </div>
  </ng-template>
</div>

<style>
  .summary-table {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .summary-mat-table {
    width: 100%;
    min-width: 100%;
    border-collapse: collapse;
    table-layout: auto;
  }

  .summary-header-cell {
    position: sticky;
    top: 0;
    background-color: #F2F3F6;
    padding: 8px 12px;
    white-space: nowrap;
    font-weight: 600;
    color: #5F6C81;
    border-bottom: 1px solid #e0e0e0;
    z-index: 2;
    min-width: 120px;
    vertical-align: top;
  }

  .summary-cell {
    padding: 12px;
    white-space: nowrap;
    min-width: 120px;
    border-bottom: 1px solid #f0f0f0;
    color: #1B2140;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .summary-header-row {
    height: auto;
  }

  .summary-data-row {
    height: 48px;
  }

  .summary-data-row:hover {
    background-color: #f5f5f5;
  }

  .mat-table {
    width: 100%;
    display: table;
    border-spacing: 0;
  }

  .mat-header-row, .mat-row {
    display: table-row;
  }

  .mat-header-cell, .mat-cell {
    display: table-cell;
    border-right: 1px solid #e0e0e0;
  }

  .mat-header-cell:last-child, .mat-cell:last-child {
    border-right: none;
  }

  .filter-icon {
    width: 24px;
    height: 24px;
    line-height: 24px;
    color: #5F6C81;
  }

  .filter-menu {
    position: absolute;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    z-index: 100;
    margin-top: 8px;
    max-height: 300px;
    overflow-y: auto;
    width: 200px;
  }

  .filter-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 500;
  }

  .filter-menu-content {
    padding: 4px 0;
  }

  .filter-option {
    padding: 8px 16px;
    cursor: pointer;
  }

  .filter-option:hover {
    background-color: #f5f5f5;
  }

  .filter-option.selected {
    background-color: #e3f2fd;
    color: #1976d2;
  }
</style>