.pm-revenue {
  margin-top: -48px;
  margin-left: 270px;

  .billing-text{
    text-decoration: underline;
    font-size: 12px;
    font-family: var(--milestoneFont) !important;
    font-weight: 500;
    cursor: pointer;
  }

  .billing-class{
    display: flex;
    justify-content: center;
    width: max-content;
  }
}

.container {
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 10px;
}

.main-container {
  display: flex;
  align-items: center;
}

.flex-container {
  display: flex;
  align-items: center;
}

.overall-revenue-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: var(--billingShades);
  padding: 5px 8px;
  border-radius: 5px;
}

.purchase-order {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.icons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.menu-button {
  color: rgb(125, 131, 139);
  font-weight: 400;
  text-transform: capitalize;
  letter-spacing: 0.24px;
  line-height: 31px !important;
  height: 31px !important;
  font-size: 13px !important;
  padding-left: 25px;
}

.menu-button-separator {
  border-top: 1px solid rgb(125, 131, 139);
}

.menu-button-dot {
  width: 6px;
  height: 6px;
  border-radius: 9999px;
  margin-top: 12px;
  position: absolute;
  margin-left: -14px;
}

.sort-icon {
  cursor: pointer;
  font-size: 14px;
  margin-top: 17px;
  margin-left: -12px;
  color: #7D838B;
}

.all {
  cursor: pointer;
  color: #7D838B;
  font-size: 13px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
}

.all-icon {
  font-size: 15px;
  display: flex;
  white-space: nowrap;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.overall-revenue-section-header {
  color: var(--billingButton);
  font-size: 12px;
  font-weight: 600;
  text-transform: capitalize;
  line-height: 16px;
  letter-spacing: 0.24px;
}

.purchase-order-header {
  color: #6E7B8F;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 16px;
  letter-spacing: 0.24px;
}

.status-color {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.separator {
  border-top: 1px solid #7d838b;
}

.divider {
  width: 1px;
  background-color: #DADCE2;
  margin: 0 20px;
  align-self: stretch;
}

.font-family {
  font-family: var(--milestoneFont) !important;
}

.menu-overflow {
  overflow: hidden !important;
}

.button-common {
  color: rgb(125, 131, 139);
  font-weight: 400;
  text-transform: capitalize;
  letter-spacing: 0.24px;
  line-height: 31px !important;
  height: 31px !important;
  font-size: 13px !important;
  padding-left: 25px;
  position: relative;
}


.button-inactive {
  border-top: 1px solid rgb(125, 131, 139);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 9999px;
  margin-top: 12px;
  position: absolute;
  margin-left: -14px;
}


::ng-deep .overall-revenue-section .currency-wrapper .code-header,
::ng-deep .overall-revenue-section .currency-wrapper .data-label {
  font-weight: 900 !important;
}

.revenue-body {
  margin-left: -245px;
}

.bill-body-total {
}

.bill-body-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 7px 0;
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
  border-top: 1px solid #B9C0CA;
  border-bottom: 1px solid #B9C0CA;
  margin-top: 15px;
  color: gray;
  font-size: 12px;
}

.bill-body-list-outer {
  overflow-y: auto;
  height: var(--milestoneListHeight);
  margin-top: 12px;
}

.bill-body-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 0 10px 0;
  font-size: 13px;
  font-weight: 500;
}

.select-id-header,
.select-id-col {
  flex: 0 0 50px;
  display: flex;
  justify-content: flex-start;
}

.name-header,
.name-col {
  flex: 0 0 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 30px;
}

.id-header {
  flex: 0 0 100px;
  display: flex;
  text-align: center;
}

.id-col {
  display: flex;
  flex: 0 0 100px;
  justify-content: flex-start;
}

.value-header,
.value-col {
  flex: 0 0 150px;
  display: flex;
  text-align: center;
}

.status-header,
.status-col,
.startDate-header,
.startDate-col,
.endDate-header,
.endDate-col {
  flex: 0 0 140px;
  display: flex;
  text-align: center;
}

.actions-col {
  flex: 0 0 100px;
  display: flex;
  text-align: center;
  gap: 6px;
}

.actions-col mat-icon {
  cursor: pointer;
  margin-right: 5px;
}

.action-icons {
  display: flex;
  align-items: flex-start;
  // min-width: 200px;
  gap: 6px;
}


.name-col div {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::ng-deep .mat-menu-panel {
  min-width: 112px;
  max-width: 280px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  max-height: calc(100vh - 48px);
  border-radius: 4px;
  outline: 0;
  min-height: 64px;
  overflow: hidden;
}

::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background-color: var(--milestoneButton) !important;
}

::ng-deep .mat-checkbox-ripple .mat-ripple-element {
  background-color: var(--milestoneButton) !important;
}

::ng-deep .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
  background-color: var(--milestoneButton) !important;
}

::ng-deep .button-toggle-class .mat-button-toggle-checked .mat-button-toggle-button {
  background-color: transparent;
}

::ng-deep .mat-checkbox-inner-container {
  width: 14px !important;
  height: 14px !important;
}

::ng-deep .mat-checkbox-inner-container-no-side-margin {
  margin-left: 0;
  margin-right: 0;
  margin-bottom: -8px !important;
}

.status-chip {
  width: max-content;
  height: 20px;
  padding: 2px 8px;
  border-radius: 16px;
  border: 1px solid #FF3A46;
  margin-top: 2px;
  background: linear-gradient(0deg, #FFEBEC, #FFEBEC), linear-gradient(0deg, #FF3A46, #FF3A46);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-text {
  height: 14px;
  font-family: var(--milestoneFont) !important;
  font-size: 12px;
  font-weight: 400;
  line-height: 14px;
  letter-spacing: 0.02em;
  text-align: center;
  color: #FF3A46;
  width: max-content;
}

.column-list {
  display: flex;
  flex-direction: column;
  width: 145px;
  margin-right: 10px;
}

.column-item {
  display: flex;
  align-items: center;
}

.content {
  left: 43%;
  position: fixed;
  top: 46%;

}

.save-button {
  width: auto;
  height: 40px;
  border-radius: 4px;
  gap: 8px;
  font-family: var(--milestoneFont) !important;
  font-size: 14px;
  font-weight: 700;
  line-height: 16px;
  letter-spacing: 0.04em;
  color: #FFFFFF;
  background: var(--milestoneButton) !important;
  margin-left: 100px;
  margin-top: 10px;
}

.description {
  width: 272px;
  height: 32px;
  font-family: var(--milestoneFont) !important;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0.02em;
  text-align: center;
  color: #45546E;
  white-space: nowrap;
  margin-top: 5px;
}

.tittle {
  width: 272px;
  height: 24px;
  font-family: var(--milestoneFont) !important;
  font-size: 14px;
  font-weight: 700;
  line-height: 24px;
  letter-spacing: 0.02em;
  text-align: center;
  color: #45546E;

}

.img-data {
  width: 130.4px;
  height: 125px;
  margin-left: 67px;
}

.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white !important;
}

.loader-container ::ng-deep .green-spinner circle {
  stroke: var(--milestoneButton) !important;
}

.loader {
  top: 45%;
  position: absolute;
  left: 50%;
  bottom: 50%;
}

.create {
  width: 20px;
  height: 32px;
  color: #7D838B;
  margin-top: 9px;
  margin-left: 9px;
  cursor: pointer;
  font-size: 22px !important;
}

.pop-up-container,
.pop-up-container1 {
  height: 92px;
  width: 110px;
  font-size: 13px;
  padding: 18px;
  border-radius: 10px !important;
  position: absolute !important;
  background-color: white !important;
  z-index: 1 !important;
  transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1) !important;
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
}

.pop-up-container {
  margin-left: 55%;
  margin-top: -5%;
}

.pop-up-container1 {
  margin-left: 638px;
  margin-top: 68px;
}

.edit {
  width: 24px;
  height: 24px;
  margin-left: 15px;
  color: #7D838B;
  cursor: pointer;
  font-size: 22px !important;
}

.delete {
  width: 24px;
  height: 24px;
  margin-left: 15px;
  color: #7D838B;
  cursor: pointer;
  font-size: 22px !important;
}