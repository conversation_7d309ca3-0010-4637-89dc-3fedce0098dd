import { Component, OnInit, NgModule, Inject, OnD<PERSON>roy } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import * as _ from "underscore";
import { MailUtilityService } from './services/mail-utility.service'

@Component({
  selector: 'app-view-mail',
  templateUrl: './view-mail.component.html',
  styleUrls: ['./view-mail.component.scss']
})
export class ViewMailComponent implements OnInit,OnDestroy {

   totalPage : number;
   currentPage : number = 1;
   reviewPageKeys : any = []

  constructor(
    public dialogRef: MatDialogRef<ViewMailComponent>,
    public mailUtilityService : MailUtilityService
  ) { }


  async ngOnInit() {

    this.mailUtilityService.closeDialog$.subscribe(() => {
      this.closeDialog();
    });
    
   this.handleBasedOnApplication()

   this.mailUtilityService.mailUiData.selectedMailFolder = this.mailUtilityService.mailFolders[0]
  
   this.mailUtilityService.mUtilityData.reloadBodyComponent();

   this.mailUtilityService.mailFunctions.initMiniCardSelection()

  }

  onMenuClick(menuIndex) {

    this.mailUtilityService.mailFolders.forEach((item,index) => {
      if(menuIndex == index) {
        item.isEnabled = true
        this.mailUtilityService.mailUiData.selectedMailFolder = item
      }
      else {
        item.isEnabled = false
      }
    })
    this.mailUtilityService.cancelApiCall()
    this.mailUtilityService.mUtilityData.reloadBodyComponent();
  }

  prevClicked () {
    this.currentPage -= 1

    this.mailUtilityService.mUtilityData.selectedDunningMailData 
    = this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]]

    this.mailUtilityService.mUtilityData.selectedNewMailTemplateData
    = this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]]


    this.mailUtilityService.mUtilityData.recipientMailIdArr 
    = this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]].toMailIds

      this.mailUtilityService.mailFunctions.initMiniCardSelection()
      this.mailUtilityService.mUtilityData.reloadBodyComponent();

      this.mailUtilityService.mUtilityData.currentMailMode = {mode:'not selected'}
      this.mailUtilityService.mailFunctions.switchViewMode()

 
  }

  nextClicked () {
    this.currentPage += 1
    
    this.mailUtilityService.mUtilityData.selectedDunningMailData 
    = this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]]

    this.mailUtilityService.mUtilityData.selectedNewMailTemplateData
    = this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]]

    this.mailUtilityService.mUtilityData.recipientMailIdArr 
    = this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]].toMailIds


    this.mailUtilityService.mailFunctions.initMiniCardSelection()
    this.mailUtilityService.mUtilityData.reloadBodyComponent()

    this.mailUtilityService.mUtilityData.currentMailMode = { mode:'not selected'}
    this.mailUtilityService.mailFunctions.switchViewMode()

  }

  handleBasedOnApplication () {

    // if(this.mailUtilityService.mUtilityData.applicationId == 235) {

    //   this.mailUtilityService.mUtilityData.dunningMailData.forEach((item,index) => {
    //     this.mailUtilityService.mUtilityData.reviewPagination[index] = item
    //   })
    //   this.reviewPageKeys = Object.keys(this.mailUtilityService.mUtilityData.reviewPagination)
    //   this.mailUtilityService.mUtilityData.recipientMailIdArr = this.mailUtilityService.mUtilityData.reviewPagination[0].toMailIds

    //   this.mailUtilityService.mUtilityData.selectedDunningMailData = this.mailUtilityService.mUtilityData.reviewPagination[0]
    //   this.totalPage = this.mailUtilityService.mUtilityData.dunningMailData.length;

    // }

    if(this.mailUtilityService.mUtilityData.hasInitiateNewMailTemplate) {

      this.mailUtilityService.mUtilityData.newMailTemplateData.forEach((item,index) => {
        this.mailUtilityService.mUtilityData.reviewPagination[index] = item
      })

      this.reviewPageKeys = Object.keys(this.mailUtilityService.mUtilityData.reviewPagination)
      this.mailUtilityService.mUtilityData.recipientMailIdArr = this.mailUtilityService.mUtilityData.reviewPagination[0].toMailIds

      this.mailUtilityService.mUtilityData.selectedNewMailTemplateData = this.mailUtilityService.mUtilityData.reviewPagination[0]
      this.totalPage = this.mailUtilityService.mUtilityData.newMailTemplateData.length;

      console.log(this.mailUtilityService.mUtilityData.selectedNewMailTemplateData)

    }

  }

  changeMailFolder() {

  }

  ngOnDestroy () {
    //this.mailUtilityService.resetData()
    this.mailUtilityService.mUtilityData.dunningMailData = []
    this.mailUtilityService.mUtilityData.currentMailMode= {}
  }

  closeDialog () {
    this.dialogRef.close();
    this.mailUtilityService.mUtilityData.dunningMailData = []
  }

}

import { CommonModule } from '@angular/common';
import { ComponentsModule } from './components/components.module'
import { TagInputModule } from 'ngx-chips';
import {MatRippleModule} from '@angular/material/core';
import {MatCardModule} from '@angular/material/card';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatListModule } from '@angular/material/list';
import { MatSidenavModule } from '@angular/material/sidenav';
import {PipesModule} from './pipes/pipes.module'
import { DxHtmlEditorModule } from 'devextreme-angular';
import {ScrollingModule} from '@angular/cdk/scrolling';
import { InfiniteScrollModule } from "ngx-infinite-scroll";


 
@NgModule({
  declarations: [
    ViewMailComponent
  ],
  imports: [
    CommonModule,
    ComponentsModule,
    TagInputModule,
    DxHtmlEditorModule,
    MatRippleModule,
    MatCardModule,
    DragDropModule,
    MatListModule,
    MatSidenavModule,
    PipesModule,
    ScrollingModule,
    InfiniteScrollModule
  ]
})
export class ViewMailModule { }
