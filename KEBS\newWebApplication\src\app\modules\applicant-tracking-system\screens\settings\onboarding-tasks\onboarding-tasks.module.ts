import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { OnboardingTasksRoutingModule } from './onboarding-tasks-routing.module';
import { ApplicantTrackingSystemModule } from '../../../applicant-tracking-system.module';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';

import { LandingPageComponent } from './pages/landing-page/landing-page.component';

import { OverlayModule } from '@angular/cdk/overlay';
import { TooltipModule } from 'ng2-tooltip-directive';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';

@NgModule({
  declarations: [LandingPageComponent],
  imports: [
    CommonModule,
    OnboardingTasksRoutingModule,
    ApplicantTrackingSystemModule,
    SharedComponentsModule,
    OverlayModule,
    TooltipModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatSlideToggleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
    MatRadioModule,
    MatSelectModule,
    DragDropModule,
    MatDividerModule,
    MatTooltipModule,
  ],
})
export class NewHireTasksModule {}
