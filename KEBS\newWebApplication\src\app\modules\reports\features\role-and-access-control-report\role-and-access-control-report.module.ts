import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule,ReactiveFormsModule } from '@angular/forms';

import { RoleAndAccessControlReportRoutingModule } from './role-and-access-control-report-routing.module';
import { RoleListHomePageComponent } from './components/role-list-home-page/role-list-home-page.component';
import { RoleDetailsPageComponent } from './components/role-details-page/role-details-page.component';

//Materail
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatStepperModule } from '@angular/material/stepper';
import { DxDataGridModule, DxButtonModule } from 'devextreme-angular';
import { NgxSpinnerModule } from 'ngx-spinner';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';

import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';


@NgModule({
  declarations: [RoleListHomePageComponent, RoleDetailsPageComponent],
  imports: [
    CommonModule,
    RoleAndAccessControlReportRoutingModule,
    SharedComponentsModule,
    NgxSpinnerModule,
    ReactiveFormsModule,
    FormsModule,
    MatTabsModule,
    MatDividerModule,
    MatCardModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatIconModule,
    MatTooltipModule,
    MatButtonModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatButtonToggleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    DxDataGridModule,
    MatStepperModule,
    NgxMatSelectSearchModule,
    DxButtonModule
  ]
})
export class RoleAndAccessControlReportModule { }
