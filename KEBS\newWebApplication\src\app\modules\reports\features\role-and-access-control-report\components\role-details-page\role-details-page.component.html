<div class="RoleGroupDetailsPage">
    <div class="row border-bottom solid titleRow">
        <div class="col-11 pt-2 d-flex popupTitle">
            <div mat-icon-button class="bubble mt-1">
                <mat-icon style="font-weight: 600;">list</mat-icon>
            </div>
            <span class="name my-auto ml-2" style="font-weight: 600;">Role Details</span>
        </div>
        <div class="col-1 d-flex">
            <button mat-icon-button class="ml-auto close-button mt-1">
                <mat-icon class="ml-auto close-button mt-1" style="cursor: pointer;  color: #4d4d4b;" class="close-Icon"
                    matTooltip="Close" (click)="closePage()">close</mat-icon>
            </button>
        </div>
    </div>

    <mat-tab-group>
        <mat-tab label class="permissionTabHead">
            <ng-template mat-tab-label>
                <div class="permissionTab" style="display: flex;
                            align-items: flex-start;
                            margin-top: 10px;">
                    <mat-icon style="padding-right:2px;font-size: 18px;">security</mat-icon>
                    <span>Permissions</span>
                </div>
            </ng-template>

            <!-- <div class="titile_card row col-12 align-items-center" style="padding-top:15px;">
                <div class="col-5"></div>
                <div class="title row col-5" style="text-align: center;color:rgb(251, 100, 100);">
                    <span>Permission List</span>
                    <mat-icon class="iconsSize" matTooltip="Refresh"
                        style="margin-left:30px;color:rgb(251, 100, 100);cursor: pointer;"
                        (click)="loadRolePerimissionLits()">refresh</mat-icon>
                </div>

                <div class="row col deleteRoles">
                    <button class="deleteRoleBtn" (click)="deleteSelectedRolePermissions()"
                        [ngClass]="isDeletePermissionsLoading ? 'deleteRoleBtn-loading' : 'deleteRoleBtn'" *ngIf="hasDeleteRolePermissionAccess">
                        <mat-icon *ngIf="!isDeletePermissionsLoading" matTooltip="Delete Permission">delete</mat-icon>
                        <mat-spinner *ngIf="isDeletePermissionsLoading" matTooltip="Please wait..."
                            class="spinner-align" diameter="20"></mat-spinner>

                    </button>
                </div>

                <div class="createNewRole">
                    <button class="createNewRoleBtn" matTooltip="Add New Permission"
                        (click)="displayAddNewPermission()" *ngIf="hasAddRolePermissionAccess">
                        <mat-icon>add</mat-icon><span>Add Permission</span>
                    </button>
                </div>
            </div> -->


            <div *ngIf="!isPermisisonDataLoading" class="row justify-content-center" style="padding-top: 10vh;">
                <mat-spinner diameter="30"></mat-spinner>
            </div>

            <div *ngIf="isPermisisonDataLoading">

                <div class="col p-0 ca-m card-body-height align-items-center" style="margin-top: 1%">
                    <div class="col">
                        <div class="row d-flex justify-content-between align-items-center">
                            <div class="row"></div>
                            <div class="row">
                                <dx-button icon="refresh" matTooltip="Refresh"
                                    (onClick)="loadRolePerimissionLits()"></dx-button>
                                <!-- <dx-button icon="trash" matTooltip="Delete Permission" (onClick)="deleteSelectedRolePermissions()" *ngIf="hasDeleteRolePermissionAccess"> </dx-button> -->
                                <!-- <div>
                                    <dx-button icon="trash" matTooltip="Delete Permission"
                                        (onClick)="deleteSelectedRolePermissions()"
                                        *ngIf="hasDeleteRolePermissionAccess && !isDeletePermissionsLoading">
                                    </dx-button>
                                    <div *ngIf="isDeletePermissionsLoading" class="spinner-align">
                                        <mat-spinner diameter="20" matTooltip="Please wait..."
                                            style="margin-top: 8px;color:gray;"></mat-spinner>
                                    </div>
                                </div> -->
                                <!-- <dx-button icon="add" matTooltip="Add New Permission"
                                    (onClick)="displayAddNewPermission()" *ngIf="hasAddRolePermissionAccess">
                                </dx-button> -->
                                <dx-button icon="download" matTooltip="Download Report"
                                    (onClick)="exportGridToExcelPermissionList()"> </dx-button>
                                <dx-button icon="column-chooser" matTooltip="Confgure Report"
                                    (onClick)="openColumnChooserPermission()"> </dx-button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <dx-data-grid #premissionListGrid [allowColumnResizing]="true"
                            [dataSource]="permissionTableData" [showBorders]="true" [hoverStateEnabled]="true"
                            class="dev-style" style="padding:10px;padding-top:0px;" [columnAutoWidth]="true"
                            (onExporting)="onExportingPermission($event)" [width]="'100%'"
                            [selectedRowKeys]="selectedPermissionRowKeys"
                            (onSelectionChanged)="onPermissionSelectionChanged($event)"
                            [selection]="{ mode: 'single', showCheckBoxesMode: 'always' }"
                            (onRowUpdating)="onPermissionRowUpdating($event)">

                            <!-- <dxo-editing mode="row" [allowUpdating]="true"> </dxo-editing>

                            <dxo-selection [selectAllMode]="allMode" [showCheckBoxesMode]="checkBoxesMode"
                                mode="single"></dxo-selection> -->

                            <dxo-column-chooser [enabled]="true" mode="select"> </dxo-column-chooser>

                            <dxo-search-panel [visible]="true" [width]="240" placeholder="Search..."></dxo-search-panel>

                            <dxo-header-filter [visible]="true"></dxo-header-filter>
                            <dxo-filter-row [visible]="true"></dxo-filter-row>

                            <dxo-export [enabled]="true"></dxo-export>

                            <!-- <dxi-column dataField="id" caption="Seq.ID" [allowSorting]="true" [allowFiltering]="true"
                                [minWidth]="auto" [allowReordering]="true" alignment='left' [allowEditing]="false">
                            </dxi-column> -->

                            <dxi-column dataField="role_id"
                                caption="Role ID"
                                [allowSorting]="true" [allowFiltering]="true" [minWidth]="auto" [allowReordering]="true"
                                alignment='left' [allowEditing]="false">
                            </dxi-column>

                            <dxi-column dataField="role_name"
                                caption="Role Name"
                                [allowSorting]="true" [allowFiltering]="true" [minWidth]="auto" [allowReordering]="true"
                                alignment='left' [allowEditing]="false">
                            </dxi-column>

                            <dxi-column dataField="application_id" caption="Application Name" [allowSorting]="true"
                                [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
                                <dxo-lookup [dataSource]="applicationList" displayExpr="application_name"
                                    valueExpr="id">
                                </dxo-lookup>
                            </dxi-column>

                             <dxi-column dataField="object_id" caption="Object Name" [allowSorting]="true"
                                [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
                                <dxo-lookup [dataSource]="applicationObjectList" displayExpr="object_name"
                                    valueExpr="id">
                                </dxo-lookup>
                            </dxi-column>
                            
                            <dxi-column dataField="object_value" caption="Access Level" [allowSorting]="true"
                                [allowFiltering]="true" alignment='left' [minWidth]="50" [allowReordering]="true">
                                <dxo-lookup [dataSource]="accessObjectValue" displayExpr="label" valueExpr="value">
                                </dxo-lookup>
                            </dxi-column>

                            <dxi-column dataField="operation" caption="Operation" [allowSorting]="true"
                                [allowFiltering]="true" alignment='left' [minWidth]="50" [allowReordering]="true">
                                <dxo-lookup [dataSource]="operationValue" displayExpr="label" valueExpr="value">
                                </dxo-lookup>
                            </dxi-column>
                        </dx-data-grid>
                    </div>
                </div>
            </div>
        </mat-tab>

        <mat-tab label class="usersTabHead">
            <ng-template mat-tab-label>
                <div class="permissionTab" style="display: flex;
                            align-items: flex-start;
                            margin-top: 10px;">
                    <mat-icon style="padding-right:2px;font-size: 18px;">person</mat-icon>
                    <span>Users</span>
                </div>
            </ng-template>

            <div *ngIf="!isUserDataLoading" class="row justify-content-center" style="padding-top: 10vh;">
                <mat-spinner diameter="30"></mat-spinner>
            </div>

            <div *ngIf="isUserDataLoading">

                <div class="col p-0 ca-m card-body-height align-items-center" style="margin-top:1%">

                    <div class="col">
                        <div class="row d-flex justify-content-between align-items-center">
                            <div class="row"></div>
                            <div class="row">
                                <dx-button icon="refresh" matTooltip="Refresh"
                                    (onClick)="loadRoleUsersList()"></dx-button>
                                <!-- <dx-button icon="trash" (onClick)="deleteSelectedRolePermissions()"> </dx-button>
                                <dx-button icon="add" (onClick)="displayAddNewPermission()"> </dx-button> -->
                                <dx-button icon="download" matTooltip="Download Report"
                                    (onClick)="exportGridToExcelUsersList()"> </dx-button>
                                <dx-button icon="column-chooser" matTooltip="Configure Report"
                                    (onClick)="openColumnChooserUsers()"> </dx-button>
                            </div>
                        </div>
                    </div>

                    <dx-data-grid #usersListGrid id="gridContainer" [allowColumnResizing]="true"
                        [dataSource]="userTableData" [showBorders]="true" [hoverStateEnabled]="true" class="dev-style"
                        style="padding:10px;padding-top:0px;" [columnAutoWidth]="true"
                        (onExporting)="onExportingUsers($event)" [width]="'100%'">

                        <dxo-column-chooser [enabled]="true" mode="select"> </dxo-column-chooser>

                        <dxo-search-panel [visible]="true" [width]="240" placeholder="Search..."></dxo-search-panel>

                        <dxo-header-filter [visible]="true"></dxo-header-filter>
                        <dxo-filter-row [visible]="true"></dxo-filter-row>

                        <dxo-export [enabled]="true"></dxo-export>

                        <!-- <dxi-column dataField="id" caption="Seq.ID" [allowSorting]="true" [allowFiltering]="true"
                            width="110" [allowReordering]="true" alignment='left'>
                        </dxi-column> -->

                        <dxi-column dataField="role_id"
                            caption="Role ID" [allowSorting]="true"
                            [allowFiltering]="true" width="110" [allowReordering]="true" alignment='left'>
                        </dxi-column>

                        <dxi-column dataField="associate_id" caption="Associate ID" [allowSorting]="true"
                            [allowFiltering]="true" width="120" [allowReordering]="true" alignment='left'>
                        </dxi-column>

                        <dxi-column dataField="name" caption="Employee Name" [allowSorting]="true"
                            [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
                        </dxi-column>

                        <dxi-column dataField="oid" caption="Employee OID" [allowSorting]="true" [allowFiltering]="true"
                            alignment='left' [minWidth]="auto" [allowReordering]="true" [visible]="false">
                        </dxi-column>

                        <dxi-column dataField="email" caption="Employee Email" [allowSorting]="true"
                            [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
                        </dxi-column>

                        <dxi-column caption="" dataField="edit_user_role" name="actionColumn" [allowSorting]="false" [allowFiltering]="false" 
                            cellTemplate="notificationTemplate" [showFilterRow]="false" [allowReordering]="false">
                            <div style="text-align: right;"></div>
                        </dxi-column>

                        <div style="display: flex; align-items: center;gap:5px;float:right;cursor: pointer;"
                            matTooltip="Edit User Role" [allowReordering]="true"
                            *dxTemplate="let data of 'notificationTemplate'">
                            <div (click)="editUserRole(data.data)">
                                <mat-icon style="font-size:18px;color:gray;margin-top: 0px;">edit</mat-icon>
                            </div>
                        </div>
                    </dx-data-grid>

                </div>
            </div>

        </mat-tab>
    </mat-tab-group>

</div>