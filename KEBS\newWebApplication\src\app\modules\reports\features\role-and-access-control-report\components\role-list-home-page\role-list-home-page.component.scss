.roleListHomePageContainer {
    .createNewRoleBtn {
        border-radius: 4px;
        background: var(--primary-maroon, #EE4961);
        width: auto;
        height: auto;
        border: none !important;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 4px;
        padding-right: 7px;
        font-size: 14px;
        color: white;
        font-weight: 500;
    }

    .deleteRoleBtn {
        border-radius: 4px;
        background: var(--primary-maroon, #EE4961);
        width: auto;
        height: 32px;
        border: none !important;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 4px;
        padding-right: 4px;
        font-size: 14px;
        color: white;
        font-weight: 500;
    }

    .deleteRoleBtn-loading {
        background-color: white;
    }

    ::ng-deep .mat-button-toggle-label-content {
        font-size: 12px !important;
    }

    ::ng-deep .dx-datagrid-header-panel {
        display: none;
    }

    ::ng-deep .dx-toolbar .dx-toolbar-items-container {
        height: 36px;
    }

    // Data Row Height
    ::ng-deep .dx-datagrid .dx-row>td {
        padding-top: 6px;
        padding-bottom: 6px;
        font-size: 13px;
        line-height: 25px;
    }

    // columns
    ::ng-deep .dx-datagrid-headers .dx-datagrid-table .dx-row>td {
        border-bottom: 1px solid #e0e0e0;
        padding-top: 11px;
        padding-bottom: 11px;
        line-height: 15px;
        font-weight: 500;
        font-size: 11px;
    }

    //Filter Search row
    ::ng-deep .dx-datagrid-filter-row {
        background-color: #fafafa;
        height: 15px !important;
    }

    ::ng-deep .dx-header-row>td>.dx-datagrid-text-content {
        font-size: 12px;
    }

    ::ng-deep .dx-datagrid .dx-editor-with-menu {
        height: 15px !important;
    }

    ::ng-deep .dx-datagrid .dx-menu .dx-menu-item .dx-menu-item-content,
    ::ng-deep .dx-datagrid-container .dx-menu .dx-menu-item .dx-menu-item-content {
        //  padding: 11px 4px !important;
        line-height: 15px !important;
        padding-top: 1px !important;
        padding-bottom: 3px !important;
        margin-top: -3px;

    }

    ::ng-deep .dx-editor-cell .dx-texteditor .dx-texteditor-input {
        background: rgba(255, 255, 255, 0);
        font-size: 13px;
        height: 15px;
        line-height: 15px;
        // padding: 10px 38px;
        padding-top: 10px;
        padding-bottom: 10px;
        margin-top: -3px;
    }

    ::ng-deep .dx-datagrid .dx-menu .dx-menu-item .dx-menu-item-content .dx-icon,
    ::ng-deep .dx-datagrid-container .dx-menu .dx-menu-item .dx-menu-item-content .dx-icon {
        width: 22px;
        height: 22px;
        background-position: 0 0;
        background-size: 22px 22px;
        padding: 2px 2px;
        font-size: 18px;
        text-align: center;
        line-height: 15px;
        margin: 0 3px;
    }

    ::ng-deep .dx-icon-refresh::before {
        color: gray;
    }

    ::ng-deep .dx-icon-trash::before {
        color: gray;
    }

    ::ng-deep .dx-icon-add::before {
        color: gray;
    }

    ::ng-deep .dx-icon-download::before {
        color: gray;
    }

    ::ng-deep .dx-icon-column-chooser::before {
        color: gray;
    }

    ::ng-deep .dx-datagrid-content .dx-datagrid-table .dx-row .dx-command-edit .dx-link {
        display: inline-block;
        color: gray;
    }

    .downloadPermissionListBtn{
        width: 192px;
        height: 32px;
        opacity: 1;
        border-radius: 8px;
        border-width: 1px;
        padding-top: 8px;
        padding-right: 8px;
        padding-bottom: 8px;
        padding-left: 8px;
        gap: 8px;
        border: 1px solid;
        justify-content: center;
        align-items: center;
    }
}