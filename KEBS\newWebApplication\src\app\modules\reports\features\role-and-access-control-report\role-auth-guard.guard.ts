import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { RoleAccessServiceService } from './service/role-access-service.service';

@Injectable({
  providedIn: 'root'
})
export class RoleAuthGuardGuard implements CanActivate {
  hasAccess:boolean = false;
  constructor(
    private _apiservice: RoleAccessServiceService,
    private router: Router,
    ){
      
    }
    
    async canActivate(
      route: ActivatedRouteSnapshot,
      state: RouterStateSnapshot) {
  
      let allowed = await this.checkAccessForRoleAccessReport()
      console.log("checkForRoleAccess : ",allowed)
      if (allowed) {
        return true
      }
      else {
        this.router.navigateByUrl(`/main`);
      }
    }

    checkAccessForRoleAccessReport(){
      return new Promise((resolve, reject) => {
        this._apiservice.checkAccessForRoleAccessReport().subscribe((res:any) =>
        {
          resolve(res.has_access);
        },(err) => {
          console.log(err);
          reject(err)
        })
      })
    }
}
