const mysql = require("mysql");
const fs = require("fs");
const logger = require("./logger").logger;
const util = require("util");

module.exports = {

  init: () => {

    return new Promise((resolve, reject) => {

      const config = require("config");

      try {

        let dbConfig = {
          connectionLimit: config.connectionLimit,
          host: config.host,
          user: config.user,
          password: config.password,
          port: config.port,
          debug: config.debug,
          waitForConnections: true,
          multipleStatements: true,
          acquireTimeout: 1000000
        };

        let rdbConfig = {
          connectionLimit: config.connectionLimit,
          host: config.rr_host,
          user: config.rr_user,
          password: config.rr_password,
          port: config.port,
          debug: config.debug,
          waitForConnections: true,
          multipleStatements: true,
          acquireTimeout: 1000000
        };

        // console.log(config);
        // console.log(dbConfig);
        // console.log(rdbConfig);

        
        // logger.info(config)
        // logger.info(dbConfig)
        // logger.info(rdbConfig)

        if (config.ssl) {
          dbConfig.ssl = {
            ca: fs.readFileSync(__dirname + "/kebs.crt.pem")
          };

          rdbConfig.ssl = {
            ca: fs.readFileSync(__dirname + "/kebs.crt.pem")
          };
        }

        //creating a node pool
        let prim_pool = mysql.createPool(dbConfig)
        let read_pool = mysql.createPool(rdbConfig)

        //binding query functionality to pool
       //  module.exports.pool = prim_pool.query
       //  module.exports.rpool = read_pool.query

        module.exports.pool = util.promisify(prim_pool.query).bind(prim_pool);
        module.exports.rpool = util.promisify(read_pool.query).bind(read_pool);

        //Verifying connection

        prim_pool.getConnection((err, connection) => {
          if (err) {
            logger.info(err);
            if (err.code === "PROTOCOL_CONNECTION_LOST") {
              reject("Database connection was closed.");
            }
            if (err.code === "ER_CON_COUNT_ERROR") {
              reject("Database has too many connections.");
            }
            if (err.code === "ECONNREFUSED") {
              reject("Database connection was refused.");
            }
            reject(err);
          }
          if (connection) {
            connection.release();
            resolve();
          }
          return;
        });

        read_pool.getConnection((err, connection) => {
          if (err) {
            logger.info(err);
            if (err.code === "PROTOCOL_CONNECTION_LOST") {
              reject("Database connection was closed.");
            }
            if (err.code === "ER_CON_COUNT_ERROR") {
              reject("Database has too many connections.");
            }
            if (err.code === "ECONNREFUSED") {
              reject("Database connection was refused.");
            }
            reject(err);
          }
          if (connection) {
            connection.release();
            resolve();
          }
          return;
        });


        resolve("success");

      }
      catch (err) {

        console.log(err);
        logger.info(err);
        reject(err);

      }



      // //Connection tests
      // prim_pool.getConnection((err, connection) => {
      //   if (err) {
      //     logger.info(err);
      //     if (err.code === "PROTOCOL_CONNECTION_LOST") {
      //       reject("Database connection was closed.");
      //     }
      //     if (err.code === "ER_CON_COUNT_ERROR") {
      //       reject("Database has too many connections.");
      //     }
      //     if (err.code === "ECONNREFUSED") {
      //       reject("Database connection was refused.");
      //     }
      //     reject(err);
      //   }
      //   if (connection) {
      //     read_pool.getConnection((err, connection) => {
      //       if (err) {
      //         logger.info(err);
      //         if (err.code === "PROTOCOL_CONNECTION_LOST") {
      //           reject("Database connection was closed.");
      //         }
      //         if (err.code === "ER_CON_COUNT_ERROR") {
      //           reject("Database has too many connections.");
      //         }
      //         if (err.code === "ECONNREFUSED") {
      //           reject("Database connection was refused.");
      //         }
      //         reject(err);
      //       }
      //       if (connection) {
      //         connection.release();
      //         resolve("success");
      //       }
      //     });
      //     connection.release();
      //     // resolve();
      //   }
      //   // return;
      // });



      // resolve("success")
    })

  },

  checkConnection: (pool) => {

    return new Promise((resolve, reject) => {
      pool.getConnection((err, connection) => {
        if (err) {
          logger.info(err);
          if (err.code === "PROTOCOL_CONNECTION_LOST") {
            reject("Database connection was closed.");
          }
          if (err.code === "ER_CON_COUNT_ERROR") {
            reject("Database has too many connections.");
          }
          if (err.code === "ECONNREFUSED") {
            reject("Database connection was refused.");
          }
          reject(err);
        }
        if (connection) {
          connection.release();
          resolve();
        }
        return;
      });
    });
  },

  checkConnectionR: (rpool) => {
    return new Promise((resolve, reject) => {
      rpool.getConnection((err, connection) => {
        if (err) {
          logger.info(err);
          if (err.code === "PROTOCOL_CONNECTION_LOST") {
            reject("Database connection was closed.");
          }
          if (err.code === "ER_CON_COUNT_ERROR") {
            reject("Database has too many connections.");
          }
          if (err.code === "ECONNREFUSED") {
            reject("Database connection was refused.");
          }
          reject(err);
        }
        if (connection) {
          connection.release();
          resolve();
        }
        return;
      });
    });
  }

}
