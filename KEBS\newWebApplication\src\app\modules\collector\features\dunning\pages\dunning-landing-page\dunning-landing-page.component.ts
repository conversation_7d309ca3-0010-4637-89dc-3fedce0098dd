import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subject, Subscription } from 'rxjs'
import { takeUntil } from 'rxjs/operators';
import * as _ from 'underscore';
import * as moment from 'moment';

// import { MailUtilityService } from 'mail-box-modal';
import { MailUtilityService } from 'src/app/app-shared/app-shared-components/mail-box-modal/services/mail-utility.service';

import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { ErrorService } from 'src/app/services/error/error.service';
import { DunningService } from '../../../../services/dunning-service/dunning.service'
import { TenantService } from 'src/app/services/tenant-service/tenant.service';
import { MatDialog } from '@angular/material/dialog';
import { GraphApiService } from 'src/app/services/graph/graph-api.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { LoginService } from 'src/app/services/login/login.service';
import { truncate } from 'fs';
import {DunningTemplateSelectionComponent} from '../../components/dunning-template-selection/dunning-template-selection.component';
import { NgxSpinnerService } from 'ngx-spinner';
@Component({
  selector: 'app-dunning-landing-page',
  templateUrl: './dunning-landing-page.component.html',
  styleUrls: ['./dunning-landing-page.component.scss']
})
export class DunningLandingPageComponent implements OnInit, OnDestroy {

  applicationId = 235;
  requestDataSubscription: Subscription;
  dunningItemDataCurrentIndex = 0;
  protected _onDestroy = new Subject<void>();
  protected _onAppApiCalled = new Subject<void>();
  user = this._loginService.getProfile().profile;
  isOpenMailBoxLoading: boolean = false
  dateFormats:any;
  dateRange: any;
  startOfWeek: any;
  endOfWeek: any;

  filterId: any;

  collectorRoleAccessData: any;

  templateDetails: any;

  udrfBodyColumns = [
    // {
    //   item: 'checkBox',
    //   header: '',
    //   isActive: true,
    //   isVisible: 'false',
    //   type: 'checkBox',
    //   position: 1,
    //   colSize: 1,
    //   width: 90,
    //   sortOrder: 'N'
    // },
    {
      item: 'customer_id',
      header: 'Customer ID',
      isActive: true,
      isVisible: 'true',
      type: 'text1',
      textClass: 'value13Bold',
      position: 2,
      colSize: 3,
      sortOrder: 'N',
      width: 150,
    },
    {
      item: 'customer_name',
      header: 'Customer Name',
      isActive: true,
      isVisible: 'true',
      type: 'text1',
      textClass: 'value13Bold colorRed',
      position: 3,
      colSize: 3,
      sortOrder: 'N',
      width: 260,
    },
    {
      item: 'ar_value',
      header: 'Outstanding Value',
      isActive: true,
      isVisible: 'true',
      type: 'currency',
      textClass: 'value13Bold',
      position: 4,
      colSize: 3,
      sortOrder: 'N',
      width: 240,

    },
    {
      item: 'invoices_count',
      header: 'Outstanding Invoices',
      isActive: true,
      isVisible: 'true',
      type: 'text',
      textClass: 'value13Bold',
      position: 5,
      colSize: 3,
      width: 200,
      sortOrder: 'N'
    },
    {
      item: 'dunning_last_sent_on',
      header: 'Last Sent On',
      isActive: true,
      isVisible: 'true',
      type: 'date',
      textClass: 'value13light',
      position: 6,
      colSize: 2,
      width: 150,
      sortOrder: 'N'
    },
    {
      item: 'dunning_status',
      header: 'Status',
      isActive: true,
      isVisible: 'true',
      type: 'status',
      textClass: 'value13Bold',
      position: 7,
      colSize: 3,
      width: 260,
      sortOrder: 'N'
    },
    {
      item: 'credit_period',
      header: 'Credit Period',
      isActive: true,
      isVisible: 'true',
      type: 'text',
      textClass: 'value13Bold',
      position: 8,
      colSize: 2,
      width: 180,
      sortOrder: 'N'
    },
    {
      item: 'region',
      header: 'Region',
      isActive: true,
      isVisible: 'true',
      type: 'text',
      textClass: 'value13Bold',
      position: 8,
      colSize: 2,
      width: 180,
      sortOrder: 'N'
    },
    {
      item: 'action',
      header: 'Actions',
      isActive: true,
      isVisible: 'true',
      type: 'action',
      position: 9,
      colSize: 2,
      width: 240,
      sortOrder: 'N'
    },

  ]

  udrfItemStatusColor: any[] = [
    {
      status: "Updated",
      color: "#ffb142"
    },
    {
      status: "YTS",
      color: "#27A9F3"
    },
    {
      status: "Sent",
      color: "#5CFF5C"
    }
  ]

  dunningTemplate:any;
  result:any;
  invoiceTenantDetails: any;
  multiple_mail_send_applicable: boolean = false;
  isSendMailAuthorized: any;
  constructor(

    public udrfService: UdrfService,
    private errorService: ErrorService,
    private dunningService: DunningService,
    private dialog: MatDialog,
    private _graphService: GraphApiService,
    private _loginService: LoginService,
    public mailUtilityService: MailUtilityService,
    private snackBar: MatSnackBar,
    private spinnerService: NgxSpinnerService,
    private tenantService: TenantService


  ) { }

  async ngOnInit() {

    // Get Collector Role access Data
    this.collectorRoleAccessData = await this.collectorRoleAccess();
    await this.getTenantInfo();
    let isMailAuthorized = await this.getSendMailAuthorization();
    this.isSendMailAuthorized = isMailAuthorized
    if(this.collectorRoleAccessData["messType"] == "E")
    this.snackBar.open(this.collectorRoleAccessData["messText"], "Dismiss")

    this.handleDateRange('current')
    this.initUdrfConfig()
    this.initMailBox()

    this.templateDetails = await this.dunningService.getAllDunningTemplate()

  }

  // Collector Role access
  collectorRoleAccess() {
    return new Promise((resolve, reject) => {
      this.dunningService.getCollectorRoleAccess().subscribe(res => {

        if (res["messType"] == "E") {
          this.snackBar.open(res["messData"], "Dismiss", { duration: 2000 })
        }

        resolve(res);

      }, err => {
        console.error(err);
        reject(err);

      })
    })

  }

  /**
   * Init mail box 
   */
  initMailBox() {

    this.mailUtilityService.mUtilityData.saveRecipientMailIds = this.saveMailIds.bind(this)
    this.mailUtilityService.mUtilityData.applicationId = this.applicationId
    this.mailUtilityService.mUtilityData.isCcRecipientFieldHasSaveButton = true
    this.mailUtilityService.mUtilityData.isBccRecipientFieldHasSaveButton = true
    this.mailUtilityService.mUtilityData.isToRecipientFieldHasSaveButton = true
    this.mailUtilityService.mUtilityData['hasInitiateNewMailTemplate'] = true
    this.mailUtilityService.mUtilityData['formatTableForTemplate'] = true
    this.mailUtilityService.mUtilityData.currentUserMailId = this.user['email']
    this.mailUtilityService.mUtilityData['formatTableForTemplate'] = true
    this.mailUtilityService.mUtilityData['hasInitiateNewMailTemplate'] = true
    this.mailUtilityService.mUtilityData.saveSpocName = this.saveSpocNameForCustomer.bind(this)
  }



  /**
   * Init UDRF
   */
  initUdrfConfig() {

    this.dunningItemDataCurrentIndex = 0;
    this.udrfService.udrfBodyData = []
    this.udrfService.udrfData.applicationId = this.applicationId
    this.udrfService.udrfUiData.showNewReleasesButton = true
    this.udrfService.udrfUiData.showItemDataCount = true
    this.udrfService.udrfUiData.udrfItemStatusColor = this.udrfItemStatusColor
    this.udrfService.udrfUiData.itemDataType = ""
    this.udrfService.udrfUiData.totalItemDataCount = 0
    this.udrfService.udrfUiData.showSearchBar = true
    this.udrfService.udrfUiData.showActionButtons = true
    this.udrfService.udrfUiData.showGroupByButton = true
    this.udrfService.udrfUiData.showUdrfModalButton = true
    this.udrfService.udrfUiData.showSettingsModalButton = false
    this.udrfService.udrfUiData.isReportDownloading = false
    this.udrfService.udrfUiData.showReportDownloadButton = false
    this.udrfService.udrfUiData.showColumnConfigButton = true
    this.udrfService.udrfUiData.summaryCardsItem = {}
    this.udrfService.udrfUiData.showHierarchyData = {}
    this.udrfService.udrfUiData.inlineEditData = {}
    this.udrfService.udrfUiData.downloadItemDataReport = () => { }
    this.udrfService.udrfUiData.openComments = this.openComments.bind(this)
    this.udrfService.udrfUiData.openCommentsData = {}
    this.udrfService.udrfUiData.itemDataScrollDown = this.onCollectionItemDataScrollDown.bind(this)
    this.udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColumns
    this.udrfService.udrfUiData.udrfVisibleBodyColumns = this.udrfService.udrfUiData.udrfVisibleBodyColumns
    this.udrfService.udrfUiData.udrfInvisibleBodyColumns = this.udrfService.udrfUiData.udrfInvisibleBodyColumns
    this.udrfService.udrfUiData.variant = 0;
    this.udrfService.udrfUiData.itemHasQuickCta = false
    this.udrfService.udrfUiData.itemHasComments = true
    this.udrfService.udrfUiData.itemHasHierarchyView = false;
    this.udrfService.udrfUiData.itemHasInfoButton = false;
    this.udrfService.udrfUiData.itemHasMoreActions = false;
    this.udrfService.udrfUiData.showCollapseButton = false;
    this.udrfService.udrfUiData.horizontalScroll = true;
    this.udrfService.getAppUdrfConfig(this.applicationId, this.initReport.bind(this));
    if(this.isSendMailAuthorized){
      this.udrfService.udrfUiData.itemHasSendButton = true;
      this.udrfService.udrfUiData.openSendData = {}
      this.udrfService.udrfUiData.openSend = this.sendSingleDunning.bind(this)
    }
    this.udrfService.udrfUiData.itemHasHistoryButton = true;
    this.udrfService.udrfUiData.openHistoryData = {}
    this.udrfService.udrfUiData.openHistory = this.openHistory.bind(this)


    this.udrfService.udrfUiData.callgroupByData = this.callgroupByData.bind(this);
    this.udrfService.udrfUiData.groupByUiArray = [];
    this.udrfService.udrfUiData.groupByResponseData = [];

    this.udrfService.getNotifyReleasesUDRF();

    // this.udrfService.udrfUiData.ghostButtonUI = false;

  }

  /**
   * Init Report
   */
  async initReport() {

    this._onAppApiCalled.next();
    this.dunningItemDataCurrentIndex = 0;
    this.udrfService.udrfBodyData = [];

    this.udrfService.resolveMyView();

    this.udrfService.udrfUiData.resolveColumnConfig();

    console.log("here", this.udrfService.udrfData.appliedConfig)

    if (this.udrfService.udrfData.appliedConfig["activeView"].length > 0) {
      console.log("one")
      if (this.udrfService.udrfData.appliedConfig["activeView"][0].groupByView) {
        this.udrfService.groupBy = true;
        this.callgroupByData();
      }
    }
    else {
      console.log("two")
      this.getDunningList();
    }


  }

  /**
   * Dunning List API call
   */
  async getDunningList() {

    let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));

    let filterConfig = {
      startIndex: this.dunningItemDataCurrentIndex,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails,
      groupByActive: this.udrfService.groupBy
    };

    if (this.requestDataSubscription)
      this.requestDataSubscription.unsubscribe();


    this.requestDataSubscription = this.dunningService.getDunningList(filterConfig, this.collectorRoleAccessData).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(
        async (res) => {

          console.log('data', res)

          if (
            res['messType'] == 'S' &&
            res['messData'] &&
            res['messData'].length > 0
          ) {

            this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat(res['messData'])

            this.udrfService.udrfUiData.totalItemDataCount = res['total']


          } else {

            this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat([]);
            this.udrfService.udrfUiData.totalItemDataCount = 0
            this.udrfService.udrfData.noItemDataFound = true;

          }


          this.udrfService.udrfData.isItemDataLoading = false;

        }, (err) => {
          this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Invoice data", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        });

  }


  async onCollectionItemDataScrollDown() {

    if (!this.udrfService.udrfData.noItemDataFound) {
      if (!this.udrfService.udrfData.isItemDataLoading) {
        this.dunningItemDataCurrentIndex += this.udrfService.udrfData.defaultRecordsPerFetch;
        this.udrfService.udrfData.isItemDataLoading = true;
        await this.getDunningList();

      }
    }

  }

  callgroupByData() {
    this.filterId = this.udrfService.udrfData.appliedConfig["activeView"][0].groupByViewArray[0].filterId;
    console.log(this.udrfService.udrfUiData.groupByUiArray);
    this.callgroupByItemData();
  }

  callgroupByItemData() {
    let stat = false;
    let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));
    console.log('main filter array', mainFilterArray)
    let tempArray = [], statArray = [];
    for (let mainFilterArrayItem of mainFilterArray) {
      if (mainFilterArrayItem.filterId != this.filterId) {
        stat = true;
      }
      else if (mainFilterArrayItem.filterId == this.filterId) {
        stat = false;
        statArray = mainFilterArrayItem.multiOptionSelectSearchValues;
      }
    }
    if (stat) {
      tempArray = JSON.parse(JSON.stringify(_.where(this.udrfService.udrfData.filterTypeArray, { filterId: this.filterId })));
      mainFilterArray.push(tempArray[0]);

    }


    let iterateData = _.where(this.udrfService.udrfUiData.groupByUiArray, { groupByItemId: this.filterId });

    if (this.udrfService.udrfUiData.scrolled) {
      if (mainFilterArray.length > 0) {
        for (let mainFilterArrayItem of mainFilterArray) {
          if (mainFilterArrayItem.filterId == this.filterId) {
            mainFilterArrayItem.multiOptionSelectSearchValues = [iterateData[0].groupByItemValues[this.udrfService.udrfUiData.scrollIndex].categoryName];
          }
        }
      }
      else {
        mainFilterArray = JSON.parse(JSON.stringify(_.where(this.udrfService.udrfData.filterTypeArray, { filterId: this.filterId })));
        mainFilterArray[0].multiOptionSelectSearchValues = [iterateData[0].groupByItemValues[this.udrfService.udrfUiData.scrollIndex].categoryName];
      }
      let filterConfig = {
        startIndex: this.dunningItemDataCurrentIndex,
        startDate: this.udrfService.udrfData.mainApiDateRangeStart,
        endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
        mainFilterArray: mainFilterArray,
        txTableDetails: this.udrfService.udrfData.txTableDetails,
        mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
        searchTableDetails: this.udrfService.udrfData.searchTableDetails,
        pinFavoriteIdArray: this.udrfService.udrfData.pinFavoriteIdArray,
        bookmarkId: this.udrfService.udrfUiData.bookmarkId,
        isBookmarkLoaded: false
      };
      this.dunningService
        .getDunningList(filterConfig, this.collectorRoleAccessData)
        .pipe(takeUntil(this._onDestroy))
        .pipe(takeUntil(this._onAppApiCalled))
        .subscribe(
          async (res) => {

            if (
              res['messType'] == 'S' &&
              res['messData'] &&
              res['messData'].length > 0
            ) {
              this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat(res['messData']);
              console.log(res['messData']);
              for (let j = 0; j < this.udrfService.udrfUiData.groupByUiArray.length; j++) {
                if (this.udrfService.udrfUiData.groupByUiArray[j].groupByItemId == this.filterId) {
                  this.udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[this.udrfService.udrfUiData.scrollIndex].categoryItems = this.udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[this.udrfService.udrfUiData.scrollIndex].categoryItems.concat(res['messData']);
                }
              }

            }
            else {
              this.udrfService.udrfData.noItemDataFound = true;
            }
            this.udrfService.udrfData.isItemDataLoading = false;
          });



    }
    else {
      for (let i = 0; i < (statArray.length > 0 ? statArray.length : iterateData[0].groupByItemValues.length); i++) {
        if (mainFilterArray.length > 0) {
          for (let mainFilterArrayItem of mainFilterArray) {
            if (mainFilterArrayItem.filterId == this.filterId) {
              console.log(mainFilterArrayItem.multiOptionSelectSearchValues);
              console.log(mainFilterArrayItem.multiOptionSelectSearchValues.length)
              mainFilterArrayItem.multiOptionSelectSearchValues = [(statArray.length > 0 ? statArray[i] : iterateData[0].groupByItemValues[i].categoryName)];
            }
          }
        }
        else {
          mainFilterArray = JSON.parse(JSON.stringify(_.where(this.udrfService.udrfData.filterTypeArray, { filterId: this.filterId })));
          mainFilterArray[0].multiOptionSelectSearchValues = [iterateData[0].groupByItemValues[i].categoryName];
        }
        let filterConfig = {
          startIndex: this.dunningItemDataCurrentIndex,
          startDate: this.udrfService.udrfData.mainApiDateRangeStart,
          endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
          mainFilterArray: mainFilterArray,
          txTableDetails: this.udrfService.udrfData.txTableDetails,
          mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
          searchTableDetails: this.udrfService.udrfData.searchTableDetails,
          pinFavoriteIdArray: this.udrfService.udrfData.pinFavoriteIdArray,
          bookmarkId: this.udrfService.udrfUiData.bookmarkId,
          isBookmarkLoaded: false
        };
        this.dunningService
          .getDunningList(filterConfig, this.collectorRoleAccessData)
          .pipe(takeUntil(this._onDestroy))
          .pipe(takeUntil(this._onAppApiCalled))
          .subscribe(
            async (res) => {

              if (
                res['messType'] == 'S' &&
                res['messData'] &&
                res['messData'].length > 0
              ) {
                this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat(res['messData']);
                for (let j = 0; j < this.udrfService.udrfUiData.groupByUiArray.length; j++) {
                  if (this.udrfService.udrfUiData.groupByUiArray[j].groupByItemId == this.filterId) {
                    this.udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[i].categoryItems = this.udrfService.udrfUiData.groupByUiArray[j].groupByItemValues[i].categoryItems.concat(res['messData']);
                  }
                }

              }
              else {
                this.udrfService.udrfData.noItemDataFound = true;
              }
              this.udrfService.udrfData.isItemDataLoading = false;
            });
      }
    }
    let itemArray = _.where(this.udrfService.udrfUiData.groupByUiArray, { groupByItemId: this.filterId });
    this.udrfService.udrfUiData.groupByResponseData = itemArray[0].groupByItemValues;
    console.log(this.udrfService.udrfUiData.groupByResponseData);
  }

  /**
   * Open comments modal
  */
  async openComments() {
    let inputData
    let itemData = this.udrfService.udrfUiData.openCommentsData['data']

    if (itemData) {
      inputData = {
        application_id: 235,
        unique_id_1: itemData.customer_id,
        unique_id_2: itemData.billing_id,
        application_name: 'Collection management - Dunning tab'
      }

      inputData.title = itemData.customer_name ? itemData.customer_name : '';

      let modalParams = {
        inputData: inputData,
        context:
        {
          'Customer name': itemData.customer_name ? itemData.customer_name : '',
          'Dunning Status': itemData.dunning_status ? itemData.dunning_status : '',
          'Region': itemData.region ? itemData.region : ''
        },
        commentBoxHeight: '100vh',
        commentBoxScrollHeight: '80%'
      };

      const { ChatCommentContextModalComponent }
        = await import('src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component');
      const openChatCommentContextModalComponent = this.dialog.open(ChatCommentContextModalComponent, {
        height: '100%',
        width: '65%',
        position: { right: '0px' },
        data: { modalParams: modalParams }
      });
    }
  }

  /**
   * Date range
   * @param week 
   */
  handleDateRange(week) {
    if (week == 'current') {
      this.startOfWeek = moment().startOf('week').format("DD-MMM-YY")
      this.endOfWeek = moment().endOf('week').format("DD-MMM-YY")
      this.dateRange = this.startOfWeek + " to " + this.endOfWeek
    }

    else if (week == 'previous') {
      this.startOfWeek = moment(this.startOfWeek).subtract(1, 'weeks').startOf('week').format("DD-MMM-YY")
      this.endOfWeek = moment(this.endOfWeek).subtract(1, 'weeks').endOf('week').format("DD-MMM-YY")
      this.dateRange = this.startOfWeek + " to " + this.endOfWeek
    }

    else if (week == 'next') {
      this.startOfWeek = moment(this.startOfWeek).add(1, 'weeks').startOf('week').format("DD-MMM-YY")
      this.endOfWeek = moment(this.endOfWeek).add(1, 'weeks').endOf('week').format("DD-MMM-YY")
      this.dateRange = this.startOfWeek + " to " + this.endOfWeek
    }
  }

  /**
   * Open dunning history modal
   */
  async openHistory() {
    console.log('history opened')
    console.log(this.udrfService.udrfUiData.openHistoryData)
    let customer_id = this.udrfService.udrfUiData.openHistoryData['data'].customer_id
    const { DunningLetterHistoryComponent } = await import('../../components/dunning-letter-history/dunning-letter-history.component');
    const openHistoryComponent = this.dialog.open(DunningLetterHistoryComponent, {
      width: "50%",
      height: "97%",
      maxWidth: "100vw",
      maxHeight: "100vw",
      data: { customer_id },
      disableClose: true
    });
  }

  /**
   * Open mail box for single dunning item
   */
  async sendSingleDunning() {
    let customer_id = this.udrfService.udrfUiData.openSendData['data'].customer_id
    this.spinnerService.show();
    let result: any = await this.dunningAgeingPaymentsForSingle(customer_id);
    if(!result || result.messType == 'E' ){
      this.snackBar.open(result["messText"] || 'Failed to retreive Customer Dunning Data', "Dismiss",{duration:2000})
      this.spinnerService.hide();
      return
    }
    this.spinnerService.hide();
    // Open the template selection dialog
  const dialogRef = this.dialog.open(DunningTemplateSelectionComponent, {
    data:  {templateDetails: this.templateDetails, invoiceDetails: result, type:'single'},
    height : '95%',
    width : '60%',
    position: {
      top: '20px'
    }
  });

  dialogRef.afterClosed().subscribe(async (res) => {
    if (res && res.selectedTemplate && res.selectedRows && res.selectedRows.length > 0) {

      this.initMailBox();
      this.isOpenMailBoxLoading = true;
  
      let dunningTemplate: any = res.selectedTemplate;
      this.dunningTemplate = res.selectedTemplate;
      this.result = result
      result.result = res.selectedRows 
      this.dateFormats = await this.getTenantDateFormats()
      let formattedData = this.dunningService.formatDunningTemplatesDataForMailComponent(result, dunningTemplate, this.dateFormats);
      if (dunningTemplate && dunningTemplate?.table_auto_structure) {
        this.mailUtilityService.mUtilityData['isTableLayoutAuto'] = dunningTemplate?.table_auto_structure == 1 ? true : false;
      } else {
        this.mailUtilityService.mUtilityData['isTableLayoutAuto'] = false;
      }
      this.mailUtilityService.mUtilityData.authorizedMailSenders = _.pluck(
        result.from_mail_id,
        'from_mail_ID'
      );  

      if(result?.send_mail_from_common_id){
        this.mailUtilityService.mUtilityData['fromCommonMailId'] = true
        this.mailUtilityService.mUtilityData.currentUserMailId = result?.common_from_mail_id
        this.mailUtilityService.mUtilityData.sendMailAPIDetails = {
        url:"/api/invoice/v2/sendMailFromCommonId",
        jwtToken:this._loginService.getJwtToken()
      }
      }
      else{
        this.mailUtilityService.mUtilityData.o365Token = { token: (await this._graphService.getO365Token()) }
        this.mailUtilityService.mUtilityData['fromCommonMailId'] = false
      }

      this.mailUtilityService.mUtilityData['newMailTemplateData'].push(formattedData);
      this.mailUtilityService.mUtilityData.currentMailMode = { mode: 'create' };
      this.mailUtilityService.mUtilityData['saveHistoryInTable'] = true
      if (dunningTemplate && dunningTemplate?.is_to_show_spoc) {
        this.mailUtilityService.mailUiData.showSpocName = true
      }
      if (dunningTemplate && dunningTemplate?.is_to_show_title) {
        this.mailUtilityService.mailUiData.showTitle = true
        let customername = _.uniq(_.pluck(result.result, 'customer_name'));
        if (customername) {
          this.mailUtilityService.mailUiData.title = `Mail to ${customername}`
        }
      }
      this.mailUtilityService.mUtilityData.saveHistoryInKebsApiData = {
        url:"/api/collector/dunningEmailDataUpdate",
        jwtToken:this._loginService.getJwtToken(),
        paramsArr:[
          {
            customer_id: customer_id
          }
        ]
      }
      this.isOpenMailBoxLoading = false;


  
      const { ViewMailComponent } = await import('src/app/app-shared/app-shared-components/mail-box-modal/view-mail.component');
      const openViewMailComponent = this.dialog.open(ViewMailComponent, {
        width: "96%",
        height: "97%",
        maxWidth: "100vw",
        maxHeight: "100vw",
        data: {},
        disableClose: true
      });
  
      openViewMailComponent.afterClosed().subscribe(res => {
        this.mailUtilityService.resetMailData();
        window.location.reload();
      });
    }
  });

}

    // this.initMailBox()
    // this.isOpenMailBoxLoading = true
    // let customer_id = this.udrfService.udrfUiData.openSendData['data'].customer_id
    // let result: any = await this.dunningService.dunningAgeingPayments(customer_id);
    // let dunningTemplate: any = await this.dunningService.getDunningTemplate();
    // let formattedData = this.dunningService.formatDunningDataForMailComponent(result, dunningTemplate)

    // // this.mailUtilityService.mUtilityData.authorizedMailSenders = formattedData['authorizedMailSenders']

    // this.mailUtilityService.mUtilityData['newMailTemplateData'].push(formattedData)

    // this.mailUtilityService.mUtilityData.currentMailMode = { mode: 'create' }

    // // this.mailUtilityService.mUtilityData.o365Token = { token: (await this._graphService.getO365Token()) }

    // this.isOpenMailBoxLoading = false

    // const { ViewMailComponent } = await import('mail-box-modal');
    // const openViewMailComponent = this.dialog.open(ViewMailComponent, {
    //   width: "96%",
    //   height: "97%",
    //   maxWidth: "100vw",
    //   maxHeight: "100vw",
    //   data: {},
    //   disableClose: true
    // })

    // openViewMailComponent.afterClosed().subscribe(res => {
    //   this.mailUtilityService.resetMailData()
    // })
  // }

  /**
   * Handle select all check box
   * @param event Object
   */
  selectAllItemData(event) {
    this.udrfService.udrfUiData.isSelectAllItemCheckBox = event.checked;
    this.udrfService.udrfUiData.handleSelectAllCheckBox();
  }

  /**
   * Open mail box for multiple dunning items
   */
  async sendMultipleDunning() {
    const dialogRef = this.dialog.open(DunningTemplateSelectionComponent, {
      data:  {templateDetails: this.templateDetails,type:'multiple'},
      height : '50%',
      width : '20%'
    });
    dialogRef.afterClosed().subscribe(async (selectedTemplate) => {
    if (selectedTemplate) {
    this.initMailBox()
    this.isOpenMailBoxLoading = true

    let result: any;
    let dunningTemplate: any = selectedTemplate
    this.dateFormats = await this.getTenantDateFormats()

    for (let index of this.udrfService.udrfUiData.checkedBodyItemIndex) {
      let customer_id = this.udrfService.udrfBodyData[index].customer_id

      result = await this.dunningService.dunningAgeingPayments(customer_id)

      this.mailUtilityService.mUtilityData['newMailTemplateData']
        .push(this.dunningService.formatDunningTemplatesDataForMailComponent(result, dunningTemplate,this.dateFormats))
    }

    this.mailUtilityService.mUtilityData.authorizedMailSenders = _.pluck(
      result.from_mail_id,
      'from_mail_ID'
    );

    this.mailUtilityService.mUtilityData.currentMailMode = { mode: 'create' }
    this.mailUtilityService.mUtilityData.o365Token = { token: (await this._graphService.getO365Token()) }

    this.mailUtilityService.mUtilityData['formatTableForTemplate'] = true
    if (dunningTemplate && dunningTemplate?.table_auto_structure) {
      this.mailUtilityService.mUtilityData['isTableLayoutAuto'] = dunningTemplate?.table_auto_structure == 1 ? true : false;
    } else {
      this.mailUtilityService.mUtilityData['isTableLayoutAuto'] = false;
    }

    const { ViewMailComponent } = await import('src/app/app-shared/app-shared-components/mail-box-modal/view-mail.component');
    const openViewMailComponent = this.dialog.open(ViewMailComponent, {
      width: "96%",
      height: "97%",
      maxWidth: "100vw",
      maxHeight: "100vw",
      data: {},
      disableClose: true
    });

    openViewMailComponent.afterClosed().subscribe(res => {
      this.mailUtilityService.resetMailData()
      window.location.reload();
    })

    this.isOpenMailBoxLoading = false


  }
    }
  );
}

  /**
   * Save mail recipients - used in mail box
   */
  saveMailIds() {

    let type = this.mailUtilityService.mUtilityData.saveRecipientMailIdType['type']
    let mailFields = this.mailUtilityService.mUtilityData.saveRecipientMailIdType['mailFields']
    let customer_id = this.mailUtilityService.mUtilityData.saveRecipientMailIdType['uniqueId']
    console.log('customer', customer_id)
    let emailIds = []

    if (type == 'toMailId') {
      mailFields.value.toRecipients.length > 0
        ? _.each(mailFields.value.toRecipients, (item) => { emailIds.push(item.name) })
        : emailIds = []
    }
    else if (type == 'ccMailId') {
      mailFields.value.ccRecipients.length > 0
        ? _.each(mailFields.value.ccRecipients, (item) => { emailIds.push(item.name) })
        : emailIds = []
    }
    else if (type == 'bccMailIds') {
      mailFields.value.bccRecipients.length > 0
        ? _.each(mailFields.value.bccRecipients, (item) => { emailIds.push(item.name) })
        : emailIds = []
    }

    this.dunningService
      .saveDunningRecipientEmail(type, emailIds, customer_id)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((res: any) => {
        if (res.messType == 'S') {
          this.snackBar.open(res.userMess, "Dismiss", { duration: 2000 })
        }
        else {
          console.log(res)
        }
      }, (err) => {
        console.log(err)
      })
  }

  dunningAgeingPaymentsForSingle(customer_id) {
    return new Promise((resolve, reject) => {
      this.dunningService.dunningAgeingPaymentsForSingle(customer_id).subscribe(
        (res: any) => {
          this.spinnerService.hide()
          return resolve(res);
        },
        (err) => {
          console.log(err);
          console.log("res err")
          this.spinnerService.hide()
          this.snackBar.open('Failed to retreive Customer Dunning Data', "Dismiss",{duration:2000})
          return reject(err);
        }
      );
    });

  }

  /**
 * Save SPOC Name- used in mail box
 */
  async saveSpocNameForCustomer() {
    this.mailUtilityService.mUtilityData.spocSaveEnabled = true
    let customer_id = this.mailUtilityService.mUtilityData.selectedNewMailTemplateData['uniqueId']
    let spoc_name = this.mailUtilityService.mailUiData.mailInputFields.value.spocName
    this.result['spoc_name'] = this.mailUtilityService.mailUiData.mailInputFields.value.spocName
    this.dunningService
      .saveDunningSpocName(spoc_name, customer_id)
      .pipe(takeUntil(this._onDestroy))
      .subscribe((res: any) => {
        if (res.messType == 'S') {
          this.snackBar.open(res.userMess, "Dismiss", { duration: 2000 })
        }
        else {
          console.log(res)
        }
      }, (err) => {
        console.log(err)
      })

    let body = this.mailUtilityService.mailUiData.mailInputFields.value.body
    body = body.replace(/<a[^>]*>/, '<a>');
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = body;
    const spanElement = tempDiv.querySelector('p > a');
    if (spanElement) {
      spanElement.textContent = this.mailUtilityService.mailUiData.mailInputFields.value.spocName;
    }
    const modifiedBody = tempDiv.innerHTML;

    let formattedData = await this.dunningService.formatDunningTemplatesDataForMailComponent(this.result, this.dunningTemplate, this.dateFormats);
    formattedData['mailBody'] = modifiedBody
    this.mailUtilityService.mUtilityData['newMailTemplateData'] = []
    this.mailUtilityService.mUtilityData['newMailTemplateData'].push(formattedData);

    this.mailUtilityService.mUtilityData.currentMailMode = { mode: 'not selected' };
    this.mailUtilityService.initiateNewMail()
  }
    //Get Tenant Date Formats
    getTenantDateFormats() {
      return new Promise((resolve, reject) => {
        this.dunningService.getTenantDateFormats().subscribe(
          (res: any) => {
            resolve(res.data);
          },
          (err) => {
            console.log(err);
            resolve(err);
          }
        );
      });
  
    }

    async getTenantInfo() {
      this.tenantService.getTenantInfo().then(async (tenantInfo: any) => {
        let tenant = tenantInfo
        this.invoiceTenantDetails = await this.getInvoiceTenantRoleCheckDetail(tenant.tenant_name, 'Role');
        if(this.invoiceTenantDetails){
        let data = this.invoiceTenantDetails['data']
        if(data && data.hasOwnProperty('multiple_mail_send_applicable') && data.multiple_mail_send_applicable){
          this.multiple_mail_send_applicable = true
        }
      }
      },
        err => {
          console.log("cbfRoleCheck func error")
          console.log(err);
        })
    }

      /** 
  * @description getInvoiceTenantRoleCheckDetails
  */
  getInvoiceTenantRoleCheckDetail = (tenantName, checkType) => {
    return new Promise((resolve, reject) => {
      this.dunningService.getInvoiceTenantCheckDetail(tenantName, checkType).subscribe(res => {
        resolve(res);
      },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  };

  ngOnDestroy() {
    this._onDestroy.next()
    this._onDestroy.complete()
    this.udrfService.resetUdrfData();
  }

  getSendMailAuthorization() {
    return new Promise((resolve, reject) => {
      this.dunningService.getSendMailAuthorization().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          resolve(err);
        }
      );
    });
  
  }

}
