<div class="container-fluid billed-invoice-detail px-0">
    <div class="row pt-4 justify-content-center" *ngIf="isLoading">
        <mat-spinner role="progressbar" mode="indeterminate" diameter="30" 
         style="width: 30px; height: 30px;">
        </mat-spinner>
    </div>
    <div class="row">
        <div
            [ngClass]="!isLoading ? 'col-9 px-0 pt-3 content-bg-color': ''">
            <div class="row" *ngIf="!isLoading">
                <div class="col-1 my-auto">
 
                    <button mat-icon-button class="icon-button" (click)="goBack()" matTooltip="Back">
                        <mat-icon class="icon-button">arrow_back</mat-icon>
                    </button>
 
                </div>
                <div class="col-9 px-0 my-auto">
                    <mat-button-toggle-group fxLayoutWrap [disabled]="isComponentLoading" (change)="selectToggle($event)" [value]="selectedToggleId">
                        <mat-button-toggle [disabled]="isComponentLoading || tab.disabled" *ngFor="let tab of tabs" [value]="tab.id" 
                            class="toggle-btn" [ngClass]="{ 'btn-toggle-selected': selectedToggleId == tab.id }">
                            {{ tab.label }}
                        </mat-button-toggle>
                    </mat-button-toggle-group>
                </div>
                <div class="col-1 my-auto">

                   <button *ngIf="(selectedToggleId == 2 && taskListArray.length>0)"
                    mat-icon-button 
                    class="icon-button-inactive slide-in-top" 
                    (click)="createItem(selectedToggleId)"
                    [matTooltip]="selectedToggleId == 2 ? 'Create Task' :''">
                        <mat-icon class="iconButton">add_circle_outline</mat-icon>
                    </button>
                </div>
            </div>
 
            <div class="row pt-4">
                <div class="col-12 px-0 overflow" #scrollFrame>
                    <ng-container #contentContainer></ng-container>
                </div>
            </div>
        </div>
 
        <div class="col-3 pt-3" *ngIf="!isLoading">
            <div class="container-fluid">
                <div (click)="viewInvoice()" class="row slide-in-right">
                    <button [disabled]="isModalLoading" mat-stroked-button class="py-1 side-btn">View Invoice</button>
                </div>
                <div class="row mt-3 slide-in-right">
                    <button [disabled]="isModalLoading" (click)="viewOutstandings()" mat-stroked-button class="py-1 side-btn">View Outstandings</button>
                </div>
                <div (click)="openComments()" class="row mt-3 slide-in-right">
                    <button [disabled]="isModalLoading" mat-stroked-button class="py-1 side-btn">Comments</button>
                </div>
            </div>
 
            <div class="container-fluid mt-4 px-0 slide-in-right">
                <!-- <div class="row side-header">
                    Legal notices sent to {{billedInvoiceDetails?.customer_name}}
                </div>
                <div class="row mt-3 value-font">
                    {{legalCount ? legalCount : 0}}
                </div> -->
                <div class="row px-0">
                    <div class="col-4 cp px-0">
                        <div class="row justify-content-center value-font" 
                        matTooltipPosition = 'above' 
                        [matTooltip]="legalArDetails?.value">{{legalArDetails?.value_formatted}} {{legalArDetails?.value_symbol}}</div>
                        <div class="row justify-content-center side-header-sm">Legal AR</div>
                    </div>
                    <div class="col-4 cp px-0">
                        <div class="row justify-content-center value-font" 
                        matTooltipPosition = 'above' 
                        [matTooltip]="arDetails?.value">{{arDetails?.value_formatted}} {{arDetails?.value_symbol}}</div>
                        <div class="row justify-content-center side-header-sm">AR</div>
                    </div>
                    <div class="col-4 cp px-0">
                        <div class="row justify-content-center value-font">{{legalCount}}</div>
                        <div class="row justify-content-center side-header-sm">Legal Count</div>
                    </div>
                </div>
            </div>
 
            <div class="container-fluid mt-4">
                <div class="side-header" style="text-overflow: ellipsis; overflow: hidden;" [matTooltip]="billedInvoiceDetails?.customer_name">{{billedInvoiceDetails?.customer_name}} Outstandings</div>
                <div class="mt-2" id="chartDiv"></div>
            </div>

        </div>
    </div>

</div>
 
 

