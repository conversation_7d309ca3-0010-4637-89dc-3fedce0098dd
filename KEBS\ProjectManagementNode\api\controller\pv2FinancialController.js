const logger = require("../../logger").logger;

//services
const errorService = require("../utils/pv2ErrorService");
const financialService = require("../services/pv2FinancialService");
const utilityService = require("../utils/pv2UtilityService");
//dataLayer
const moment = require("moment");
const financialData = require("../dataLayer/pv2FinancialData");
const settingData = require("../dataLayer/pv2SettingData");
const axiosService = require("../services/pv2AxiosService");
const utilService = require("./../utils/pv2UtilityService")
//=========================================================

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Project Financial Information 
 * <AUTHOR>
 * @returns 
 */
module.exports.getProjectFinancialInfo = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id = req.body.project_id;
        let item_id = req.body.item_id;
        let date = req.body.date || moment().format();
        await errorService.checkDatabase(db);

        let result = await financialService.getProjectFinancialInfo(project_id, item_id, date, db, req.user, req.headers.authorization)

        res.json(result)

 

    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while retrieving Project Financial Information"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Insert Costing Header
 * <AUTHOR> Raam Baskar
 * @version 1.0
 * @returns 
 */
module.exports.insertCostingHeader = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id = req.body.project_id;
        let item_id = req.body.item_id;
        let data = req.body.costingHeaderForm;
        let date = req.body.date || moment().format();
        await errorService.checkDatabase(db);

        let result = await financialService.insertCostingHeader(project_id, item_id, data, date, db, req.user, req.headers.authorization);
        
        res.json(result)

    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", error: err, message:"Error while inserting Costing Header Details"})
    }
}



/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Fetch Project Currency Conversion Rate
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.fetchProjectCurrency = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id = req.body.project_id;
        let item_id = req.body.item_id;
        let curreny_code = req.body.currency_code || "USD";
        let date = req.body.date || moment().format();
        await errorService.checkDatabase(db);

        let result = await utilityService.fetchProjectCurrency(project_id, item_id, curreny_code, date, db);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while fetching Project Currency Conversion Rate!"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Project Costing Header
 * <AUTHOR> Raam Baskar
 */
module.exports.getProjectCostingHeader = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id = req.body.project_id;
        let item_id = req.body.item_id;
        let date = req.body.date || moment().format();
        await errorService.checkDatabase(db);

        let result = await financialService.getProjectCostingHeader(project_id, item_id, date, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while fetching Project Costing Headers!"})
    }
}
 /**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description insert Position
 * <AUTHOR> K Vijay
 */
module.exports.insertPosition = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let data = req.body.data;
        let project_id=req.body.project_id
        let item_id=req.body.item_id
        let costing_sheet_id=req.body.costing_sheet_id
        let code=req.body.code
        await errorService.checkDatabase(db);

        let result = await financialData.insertPosition(project_id, item_id, data,costing_sheet_id,code, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while inserting position!"})
    }
}
 /**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description costing header creation
 * <AUTHOR> K Vijay
 */
 module.exports.createCostingSheetHeader = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let data = req.body.data;
        let name=req.body.name
        await errorService.checkDatabase(db);

        let result = await financialData.createCostingSheetHeader(data,name, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while inserting costing sheet header!"})
    }
}
 /**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description get particular costing sheet header
 * <AUTHOR> K Vijay
 */
 module.exports.getProjectCostingHeaderForid = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let item_id=req.body.item_id
        let costing_sheet_id=req.body.costing_sheet_id
        await errorService.checkDatabase(db);

        let result = await financialData.getProjectCostingHeaderForid(project_id, item_id,costing_sheet_id, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while getting particular costing sheet header!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description get direct cost details
 * <AUTHOR> K Vijay
 */
module.exports.getDirectCostDetails = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let item_id=req.body.item_id
        let costing_sheet_id=req.body.costing_sheet_id
        let quote=req.body.quote
        let code=req.body.code
        await errorService.checkDatabase(db);

        let result = await financialService.getDirectCostDetails(project_id, item_id,costing_sheet_id,quote,code, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while getting particular costing sheet header!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Save Costing sheet
 * <AUTHOR> K Vijay
 */
module.exports.SaveCostingSheet = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let data=req.body.data
        let id=req.body.id
        let project_id=req.body.project_id
        let item_id=req.body.item_id
        let header=req.body.header
        let project_name=req.body.project_name
        let quote=req.body.quote
        let code=req.body.code
        await errorService.checkDatabase(db);

        let result = await financialService.SaveCostingSheet(data,id,project_id,item_id,header,project_name,quote,code, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while saving costing sheet!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description get In direct cost details
 * <AUTHOR> K Vijay
 */
module.exports.getInDirectCostDetails = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let item_id=req.body.item_id
        let costing_sheet_id=req.body.costing_sheet_id
        await errorService.checkDatabase(db);

        let result = await financialService.getInDirectCostDetails(project_id, item_id,costing_sheet_id, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while getting particular IN direct cost details!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description get all Costing sheet
 * <AUTHOR> K Vijay
 */
module.exports.getCostingSheet = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let item_id=req.body.item_id
        await errorService.checkDatabase(db);

        let result = await financialData.getCostingSheet(project_id,item_id, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while get costing sheet!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description get costing quote data
 * <AUTHOR> K Vijay
 */
module.exports.getQuoteCostingData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let item_id=req.body.item_id
        let costing_sheet_id=req.body.costing_sheet_id
        let code =req.body.code
        await errorService.checkDatabase(db);

        let result = await financialService.getQuoteCostingData(project_id, item_id,costing_sheet_id,code, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while getting quote costing data!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description get quote data
 * <AUTHOR> K Vijay
 */
module.exports.getQuoteDetails = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let id=req.body.id
        await errorService.checkDatabase(db);

        let result = await financialData.getQuoteDetails(id, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while getting quote data!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description get project cost
 * <AUTHOR> K Vijay
 */
module.exports.getProjectCost = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let item_id=req.body.item_id
        let costing_sheet_id=req.body.costing_sheet_id
        await errorService.checkDatabase(db);

        let result = await financialService.getProjectCost(project_id, item_id,costing_sheet_id, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while getting project cost!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description release DCS
 * <AUTHOR> K Vijay
 */
module.exports.releaseDCS = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let item_id=req.body.item_id
        let costing_sheet_id=req.body.costing_sheet_id
        await errorService.checkDatabase(db);

        let result = await financialData.releaseDCS(project_id, item_id,costing_sheet_id, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while release dcs!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description deleteCostingSheet
 * <AUTHOR> K Vijay
 */
module.exports.deleteCostingSheet = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let data=req.body.data
        await errorService.checkDatabase(db);

        let result = await financialData.deleteCostingSheet(data, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while delete costing sheet!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description delete position
 * <AUTHOR> K Vijay
 */
module.exports.deletePosition = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let id=req.body.id
        await errorService.checkDatabase(db);

        let result = await financialData.deletePosition(id, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while delete position!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description get resource name
 * <AUTHOR> K Vijay
 */
module.exports.getResourceName = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let aid=req.body.aid
        let current_date=req.body.current_date
        await errorService.checkDatabase(db);

        let result = await financialService.getResourceName(aid,current_date, db, req.user);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json({messType:"E", error: err, message:"Error while get resource name!"})
    }
}

/**
 * 
 * 
 * @description Milestone Type List
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getMilestoneTypeList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        await errorService.checkDatabase(db);

        let result = await financialService.getMilestoneTypeList(db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching Milestone Type List"})
    }
}
/**
 * 
 * 
 * @description revert Milestone from ytb
 * @param milestone_id
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.revertMilestoneFromYTB = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let milestone_id = req.body.milestone_id
        await errorService.checkDatabase(db);

        let result = await financialData.revertMilestoneFromYTB(milestone_id, db)

        if(result['messType']=="S")
        {
            axiosService.revenueReversal(milestone_id, db, req.headers.authorization)
        }
        //settingData.updateProjectTimeTrackerBillableHours(db, req.user, req.headers.authorization)

        let old_data = {Status: "YTB"}
        let new_data = {Status: "Execution"}
        let item_id = await financialData.getProjectItemIdBasedOnMilestone(milestone_id, db)

        if(old_data && new_data){
            new_data.milestone_id = milestone_id;
              let edit_log_data = {
                source: 84,
                old_data: old_data,
                new_data: new_data,
                action_type: 'UPDATE',
                item_id:item_id             
              }
              utilService.inserEditLog(db, req.user, req.headers.authorization,edit_log_data);
        }

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching Milestone Type List"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Conversion Rate Common
 * <AUTHOR> Raam Baskar
 */
module.exports.getConversionRatesCommon = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let currency_from = req.body.currency_from;
        let currency_to = req.body.currency_to;
        let conversionTypeId = req.body.conversion_type_id;
        let date = moment().format("YYYY-MM-DD")

        let result = await utilityService.getConversionRatesCommon(date, currency_from, currency_to, conversionTypeId, db)

        res.json(result)
    }   
    catch(err){
        logger.info(err)
        res.json(err)
    }

}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Fetch Billing Advice Data
 * <AUTHOR> K Vijay
 */
module.exports.fetchBillingAdviceData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let milestoneId = req.body.milestoneId;
        let date = moment().format("YYYY-MM-DD")

        let result = await financialService.fetchBillingAdviceData(milestoneId, db)
        res.json(result)
    }   
    catch(err){
        logger.info(err)
        res.json(err)
    }

}
 /**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description getAllocatedHoursForEmployeeWithDates
 * <AUTHOR> K Vijay
 */
module.exports.getAllocatedHoursForEmployeeWithDates = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let associate_id = req.body.associate_id;
        let start_date = req.body.start_date;
        let end_date = req.body.end_date;

        let result = await financialData.getAllocatedHoursForEmployeeWithDates(associate_id,start_date,end_date, db)

        res.json(result)
    }   
    catch(err){
        logger.info(err)
        res.json(err)
    }

}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Fetch Billing Advice Data
 * <AUTHOR> K Vijay
 */
module.exports.getRPAProjectFTEDetails = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let milestoneId = req.body.milestoneId;
        let date = moment().format("YYYY-MM-DD")

        let result = await financialService.getRPAProjectInvoiceDetails(milestoneId, db);
        
        res.json(result)
    }   
    catch(err){
        logger.info(err)
        res.json(err)
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res  
 * @description getEndDateForEmployeeWithDuration
 * <AUTHOR> K Vijay
 */
module.exports.getEndDateForEmployeeWithDuration = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let associate_id = req.body.associate_id;
        let start_date = req.body.start_date;
        let duration = req.body.duration;
        let project_end_date=req.body.project_end_date
        let current_date=moment().format('YYYY-MM-DD')
        let result = await financialData.getEndDateForEmployeeWithDuration(associate_id,start_date,duration,current_date,project_end_date, db)

        res.json(result)
    }   
    catch(err){
        logger.info(err)
        res.json(err)
    }

}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description getPositionCummulativeAlloctaedHours
 * <AUTHOR> Aparna V
 */
module.exports.getPositionCummulativeAlloctaedHours = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let position_id = req.body.position_id;
        let isa_id = req.body.isa_id || null;

        let result = await financialData.getPositionCummulativeAlloctaedHours(db,position_id,isa_id)

        res.json(result)
    }   
    catch(err){
        logger.info(err)
        res.json(err)
    }
}
/**
 * 
 * 
 * @description getQuoteAndQuotePositionForResourceLoading
 * @param project_id
 * @param item_id
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getQuoteAndQuotePositionForResourceLoading = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let project_item_id=req.body.project_item_id
        await errorService.checkDatabase(db);

        let result = await financialService.getQuoteAndQuotePositionForResourceLoading(project_id,project_item_id,quote_id,db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching getQuoteAndQuotePositionForResourceLoading"})
    }
}
/**
 * 
 * 
 * @description getIsaData
 * @param project_id
 * @param item_id
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getIsaData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let project_item_id=req.body.project_item_id
        let billing=req.body.billing
        await errorService.checkDatabase(db);

        let result = await financialService.getIsaData(project_id,project_item_id,billing,db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching getQuoteAndQuotePositionForResourceLoading"})
    }
}
/**
 * 
 * 
 * @description getRateCardPositionForResourceLoading
 * @param project_id
 * @param item_id
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getRateCardPositionForResourceLoading = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let project_item_id=req.body.project_item_id
        await errorService.checkDatabase(db);

        let result = await financialService.getRateCardPositionForResourceLoading(project_id,project_item_id,db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching getRateCardPositionForResourceLoading"})
    }
}
/**
 * 
 * 
 * @description saveResourceLoadingData
 * @param data
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.saveResourceLoadingData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let data=req.body.data
        let project_id=req.body.project_id
        let project_item_id=req.body.project_item_id
        let quote_id=req.body.quote_id
        let version_id=req.body.version_id
        let status_id=req.body.status_id
        let action_id=req.body.action_id
        await errorService.checkDatabase(db);

        let result = await financialService.saveResourceLoadingData(project_id,project_item_id,data,quote_id,version_id,status_id,action_id,db, req.user,req.headers.authorization)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching saveResourceLoadingData"})
    }
}
/**
 * 
 * 
 * @description getResourceLoadingData
 * @param data
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getResourceLoadingData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let project_item_id=req.body.project_item_id
        let quote_id=req.body.quote_id
        let monthList = req.body.monthList ? req.body.monthList : []
        await errorService.checkDatabase(db);

        let result = await financialService.getResourceLoadingData(project_id,project_item_id,quote_id,monthList,db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching getResourceLoadingData"})
    }
}
/**
 * 
 * 
 * @description getQuoteDetailsForResourceLoading
 * @param data
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getQuoteDetailsForResourceLoading = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let project_item_id=req.body.project_item_id
        let quote_id=req.body.quote_id
        await errorService.checkDatabase(db);

        let result = await financialService.getQuoteDetailsForResourceLoading(project_id,project_item_id,quote_id,db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching getQuoteDetailsForResourceLoading"})
    }
}
/**
 * 
 * 
 * @description getResourceLoadingHeaderVersion
 * @param data
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getResourceLoadingHeaderVersion = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_id=req.body.project_id
        let project_item_id=req.body.project_item_id
        let quote_id=req.body.quote_id
        await errorService.checkDatabase(db);

        let result = await financialService.getResourceLoadingHeaderVersion(project_id,project_item_id,quote_id,db, req.user)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching getResourceLoadingHeaderVersion"})
    }
}

/**
 * @description loadResourceLoadingFromQuote
 * @param {*} req 
 * @param {*} res 
 * <AUTHOR> Aparna V
 */
module.exports.loadResourceLoadingFromQuote = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_start_date = req.body.startDate ? moment(req.body.startDate).format('YYYY-MM-DD') : null
        let project_end_date = req.body.endDate ? moment(req.body.endDate).format('YYYY-MM-DD') : null
        let quote_id = req.body.quote_id
        await errorService.checkDatabase(db);

        let result = await financialService.loadResourceLoadingFromQuote(project_start_date,project_end_date,quote_id,db, req.user)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching loadResourceLoadingFromQuote"})
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Insert Billing Plan Data
 * <AUTHOR> Raam Baskar
 * 
 */
module.exports.insertBillingPlanData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let data=req.body.data
        let project_id=req.body.project_id
        let project_item_id=req.body.project_item_id
        let quote_id=req.body.quote_id;
        let status_id=req.body.status_id
        let action_id=req.body.action_id
        let resource_type_id = req.body.resource_type_id;
        let deliverable_id = req.body.deliverable_id;

        await errorService.checkDatabase(db);

        let insertBillingPlanData = await financialService.insertBillingPlanData(data, project_id, project_item_id, quote_id, resource_type_id, deliverable_id, status_id, action_id, db, req.user, req.headers.authorization);

        res.json(insertBillingPlanData)


    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while inserting Billing Plan Data!"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Import Billing Plan From Quote
 * <AUTHOR> Raam Baskar
 */
module.exports.importBillingPlanFromQuote = async( req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let project_id = req.body.project_id;
        let project_item_id = req.body.project_item_id;

        await errorService.checkDatabase(db);

        let importQuoteData = await financialService.importBillingPlanFromQuote(project_id, project_item_id, db, req.user, req.headers.authorization);

        res.json(importQuoteData)


    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while importing Billing Plan Quote"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description GetBilling Plan Version History
 * <AUTHOR> Raam Baskar
 */
module.exports.getBillingPlanVersionHistory = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let project_id = req.body.project_id;
        let project_item_id = req.body.project_item_id;
        let quote_id = req.body.quote_id;
        let deliverable_id = req.body.deliverable_id;
        let resource_type_id=  req.body.resource_type_id;

        await errorService.checkDatabase(db);

        let result = await financialService.getBillingPlanVersionHistory(project_id, project_item_id, quote_id, deliverable_id, resource_type_id, db, req.user, req.headers.authorization);

        res.json({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Billing Plan Version History"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Quote Deatils For Billing Plan
 * <AUTHOR> Raam Baskar
 */
module.exports.getQuoteDetailsForBillingPlan = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let project_id = req.body.project_id;
        let project_item_id = req.body.project_item_id;
        let quote_id = req.body.quote_id;
        let resource_type_id = req.body.resource_type_id;

        await errorService.checkDatabase(db);

        let result = await financialService.getQuoteDetailsForBillingPlan(project_id, project_item_id, quote_id, resource_type_id, db, req.user, req.headers.authorization);

        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.json({messType:"E",error: err, message:"Error while getting Quote Details for Billing Plan"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Billing Plan Data
 * <AUTHOR> Raam Baskar
 */
module.exports.getBillingPlanData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let project_id = req.body.project_id;
        let project_item_id = req.body.project_item_id;
        let quote_id = req.body.quote_id;
        let resource_type_id = req.body.resource_type_id;
        let deliverable_id = req.body.deliverable_id;
        let monthList = req.body.monthList;
        let decimal_places = req.body?.decimal_places || 2;

        await errorService.checkDatabase(db);

        let result = await financialService.getBillingPlanData(project_id, project_item_id, quote_id, resource_type_id, deliverable_id, monthList, db, req.user, req.headers.authorization,decimal_places)
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Billing Plan Data"})
    }
}

/**
 * @description getProjectFinancialValues
 * @param {*} req 
 * @param {*} res 
 * <AUTHOR> Rajendran
 */
module.exports.getProjectFinancialValues = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let project_item_id = req.body.projectItemID ? req.body.projectItemID : null;
        await errorService.checkDatabase(db);

        if(!project_item_id)
            res.json({messType:"E",message:"Project details not found"});

        let result = await financialService.getProjectFinancialValues(project_item_id,db, req.user,req.headers.authorization)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching Project Financial Values"})
        
    }
}

/**
 * @description Get Billing Advice Version History
 * <AUTHOR> Raam Baskar
 */
module.exports.getBillingAdviceVersionHistory= async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        
        let milestone_id = req.body.milestone_id;

        await errorService.checkDatabase(db);

        let result = await financialData.getBillingAdviceVersionHistory(milestone_id, db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        
        res.json({messType:"E", error: err, message:"Error while getting Billing Advice Version History!"})
       
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Milestone Matrix
 * <AUTHOR> Raam Baskar
 */
module.exports.getMilestoneMatrix = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        await errorService.checkDatabase(db);

        let result = await financialData.getMilestoneMatrix(db);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Milestone Matrix!"})
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Milestone Matrix
 * <AUTHOR> Raam Baskar
 */
module.exports.getMilestoneMatrix = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        await errorService.checkDatabase(db);

        let result = await financialData.getMilestoneMatrix(db);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Milestone Matrix!"})
    }
}

/**
 * @description Get Bill data
 * @param {*} req 
 * @param {*} res 
 */
module.exports.getBillData = async (req, res) => {
    try {
        let db = req.user.uad || req.body.db_name || null;
        let project_id = req.body.project_id;
        let item_id = req.body.item_id;
        let sort = req.body.sort ? req.body.sort : 'DESC'
        await errorService.checkDatabase(db);

        let data = await financialData.getBillData(project_id, item_id, db,sort, req.user, req.headers.authorization)

        res.json(data)
    }
    catch (err) {
        logger.info(err)
        res.json({ messType: "E", error: err })
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Update Invoice Date
 * <AUTHOR> Raam Baskar

 */
module.exports.updateInvoiceDate = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let milestone_id = req.body.milestone_id;
        let invoice_date = req.body.invoice_date;
        let old_data = req.body.oldInvoiceDate;
        let new_data = req.body.newInvoiceDate;
        let project_item_id = req.body.project_item_id;
        await errorService.checkDatabase(db);

        let data = await financialData.updateInvoiceDate(milestone_id, invoice_date,old_data, new_data, db, req.user, req.headers.authorization)

        if(data['messType'] == "S"){
            if(old_data && new_data){
                new_data.milestone_id = milestone_id;
                  let edit_log_data = {
                    source: 28,
                    old_data: old_data,
                    new_data: new_data,
                    action_type: 'UPDATE',
                    item_id:project_item_id             
                  }
                  utilityService.inserEditLog(db,req.user, req.headers.authorization,edit_log_data);
            }
        }
        res.json(data)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Validate YTB Data
 * <AUTHOR> Raam Baskar
 */
module.exports.validateYTBData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        

        await errorService.checkDatabase(db);


        let project_id = req.body.project_id;
        let item_id = req.body.item_id;
        let value = req.body.value;
        let milestone_id = req.body.milestone_id;
        let milestone_type = req.body.milestone_type;

        let data = await financialService.validateYTBData(project_id, item_id, milestone_id, milestone_type, value, db, req.user, req.headers.authorization)

        res.json({messType :'S', data:data})
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err})
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Validate YTB Data
 * <AUTHOR> Raam Baskar
 */
module.exports.validateAccruedData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        

        await errorService.checkDatabase(db);


        let project_id = req.body.project_id;
        let item_id = req.body.item_id;
        let value = req.body.value;
        let milestone_id = req.body.milestone_id;

        let data = await financialService.validateAccruedData(project_id, item_id, milestone_id, value, db, req.user, req.headers.authorization)

        res.json({messType :'S', data:data})
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err})
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description 
 */
module.exports.getBillingAdviceBillingPlanData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        

        await errorService.checkDatabase(db);


        let project_id = req.body.project_id;
        let item_id = req.body.project_item_id;
        let quote_id = req.body.quote_id;

        let data = await financialService.getBillingAdviceBillingPlanData(project_id, item_id, quote_id, db, req.user, req.headers.authorization)

        res.json(data)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err})
    }
}

/**
 *  
 * @param {*} req 
 * @param {*} res 
 * @description create milestone bills
 * <AUTHOR> Raam
 */
module.exports.createMilestone = async (req, res) => {
    try {
        let db = req.user.uad || req.body.db_name || null;
        let milestone_data = req.body.milestone_data || null;
        
        if (!milestone_data) {
            res.json({ messType: "E", message: "Payload is not Passed !" });
        }

        if(milestone_data['action_type']=="Expense" || milestone_data['action_type']=="P2P")
        {
            let result = await financialService.createMilestone(milestone_data, db, req.user);
            
            res.json(result);
        }
        else
        {
            res.json({messType:"E", message:"Action Type is required! Please provide Expense or P2P"})
        }

        
    } catch (err) {
        logger.error(err);
        res.json({ messType: "E", error: err, message: "Error while creating milestone!" });
    }
}
module.exports.getPoMasterData = async (req,res) => {
	try {
		const db = req.user.uad || null;
		const user_aid = req.user.aid;
        let projectId=req.body.project_id
        let ItemID=req.body.project_item_id

		let result = await financialService.getPoMasterData(
			user_aid,
			db,
            projectId,
            ItemID

		);
		res.status(200).send({err:false,data:result.data});
	}
	catch(err) {
	logger.info(err);
    console.log(err);
    res.status(500).send({
      err: "Y",
      code: "PRJPOER",
      errMessage: {
        error: err,
        msg: err.message,
        stack: err.stack,
      },
    });
	}
}
module.exports.deletePoMaster = async (req,res) => {
	try {
		const db = req.user.uad || null;
		const user_aid = req.user.aid;
        let recordId=req.body.recordid
   

		let result = await financialService.deletePoMaster(
			user_aid,
			db,
            recordId,
            req.user,
            req.headers.authorization
		);
		// Determine status based on result code
        if (result.code === 'S') {
            res.status(200).send({ err: false, messType:"S",msg: result.message, data: result.data });
        } else if (result.code === 'W') {
            res.status(200).send({ err: false, messType:"W",code: 'PRJPOER_W', msg: result.message });
        } else {
            res.status(404).send({ err: true, messType:"E",code: 'PRJPOER_E', msg: result.message });
        }
	}
	catch(err) {
	logger.info(err);
    console.log(err);
    res.status(500).send({
      err: "Y",
      code: "PRJPOER",
      errMessage: {
        error: err,
        msg: err.message,
        stack: err.stack,
      },
    });
	}
}


module.exports.getProjectCurrency = async (req,res) => {
	try {
		const db = req.user.uad || null;
		const user_aid = req.user.aid;
        let projectId=req.body.project_id
        let ItemID=req.body.project_item_id

		let result = await financialService.getProjectCurrency(
			user_aid,
			db,
            projectId,
            ItemID

		);
		res.status(200).send({err:false,data:result.data});
	}
	catch(err) {
	logger.info(err);
    console.log(err);
    res.status(500).send({
      err: "Y",
      code: "PRJPOER",
      errMessage: {
        error: err,
        msg: err.message,
        stack: err.stack,
      },
    });
	}
}


module.exports.insertPoMaster = async (req, res) => {
    try {
        const db = req.user.uad || null;
        const user_aid = req.user.aid;
        const details = req.body.details;
        const project_id = req.body.project_id;
        const project_item_id = req.body.project_item_id;
  
        let result = await financialService.insertPoMaster(
            user_aid,
            db,
            details,
            project_id,
            project_item_id,
            req.user,
            req.headers.authorization
		);
        // Check the response type from the service function
        if (result.code === 'W') {
            res.status(200).send({
                err: false,
                message: result.message || "Operation completed with warnings",
                messType: 'W',
                data: result.data
                });
            } else {
                res.status(200).send({
                    err: false,
                    message: "PO Master Details successfully",
                    messType: 'S',
                    data: result.data
                });
            }
	}
	catch(err) {
	logger.info(err);
    console.log(err);
    res.status(500).send({
      err:true,
      code: "PRJPOER",
      messType :'E',
      errMessage: {
        error: err,
        msg: err.message,
        stack: err.stack,
      },
    });
	}
}


module.exports.updatePoMaster = async (req, res) => {
    try {
      const db = req.user.uad || null;
      const user_aid = req.user.aid;
      const details = req.body.details;
      const project_id = req.body.project_id;
      const project_item_id = req.body.project_item_id;
      const record_id = req.body.record_id;
      const old_data = req.body.oldData;
      const new_data = req.body.newData;
  
      let result = await financialService.updatePoMaster(
        user_aid,
        db,
        details,
        project_id,
        project_item_id,
        record_id,
        old_data,
        new_data,
        req.user,
        req.headers.authorization

      );
  
      if (result.code === 'S') {
        res.status(200).send({ err: false, message: result.message, messType: 'S' });
      } else if (result.code === 'W') {
        res.status(200).send({ err: false, message: result.message, messType: 'W' });
      } else {
        res.status(404).send({ err: true, message: result.message, messType: 'E' });
      }
    } catch (err) {
      logger.info(err);
      console.log(err);
      res.status(500).send({
        err: true,
        code: "PRJPOER",
        messType: 'E',
        errMessage: {
          error: err,
          msg: err.message,
          stack: err.stack,
        },
      });
    }
  };
  



module.exports.checkPoNumberExists= async (req,res) => {
	try {
		const db = req.user.uad || null;
		const user_aid = req.user.aid;
        let project_id=req.body.project;
        let project_item_id=req.body.item
        let po_number=req.body.ponumber

		let result = await financialService.checkPoNumberExists(
			user_aid,
			db,
            project_id,
            project_item_id,
            po_number
		);
		if (result?.data && result.data?.length >0) {
            res.json({ messType: 'S', data: result.data });
          } else {
            res.json({ messType: 'F', data: [] });
          }
	}
	catch(err) {
	logger.info(err);
    console.log(err);
    res.status(500).send({
      err:true,
      code: "PRJPOER",
      messType :'E',
      errMessage: {
        error: err,
        msg: err.message,
        stack: err.stack,
      },
    });
	}
}


module.exports.getPoMastercheckData = async (req,res) => {
	try {
		const db = req.user.uad || null;
		const user_aid = req.user.aid;
        let projectId=req.body.project_id
        let ItemID=req.body.project_item_id

		let result = await financialService.getPoMastercheckData(
			user_aid,
			db,
            projectId,
            ItemID

		);
		res.status(200).send({err:false,data:result.data});
	}
	catch(err) {
	logger.info(err);
    console.log(err);
    res.status(500).send({
      err: "Y",
      code: "PRJPOER",
      errMessage: {
        error: err,
        msg: err.message,
        stack: err.stack,
      },
    });
	}
}

module.exports.checkmilestonestatus = async (req, res) => {
    try {
      const db = req.user.uad || null;
      const user_aid = req.user.aid;
      const project_id = req.body.project_id;
      const project_item_id = req.body.project_item_id;
      const po_number=req.body.po_number;
  
      let result = await financialService.checkmilestonestatus(
        db,
        user_aid, 
        project_id,
        project_item_id,
        po_number
      );
  
      if (result.code === 'S') {
        res.status(200).send({ err: false,disable:result.disable, msg: result.message, messType: 'S' });
      } else if (result.code === 'W') {
        res.status(200).send({ err: false,disable:result.disable, msg: result.message, messType: 'W' });
      } else {
        res.status(404).send({ err: true,disable:result.disable, msg: result.message, messType: 'E' });
      }
    } catch (err) {
      logger.info(err);
      console.log(err);
      res.status(500).send({
        err: true,
        code: "PRJPOER",
        messType: 'E',
        errMessage: {
          error: err,
          msg: err.message,
          stack: err.stack,
        },
      });
    }
  }
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description  For Fetching the Remaining Hours Data
 * <AUTHOR> Aparna V
 */
module.exports.getPositionRemainingLogData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        

        await errorService.checkDatabase(db);


        let project_item_id =  req.body.project_item_id;
        let quote_position_id = req.body.quote_position_id;
        let start = req.body.start;
        let limit = req.body.limit;

        let data = await financialService.getPositionRemainingLogData(db,project_item_id,quote_position_id,start,limit)

        res.json(data)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Convert Billing Advice Currency
 * <AUTHOR> Raam Baskar
 */
module.exports.getUpdatedBillingValue = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        

        await errorService.checkDatabase(db);


        let project_id = req.body.projectID;
        let project_item_id = req.body.ItemID;
        let milestone_id = req.body.milestoneID;
        let value = req.body.value;
        let date = req.body.date;
        let currency_code = req.body.currency_code;

        let result = await financialData.getUpdatedBillingValue(project_id, project_item_id, milestone_id, value, currency_code, date, db, req.user, req.headers.authorization);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json('[]')
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Check Changing Actual Efforts Milestone
 * <AUTHOR> Raam Baskar
 * 
 */
module.exports.checkChangingActualEffortsMilestone = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
    
        await errorService.checkDatabase(db);

        let project_id = req.body.project_id;
        let project_item_id = req.body.project_item_id;

        let validate = await financialService.checkChangingActualEffortsMilestone(project_id, project_item_id, db, req.user, req.headers.authorization);

        res.json(validate)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while validating the status change!"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get General Planned Value
 * <AUTHOR> Raam Baskar
 */
module.exports.getGeneralPlannedValue = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
    
        await errorService.checkDatabase(db);

        let start_date = req.body.start_date;
        let end_date = req.body.end_date;
        let project_type = req.body.project_type;
        let project_status = req.body.project_status;
        let project_flag = req.body.project_flag;
        let milestone_type = req.body.milestone_type;
        let milestone_status = req.body.milestone_status;
        let currency = req.body.currency;
    

        let result = await financialService.getGeneralPlannedValue(start_date, end_date, project_type, project_status, project_flag, milestone_type, milestone_status, currency, db, req.user, req.headers.authorization)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Planned Value for Revenue Report!"})
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Project Planned Value
 * <AUTHOR> Raam Baskar
 */
module.exports.getProjectPlannedValue = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
    
        await errorService.checkDatabase(db);

        let start_date = req.body.start_date;
        let end_date = req.body.end_date;
        let project_type = req.body.project_type;
        let project_status = req.body.project_status;
        let project_flag = req.body.project_flag;
        let milestone_type = req.body.milestone_type;
        let milestone_status = req.body.milestone_status;
        let currency = req.body.currency;
    

        let result = await financialService.getProjectPlannedValue(start_date, end_date, project_type, project_status, project_flag, milestone_type, milestone_status, currency, db, req.user, req.headers.authorization)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Planned Value for Revenue Report!"})
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Create Partial Invoice Milestones
 * <AUTHOR> Raam Baskar
 */
module.exports.createPartialInvoiceMilestone = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        
       
        let partialInvoiceData = req.body.partialInvoiceData;
        let parent_milestone_id = req.body.parent_milestone_id;
        let writeOffInvoiceData = req.body.writeOffInvoiceData
        let status_id = req.body.statusId;
        

        await errorService.checkDatabase(db);

        let result = await financialService.createPartialInvoiceMilestone( partialInvoiceData, parent_milestone_id, status_id, db, req.user, req.headers.authorization);

        if(result['messType']=="S")
        {
            let writeOff = await financialService.createWriteoffMilestone( writeOffInvoiceData, parent_milestone_id, status_id, db, req.user, req.headers.authorization);

            if(writeOff['messType']=="S")
            {
                let writeoff_milestone_id = writeOff['writeoff_milestone_id']
                let partial_milestone_id = result['partial_milestone_id']
                result['writeoff_milestone_id'] = writeOff['writeoff_milestone_id'];
                result['writeoffMilestoneDetails'] = writeOff['milestoneDetails']

                let update = await financialService.updateWriteOffMilestoneId(writeoff_milestone_id, partial_milestone_id, db, req.user, req.headers.authorization);


            }
        }

        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while creating Partial Invoice Milestones!"})
    }
}


/**
 * @param {*} req 
 * @param {*} res 
 * @description Get General Planned Value
 * <AUTHOR> Raam Baskar
 */
module.exports.getGeneralPlannedValue = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
    
        await errorService.checkDatabase(db);

        let start_date = req.body.start_date;
        let end_date = req.body.end_date;
        let project_type = req.body.project_type;
        let project_status = req.body.project_status;
        let project_flag = req.body.project_flag;
        let milestone_type = req.body.milestone_type;
        let milestone_status = req.body.milestone_status;
        let currency = req.body.currency;
        let report_id = req.body.report_id;
    

        let result = await financialService.getGeneralPlannedValue(start_date, end_date, project_type, project_status, project_flag, milestone_type, milestone_status, currency, db, req.user, req.headers.authorization, report_id)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Planned Value for Revenue Report!"})
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Create Credit Note Invoice Milestones
 * <AUTHOR> Raam Baskar
 */
module.exports.createCreditNoteInvoiceMilestone = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        
       
        let creditNoteInvoiceData = req.body.creditNoteInvoiceData;
        let parent_milestone_id = req.body.parent_milestone_id;
        let invoiceList= req.body.invoiceList;
        let value_config = req.body.value_config;
        let status_id = req.body.milestoneStatus;
        

        await errorService.checkDatabase(db);

        let result = await financialService.createCreditNoteMilestone( creditNoteInvoiceData, parent_milestone_id, invoiceList, status_id, value_config, db, req.user, req.headers.authorization);
        

        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while creating Partial Invoice Milestones!"})
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Trigger approval for hours deviation
 * <AUTHOR> Rajendran
 */
module.exports.triggerApprovalForHoursDeviation = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
        let project_id = req.body.projectId;
        let item_id = req.body.itemId;
        let requested_status = req.body.requestedStatus;
        let milestone_id = req.body.milestoneId
        let milestone_status_id = req.body.milestoneStatusId;
        let approval_group_id = req.body.approvalGroupId
    
        let result = await financialService.triggerApprovalForHoursDeviation(db, project_id, item_id, requested_status, milestone_id, milestone_status_id, approval_group_id,req.user, req.headers.authorization)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Fetch workflow details based on source id
 * <AUTHOR> Rajendran
 */
module.exports.getWorkflowDetailsBasedOnSource = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
        let project_id = req.body.projectId;
        let item_id = req.body.itemId;
        let source_id = req.body.sourceId
    
        let result = await financialService.getWorkflowDetailsBasedOnSource(db, project_id, item_id, source_id,req.user, req.headers.authorization)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Update Project Workflow request
 * <AUTHOR> Rajendran
 */
module.exports.updateProjectWorkflowRequest = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
        let approval_type = req.body.approvalType
        let workflow_header_id = req.body.workflowHeaderId;
        let comments = req.body.comments;
    
        let result = await financialService.updateProjectWorkflowRequest(db, approval_type, workflow_header_id, comments, req.user, req.headers.authorization)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Trigger approval for hours deviation
 * <AUTHOR> Rajendran
 */
module.exports.triggerApprovalForValueDeviation = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
        let project_id = req.body.projectId;
        let item_id = req.body.itemId;
        let milestone_id = req.body.milestoneId;
        let old_billing_value = req.body.oldBillingValue;
        let new_billing_value = req.body.newBillingValue;
        let milestone_details = req.body.milestoneDetails;
        let workflow_group_id = req.body.approvalGroupId
    
        let result = await financialService.triggerApprovalForValueDeviation(db, project_id, item_id,  milestone_id, old_billing_value, new_billing_value, milestone_details, workflow_group_id,req.user, req.headers.authorization)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}



/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Fetch workflow details for projects
 * <AUTHOR> Rajendran
 */
module.exports.getProjectWorkflowDetails = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
        let search_params = req.body.searchParams ? req.body.searchParams : null;
        let sort = req.body.sort ? req.body.sort : null;
        let filter_query = req.body.filterQuery ? req.body.filterQuery : null;
        let skip = req.body.skip
        let limit = req.body.limit
    
        let result = await financialService.getProjectWorkflowDetails(db,search_params, sort, filter_query, skip, limit,req.user, req.headers.authorization)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Fetch workflow details for projects
 * <AUTHOR> Rajendran
 */
module.exports.getProjectWorkflowDetailsCount = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
        let search_params = req.body.searchParams ? req.body.searchParams : null;
        let filter_query = req.body.filterQuery ? req.body.filterQuery : null;
    
        let result = await financialData.getProjectWorkflowDetailsCount(db, search_params, filter_query,req.user, req.headers.authorization)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description check opportuity has banket po
 * <AUTHOR> Rajendran
 */
module.exports.checkOpportunityBlanketPO = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
        let project_id = req.body.projectId;
        let item_id = req.body.itemId
        let quote_id = req.body.quoteId;
    
        let result = await financialData.checkOpportunityBlanketPO(db, project_id, item_id,quote_id,req.user, req.headers.authorization)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Update Project Workflow request
 * <AUTHOR> Rajendran
 */
module.exports.updateInboxProjectWorkflowRequest = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
        let approval_type = req.body.status ? req.body.status : null;
        let workflow_header_id = req.body.selectedDetails[0]?.workflow_header_id ? req.body.selectedDetails[0]?.workflow_header_id : null;
        let comments = req.body.comments ? req.body.comments : null;
    
        let result = await financialService.updateProjectWorkflowRequest(db, approval_type, workflow_header_id, comments, req.user, req.headers.authorization)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}
/** 
 * <AUTHOR> Raam Baskar
 * @description Get Fixed Billing Plan Quote Details
 * @description 
 */
module.exports.getFixedBillingPlanQuoteDetails = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        

        await errorService.checkDatabase(db);


        let project_id = req.body.project_id;
        let item_id = req.body.item_id;
        let quote_id = req.body.quote_id;

        let data = await financialService.getFixedBillingPlanQuoteDetails(project_id, item_id, quote_id, db, req.user, req.headers.authorization)

        res.json(data)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 */
module.exports.insertFixedBillingPlanDeliverable = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        

        await errorService.checkDatabase(db);


        let formData = req.body.formData;
        let project_id = req.body.project_id;
        let item_id = req.body.item_id;
        let quote_id = req.body.quote_id;

        let data = await financialService.insertFixedBillingPlanDeliverable(project_id, item_id, quote_id, formData, db, req.user, req.headers.authorization)

        res.json(data)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err})
    }
}

/**
 * @description Get Project Planned Value
 * <AUTHOR> Raam Baskar
 */
module.exports.getProjectPlannedValue = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
    
        await errorService.checkDatabase(db);

        let start_date = req.body.start_date;
        let end_date = req.body.end_date;
        let project_type = req.body.project_type;
        let project_status = req.body.project_status;
        let project_flag = req.body.project_flag;
        let milestone_type = req.body.milestone_type;
        let milestone_status = req.body.milestone_status;
        let currency = req.body.currency;
        let report_id = req.body.report_id
    

        let result = await financialService.getProjectPlannedValue(start_date, end_date, project_type, project_status, project_flag, milestone_type, milestone_status, currency, db, req.user, req.headers.authorization, report_id)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Planned Value for Revenue Report!"})
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Insert Fixed Billing Plan Data
 * <AUTHOR> Raam Baskar
 */
module.exports.insertFixedBillingPlanData = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let data=req.body.data
        let project_id=req.body.project_id
        let project_item_id=req.body.project_item_id
        let quote_id=req.body.quote_id;
        

        await errorService.checkDatabase(db);

        let insertBillingPlanData = await financialService.insertFixedBillingPlanDeliverable(project_id, project_item_id, quote_id, data, db, req.user, req.headers.authorization);

        res.json(insertBillingPlanData)
    }
    catch(err){
        logger.info(err)
        res.json()
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description GetBilling Plan Version History
 * <AUTHOR> Raam Baskar
 */
module.exports.getFixedBillingPlanVersionHistory = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let project_id = req.body.project_id;
        let project_item_id = req.body.project_item_id;


        await errorService.checkDatabase(db);

        let result = await financialService.getFixedBillingPlanVersionHistory(project_id, project_item_id, db, req.user, req.headers.authorization);

        res.json({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Billing Plan Version History"})
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description GetBilling Plan Version History
 * <AUTHOR> Raam Baskar
 */
module.exports.mergeBillingAdviceDetails = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let project_id = req.body.project_id;
        let project_item_id = req.body.project_item_id;
        let parent_milestone_id = req.body.parent_milestone_id;
        let milestone_array = req.body.milestone_array;


        let result = await financialService.mergeBillingAdviceDetails(project_id, project_item_id,parent_milestone_id, milestone_array,db, req.user, req.headers.authorization);

        res.json({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Billing Plan Version History"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Save Attach Depdencies
 */
module.exports.saveAttachDependencies = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let deliverable_id = req.body.deliverable_id;
        let line_item_ids=  req.body.line_item_ids;


        await errorService.checkDatabase(db);

        let result = await financialData.saveAttachDependencies(deliverable_id, line_item_ids, db, req.user, req.headers.authorization);

        res.json({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while saving Attach dependecies"})
    }
}

/*
 * @description GetBilling Plan Version History
 * <AUTHOR> Raam Baskar
 */
module.exports.getApproversDetails = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let project_id = req.body.projectId;
        let project_item_id = req.body.itemId;
        let workflow_group_id = req.body.approvalGroupId;

        let result = await financialService.getApproversDetails(db, workflow_group_id, project_id, project_item_id, req.user, req.headers.authorization);
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Approver Details"})
    }
}

/**
 * @description Reverse the Accrued Milestone to Reverse Accrued
 * @param milestone_id
 * <AUTHOR> Raam
 * @returns 
 */
module.exports.moveMilestoneToReverseAccrued = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let milestone_id = req.body.milestone_id
        await errorService.checkDatabase(db);

        let result = await financialData.moveMilestoneToReverseAccrued(milestone_id,db)

        if(result['messType']=="S")
        {
            axiosService.updateUBROnMilestoneCancellation(db, milestone_id,  req.headers.authorization)

            let old_data = {Status: "Accrual"}
            let new_data = {Status: "Accrual Reversed"}
            let item_id = await financialData.getProjectItemIdBasedOnMilestone(milestone_id, db)

            if(old_data && new_data){
                new_data.milestone_id = milestone_id;
                let edit_log_data = {
                    source: 84,
                    old_data: old_data,
                    new_data: new_data,
                    action_type: 'UPDATE',
                    item_id:item_id             
                }
                utilService.inserEditLog(db,req.user, req.headers.authorization,edit_log_data);
            }
        }
        //settingData.updateProjectTimeTrackerBillableHours(db, req.user, req.headers.authorization)


        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while getting Billing Plan Version History"})
    }
}



/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Save Attach Depdencies
 */
module.exports.resourceTypeConfigQuote = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;

        let project_item_id = req.body.project_item_id;
        let quote_id = req.body.quote_id;


        await errorService.checkDatabase(db);

        let result = await financialService.resourceTypeConfigQuote(project_item_id, quote_id, db, req.user, req.headers.authorization);

        
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error:err,message:"Error while fetching Milestone Type List"})
    }
}

/**
 * 
 * 
 * @description revert Milestone from ytb
 * @param milestone_id
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.revertMilestoneFromAccrual = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let milestone_id = req.body.milestone_id
        await errorService.checkDatabase(db);

        let result = await financialData.revertMilestoneFromAccrual(milestone_id,db)

        if(result['messType']=="S")
        {
            axiosService.updateUBROnMilestoneCancellation(db, milestone_id, req.headers.authorization)

            let old_data = {Status: "Accrual"}
            let new_data = {Status: "Execution"}
            let item_id = await financialData.getProjectItemIdBasedOnMilestone(milestone_id, db)

            if(old_data && new_data){
                new_data.milestone_id = milestone_id;
                let edit_log_data = {
                    source: 84,
                    old_data: old_data,
                    new_data: new_data,
                    action_type: 'UPDATE',
                    item_id:item_id             
                }
                utilService.inserEditLog(db, req.user, req.headers.authorization,edit_log_data);
            }
        }
        //settingData.updateProjectTimeTrackerBillableHours(db, req.user, req.headers.authorization)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while saving Attach dependecies"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Trigger approval for credit note creation
 * <AUTHOR> Rajendran
 */
module.exports.triggerApprovalForCreditNoteCreation = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
        let project_id = req.body.projectId;
        let item_id = req.body.itemId;
        let requested_status = req.body.requestedStatus;
        let milestone_id = req.body.milestoneId
        let milestone_status_id = req.body.milestoneStatusId;
        let approval_group_id = req.body.approvalGroupId;
        let milestone_details = req.body.milestoneDetails
    
        let result = await financialService.triggerApprovalForCreditNoteCreation(db, project_id, item_id, requested_status, milestone_id, milestone_status_id, approval_group_id, milestone_details,req.user, req.headers.authorization)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}
        

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Check Previous Milestone Status
 * <AUTHOR> Raam Baskar
 */
module.exports.checkPreviousMilestoneStatus = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
       
        let milestone_id = req.body.milestone_id;

    
        let result = await financialService.checkPreviousMilestoneStatus(milestone_id, db, req.user, req.headers.authorization)

        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/*
 * @description Create Credit Note Invoice Milestones for Bills
 * <AUTHOR> Raam Baskar
 */
module.exports.createCreditNoteInvoiceMilestoneForBills = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        
       
        let creditNoteInvoiceData = req.body.creditNoteInvoiceData;
        let parent_milestone_id = req.body.parent_milestone_id;
        let invoiceList= req.body.invoiceList;
        let value_config = req.body.value_config;
        let status_id = req.body.milestoneStatus;
        let is_bill =  req.body.is_bill;
        

        await errorService.checkDatabase(db);

        let result = await financialService.createCreditNoteInvoiceMilestoneForBills( creditNoteInvoiceData, parent_milestone_id, invoiceList, status_id, value_config,is_bill, db, req.user, req.headers.authorization);
        

        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while creating Partial Invoice Milestones for Bills!"})
    }
}

module.exports.checkActualEffortsMilestone = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
    
        await errorService.checkDatabase(db);

        let project_id = req.body.project_id;
        let project_item_id=req.body.project_item_id
        let quote_id = req.body.quote_id
        let milestone_id=req.body.milestone_id

        let validate = await financialService.checkActualEffortsMilestone(project_id,project_item_id, quote_id,db, req.user, req.headers.authorization,milestone_id);

        res.json(validate)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while validating the status change!"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Acquire Forecast Sub Division Report
 * <AUTHOR> Raam Baskar
 */
module.exports.acquireForecastSubDivisionReport = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
     
        await errorService.checkDatabase(db);
  
        let start_date = req.body.start_date;
        let end_date = req.body.end_date;
        let project_type = req.body.project_type;
        let project_status = req.body.project_status;
       
  
        let result = await financialService.acquireForecastSubDivisionReport(start_date, end_date, project_type, project_status, db, req.user, req.headers.authorization);
  
        res.json(result)
  
  
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", error: err, message:"Error while forecasting Sub Division Report"})
    }
  }
  
/**
 * 
  * @description Check Revenue = Billed during Project Completion
  * <AUTHOR> Raam Baskar
  */
 module.exports.checkRevenueBilledEqual = async(req, res)=>{
     try{
         let db = req.user.uad || req.body.db_name || null;
     
         await errorService.checkDatabase(db);
 
         let project_item_id = req.body.project_item_id
         let project_id = req.body.project_id;
 
         let validate = await financialService.checkRevenueBilledEqual(project_id, project_item_id ,db, req.user, req.headers.authorization);
 
         res.json(validate)
     }
     catch(err){
         logger.info(err)
         res.json({messType})
     }
 }

 module.exports.getExpenseData = async (req, res) => {
    try {
        let db = req.user.uad || req.body.db_name || null;
        let project_id = req.body.project_id;
        let item_id = req.body.item_id;
        
        await errorService.checkDatabase(db);

        let data = await financialData.getExpenseData(project_id, item_id, db, req.user, req.headers.authorization);

        res.json(data);
    }
    catch (err) {
        logger.info(err);
        res.json({ messType: "E", error: err });
    }
};