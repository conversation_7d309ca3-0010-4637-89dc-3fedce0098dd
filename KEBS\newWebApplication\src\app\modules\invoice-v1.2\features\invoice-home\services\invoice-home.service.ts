import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RolesService } from 'src/app/services/acl/roles.service';
import * as _ from "underscore";
import * as moment from 'moment';
import { MailUtilityService } from 'src/app/app-shared/app-shared-components/mail-box-modal/services/mail-utility.service';
@Injectable({
  providedIn: 'root'
})
export class InvoiceHomeService {

  constructor(
    private http:HttpClient,
    private rolesService: RolesService,
    public mailUtilityService: MailUtilityService,
  ) { }

  roleCheck() {
    return this.http.post("api/invoice/roleCheck", { });
  }
  getYtbMilestones(orgCodes) {
    return this.http.post("/api/invoice/v2/getYtbMilestones", {
      orgCodes: orgCodes,
    });
  }
  paymentReversal(milestoneId) {
    return this.http.post("api/invoice/paymentReversal", { milestoneId: milestoneId });
  }
  getBilledMilestones(orgCodes) {
    return this.http.post("/api/invoice/v2/getBilledMilestones", {
      orgCodes: orgCodes,
    });
  }
  getPartialPaymentMilestones(orgCodes) {
    return this.http.post("/api/invoice/v2/getPartialPaymentMilestones", {
      orgCodes: orgCodes,
    });
  }
  getPaymentReceivedMilestones(orgCodes) {
    return this.http.post("/api/invoice/v2/getPaymentReceivedMilestones", {
      orgCodes: orgCodes,
    });
  }

  viewInvoice(billingId) {
    return this.http.post("api/invoice/viewInvoice", {
      billingId: billingId,
    });
  }

  viewAnnexure(billingId,serviceTypeId) {
    return this.http.post("api/invoice/viewInvoiceAnnexure", {
      billingId: billingId,
      serviceTypeId: serviceTypeId,
    });
  }
  undoInvoice(milestoneId, billingId) {
    return this.http.post("api/invoice/v2/undoInvoice", {
      milestoneId: milestoneId,
      billingId: billingId,
    });
  }
  invoiceUndoAccess() {

    let accessList = _.where(this.rolesService.roles, { application_id: 907, object_id: 320 });

    if (accessList.length > 0)
      return true;

    else
      return false;
  }

  getMailTemplate = (milestone_type, curentlyActiveScreen) => {
    return new Promise((resolve, reject) => {
      this.http.post("/api/invoice/v2/getMailTemplate", {
        milestone_type : milestone_type,
        curentlyActiveScreen : curentlyActiveScreen
      })
        .subscribe((res) => {
          return resolve(res);
        }, (err) => {
          return reject(err)
        })
    })
  }

  getCustomerBillingDetails = (billingID) => {
    return new Promise((resolve, reject) => {
      this.http.post("/api/invoice/getCustomerBillingDetails", {
        billing_id : billingID
      })
        .subscribe((res) => {
          return resolve(res);
        }, (err) => {
          return reject(err)
        })
    })
  }

  formatDataForMailComponent = (data: any, dunningTemplate: any , dateFormats: any) => {

    let result = {}
    let fromMailIds
    let toMailIds
    let ccMailIds
    let bccMailIds
    let alertFlag
    let customerName
    let currency
    let bodyText = dunningTemplate[0].template.body_text;
    let text = ''
    let finalText = dunningTemplate[0].template.final_text;
    let currentMonthYear = moment().format("MMM 'YY");
    let mailContentDateFormat = dateFormats?.invoice_mail_date_format ? dateFormats.invoice_mail_date_format : 'DD-MM-YYYY';
    let finalTodaysDate =  moment().format(mailContentDateFormat);
    let subjectTxt = dunningTemplate[0].template?.subject_text ? dunningTemplate[0].template?.subject_text :'Invoice details as on ' + finalTodaysDate + ' for your reference.';
    let defaultSpocName = dunningTemplate[0]?.default_spoc_name
    let spocName = data.spoc_name
    let invoiceNo = data.result[0].invoiceNo? data.result[0].invoiceNo : "";
    let milestoneName = data.result[0].milestoneName? data.result[0].milestoneName: "";

    console.log("invoiceNo")
    console.log(invoiceNo)
    console.log(data)
    console.log(milestoneName)

    spocName = this.mailUtilityService.mailUiData.mailInputFields?.value?.spocName ? this.mailUtilityService.mailUiData.mailInputFields?.value?.spocName : spocName
    if (bodyText?.includes('[SPOC]')) {
      if(spocName != null){
        bodyText = bodyText.replace('[SPOC]', spocName);
      }
      else{
        bodyText = bodyText.replace('[SPOC]', defaultSpocName);
      }
    }
    let invoiceDateFormat;
    let getInvoiceDate;
    let invoiceMonthFormat;
    let invoiceYearFormat;

    fromMailIds = _.pluck(data.from_mail_id, 'from_mail_ID');

    toMailIds = data.to_mail_id ? data.to_mail_id : [];

    ccMailIds = data.cc ? data.cc : [];


    bccMailIds = data.bcc ? data.bcc : [];

    customerName = _.uniq(_.pluck(data.result, 'customer_name'));

    currency = _.uniq(_.pluck(data.result, 'currency'));

    invoiceDateFormat = _.uniq(_.pluck(data.result, 'invoiceDate'));

    invoiceDateFormat = moment(invoiceDateFormat,"MM/DD/YYYY").format("MMM 'YY");
    
    getInvoiceDate  = _.uniq(_.pluck(data.result, 'invoiceDate'));

    console.log("getInvoiceDate")
    console.log(getInvoiceDate)

    invoiceMonthFormat = moment(getInvoiceDate, "MM/DD/YYYY").format("MMMM");

    console.log("invoiceMonthFormat")
    console.log(invoiceMonthFormat)

    invoiceYearFormat = moment(getInvoiceDate, "MM/DD/YYYY").format("YYYY");

    console.log("invoiceMonthFormat")
    console.log(invoiceMonthFormat)


    let invoiceDate = moment(_.uniq(_.pluck(data.result, 'invoiceDate')),"MM/DD/YYYY").format(mailContentDateFormat);
    let currentDate = moment().format(mailContentDateFormat);



    if (subjectTxt?.includes('[CURRENTMONTHYEAR]')) {
      subjectTxt = subjectTxt.replace('[CURRENTMONTHYEAR]', currentMonthYear);
    }
    
    if (bodyText?.includes('[CURRENTMONTHYEAR]')) {
      bodyText = bodyText.replace('[CURRENTMONTHYEAR]', currentMonthYear);
    }

    if (bodyText?.includes('[invoiceNo]')) {
      bodyText = bodyText.replace('[invoiceNo]', invoiceNo);
    }

    if (bodyText?.includes('[milestoneName]')) {
      bodyText = bodyText.replace('[milestoneName]', milestoneName);
    }
        
    if (subjectTxt?.includes('[INVOICEMONTHYEAR]')) {
      subjectTxt = subjectTxt.replace('[INVOICEMONTHYEAR]', invoiceDateFormat);
    }

         
    if (subjectTxt?.includes('[INVOICEMONTH]')) {
      subjectTxt = subjectTxt.replace('[INVOICEMONTH]', invoiceMonthFormat);
    }
         
    if (subjectTxt?.includes('[INVOICEYEAR]')) {
      subjectTxt = subjectTxt.replace('[INVOICEYEAR]', invoiceYearFormat);
    }
    
    if (bodyText?.includes('[INVOICEMONTHYEAR]')) {
      bodyText = bodyText.replace('[INVOICEMONTHYEAR]', invoiceDateFormat);
    }

    if (subjectTxt?.includes('[INVOICEDATE]')) {
      subjectTxt = subjectTxt.replace('[INVOICEDATE]', invoiceDate);
    }
    
    if (bodyText?.includes('[INVOICEDATE]')) {
      bodyText = bodyText.replace('[INVOICEDATE]', invoiceDate);
    }

    if (subjectTxt?.includes('[CURRENTDATE]')) {
      subjectTxt = subjectTxt.replace('[CURRENTDATE]', currentDate);
    }
    
    if (bodyText?.includes('[CURRENTDATE]')) {
      bodyText = bodyText.replace('[CURRENTDATE]', currentDate);
    }

    let currencySymbol = _.uniq(_.pluck(data.result, 'currency_symbol'));



    let risk = data.result
    console.log(risk)
    console.log(risk.projectName)

    let config = JSON.parse(dunningTemplate[0].table_config)
    text +=
    "<table border='1' style='border-collapse: collapse !important; width: 100% !important; table-layout: auto !important;'>";
  text += '<tr>';
    // Generate table header based on the configuration
    config.forEach((column) => {
      if (column.is_active && column.key != 'totalAmount') {
        const width = `width='${column.width}'`;
        text += `<th ${width} style='text-align:center;'>${column.description}</th>`;
      }
      else if(column.is_active && column.key == 'totalAmount'){
        const width = `width='${column.width}'`;
        text += `<th ${width} style='text-align:center;'>${column.description} - ${currencySymbol || currency}</th>`;
      }
    });

    text += '</tr>';

  
  // Generate table data based on the configuration
  _.each(data.result, (val) => {
    text += '<tr>';
    config.forEach((column) => {
      if (column.is_active && column.is_to_format == 0) {
        const width = `width='${column.width}'`;
        text += `<td ${width} style='text-align:center; font-weight:bold;'>${val[column.key]}</td>`;
      }
      if(column.is_active && column.is_to_format == 1){
      const width = `width='${column.width}'`;
      text += `<td ${width} style='text-align:center; font-weight:bold;'>${column.format ? moment(val[column.key]).format(column.format) : val[column.key]}</td>`;
      }
    });

    text += '</tr>';
  });

    // text +=
    //   "<table border='1' style='width:1000px !important; border-collapse: collapse !important;'>";
    // text += "<tr>";
    // text += "<th width='200' style='text-align:center;'>Project</th>";
    // text +=
    //   "<th width='200' style='text-align:center;'>Milestone Name</th>";
    // text +=
    //   "<th width='100' style='text-align:center;'>Invoice Date</th>";
    // text +=
    //   "<th width='100' style='text-align:center;'>Invoice Number</th>";
    // text +=
    //   "<th width='100' style='text-align:center;'>Value in " +
    //   currency +
    //   '</th>';

    // text += '</tr>';

    // let val = data.result[0]

    // // _.each(data.result, (val) => {
    //   text += "<tr>";
    //   text +=
    //     "<td width='200' style='text-align:center; font-weight:bold;'>" +
    //     val.projectName +
    //     '</td>';
    //   text +=
    //     "<td width='200' style='text-align:center; font-weight:bold;'>" +
    //     val.workSummary[0].description        +
    //     '</td>';
    //   text +=
    //     "<td width='100' style='text-align:center; font-weight:bold;'>" +
    //     moment(val.invoiceDate).format("DD-MM-YYYY") +
    //     '</td>';
    //   text +=
    //     "<td width='100' style='text-align:center; font-weight:bold;'>" +
    //     val.invoiceNo +
    //     '</td>';
    //   text +=
    //     "<td width='100' style='text-align:center; font-weight:bold;'>" +
    //     val.totalAmount +
    //     '</td>';

    //   text += '</tr>';
    // // });

    // text += "<tr>";

    text += '</table><br/>';

    let mailBody = bodyText + text + finalText
    result = {

      fromMailIds: fromMailIds,
      toMailIds: toMailIds,
      ccMailIds: ccMailIds,
      authorizedMailSenders: fromMailIds,
      uniqueId: data.result[0].customerId,
      uniqueName: customerName,
      bccMailIds: bccMailIds,
      subjectTxt: subjectTxt,
      mailBody: mailBody,
      spocName : spocName
    }

    return result

  }

  intercompanyTabAccess() {

    let accessList = _.where(this.rolesService.roles, { application_id: 907, object_id: 29898});
  
    if (accessList.length > 0)
    {
  
      if (accessList[0].object_value == '*' ||accessList[0].object_value == 'True'){
        return true;
      }
  
      else if (accessList[0].object_value == 'User' && accessList[0].object_id_values &&
      (accessList[0].object_id_values == 'ALL' ||
        JSON.parse(accessList[0].object_id_values)[0] == 'ALL' ||
        _.contains(
          JSON.parse(accessList[0].object_id_values),
          10
        ))){
          return true;
        }
        
        else return false;
        
    }
     
    else
      return false;
  }

  // Filter 

getYtbMilestonesV2(orgCodes, filterConfig) {
  return this.http.post("/api/invoice/v2/getYtbMilestones", {
    orgCodes: orgCodes,
    filterConfig: filterConfig
  });
}

  getAttachmentDetails = (milestoneId) => {
    return new Promise((resolve, reject) => {
      this.http.post("/api/invoice/getAttachmentDetails", {
        milestone_id : milestoneId
      })
        .subscribe((res) => {
          return resolve(res);
        }, (err) => {
          return reject(err)
        })
    })
  }


getBilledMilestonesV2(orgCodes, filterConfig) {
  return this.http.post("/api/invoice/v2/getBilledMilestones", {
    orgCodes: orgCodes,
    filterConfig: filterConfig
  });
}

getPartialPaymentMilestonesV2(orgCodes, filterConfig) {
  return this.http.post("/api/invoice/v2/getPartialPaymentMilestones", {
    orgCodes: orgCodes,
    filterConfig: filterConfig
  });
}
getPaymentReceivedMilestonesV2(orgCodes, filterConfig) {
  return this.http.post("/api/invoice/v2/getPaymentReceivedMilestones", {
    orgCodes: orgCodes,
    filterConfig: filterConfig
  });
}

markInvoiceAsSent(billingId) {
  return this.http.post("api/invoice/v2/markInvoiceAsSent", {
    billingId: billingId,
  });
}

getInvoiceConfig() {
  return new Promise((resolve, reject) => {
    this.http
      .post("/api/invoice/getInvoiceConfigDetails", { })
      .subscribe((res) => {
        console.log(res);
        return resolve(res);
      });
  });
}

retrieveUploadedObjects = (destination_bucket, context_id) => {
  return this.http.post('/api/exPrimary/retrieveUploadedObjects', {
    bucket_name: destination_bucket,
    context_id: context_id,
  });
};

paymentReversalForCreditNote(milestoneId) {
  return this.http.post("api/invoice/v2/paymentReversalForCreditNote", { milestoneId: milestoneId });
}

getCommentStatus(milestone_id) {
  return new Promise((resolve, reject) => {
    this.http
      .post("/api/invoice/v2/getCommentStatus", { milestoneId: milestone_id})
      .subscribe((res) => {
        console.log(res);
        return resolve(res);
      });
  });
}
getSendMailAuthorization = () =>{
  return this.http.post("/api/invoice/v2/getSendMailAuthorization",{});
}


}




