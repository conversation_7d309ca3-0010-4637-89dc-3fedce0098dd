<div class="container-fluid invoice-info" *ngIf="billedInvoiceDetails">
    <div class="container slide-in-top" style="background-color: #FFF9F9;padding: 25px;">
        <div class="row main-header">
            Details
        </div>
        <div class="row mt-4">
            <div class="col-lg-6 col-sm-12 px-0 pr-3">
                <div class="row py-2">
                    <div class="col-6 px-0 side-header">
                        Invoice Raised On
                    </div>
                    <div class="col-3 px-0 field-value">
                        {{billedInvoiceDetails?.billed_on ? getDate(billedInvoiceDetails.billed_on) : '-'}}
                    </div>
                    <div *ngIf="billedInvoiceDetails?.aging_days > billedInvoiceDetails?.CP" 
                        class="col-3 px-0" 
                        style="color: #cf0001;font-weight: 100;">
                        ({{this.overdue_label}})
                    </div>
                </div>
                <div class="row py-2">
                    <div class="col-6 px-0 side-header" *ngIf="invoiceVersion2">
                        Portfolio Name
                    </div>
                    <div class="col-6 px-0 side-header" *ngIf="!invoiceVersion2">
                        Project Name
                    </div>
                    <div class="col-6 px-0 field-value">
                        {{billedInvoiceDetails?.project_name ? (billedInvoiceDetails.project_name) : '-'}}
                    </div>
                </div>
                <div class="row py-2">
                    <div class="col-6 px-0 side-header">
                        Milestone Name
                    </div>
                    <div class="col-6 px-0 field-value">
                        {{billedInvoiceDetails?.milestone_name ? (billedInvoiceDetails.milestone_name) : '-'}}
                    </div>
                </div>
                <div class="row py-2">
                    <div class="col-6 px-0 side-header">
                        Customer
                    </div>
                    <div class="col-6 px-0 field-value" style="text-overflow: ellipsis; overflow: hidden;" [matTooltip]="billedInvoiceDetails?.customer_name">
                        {{billedInvoiceDetails?.customer_name ? (billedInvoiceDetails.customer_name) : '-'}}
                    </div>
                </div>
                <div class="row py-2">
                    <div class="col-6 px-0 side-header">
                        Payment Terms
                    </div>
                    <div class="col-6 px-0 field-value">
                        {{billedInvoiceDetails?.CP || billedInvoiceDetails?.CP ==0 ? (billedInvoiceDetails.CP) : '-'}} days
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-sm-12 px-0">
                <div class="row py-2">
                    <div class="col-6 px-0 side-header">
                        AR Value 
                    </div>
                    <div 
                    class="col-6 px-0 field-value-highlighted"
                    [matTooltip]="billedInvoiceDetails?.ar_value">
                        {{billedInvoiceDetails?.ar_value_formatted ? (billedInvoiceDetails.ar_value_formatted) : '-'}}
                    </div>
                </div>
                <div class="row py-2">
                    <div class="col-6 px-0 side-header">
                        Payment Expected On
                    </div>
                    <div class="col-6 px-0 field-value">
                        {{billedInvoiceDetails?.payment_expected_on ? getDate(billedInvoiceDetails.payment_expected_on) : '-'}}
                    </div>
                </div>
                <div class="row py-2">
                    <div class="col-6 px-0 side-header">
                        Planned On
                    </div>
                    <div class="col-6 px-0 field-value">
                        {{billedInvoiceDetails?.planned_on  ? getDate(billedInvoiceDetails.planned_on) : '-'}}
                    </div>
                </div>
                <div class="row py-2">
                    <div class="col-6 px-0 side-header">
                        Age
                    </div>
                    <div class="col-6 px-0 field-value-highlighted">
                        {{billedInvoiceDetails?.aging_days ? (billedInvoiceDetails.aging_days) : '-'}}
                    </div>
                </div>
            </div>
        </div>
    </div>
 
    <div class="container-fluid mt-4 slide-from-down">
        <div class="row main-header">
            Course of action !
        </div>
 
        <div class="row mt-4" style="font-size: 15px;text-overflow: ellipsis; overflow: hidden;" [matTooltip]="billedInvoiceDetails.course_of_action">
            {{billedInvoiceDetails?.course_of_action ? (billedInvoiceDetails.course_of_action) : '-'}}
        </div>
 
        <div class="row">
            <!-- <div class="col-lg-3 col-sm-12 pl-0 pr-2 mt-4">
                    <button [disabled]="sendMailLoading" (click)="openMailBox()" mat-raised-button class="mr-2 send-btn">
                        Send mail
                    </button>
            </div> -->
            <!-- <div class="col-lg-3 col-sm-12 pl-0 mt-4">
                <button mat-button class="mr-2 normal-btn" disabled >Mark as complete</button>
            </div>  
            <div class="col-lg-3 col-sm-12 pl-0 mt-4">
                <button mat-button class="mr-2 normal-btn" disabled >Remind me later</button>
            </div> -->
        </div>
    </div>
</div>
 
 

