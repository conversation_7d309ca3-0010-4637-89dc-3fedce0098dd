{"name": "notifications", "version": "1.0.0", "description": "This node is used for Notification Service", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "set NODE_ENV=default&&nodemon server.js", "start-serve": "node secretsProcessor.js && nodemon server.js"}, "keywords": ["notifications", "notification"], "author": "", "license": "ISC", "dependencies": {"@opentelemetry/auto-instrumentations-node": "^0.39.2", "@opentelemetry/exporter-trace-otlp-http": "^0.43.0", "@opentelemetry/resources": "^1.17.0", "@opentelemetry/sdk-node": "^0.43.0", "@opentelemetry/semantic-conventions": "^1.17.0", "@sentry/node": "^7.51.2", "aws-cloudfront-sign": "^3.0.2", "aws-sdk": "^2.1692.0", "axios": "^0.24.0", "body-parser": "^1.19.0", "compression": "^1.7.4", "config": "^3.3.6", "dotenv": "^10.0.0", "express": "^4.17.1", "moment": "^2.29.1", "mongodb": "^4.7.0", "morgan": "^1.10.0", "multer": "^1.4.3", "multer-s3": "^2.10.0", "mysql": "^2.18.1", "nodemon": "^2.0.14", "number-to-words": "^1.2.4", "passport": "^0.5.0", "passport-jwt": "^4.0.0", "process": "^0.11.10", "prom-client": "^14.2.0", "prometheus-gc-stats": "^1.1.0", "underscore": "^1.13.1", "winston": "^3.3.3", "winston-logrotate": "^1.3.0", "xlsx": "^0.18.5"}}