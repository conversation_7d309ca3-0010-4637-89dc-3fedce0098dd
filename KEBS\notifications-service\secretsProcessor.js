// Use this code snippet in your app.
// If you need more information about configurations or implementing the sample code, visit the AWS docs:
// https://aws.amazon.com/developers/getting-started/nodejs/




const fs = require("fs")
const path = require("path")

// Load the AWS SDK
let AWS = require('aws-sdk'),
    region = "us-east-1",
    secretName = `kebs/` + (((process.env.NODE_ENV == "prd") || (process.env.NODE_ENV == "preprd")) ? process.env.NODE_ENV : "dev") + `/v2/notifications`,
    secret,
    decodedBinarySecret;


// Create a Secrets Manager client
var client = new AWS.SecretsManager({
    region: region
});


module.exports.setSecrets = () => {
    if (process.env.NODE_ENV == "default") {
        return Promise.resolve("default")
    }
    return new Promise((resolve, reject) => {
        try {

            client.getSecretValue({ SecretId: secretName }, (err, data) => {
                if (err) {

                    console.log(err)

                    if (err.code === 'DecryptionFailureException')
                        // Secrets Manager can't decrypt the protected secret text using the provided KMS key.
                        // Deal with the exception here, and/or rethrow at your discretion.
                        throw err;
                    else if (err.code === 'InternalServiceErrorException')
                        // An error occurred on the server side.
                        // Deal with the exception here, and/or rethrow at your discretion.
                        throw err;
                    else if (err.code === 'InvalidParameterException')
                        // You provided an invalid value for a parameter.
                        // Deal with the exception here, and/or rethrow at your discretion.
                        throw err;
                    else if (err.code === 'InvalidRequestException')
                        // You provided a parameter value that is not valid for the current state of the resource.
                        // Deal with the exception here, and/or rethrow at your discretion.
                        throw err;
                    else if (err.code === 'ResourceNotFoundException')
                        // We can't find the resource that you asked for.
                        // Deal with the exception here, and/or rethrow at your discretion.
                        throw err;
                }
                else {

                    if(!((process.env.NODE_ENV == "prd") || (process.env.NODE_ENV == "preprd"))) {
                        data.SecretString = (typeof data?.SecretString == 'string') ? JSON.parse(data?.SecretString) : data?.SecretString
                        data.SecretString = data?.SecretString[process.env.NODE_ENV]
                    }

                    data = (typeof data?.SecretString != 'string') ? JSON.stringify(data?.SecretString) : data?.SecretString

                    fs.writeFileSync(path.join(__dirname, `/config/${process.env.NODE_ENV}.json`), data);

                    return resolve("success");
                }

                // Your code goes here. 
            });
        }
        catch (err) {
            console.log(err)
            // logger.info(err)
            return reject(err)
        }

    })
}

// this.setSecrets()