<div class="widget-container">
    <div class="header">
      <div class="d-flex align-items-center">
        <div class="chart-title">{{ widgetConfig?.label }}</div>
      </div>
       <div class="summary-icon" *ngIf="widgetConfig?.widget_config?.show_summary" (click)="openSummaryForm()">
                <svg width="17" height="15" viewBox="0 0 14 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <!-- Outer box -->
                    <rect x="0.5" y="0.5" width="13" height="10" rx="1" stroke="#B9C0CA" stroke-width="1" />
            
                    <!-- Horizontal line (centered) -->
                    <line x1="1" y1="5.5" x2="13" y2="5.5" stroke="#B9C0CA" stroke-width="1" />
            
                    <!-- Two vertical lines (at 1/3 and 2/3 positions) -->
                    <line x1="4.67" y1="1" x2="4.67" y2="10" stroke="#B9C0CA" stroke-width="1" />
                    <line x1="9.33" y1="1" x2="9.33" y2="10" stroke="#B9C0CA" stroke-width="1" />
                </svg>
            </div>
    </div>
    <ng-container *ngIf="data?.percentage !== undefined && !isLoading">
      <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight + 'px' }">
        <div
          class="progress-circle-container"
          [ngStyle]="{
            width: spinnerSize + 'px',
            height: spinnerSize + 'px'
          }"
        >
          <svg
            [attr.width]="spinnerSize"
            [attr.height]="spinnerSize"
            [attr.viewBox]="'0 0 ' + spinnerSize + ' ' + spinnerSize"
          >
            <!-- Background circle -->
            <circle
              [attr.cx]="spinnerSize / 2"
              [attr.cy]="spinnerSize / 2"
              [attr.r]="radius"
              stroke="#e6e6e6"
              stroke-width="7"
              fill="none"
            ></circle>
  
            <!-- Foreground circle (progress) -->
            <circle
              [attr.cx]="spinnerSize / 2"
              [attr.cy]="spinnerSize / 2"
              [attr.r]="radius"
              [style.stroke]="data?.color"
              stroke-width="7"
              fill="none"
              [attr.stroke-dasharray]="circumference"
              [attr.stroke-dashoffset]="strokeDashoffset"
              stroke-linecap="round"
              [attr.transform]="'rotate(-90 ' + spinnerSize / 2 + ' ' + spinnerSize / 2 + ')'"
            ></circle>
          </svg>
          <div class="percentage-label">{{ data?.percentage }}%</div>
        </div>
        <div class="total-area">
            <div class="total-header">Total</div>
            <div class="total-label">{{data?.total}}</div>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="!data?.percentage && !isLoading">
      <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight + 'px' }">
        <span class="empty-data">No Data Found!</span>
      </div>
    </ng-container>
  </div>
  