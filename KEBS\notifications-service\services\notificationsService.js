const express = require("express");
const { result } = require("lodash");
const axios = require('axios');
// const router = express.Router();
// module.exports = router
const logger = require('../logger').logger
const mongo = require("../mongo_conn_native").Connection
const { rpool } = require("../databaseCon");
const moment = require("moment");
const fs = require('fs');
const mysql = require("mysql");


/**
 * @description To retrieve notification report data
 * @route /api/v2/notifications/getNotificationsReport
 * <AUTHOR> Pavithra
 * @param {*} req
 * @param {*} res
 * @method POST
 * @returns json
 */
module.exports.getNotificationsReport = async (req, res) => {
    try {
        logger.info('Entered notifications service - reports');
        console.log('entered notifications service - reports')
        const db = req.user.uad || req.body.db_name;
        const user = req.user;
        const user_oid = user.oid;
        const params = req.body;
        console.log('db- ')
        console.log(db)

        logger.info("Parms : ")
        logger.info(params)
        if (!(Object.keys(params).length > 0))
            return res.status(400).json({
                messType: "E",
                messText: "Request Parameters for the Notifications Report not found"
            });

        logger.info('parameters - ')
        logger.info(params)

        logger.info('filter query - ')
        logger.info(params?.filterQuery)

        let filterQuery = params?.filterQuery || '';
        // let concatenateFilter = filterQuery ? (filterQuery.trim().startsWith('AND') ? `AND (${filterQuery.trim().slice(4)})` : `AND (${filterQuery.trim()})`) : '';


        // logger.info('filter - ')
        // logger.info(concatenateFilter)

        //sort active, sort order, sort col alias
        const sortParams = params?.sort?.filter(param =>
            param.is_sort_active &&
            param.sort_order != '0' &&
            param.sort_column_alias != null
        ) || [];

        //search active, search param, search col alias, search table alias     
        const searchParams = params?.search?.filter(param =>
            param.is_search_active &&
            param.search_param !== '' &&
            param.search_table_alias != null &&
            param.search_column_alias != null
        ) || [];

        const globalsearchParamsKey = params?.search?.filter(param =>
            param.is_search_active &&
            param.search_table_alias != null &&
            param.search_column_alias != null
        ) || [];

        let startDate = params?.startDate ? params?.startDate : null;
        startDate = moment(startDate).startOf('day').format("YYYY-MM-DD HH:mm:ss");
        let endDate = params?.endDate ? params?.endDate : null;
        endDate = moment(endDate).endOf('day').format("YYYY-MM-DD HH:mm:ss");

        const globalSearchParam = params?.searchParams || '';
        const skip = params?.skip ?? 0;
        const limit = params?.limit ?? 15;
        const isForDownload = params?.isForDownload ?? false;

        if (!startDate || !endDate) {
            res.status(500).send({
                messType: "E",
                messText: "Report generation failed due to duration missing!"
            })
        }


        // ORDER BY clause
        let orderBy = '';
        if (sortParams.length > 0) {
            orderBy = 'ORDER BY ';
            orderBy += sortParams.map(param => {
                let column = param.sort_column_alias || param.column_key[0]; // Fallback to column_key if sort_column_alias is not provided
                let columnType = param.column_type || 'string'; // Assume 'string' if column_type is not provided
                if (columnType === 'number') {
                    return `${column} ${param.sort_order == '1' ? 'ASC' : 'DESC'}`;
                } else {
                    let tableAlias = param.sort_table_alias || ''; // Fallback to empty string if sort_table_alias is not provided
                    return `${tableAlias ? `${tableAlias}.` : ''}${column} ${param.sort_order == '1' ? 'ASC' : 'DESC'}`;
                }
            }).join(', '); // Join multiple sort conditions with commas
        }

        // Search Condition
        let searchCondition = '';
        const individualSearchConditions = [];
        const individualGlobalSearchConditions = [];

        // if (searchParams.length > 0) {
        //     individualSearchConditions.push(
        //         searchParams.map(param => `${param.search_table_alias}.${param.search_column_alias} LIKE '%${param.search_param}%'`).join(' AND ')
        //     );
        // }

        if (searchParams.length > 0) {
            const search = searchParams.map(
                param => {
                    const col = param.search_table_alias ? `${param.search_table_alias}.${param.search_column_alias}` : `${param.search_column_alias}`

                    return `${col}  LIKE '%${param.search_param}%'`
                }
            ).join(' AND ')

            individualSearchConditions.push(search);
        }

        if (globalSearchParam && globalsearchParamsKey.length > 0) {
            const globalConditions = globalsearchParamsKey.map(param => {
                const columnExpr = param.search_table_alias
                    ? `${param.search_table_alias}.${param.search_column_alias}`
                    : `${param.search_column_alias}`;
                logger.info('column expression - ')
                logger.info(columnExpr)
                return `${columnExpr} LIKE '%${globalSearchParam}%'`
            }

            ).join(' OR ');
            individualGlobalSearchConditions.push(globalConditions);
        }

        if (individualSearchConditions.length > 0) {
            searchCondition += ` AND (${individualSearchConditions.join(' AND ')})`;
        }
        if (individualGlobalSearchConditions.length > 0) {
            searchCondition += ` AND (${individualGlobalSearchConditions.join(' AND ')})`;
        }

        logger.info('search condition -- ')
        logger.info(searchCondition)

        logger.info('report start date - ')
        logger.info(startDate)
        logger.info('report end date - ')
        logger.info(endDate)
        
        // Main Query
        let main_query = `
            SELECT tn.id AS id, mnt.notification_type AS notification_type,
                m_in.is_notify AS is_notify, mnl.notif_lock AS notif_lock,
                tn.mail_template_id AS mail_template_id, tamt.subject AS subject,
                ma.application_name AS application_name,
               RTRIM(LTRIM(CONCAT(
               COALESCE(CONCAT(tepd.first_name, ' '), ''),
               COALESCE(CONCAT(tepd.middle_name, ' '), ''),
               COALESCE(tepd.last_name, '')))) AS employee_name,
                tepd.start_date AS start_date, tepd.end_date AS end_date, tn.recipient_mail_1 AS recipient_mail_1,
                tn.recipient_id_1 AS recipient_id_1, tn.notif_text AS notif_text, tn.notif_mail_params AS notif_mail_params,
                tn.change_status AS change_status,  
                CONCAT(
                    UPPER(LEFT(tn.assigned_type, 1)),
                    LOWER(SUBSTRING(tn.assigned_type, 2))
                ) AS assigned_type,
                tn.notification_send_type AS notification_send_type, tn.created_on AS created_on,
                tn.changed_on, tn.is_active AS is_active,
                tn.notif_link,  tepd.associate_id AS associate_id
            FROM (
                SELECT *,
                    is_notify as effective_is_notify
                    FROM ${db}.t_notification
                UNION
                SELECT *,
                    CASE
                        WHEN notification_send_type in ('Both','Mail') and notif_lock = 0 THEN 0
                        ELSE is_notify
                    END AS effective_is_notify
                    FROM ${db}.t_notification_backup
            ) AS tn
            LEFT JOIN ${db}.m_notification_type mnt ON tn.notification_type = mnt.id
            LEFT JOIN ${db}.m_is_notify m_in ON tn.effective_is_notify = m_in.id
            LEFT JOIN ${db}.m_notification_lock mnl ON tn.notif_lock = mnl.id
            LEFT JOIN ${db}.t_application_mail_template tamt ON tn.mail_template_id = tamt.id
            LEFT JOIN ${db}.m_application ma ON tamt.application_id = ma.application_id
            LEFT JOIN ${db}.m_employee_master mem ON tn.recipient_id_1 = mem.oid
            LEFT JOIN ${db}.t_e360_personal_details tepd ON mem.associate_id = tepd.associate_id
            WHERE tn.is_active = ? AND tepd.is_active = ?
              AND tn.changed_on BETWEEN '${startDate}' AND '${endDate}'
            AND (
                tn.mail_template_id = 0 OR tn.mail_template_id IS NULL OR tn.mail_template_id != 0
            ) 
               ${searchCondition} ${filterQuery} `;

        if (orderBy) {
            main_query += ` ${orderBy}`;
        }
        else{
            main_query += ` ORDER BY changed_on desc`;
        }
        if (!isForDownload) {
            main_query += ` LIMIT ${skip}, ${limit}`;
        }

        logger.info("Formatted Query : ")
        let formated_query = mysql.format(main_query, [1, 1])
        logger.info(formated_query)

        const result = await rpool(main_query, [1, 1]);
     
        return res.status(200).send({
            err: false,
            data: Array.isArray(result) ? result : [],
            msg: result?.length > 0 ? "Data Retrieved Successfully" : "No Data Found",
        });
    }
    catch (error) {
        return res.status(500).json({
            messType: "E",
            messText: "Error in Fetching the Notifications Report Data!",
            msg: error.message,
            err: true,
            errMessage: {
                msg: error.message,
                stack: error.stack,
            }
        });
    }
};


/**
 * @description To retrieve is notified master data
 * @route /api/v2/notifications/getNotifiedMasterList
 * <AUTHOR> Pavithra
 * @param {*} req
 * @param {*} res
 * @method POST
 * @returns json
 */
module.exports.getNotifiedMasterList = async (req, res) => {
    try {
        const db = req.user.uad || req.body.db_name;
        const user = req.user;
        console.log('db- ')
        console.log(db)

        let main_query = `SELECT id, is_notify as name FROM ${db}.m_is_notify where is_active = 1;`

        console.log('query result - ')
        console.log(main_query)


        const result = await rpool(main_query);
        logger.info('notification (is notify) master data list - ')
        logger.info(result)

        return res.status(200).send({
            err: false,
            data: Array.isArray(result) ? result : [],
            msg: result?.length > 0 ? "Data Retrieved Successfully" : "No Data Found",
        });
    }
    catch (error) {
        return res.status(500).json({
            messType: "E",
            messText: "Error in Fetching the Notifications Report Filter Data - Is Notify!",
            msg: error.message,
            err: true,
            errMessage: {
                msg: error.message,
                stack: error.stack,
            }
        });
    }
};

/**
 * @description To retrieve is notification type master data
 * @route /api/v2/notifications/getNotificationTypeMasterList
 * <AUTHOR> Pavithra
 * @param {*} req
 * @param {*} res
 * @method POST
 * @returns json
 */
module.exports.getNotificationTypeMasterList = async (req, res) => {
    try {
        const db = req.user.uad || req.body.db_name;
        const user = req.user;
        console.log('db- ')
        console.log(db)

        let main_query = `SELECT id, notification_type as name FROM ${db}.m_notification_type where is_active = 1;`

        console.log('query result - ')
        console.log(main_query)


        const result = await rpool(main_query);
        logger.info('notification type master data list - ')
        logger.info(result)

        return res.status(200).send({
            err: false,
            data: Array.isArray(result) ? result : [],
            msg: result?.length > 0 ? "Data Retrieved Successfully" : "No Data Found",
        });
    }
    catch (error) {
        return res.status(500).json({
            messType: "E",
            messText: "Error in Fetching the Notifications Report Filter Data - Notification Type!",
            msg: error.message,
            err: true,
            errMessage: {
                msg: error.message,
                stack: error.stack,
            }
        });
    }
};

/**
 * @description To retrieve recipient mail ID master data
 * @route /api/v2/notifications/getRecipientMailIDMasterList
 * <AUTHOR> Pavithra
 * @param {*} req
 * @param {*} res
 * @method POST
 * @returns json
 */
module.exports.getRecipientMailIDMasterList = async (req, res) => {
    try {
        const db = req.user.uad || req.body.db_name;
        const user = req.user;
        console.log('db- ')
        console.log(db)

        // let main_query = `SELECT DISTINCT recipient_id_1 as id, recipient_mail_1 as name FROM ${db}.t_notification where is_active = 1;`
        let main_query = `
            SELECT DISTINCT tn.recipient_id_1 AS id, tn.recipient_mail_1 AS name
            FROM ${db}.t_notification tn
            LEFT JOIN ${db}.m_employee_master mem ON tn.recipient_id_1 = mem.oid
            LEFT JOIN ${db}.t_e360_personal_details tepd ON mem.associate_id = tepd.associate_id
            WHERE tn.is_active = 1
                AND tepd.is_active = 1
                AND tepd.start_date <= CURRENT_DATE
                AND (tepd.end_date IS NULL OR CURRENT_DATE <= tepd.end_date)
                AND tn.recipient_id_1 IS NOT NULL
                AND tn.recipient_mail_1 IS NOT NULL;`;

        console.log('query result - ')
        console.log(main_query)


        const result = await rpool(main_query);
        logger.info('notification type master data list - ')
        logger.info(result)

        return res.status(200).send({
            err: false,
            data: Array.isArray(result) ? result : [],
            msg: result?.length > 0 ? "Data Retrieved Successfully" : "No Data Found",
        });
    }
    catch (error) {
        return res.status(500).json({
            messType: "E",
            messText: "Error in Fetching the Notifications Report Filter Data - Notification Type!",
            msg: error.message,
            err: true,
            errMessage: {
                msg: error.message,
                stack: error.stack,
            }
        });
    }
};


/**
 * @description To retrieve Application Names master data
 * @route /api/v2/notifications/getApplicationNameMasterList
 * <AUTHOR> Pavithra
 * @param {*} req
 * @param {*} res
 * @method POST
 * @returns json
 */
module.exports.getApplicationNameMasterList = async (req, res) => {
    try {
        const db = req.user.uad || req.body.db_name;
        const user = req.user;
        console.log('db- ')
        console.log(db)

        let main_query = `SELECT DISTINCT ma.application_id as id, ma.application_name as name
        FROM ${db}.t_notification tn
        LEFT JOIN ${db}.t_application_mail_template tamt ON tn.mail_template_id = tamt.id
        LEFT JOIN ${db}.m_application ma ON tamt.application_id = ma.application_id
        LEFT JOIN ${db}.m_employee_master mem ON tn.recipient_id_1 = mem.oid
        LEFT JOIN ${db}.t_e360_personal_details tepd ON mem.associate_id = tepd.associate_id
        WHERE tn.is_active = 1
        AND tepd.is_active = 1
        AND tepd.start_date <= CURRENT_DATE
        AND (tepd.end_date IS NULL OR CURRENT_DATE <= tepd.end_date)
        AND ma.application_id IS NOT NULL;`

        console.log('query result - ')
        console.log(main_query)


        const result = await rpool(main_query);
        logger.info('Application names master data list - ')
        logger.info(result)

        return res.status(200).send({
            err: false,
            data: Array.isArray(result) ? result : [],
            msg: result?.length > 0 ? "Data Retrieved Successfully" : "No Data Found",
        });
    }
    catch (error) {
        return res.status(500).json({
            messType: "E",
            messText: "Error in Fetching the Notifications Report Filter Data - Applications Name!",
            msg: error.message,
            err: true,
            errMessage: {
                msg: error.message,
                stack: error.stack,
            }
        });
    }
};

/**
 * @description To retrieve Employee Names master data
 * @route /api/v2/notifications/getEmployeeNameMasterList
 * <AUTHOR> Pavithra
 * @param {*} req
 * @param {*} res
 * @method POST
 * @returns json
 */
module.exports.getEmployeeNameMasterList = async (req, res) => {
    try {
        const db = req.user.uad || req.body.db_name;
        const user = req.user;
        console.log('db- ')
        console.log(db)

        let main_query = `SELECT DISTINCT
        tepd.associate_id as id,
        RTRIM(LTRIM(CONCAT(
            COALESCE(CONCAT(tepd.first_name, ' '), ''),
            COALESCE(CONCAT(tepd.middle_name, ' '), ''),
            COALESCE(tepd.last_name, '')
        ))) AS name
        FROM ${db}.t_notification tn
        LEFT JOIN ${db}.m_employee_master mem ON tn.recipient_id_1 = mem.oid
        LEFT JOIN ${db}.t_e360_personal_details tepd ON mem.associate_id = tepd.associate_id
        WHERE tn.is_active = 1
        AND tepd.is_active = 1
        AND tepd.start_date <= CURRENT_DATE
        AND (tepd.end_date IS NULL OR CURRENT_DATE <= tepd.end_date)
        AND tepd.first_name IS NOT NULL;`

        console.log('query result - ')
        console.log(main_query)


        const result = await rpool(main_query);
        logger.info('Employee names master data list - ')
        logger.info(result)

        return res.status(200).send({
            err: false,
            data: Array.isArray(result) ? result : [],
            msg: result?.length > 0 ? "Data Retrieved Successfully" : "No Data Found",
        });
    }
    catch (error) {
        return res.status(500).json({
            messType: "E",
            messText: "Error in Fetching the Notifications Report Filter Data - Employee Name!",
            msg: error.message,
            err: true,
            errMessage: {
                msg: error.message,
                stack: error.stack,
            }
        });
    }
};

/**
 * @description To retrieve Associate Id master data
 * @route /api/v2/notifications/getAssociateIDMasterList
 * <AUTHOR> Pavithra
 * @param {*} req
 * @param {*} res
 * @method POST
 * @returns json
 */
module.exports.getAssociateIDMasterList = async (req, res) => {
    try {
        const db = req.user.uad || req.body.db_name;
        const user = req.user;
        console.log('db- ')
        console.log(db)

        let main_query = `SELECT DISTINCT tepd.associate_id as id, tepd.associate_id as name
        FROM ${db}.t_notification tn
        LEFT JOIN ${db}.m_employee_master mem ON tn.recipient_id_1 = mem.oid
        LEFT JOIN ${db}.t_e360_personal_details tepd ON mem.associate_id = tepd.associate_id
        WHERE tn.is_active = 1
        AND tepd.is_active = 1
        AND tepd.start_date <= CURRENT_DATE
        AND (tepd.end_date IS NULL OR CURRENT_DATE <= tepd.end_date)
        AND tepd.associate_id IS NOT NULL;`

        console.log('query result - ')
        console.log(main_query)


        const result = await rpool(main_query);
        logger.info('Associate ID master data list - ')
        logger.info(result)

        return res.status(200).send({
            err: false,
            data: Array.isArray(result) ? result : [],
            msg: result?.length > 0 ? "Data Retrieved Successfully" : "No Data Found",
        });
    }
    catch (error) {
        return res.status(500).json({
            messType: "E",
            messText: "Error in Fetching the Notifications Report Filter Data - Associate IDs!",
            msg: error.message,
            err: true,
            errMessage: {
                msg: error.message,
                stack: error.stack,
            }
        });
    }
};


/**
* Download Logs 
* <AUTHOR> P
* @param {*} req 
* @param {*} res 
*/
module.exports.downloadLogs = async (req, res) => {
    try {

        let log_file_name = req.body.file_name ? req.body.file_name : "web.log";

        fs.readFile(`./logs/${log_file_name}`, 'utf8', function (err, data) {


            res.status(200).send({
                logs: JSON.stringify(data)
            });
        });
    }
    catch (err) {
        res.send(err);
    }
}


/**
* Delete Logs 
* <AUTHOR> P
* @param {*} req 
* @param {*} res 
*/
module.exports.deleteLogs = async (req, res) => {
    try {

        let log_file_name = req.body.file_name ? req.body.file_name : "web.log";

        fs.writeFile(`./logs/${log_file_name}`, '', function () {

            // Display the file content
            res.status(200).send({
                err: false
            });
        });
    }
    catch (err) {
        res.send(err);
    }
}