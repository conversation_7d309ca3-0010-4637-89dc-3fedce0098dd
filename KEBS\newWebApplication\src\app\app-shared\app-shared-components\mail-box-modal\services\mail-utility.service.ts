import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpBackend} from '@angular/common/http';
import { FormBuilder } from '@angular/forms';
import { Validators } from '@angular/forms';
import * as moment from 'moment'
import * as _ from 'underscore'
import sweetAlert from 'sweetalert2';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SharedLazyLoadedComponentsService } from '../../../../modules/shared-lazy-loaded-components/services/shared-lazy-loaded-components.service';

@Injectable({
  providedIn: 'root'
})

export class MailUtilityService {
  selectedFiles = []; // Variable to store selected files

  constructor(
    private formBuilder: FormBuilder,
    private _http: HttpClient,
    handler: HttpBackend,
    private snackBar : MatSnackBar,
    private _sharedService: SharedLazyLoadedComponentsService
  ) {
    this._http = new HttpClient(handler);
    this.mUtilityDataDefault = JSON.parse(JSON.stringify(this.mUtilityData));
    this.mailUiDataDefault = JSON.parse(JSON.stringify(this.mailUiDataDefault));
  }

  currentlySelectedMailCard : any

  closeDialog$ = new Subject<void>();

  $onMailFolderSwitched = new Subject<void>();

  cancelApiCall() {
    this.$onMailFolderSwitched.next()
    this.$onMailFolderSwitched.complete()
    this.$onMailFolderSwitched = new Subject<void>();
  }

  mUtilityData = {

    recipientMailId : '',
    recipientMailIdArr : [],
    reloadBodyComponent: () => {},
    switchMailMode: () => {},
    currentMailMode: {},
    selectedMailData: {},
    isLoading: false,
    recipientMailList: {},
    reviewPagination: {},
    dunningMailData: [],
    applicationId: 0,
    selectedDunningMailData: {},
    isToRecipientFieldHasSaveButton: false,
    isCcRecipientFieldHasSaveButton: false,
    isBccRecipientFieldHasSaveButton: false,
    saveRecipientMailIds: () => {},
    saveRecipientMailIdType: {},
    authorizedMailSenders:[],
    currentUserMailId:'',
    callMailApi: () => {},
    o365Token:{}, //TODO: o365 token must be sent by application to authenticate graph API calls
    getMailFromKebsApiData:{},
    saveMailInKebsApiData:{},
    graphApiNextLink:{},
    receiveMailsFromO365:true, //TODO: To fetch mail from outlook
    receiveMailsFromKebs:true, //TODO: To fetch mail from KEBS (mails which are saved using 'sync with KEBS')
    syncWithKebsOnSendMail:false, //TODO: To save mail item in KEBS, while mail send.
    hasInitiateNewMailTemplate : false, //TODO: If application has initiate new mail template 
    newMailTemplateData:[], //TODO: Compose mail Template data to be inserted if any 
    selectedNewMailTemplateData:{}, //TODO: For internal operations
    formatTableForTemplate : false, // TODO: format table structure , if template has a table (eg.dunning,p2p)
    isTableLayoutAuto: false,
    currentItemDataIndex:0,
    defaultRecordsPerFetch:10,
    mailItemList:[],
    filesAttached: this.selectedFiles,
    saveHistoryInKebsApiData:{},
    saveHistoryInTable: false,
    retrieveHistoryFromApi:{},
    initiateNewMailTemplateData:{},
    saveSpocName: () => {},
    spocSaveEnabled : false,
    autoAttachmentApplicable: false,
    destinationBucket:'',
    routingKey:'',
    contextId:'',
    attachmentRetrievalDetails:[],
    fromCommonMailId : false,
    sendMailAPIDetails:{}
  }

  mailUiData = {

    mailInputFields: this.formBuilder.group({
      toRecipients: ['', Validators.required],
      ccRecipients: [''],
      bccRecipients: [''],
      subject: [''],
      body: [''],
      fromMailIds: [''],
      spocName:''
    }),
    selectedMailDataUi:{},
    selectedBodyDataUi:{},
    conversationIdKeys:[],
    miniCardInputData:{},
    isExpandBody:false,
    progressSpinner:false,
    selectedMailFolder:{},
    isSyncWithKebsBtn:false,
    currentMailFolder:{},
    isMailItemDataLoading:false,
    showHistoryButton: false,
    showSpocName: false,
    showTitle:false,
    title:''
  }

  mUtilityDataDefault = {}
  mailUiDataDefault = {}

  mailFolders = [
    {
      id:0,
      icon:"inbox",
      label:"Inbox",
      isEnabled:true,
      graphApiKey:"inbox",
      mongoKey:"inbox"
    },
    {
     id:1,
     icon:"send",
     label:"Sent Items",
     isEnabled:false,
     graphApiKey:"sentItems",
     mongoKey:"sent_items"

   }
  ]
  

  mailFunctions = {

    switchViewMode: () => {
      this.mailUiData.mailInputFields.reset()
      if (this.mUtilityData.currentMailMode['mode'] == 'create') {
        this.mailFunctions.handleCreateMailBasedOnApplication()
      }

      if (this.mUtilityData.currentMailMode['mode'] == 'read') {
        this.mailUiData.selectedMailDataUi = this.mUtilityData.selectedMailData;
        this.mailUiData.selectedBodyDataUi = this.mailUiData.selectedMailDataUi['body'].content ? this.mailUiData.selectedMailDataUi['body'].content : '';
      }

      if(this.mUtilityData.currentMailMode['mode'] == 'not selected') {
        this.mailUiData.selectedMailDataUi = ''
        this.mailUiData.selectedBodyDataUi = ''
      }

      if(this.mUtilityData.currentMailMode['mode'] == 'action') {
        this.mailFunctions.formatEditAreaByAction(this.mUtilityData.currentMailMode['actionType'])
      }
    },

    handleCreateMailBasedOnApplication: () => {
      if (this.mUtilityData.hasInitiateNewMailTemplate) {

        console.log("handleCreateMailBasedOnApplication")
  
        this.mailFunctions.initMiniCardSelection()
        
        //this.mailUiData.selectedMailDataUi = ''
        console.log(this.mUtilityData.selectedNewMailTemplateData['mailBody'])
        this.mailUiData.selectedBodyDataUi = this.mUtilityData.selectedNewMailTemplateData['mailBody']
  
        this.mailUiData.mailInputFields.patchValue({
          toRecipients : this.getFormatedValueForMailIds('toMailIds'),
          fromMailIds: [this.mUtilityData.currentUserMailId],
          ccRecipients: this.getFormatedValueForMailIds('ccMailIds'),
          bccRecipients: this.getFormatedValueForMailIds('bccMailIds'),
          subject: [this.mUtilityData.selectedNewMailTemplateData['subjectTxt']],
          body: this.mailUiData.selectedBodyDataUi,
          spocName: this.mUtilityData.selectedNewMailTemplateData['spocName']
        })
        console.log("mailInputFields")
        console.log(this.mailUiData.mailInputFields)
      }
      else {
        console.log('hello')
        console.log(this.getFormatedValueForMailIds('toMailIds'))
        this.mailUiData.mailInputFields.patchValue({
          fromMailIds: [this.mUtilityData.currentUserMailId],
          toRecipients : this.getFormatedValueForMailIds('toMailIds'),
          body: ""
        })

        this.mailFunctions.initMiniCardSelection()
      }
    },

    initMiniCardSelection: () => {
      console.log("uiuiuuuuuuuuuuuuuuuuuuuuuuu")
      console.log(this.mailUiData)
      console.log(this.mailUiData.conversationIdKeys)
      for(let key of this.mailUiData.conversationIdKeys) {
        console.log(key)
        //With thread (For thread parent)
        if(this.mailUiData.miniCardInputData[key].length > 1) {
          console.log("inside init card if")
          this.mailUiData.miniCardInputData[key][0].isExpanded = false
  
        }
        for(let mail of this.mailUiData.miniCardInputData[key]) {
          mail.isSelected = false
        }
      }
    },

    getMailDataFromResources: async () => {

      let selectedMailFolder = _.findWhere(this.mailFolders,{isEnabled:true})
      this.mUtilityData.currentItemDataIndex = 0;

      if(this.mUtilityData.recipientMailIdArr && this.mUtilityData.recipientMailIdArr.length>0 && !this.mUtilityData.fromCommonMailId) {
  
        this.mUtilityData.isLoading = true;
        let graphMailList = []
        let kebsMailList : any = []

        if(this.mUtilityData.receiveMailsFromO365) {

          //<---------------------Retrieve mails from graph api starts here------------------------------------>
          let graphUrl = this.constructQueryForGraphApi(this.mUtilityData.recipientMailIdArr,selectedMailFolder.graphApiKey)

          if(graphUrl && graphUrl!='') {

            let res  = await this.getMailListFromGraphApi(graphUrl)
            
            if(res['value'] && res['@odata.nextLink']) {
              this.mUtilityData.graphApiNextLink['url'] = res['@odata.nextLink']
              this.mUtilityData.graphApiNextLink['hasNextLink'] = true
              graphMailList = graphMailList.concat(res['value'])
            }
            else if(res['value']){
              this.mUtilityData.graphApiNextLink['hasNextLink'] = false
              graphMailList = graphMailList.concat(res['value']) 
            }
          }
          /*Check for threaded mails by conversationId */
          if(graphMailList.length > 0) {

            let threadMailGraphUrl = this.constructThreadQueryForGraphApi(graphMailList,selectedMailFolder.graphApiKey)
            let threadList  = await this.getMailListFromGraphApi(threadMailGraphUrl)

            _.each(threadList['value'],(item) => {
              item['folderName'] = selectedMailFolder.graphApiKey == 'sentItems'  ? 'Inbox' : 'Sent Items'
            })

            if(threadList['value']) {
              graphMailList = graphMailList.concat(threadList['value'])
            }
          }
          //<---------------------------------------Retrieve mails from graph api ends here--------------------------------->

        }
        if(this.mUtilityData.receiveMailsFromKebs) {

          //<--------------------------------------Retrieve stored mails from KEBS------------------------------------------>
            kebsMailList  = await this.getMailListFromKebs(selectedMailFolder.mongoKey,!this.mUtilityData.receiveMailsFromO365)

          //<--------------------------------------Retrieve stored mails from KEBS ends here -------------------------------->
        }

        let finalMailList = []

        if(kebsMailList['messType'] == "S" && graphMailList.length>0) {
          finalMailList = [...graphMailList,...kebsMailList['messData']]
        }
        else if(kebsMailList['messType'] == "S") {
          finalMailList = [...kebsMailList['messData']]
        }
        else if(graphMailList.length > 0){
          finalMailList = [...graphMailList]
        }
        else {
          finalMailList = []
        }
        
        //finalMailList = _.uniq(finalMailList,(item)=>{return item['id']})

        // console.log('final list',finalMailList)

        finalMailList = _.sortBy(finalMailList,(m) => new Date(m.receivedDateTime)).reverse()

        this.mUtilityData.mailItemList = finalMailList
        // console.log(this.mUtilityData.mailItemList)

        this.mailFunctions.groupByMailThread()
        this.mailFunctions.initMiniCardSelection()
        this.mUtilityData.isLoading = false
        

      }
      else {
        this.mUtilityData.isLoading = false
        this.mailUiData.miniCardInputData = {}
        this.mailUiData.conversationIdKeys = []
      }
    },


    groupByMailThread: () => {
      this.mailUiData.miniCardInputData = _.groupBy(this.mUtilityData.mailItemList,'conversationId')
      // console.log(this.mailUiData.miniCardInputData)
      this.mailUiData.conversationIdKeys = Object.keys(this.mailUiData.miniCardInputData)
      // console.log(this.mailUiData.conversationIdKeys)
    },

    /**
     * 
     * @param operationType String (send,reply,replyAll,forward)
     */
    initiateMailWriteOperation: async (operationType) => {

      if (this.mailUiData.mailInputFields.valid) {
        let toMailIds = []
        let ccMailIds = []
        let bccMailIds = []
        
        let subject = ""

        _.each(this.mailUiData.mailInputFields.value.subject, (sub) => {
          subject += sub.value && sub.display 
          ? sub.value : sub 
          ? sub : ""
        })

        let body = this.mailUiData.mailInputFields.value.body != '' 
        ? this.mailUiData.mailInputFields.value.body : ''

        this.mailUiData.mailInputFields.value.toRecipients.length > 0
        ? _.each(this.mailUiData.mailInputFields.value.toRecipients, (item) => { toMailIds.push(item.name) })
        : toMailIds = []
        
        
        this.mailUiData.mailInputFields.value.ccRecipients
        ?  _.each(this.mailUiData.mailInputFields.value.ccRecipients, (item) => { ccMailIds.push(item.name) })
        :  ccMailIds = []

        //For table purpose (To apply proper border and cell color to table)
        //Use this if your template has a table.
        if(this.mUtilityData.formatTableForTemplate) {
          body=this.formatTableStructureForTemplate(body)
          body = body.replace(/<p><br><\/p>/g, '<p></p>');
        }

        this.mailUiData.mailInputFields.value.bccRecipients 
        ? _.each(this.mailUiData.mailInputFields.value.bccRecipients, (item) => { bccMailIds.push(item.name) })
        : bccMailIds = []

        if(this.mUtilityData.authorizedMailSenders.length>0 || this.mUtilityData?.selectedNewMailTemplateData['authorizedMailSenders']?.length > 0) {
          if(_.contains(this.mUtilityData.authorizedMailSenders, this.mUtilityData.currentUserMailId)) {
            this.deliverMailByGraphApi(
              subject,
              toMailIds,
              ccMailIds,
              bccMailIds,
              body,
              operationType,
              this.mUtilityData.filesAttached
            ).then(async (res) => {

              // console.log('mail send response');
              // console.log(res);
              sweetAlert.fire({
                icon: 'success',
                title: 'Mail Sent successfully',
                showConfirmButton: false,
                timer: 1500
              })

              if(this.mUtilityData.syncWithKebsOnSendMail) {
               await this.mailFunctions.initiateSyncMail();
              }

              if(this.mUtilityData.saveHistoryInTable){
                await this.saveHistoryInTable();
              }
              this.closeDialog$.next();

            })
              .catch((err) => {
              console.log(err)
              this.snackBar.open('Could not send mail', 'Dismiss', { duration: 2000 })
            })
          }
          else {
            this.snackBar.open(
              'You are not authorized to send Mails!',
              'Dismiss',
              {duration:3000}
            );
          }
        }
        else {
          this.deliverMailByGraphApi(
            subject,
            toMailIds,
            ccMailIds,
            bccMailIds,
            body,
            operationType,
            this.mUtilityData.filesAttached
          ).then(async (res) => {
            
            console.log('mail send response');
            console.log(res); 

            if(this.mUtilityData.syncWithKebsOnSendMail) {
              await this.mailFunctions.initiateSyncMail();
            }

            if(this.mUtilityData.saveHistoryInTable){
              await this.saveHistoryInTable();
            }

            sweetAlert.fire({
              icon: 'success',
              title: 'Mail Sent successfully',
              showConfirmButton: false,
              timer: 1500
            })

            this.mailFunctions.getMailDataFromResources();
            this.snackBar.open('Mail sent successfully', 'Dismiss', { duration: 2000 })
            this.closeDialog$.next();
          })
            .catch((err) => {
            console.log(err)
            this.snackBar.open('Could not send mail', 'Dismiss', { duration: 2000 })
          })
        }
      }
      else {
        this.snackBar.open('Enter all mandatory fields', 'Dismiss', { duration: 2000 })
      }
    },

    initiateSyncMail: async () => {
      let url = `https://graph.microsoft.com/v1.0/me/mailFolders/sentItems/messages`;
      let sentItems = await this.getMailListFromGraphApi(url)
      console.log(sentItems)
      let mailItem = [sentItems['value'][0]];
      await this.saveMailItemInKebs(mailItem);
    },
    
    formatEditAreaByAction: (action) => {
    
      if(action == 'reply') {
        
        this.mailUiData.mailInputFields.patchValue({
          toRecipients : this.formatMailIdsForPatching(this.mailUiData.selectedMailDataUi,'toRecipients'),
          fromMailIds: [this.mUtilityData.currentUserMailId],
          subject: [this.formatSubjectForPatching(this.mailUiData.selectedMailDataUi,action)],
          body: this.formatBodyBasedOnAction(this.mailUiData.selectedMailDataUi,action)
        })

      }
      else if(action == 'replyAll') {

        this.mailUiData.mailInputFields.patchValue({
          toRecipients : this.formatMailIdsForPatching(this.mailUiData.selectedMailDataUi,'toRecipients'),
          fromMailIds: [this.mUtilityData.currentUserMailId],
          ccRecipients: this.formatMailIdsForPatching(this.mailUiData.selectedMailDataUi,'ccRecipients'),
          subject: [this.formatSubjectForPatching(this.mailUiData.selectedMailDataUi,action)],
          body: this.formatBodyBasedOnAction(this.mailUiData.selectedMailDataUi,action)
        })

      }
      else if(action == 'forward') {
        
        this.mailUiData.mailInputFields.patchValue({
          fromMailIds: [this.mUtilityData.currentUserMailId],
          subject: [this.formatSubjectForPatching(this.mailUiData.selectedMailDataUi,action)],
          body: this.formatBodyBasedOnAction(this.mailUiData.selectedMailDataUi,action)
        })
      }

    }
  }

  formatBodyBasedOnAction(selectedData,actionType) {

    let fromData = `${selectedData.from.emailAddress.name} < ${selectedData.from.emailAddress.address} >`
    let sentTime = moment(selectedData.sentDateTime).format('dddd MMMM D,YYYY hh:mm A')

    let toData = ``
    if(selectedData.toRecipients.length > 0) {
      _.each(selectedData.toRecipients, (item)=> {
        toData+= `${item.emailAddress.name} < ${item.emailAddress.address} >`
      })
    }

    let ccData = ``
    if(selectedData.ccRecipients.length > 0) {
      _.each(selectedData.ccRecipients, (item)=> {
        ccData+= `${item.emailAddress.name} < ${item.emailAddress.address} >`
      })
    }

    let bccData = ``
    if(selectedData.bccRecipients.length > 0) {
      _.each(selectedData.bccRecipients, (item)=> {
        bccData+= `${item.emailAddress.name} < ${item.emailAddress.address} >`
      })
    }

    let subjectData = selectedData.subject
    let formatedBodyData = ``

    if(actionType == 'reply') {
      
      let header = `
      <br>
      <div style="font-size:12pt"><hr></div>
      <br>
      <div style="font-size:11pt">
      <b>From:</b> ${fromData}
      <br>
      <b>Sent:</b> ${sentTime}
      <br>
      <b>To:</b> ${toData}
      <br>
      <b>Subject:</b> ${subjectData}
      </div>
      <br>
      <br>
      ` 
      if(selectedData.body.contentType == 'html') {
        formatedBodyData = header + selectedData.body.content
      }
      
      return formatedBodyData;

    }
    else if(actionType == 'replyAll' || actionType == 'forward') {
      let header
      if(ccData != '') {
        header = `
        <br>
        <div style="font-size:12pt"><hr></div>
        <br>
        <div style="font-size:11pt">
        <b>From:</b> ${fromData}
        <br>
        <b>Sent:</b> ${sentTime}
        <br>
        <b>To:</b> ${toData}
        <br>
        <b>Cc:</b> ${ccData}
        <br>
        <b>Subject:</b> ${subjectData}
        </div>
        <br>
        <br>
        ` 
      }
      else {
        header = `
        <br>
        <div style="font-size:12pt"><hr></div>
        <br>
        <div style="font-size:11pt">
        <b>From:</b> ${fromData}
        <br>
        <b>Sent:</b> ${sentTime}
        <br>
        <b>To:</b> ${toData}
        <br>
        <b>Subject:</b> ${subjectData}
        </div>
        <br>
        <br>
        ` 
      }

      if(selectedData.body.contentType == 'html') {
        formatedBodyData = header + selectedData.body.content
      }
      
      return formatedBodyData;

    }
  }

  formatSubjectForPatching(data,actionType) {
    if(actionType == 'reply' || actionType == 'replyAll') {
      return data.subject
    }
    else if(actionType == 'forward') {
      return data.subject
    }
    
  }

  formatMailIdsForPatching(data,recipientType) {
    let temp = []
    if(data && recipientType) {
      if(data[recipientType]?.length > 0) {
        data[recipientType].forEach((item,index) => {
          temp.push({
            id:index,
            name:item.emailAddress.address
          })
        })
      }
    }
    return temp
  }

  constructQueryForGraphApi(mailList,folderName) {
    let url = `https://graph.microsoft.com/v1.0/me/mailFolders/${folderName}/messages?`
    let kqlString = ``

    mailList = _.filter(mailList,(item) => { return (item && item!='')})

    if(folderName == 'sentItems') {

      //<--sample URL---->
      //https://graph.microsoft.com/v1.0/me/mailFolders/sentItems/messages?&search="recipients:(sramprasad@kaartech.<NAME_EMAIL>)"

      if(mailList.length == 1) {
          kqlString += `&search="recipients:(${mailList[0]})"`
      }
      else {
        mailList.forEach((id,index) => {
  
          if(index == 0) {
            kqlString += `&search="recipients:(${id} OR`
          }
  
          else if(index == mailList.length-1) {
            kqlString += `${id})"`
          }

          else {
            kqlString += `${id} OR`
          }

        })
      }
    }
    else if(folderName == 'inbox') {

      //<--sample URL---->
      //https://graph.microsoft.com/v1.0/me/mailFolders/inbox/messages?&search="from:(sramprasad@kaartech.<NAME_EMAIL>)"

      if(mailList.length == 1) {
        kqlString += `&search="from:(${mailList[0]})"`
      }
      else {
        mailList.forEach((id,index) => {

          if(index == 0) {
            kqlString += `&search="from:(${id} OR `
          }
            
          else if(index == mailList.length-1) {
            kqlString += `${id})"`
          }

          else {
            kqlString += `${id} OR `
          }
          
        })
      }
    }
    if(kqlString && kqlString!='') {
      return url + kqlString
    }
    else {
      return ''
    }
  }

  /**
   * Called when mail data is scrolled
   */
  async mailListScrolledDown(){

    if(this.mUtilityData.receiveMailsFromKebs && !this.mUtilityData.receiveMailsFromO365) {
      this.mailUiData.isMailItemDataLoading = true
      this.mUtilityData.currentItemDataIndex += this.mUtilityData.defaultRecordsPerFetch
      let selectedMailFolder = _.findWhere(this.mailFolders,{isEnabled:true})
      let kebsMailList  = await this.getMailListFromKebs(selectedMailFolder.mongoKey,!this.mUtilityData.receiveMailsFromO365)

      if(kebsMailList['messType'] == 'S') {

        this.mUtilityData.mailItemList = this.mUtilityData.mailItemList.concat(kebsMailList['messData'])
        _.uniq(this.mUtilityData.mailItemList,l => l.id)
        this.mailFunctions.groupByMailThread()
        this.mailFunctions.initMiniCardSelection()
        this.mailUiData.isMailItemDataLoading = false

      }
      else {
        this.mailUiData.isMailItemDataLoading = false
      }

    }

  }

  constructThreadQueryForGraphApi(graphMailDataList,folderName) {

    //<----Sample URL---->
    //https://graph.microsoft.com/v1.0/me/mailFolders/inbox/messages?$filter=((conversationId eq 'AAQkADc1YjdlYWI0LThkOTQtNGMwMS05NmJlLTZlZWNiNTBjODQzZAAQAEr6TTwL1plBt7NX7IlDlvw=') OR (conversationId eq 'AAQkADc1YjdlYWI0LThkOTQtNGMwMS05NmJlLTZlZWNiNTBjODQzZAAQAHg5ed3cbEbIlSZ3UbpNZsk='))&$top=50
    //https://graph.microsoft.com/v1.0/me/mailFolders/inbox/messages?$filter=(conversationId eq 'AAQkADc1YjdlYWI0LThkOTQtNGMwMS05NmJlLTZlZWNiNTBjODQzZAAQAEr6TTwL1plBt7NX7IlDlvw=')&$top=100
    let url 

    if(folderName == 'sentItems') {
      url = `https://graph.microsoft.com/v1.0/me/mailFolders/inbox/messages?`
    }
    else if(folderName == 'inbox') {
      url = `https://graph.microsoft.com/v1.0/me/mailFolders/sentItems/messages?`
    }
    let kqlString = ``

    if(graphMailDataList.length == 1) {
      kqlString += `$filter=(conversationId eq '${graphMailDataList[0].conversationId}')&top=50`
    }
    else {
      graphMailDataList.forEach((item,index) => {
        if(index == 0) {
          kqlString += `$filter=((conversationId eq '${item.conversationId}') OR ` 
        }
        else if(index == graphMailDataList.length-1) {
          kqlString +=`(conversationId eq '${item.conversationId}'))&top=50`
        }
        else {
          kqlString += `(conversationId eq '${item.conversationId}') OR ` 
        }
      })
    }

    return url+kqlString
  }

  /**
   * //TODO : Formats table structure in mail body,
   * without this function table will not appear properly
   * in the receiver end.
   * 
   * USAGE : Use If your template has a table
   */
  formatTableStructureForTemplate(body) {

    if(body.split('<tr>').length > 1) {
      body = body.split('style="border: 1px solid #000;"').join('style="border: 1px solid #000;border-collapse: collapse !important;"')
      body = body.split('style="text-align: center;').join('style="border: 1px solid #000;text-align: center;border-collapse: collapse !important;')
      let bodyArray = body.split('<tr>')
      let final=''
 
      bodyArray.forEach((ele,index) => {
          if(index==1 || index ==bodyArray.length-1) {
              let addStyle = '<tr>'
              ele = addStyle+ele;
              final+=ele
          }
          else if(index==0) {
            final+=ele
          }
          else {
            let addStyle="<tr>"
            ele = addStyle+ele;
            final+=ele
          }
      })
      return final
    }
    else {
      return body
    }

  }

  resetMailData() {

    this.mUtilityData = {
      recipientMailId : '',
      recipientMailIdArr:[],
      reloadBodyComponent: () => {},
      switchMailMode: () => {},
      currentMailMode: {},
      selectedMailData: {},
      isLoading: false,
      recipientMailList: [],
      reviewPagination: {},
      dunningMailData: [],
      applicationId: 0,
      selectedDunningMailData: {},
      isToRecipientFieldHasSaveButton: false,
      isCcRecipientFieldHasSaveButton: false,
      isBccRecipientFieldHasSaveButton: false,
      saveRecipientMailIds: () => {},
      saveRecipientMailIdType: {},
      authorizedMailSenders:[],
      currentUserMailId:'',
      callMailApi: () => {},
      o365Token:{},
      getMailFromKebsApiData:{},
      saveMailInKebsApiData:{},
      graphApiNextLink:{},
      receiveMailsFromO365:true,
      receiveMailsFromKebs:true,
      syncWithKebsOnSendMail:false,
      hasInitiateNewMailTemplate : false, //TODO: If application has initiate new mail template 
      newMailTemplateData:[], //TODO: Compose mail Template data to be inserted if any 
      selectedNewMailTemplateData:{}, //TODO: For internal operations
      formatTableForTemplate : false, // TODO: format table structure , if template has a table (eg.dunning,p2p)
      isTableLayoutAuto: false,
      currentItemDataIndex:0,
      defaultRecordsPerFetch:10,
      mailItemList:[],
      filesAttached: [],
      saveHistoryInKebsApiData:{},
      saveHistoryInTable: false,
      retrieveHistoryFromApi:{},
      initiateNewMailTemplateData:{},
      saveSpocName: () => {},
      spocSaveEnabled: false,
      autoAttachmentApplicable: false,
      destinationBucket:'',
      routingKey:'',
      contextId:'',
      attachmentRetrievalDetails:[],
      fromCommonMailId : false, 
      sendMailAPIDetails:{}
    }

    this.mailUiData = {

      mailInputFields: this.formBuilder.group({
        toRecipients: ['', Validators.required],
        ccRecipients: [''],
        bccRecipients: [''],
        subject: [''],
        body: [''],
        fromMailIds: [''],
        spocName:''
      }),
      selectedMailDataUi:{},
      selectedBodyDataUi:{},
      conversationIdKeys:[],
      miniCardInputData:{},
      isExpandBody:false,
      progressSpinner:false,
      selectedMailFolder:{},
      isSyncWithKebsBtn:false,
      currentMailFolder:{},
      isMailItemDataLoading:false,
      showHistoryButton: false,
      showSpocName: false,
      showTitle: false,
      title:''
      
    }

  }

  clearMailData() {
    this.mUtilityData = JSON.parse(JSON.stringify(this.mUtilityDataDefault));
    this.mailUiData = JSON.parse(JSON.stringify(this.mailUiDataDefault));
  }

  /**
   * Sweet alert
   * @param title String
   * @param body String
   * @returns 
   */
  confirmSweetAlert(title, body) {
    return sweetAlert.fire({
      title: title,
      text: body,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, keep it',
    });
  }
  /**
   * Sweet alert
   * @param title String
   * @param body String
   * @returns 
   */
  sendSweetAlert(title, body) {
    return sweetAlert.fire({
      title: title,
      text: body,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, go ahead!',
      cancelButtonText: 'No, let me think',
    });
  }
async initiateNewMail () {
    this.mUtilityData.currentMailMode['mode'] = 
      this.mUtilityData.currentMailMode['mode'] == 'create'
      ? 'not selected'
      : 'create'
    // this.mailUiData.isExpandBody = false
    // this.mailFunctions.switchViewMode()
    if( this.mUtilityData.currentMailMode['mode'] == 'create'){
      if(this.mUtilityData.initiateNewMailTemplateData['url'] && this.mUtilityData.initiateNewMailTemplateData['jwtToken']  && this.mUtilityData.spocSaveEnabled == false) {
        try {

          this.mUtilityData.isLoading = true
          let body = this.mUtilityData.initiateNewMailTemplateData['paramsArr']
         let refreshedData = await this._http.post(this.mUtilityData.initiateNewMailTemplateData['url'],
            body,{
            headers: new HttpHeaders({
              'Authorization': 'Bearer ' + this.mUtilityData.initiateNewMailTemplateData['jwtToken']
            })
          }
          ).toPromise();
          this.mUtilityData['newMailTemplateData']=[];
          this.mUtilityData['newMailTemplateData']
          .push(refreshedData)
          this.mUtilityData.currentMailMode = { mode: 'create' }
          this.mUtilityData['hasInitiateNewMailTemplate'] = true
          this.mUtilityData.selectedNewMailTemplateData = this.mUtilityData.newMailTemplateData[0]
          this.mailFunctions.switchViewMode()
          this.mUtilityData.isLoading = false
        }
        catch(err) {
          this.mUtilityData.isLoading = false
          this.snackBar.open("Unable to refresh","Dismiss",{duration:2000})
        }
      }
      else {
        this.mUtilityData.isLoading = false
        this.mailUiData.isExpandBody = false
        this.mUtilityData.selectedNewMailTemplateData = this.mUtilityData.newMailTemplateData[0]
        this.mailFunctions.switchViewMode()
        this.mUtilityData.spocSaveEnabled = false
      }
    }
    else{
      this.mailUiData.isExpandBody = false
      this.mailFunctions.switchViewMode()
    }

  }
  

  /**
 * Sends mail through graph api
 * @param subject String
 * @param toMail Array
 * @param ccMail Array
 * @param bccMail Array
 * @param emailContent String
 * @param operationType String (send,reply,replyAll,forward)
 * 
 */
  async deliverMailByGraphApi(subject:any, toMail:any, ccMail:any, bccMail:any, emailContent:any, operationType:any, attachment: any) {

    let recipient = [], ccRecipient = [], bccRecipient = []

    for (let i=0 ; i<toMail.length ; i++){
      recipient.push({
        emailAddress:{
          address : toMail[i]
        }
      })
    }
    for (let i=0 ; i<ccMail.length ; i++){
      ccRecipient.push({
        emailAddress:{
          address : ccMail[i]
        }
      })
    }

    for (let i=0 ; i<bccMail.length ; i++){
      bccRecipient.push({
        emailAddress:{
          address : bccMail[i]
        }
      })
    }
  
    
    try{

      const body = {
        message : {
          subject: subject,
          body: {
            contentType: "Html",
            content: emailContent
          },
          toRecipients: recipient,
          ccRecipients: [],
          bccRecipients: [],
          attachments: []
        }
      }
      if(ccMail.length > 0 && bccMail.length > 0){
        body.message.ccRecipients = ccRecipient
        body.message.bccRecipients = bccRecipient
      }
      else if(ccMail.length > 0) {
        body.message.ccRecipients = ccRecipient
      }
      else if (bccMail.length > 0) {
        body.message.bccRecipients = bccRecipient
      }
      else{
        body.message.bccRecipients = undefined
        body.message.ccRecipients = undefined
      }
    
    let url = ``
    if(attachment.length != undefined)
    {
      // Attach selected files to the email  
      if (this.mUtilityData.autoAttachmentApplicable) {
        for (const file of attachment) {
          console.log(file)
          console.log("file save")
          let res = await this._sharedService.getDownloadUrl(file?.cdn_link).toPromise();
          const fileData = await this._http.get(res["data"], { responseType: 'blob' }).toPromise();
          const fileName = file.file_name;
          const base64Content = await this.blobToBase64(fileData);

          body.message.attachments.push({
            "@odata.type": "#microsoft.graph.fileAttachment",
            name: fileName,
            contentBytes: base64Content,
            isInline: false // Set to true if you want the attachment to be displayed inline in the email
          });
        }
      }
      else {
        for (const file of attachment) {
          body.message.attachments.push({
            "@odata.type": "#microsoft.graph.fileAttachment",
            name: file.name,
            contentBytes: await this.readFileContent(file),
          })
        }
      }
    }
    else{
      // attachment not included in mail
    }

    if(!this.mUtilityData.fromCommonMailId){

    if(operationType == 'createNew') {
      url = "https://graph.microsoft.com/v1.0/me/sendMail";
    }
    else if(operationType == 'reply') {
      let unique_mail_id = this.mailUiData.selectedMailDataUi['id']
      url = `https://graph.microsoft.com/v1.0/me/messages/${unique_mail_id}/reply`
    }
    else if(operationType == 'replyAll') {
      let unique_mail_id = this.mailUiData.selectedMailDataUi['id']
      url = `https://graph.microsoft.com/v1.0/me/messages/${unique_mail_id}/replyAll`
    }
    else if(operationType == 'forward') {
      let unique_mail_id = this.mailUiData.selectedMailDataUi['id']
      url = `https://graph.microsoft.com/v1.0/me/messages/${unique_mail_id}/forward`
    }


    console.log(`mail-box-content`)
    console.log(body)
    const result = await this._http.post(url,
            body, 
            {
              headers: new HttpHeaders({
                'Authorization': "Bearer " + (await this.mUtilityData.o365Token['token']),
                "Content-Type": "application/json"
              })
            })
          .pipe(takeUntil(this.$onMailFolderSwitched))
          .toPromise();
          console.log(result);
          return Promise.resolve(result);
    
    }

    if(this.mUtilityData.fromCommonMailId && this.mUtilityData.sendMailAPIDetails['url']){
      let tokenUrl = this.mUtilityData.sendMailAPIDetails['url']

      const body = {
        message : {
          subject: subject,
          body: {
            contentType: "Html",
            content: emailContent
          },
          toRecipients: recipient,
          ccRecipients: [],
          bccRecipients: [],
          attachments: [],
          from: {}
        }
      }
      if(ccMail.length > 0 && bccMail.length > 0){
        body.message.ccRecipients = ccRecipient
        body.message.bccRecipients = bccRecipient
      }
      else if(ccMail.length > 0) {
        body.message.ccRecipients = ccRecipient
      }
      else if (bccMail.length > 0) {
        body.message.bccRecipients = bccRecipient
      }
      else{
        body.message.bccRecipients = undefined
        body.message.ccRecipients = undefined
      }

      body.message.from = {
        emailAddress: {
          address: this.mUtilityData.currentUserMailId
        }
      };
      
    if(attachment.length != undefined)
    {
      // Attach selected files to the email  
      if (this.mUtilityData.autoAttachmentApplicable) {
        for (const file of attachment) {
          console.log(file)
          let res = await this._sharedService.getDownloadUrl(file?.cdn_link).toPromise();
          const fileData = await this._http.get(res["data"], { responseType: 'blob' }).toPromise();
          const fileName = file.file_name;
          const base64Content = await this.blobToBase64(fileData);

          body.message.attachments.push({
            "@odata.type": "#microsoft.graph.fileAttachment",
            name: fileName,
            contentBytes: base64Content,
            isInline: false // Set to true if you want the attachment to be displayed inline in the email
          });
        }
      }
      else {
        for (const file of attachment) {
          body.message.attachments.push({
            "@odata.type": "#microsoft.graph.fileAttachment",
            name: file.name,
            contentBytes: await this.readFileContent(file),
          })
        }
      }
    }
    else{
      // attachment not included in mail
    }

      let response = await this._http.post(tokenUrl,
        {mailBody: body,
        fromMailId: this.mUtilityData.currentUserMailId,
        operationType: operationType
        },{
        headers: new HttpHeaders({
          'Authorization': 'Bearer ' + this.mUtilityData.sendMailAPIDetails['jwtToken']
        })
      }
      ).toPromise();
      
      console.log("response")
      console.log('Access Token:', response);

      if(response["messType"] == 'S'){
        return Promise.resolve();
      }

      if(response["messType"] == 'E'){
        return Promise.reject();
      }

    }
        } 
          catch (err) {
            console.log(err)
          return Promise.reject();
        }
  }
  
  /**
   * Gets mail data from graph api
   * @param mailFolderName Type of the folder to access
   * @returns 
   */
  async getMailListFromGraphApi(url) {
    try {

      let res = this._http.get(url,
        { 
          headers: new HttpHeaders({
            'Authorization': 'Bearer ' + (await this.mUtilityData.o365Token['token'])
          })
        }
      )
      .pipe(takeUntil(this.$onMailFolderSwitched))
      .toPromise()

      return Promise.resolve(res)

    }
    catch(err) {

      Promise.reject(err)
      
    }
  }

  // filterGraphApiMailList(mailFolderName,res) {

  //   let result = []

  //   if(res['value'] && res['value'].length > 0) {
  //     _.each(res['value'],(item) => {
  //       item['is_from_kebs'] = false
  //       if(mailFolderName == 'sentItems') {
  //         _.each(item.toRecipients,(toResp)=>{
  //           if(this.mUtilityData.recipientMailIdArr.includes(toResp.emailAddress?.address)) {
  //             result.push(item)
  //           }
  //         })
  
  //         _.each(item.ccRecipients,(ccResp)=>{
  //           if(this.mUtilityData.recipientMailIdArr.includes(ccResp.emailAddress?.address)) {
  //             result.push(item)
  //           }
  //         })
  
  //         _.each(item.bccRecipients,(bccResp)=>{
  //           if(this.mUtilityData.recipientMailIdArr.includes(bccResp.emailAddress?.address)) {
  //             result.push(item)
  //           }
  //         })
  
  //         _.each(item.replyTo,(replyTo)=>{
  //           if(this.mUtilityData.recipientMailIdArr.includes(replyTo.emailAddress?.address)) {
  //             result.push(item)
  //           }
  //         })
  
  //       }
  //       else if(mailFolderName == 'inbox') {
  //         if(this.mUtilityData.recipientMailIdArr
  //           .includes(item.from.emailAddress.address) || 
  //           this.mUtilityData.recipientMailIdArr
  //           .includes(item.sender.emailAddress.address)) {
  //           result.push(item)
  //         }
  
  //       }
  //     })
      
  //     return result
  //   }
  //   else {
  //     return []
  //   }

  // }

  async saveMailItemInKebs(mailItem) {
    let selectedMailFolder = _.where(this.mailFolders,{isEnabled : true})[0]

    if(this.mUtilityData.saveMailInKebsApiData['url'] && this.mUtilityData.saveMailInKebsApiData['jwtToken'] && this.mUtilityData.saveMailInKebsApiData['paramsArr']) {

      let param = _.where(this.mUtilityData.saveMailInKebsApiData['paramsArr'],{applicationId:this.mUtilityData.applicationId})[0]
      let body = {
        applicationId : param.applicationId,
        objectId:param.objectIdArr[0],
        mailFolder:selectedMailFolder.mongoKey,
        mailItemArr:mailItem
      }

      try {
        await this._http.post(this.mUtilityData.saveMailInKebsApiData['url'],
        body,
        {
          headers: new HttpHeaders({
            'Authorization': 'Bearer ' + this.mUtilityData.saveMailInKebsApiData['jwtToken']
          })
        }).subscribe((res:any) => {

          if(res.messType == 'S') {

            let convoId = mailItem[0]['conversationId']

            let id = mailItem[0]['id']

            _.each(this.mailUiData.miniCardInputData[convoId],(item)=> {
              if(item['id']==id) {
                item['isFromKebs'] = true
              }
            })

            this.snackBar.open("Mail synced successfully","Dismiss",{duration:2000})
          }
          else if(res.messType == 'E') {
          this.snackBar.open(res.userMess,"Dismiss",{duration:2000})
          }

        },(err) => {
          console.log(err)
          this.snackBar.open("Unable to sync mail with KEBS","Dismiss",{duration:2000})
        })
      }
      catch(err) {
        this.snackBar.open("Unable to sync mail with KEBS","Dismiss",{duration:2000})
      }
    }
    else {
      this.snackBar.open("Unable to sync mail with KEBS","Dismiss",{duration:2000})
      console.log("Invalid API details provided to save mail in KEBS")
      console.log(this.mUtilityData.saveMailInKebsApiData)
    }

  }

  async saveHistoryInTable() {

    if(this.mUtilityData.saveHistoryInKebsApiData['url'] && this.mUtilityData.saveHistoryInKebsApiData['jwtToken']) {
      try {
        let body = this.mUtilityData.saveHistoryInKebsApiData['paramsArr']
        await this._http.post(this.mUtilityData.saveHistoryInKebsApiData['url'],
          body,{
          headers: new HttpHeaders({
            'Authorization': 'Bearer ' + this.mUtilityData.saveHistoryInKebsApiData['jwtToken']
          })
        }
        ).toPromise();
      }
      catch(err) {
        this.snackBar.open("Unable to save history in KEBS","Dismiss",{duration:2000})
      }
    }
    else {
      this.snackBar.open("Unable to save history in KEBS","Dismiss",{duration:2000})
      console.log("Invalid API details provided to save history in KEBS")
      console.log(this.mUtilityData.saveHistoryInKebsApiData)
    }

  }

  async getMailListFromKebs(mailFolderName,shouldLazyLoad) {

    if(this.mUtilityData.getMailFromKebsApiData['url'] && this.mUtilityData.getMailFromKebsApiData['jwtToken'] && this.mUtilityData.getMailFromKebsApiData['paramsArr']) {
      let body = {
        paramsArr:this.mUtilityData.getMailFromKebsApiData['paramsArr'],
        mailFolder : mailFolderName,
        shouldLazyLoad : shouldLazyLoad,
        skip: this.mUtilityData.currentItemDataIndex,
        limit: this.mUtilityData.defaultRecordsPerFetch
      }
      try {
        let res = this._http.post(this.mUtilityData.getMailFromKebsApiData['url'],
        body,
        {
          headers: new HttpHeaders({
            'Authorization': 'Bearer ' + this.mUtilityData.getMailFromKebsApiData['jwtToken']
          })
        }).toPromise()
  
        return Promise.resolve(res)
      }
      catch(err) {
        console.log('err',err)
        Promise.reject(err)
      }
    }
    else {
      console.log("Warning : No API related info provided for fetching mail from KEBS")
      console.log(this.mUtilityData.getMailFromKebsApiData)
      return Promise.resolve({
        messType:'E'
      })
    }

  }



  getInitialsFromMailData (mailData,recipientMailId) {

    let initials, recipientMailName

    for(let item of mailData.toRecipients){

      if(item.emailAddress.address == recipientMailId) {
        recipientMailName = item.emailAddress.name;
        break;
      }
    }

    if(recipientMailName) {

      let nameArray

      nameArray = recipientMailName.split(' ');

      if(nameArray.length > 1) {
        initials = (nameArray.shift().charAt(0) + nameArray.pop().charAt(0)).toUpperCase();
      }
      else if(nameArray.length == 1) {
        initials = (nameArray[0].charAt(0)).toUpperCase()
      }
    }

    else {
      let formatedMailId = recipientMailId.substring(0,recipientMailId.lastIndexOf("@"));
      let name = formatedMailId.substring(1,formatedMailId.length)
      recipientMailName = name.charAt(0).toUpperCase()+name.slice(1)
      initials = formatedMailId.substring(0,2).toUpperCase()
    }
    
    return {
      recipientInitial : initials,
      recipientName : recipientMailName
    };
  }

  /**
   * For dunning data 
   * @param field_name String
   * @returns 
   */
  getFormatedValueForMailIds (field_name) {

      let temp = []

      if(this.mUtilityData.hasInitiateNewMailTemplate) {
        console.log('selected data')
        console.log(this.mUtilityData.selectedNewMailTemplateData)
        if(
          this.mUtilityData.selectedNewMailTemplateData[`${field_name}`] 
          && 
          this.mUtilityData.selectedNewMailTemplateData[`${field_name}`].length > 0
          ) {
          console.log("inside getFormatedValueForMailIds")
          console.log(field_name)
          this.mUtilityData.selectedNewMailTemplateData[`${field_name}`].forEach((element,index) => {
            temp.push({
              id : index,
              name : element
            })
          })
        }
        console.log("selectedNewMailTemplateData")
        console.log(this.mUtilityData.selectedNewMailTemplateData)
      }
      else {
        if(field_name == 'toMailIds') {
          this.mUtilityData.recipientMailIdArr.forEach((element,index) => {
            temp.push({
              id:index,
              name : element
            })
          })
        }
      }

      console.log(temp)
      return temp
  }

  resetData () {
    
    // this.mUtilityData = {
    //   recipientMailId : '',
    //   reloadBodyComponent: () => {},
    //   switchMailMode: () => {},
    //   currentMailMode: {},
    //   selectedMailData: {},
    //   isLoading: false,
    //   recipientMailList: [],
    //   dunningMailData: [],
    //   applicationId: 0,
    //   selectedDunningMailData: {},
    //   isToRecipientFieldHasSaveButton: false,
    //   isCcRecipientFieldHasSaveButton: false,
    //   isBccRecipientFieldHasSaveButton: false,
    //   saveRecipientMailIds: () => {},
    //   saveRecipientMailIdType: {},
    //   // sendMail:() => {},
    //   // sendMailData: {},
    //   currentUserMailId:'',
    //   callMailApi: () => {},
    //   o365Token:{}
  
    // }
  }


//   formatDunningDataForMailComponent = (data : any) => {

//     let result = {}
//     let fromMailIds
//     let toMailIds
//     let ccMailIds
//     let bccMailIds
//     let alertFlag 
//     let customerName
//     let currency
//     let totalValueFinal
//     let paymentTermDays
//     let paymentDaysColumn
//     let differentPaymentDays
//     let outstandingValue : any = []
//     let finalOutstandingValueArray : any = []
//     let isOutstandingValueZero
//     let finalTodaysDate = moment().format('DD-MM-YYYY');

//     let bodyText = ''
//     let text = ''
//     let finalText = ''
//     let subjectTxt = 
//     'Outstanding details as on ' +
//     finalTodaysDate +
//     ' for your reference.';

//     fromMailIds = _.pluck(data.from_mail_id,'from_mail_ID');

//     toMailIds = data.to_mail_id ? data.to_mail_id : [];

//     ccMailIds = data.cc ? data.cc : [];


//     bccMailIds = data.bcc ? data.bcc : [];

//     alertFlag = data.alertFlag;

//     customerName = _.uniq(
//       _.pluck(data.result, 'customer_name')
//     );

//     currency = _.uniq(
//       _.pluck(data.result, 'currency')
//     );

//     totalValueFinal = data.totalOutstandingValue

//     paymentTermDays = data.creditPeriodDays 
//     ? data.creditPeriodDays
//     : 30;

//     paymentDaysColumn = _.every(
//       data.result,
//       (val) =>
//         val.Payment_term_days ==
//         data.result[0].Payment_term_days
//     );

//     differentPaymentDays = _.uniq(
//       _.pluck(data.result, 'Payment_term_days')
//     );
    
//     for (let j = 0; j < differentPaymentDays.length; j++) {
//       let temp = 0;

//       for (let i = 0; i < data.result.length; i++) {
//         if (
//           data.result[i].Credit_period_days ==
//           'Overdue Payment'
//         ) {
//           if (
//             differentPaymentDays[j] ==
//             data.result[i].Payment_term_days
//           ) {
//             temp += Number(data.result[i].Value);
//           }
//         }
//       }

//       outstandingValue.push(temp);
//     }

//     for (let i = 0; i < differentPaymentDays.length; i++) {
//       let tempArray = {
//         days: differentPaymentDays[i],
//         value: outstandingValue[i],
//       };
//       finalOutstandingValueArray.push(tempArray);
//     }

//     if (outstandingValue == 0) {
//       isOutstandingValueZero = true;
//     }


//     bodyText += 'Dear Team,<br/>';
//     bodyText += 'Greetings!<br/>';
//     bodyText +=
//       'Please find the below Kaar Technologies Outstanding details as on ' +
//       finalTodaysDate +
//       ' for your reference.<br/>';
//       bodyText +=
//       '<ul><li> Total outstanding value ' +
//       currency +
//       ' ' +
//       totalValueFinal +
//       '</li>';
   
//     if(isOutstandingValueZero == false){
//     for(let i=0;i< finalOutstandingValueArray.length;i++){
//         bodyText +=
//         "<li style='color:red'> More than " +
//         finalOutstandingValueArray[i].days +
//         ' days outstanding value ' +
//         currency +
//         ' ' +
//         finalOutstandingValueArray[i].value +
//         '</li>';
//     }        
// }
//     bodyText +=
//       '<li>if you have any concerns please let us know and request you to share the payment advice along with Invoice numbers.</li></ul>';
//     bodyText +=
//       '<br/><b>Request you to release the payments at the earliest</b><br/><br/>';

//     text +=
//       "<table border='1' style='width:1000px !important; border-collapse: collapse !important;'>";
//     text += "<tr>";
//     text += "<th width='200' style='text-align:center;'>Project</th>";
//     text +=
//       "<th width='200' style='text-align:center;'>Milestone Name</th>";
//     text +=
//       "<th width='100' style='text-align:center;'>Invoice Date</th>";
//     text +=
//       "<th width='100' style='text-align:center;'>Invoice Number</th>";
//     text +=
//       "<th width='100' style='text-align:center;'>Value in " +
//       currency +
//       '</th>';
//     text += "<th width='100' style='text-align:center;'>Aging</th>";
//     if (paymentDaysColumn == false) {
//       text +=
//         "<th width='100' style='text-align:center;'>Payment Term Days</th>";
//       text +=
//         "<th width='100' style='text-align:center;'>Payment Status</th>";
//     }
//     if (paymentDaysColumn == true) {
//       text +=
//         "<th width='100' style='text-align:center;'>" +
//         paymentTermDays +
//         ' days credit period</th>';
//     }
//     text += '</tr>';

//     _.each(data.result, (val) => {
//       text += "<tr>";
//       text +=
//         "<td width='200' style='text-align:center; font-weight:bold;'>" +
//         val.project_name +
//         '</td>';
//       text +=
//         "<td width='200' style='text-align:center; font-weight:bold;'>" +
//         val.milestone_name +
//         '</td>';
//       text +=
//         "<td width='100' style='text-align:center; font-weight:bold;'>" +
//         val.invoice_date +
//         '</td>';
//       text +=
//         "<td width='100' style='text-align:center; font-weight:bold;'>" +
//         val.invoice_no +
//         '</td>';
//       text +=
//         "<td width='100' style='text-align:center; font-weight:bold;'>" +
//         val.Value +
//         '</td>';
//       text +=
//         "<td width='100' style='text-align:center; font-weight:bold;'>" +
//         val.Ageing +
//         '</td>';

//       if (paymentDaysColumn == false) {
//         text +=
//           "<td width='100' style='text-align:center; font-weight:bold;'>" +
//           val.Payment_term_days +
//           '</td>';
//       }

//       if (
//         val.Credit_period_days == 'Overdue Payment' &&
//         alertFlag == false
//       ) {
//         text +=
//           "<td width='100' style='text-align:center; font-weight:bold; color:#cf0001;'>" +
//           val.Credit_period_days +
//           '</td>';
//       } else {
//         text +=
//           "<td width='100' style='text-align:center; font-weight:bold;'>" +
//           val.Credit_period_days +
//           '</td>';
//       }
//       text += '</tr>';
//     });

//     text += "<tr>";
//     text +=
//       "<td width='200' style='text-align:center; font-weight:bold;'>Total Outstanding</td>";
//     text +=
//       "<td width='200' style='text-align:center; font-weight:bold;'></td>";
//     text +=
//       "<td width='100' style='text-align:center; font-weight:bold;'></td>";
//     text +=
//       "<td width='100' style='text-align:center; font-weight:bold;'></td>";
//     text +=
//       "<td width='100' style='text-align:center; font-weight:bold;'>" +
//       totalValueFinal +
//       ' ' +
//       currency +
//       '</td>';
//     if (paymentDaysColumn == false) {
//       text +=
//         "<td width='100' style='text-align:center; font-weight:bold;'></td>";
//     }
//     text +=
//       "<td width='100' style='text-align:center; font-weight:bold;'></td>";
//     text +=
//       "<td width='100' style='text-align:center; font-weight:bold;'></td>";
//     text += '</tr>';

//     text += '</table><br/>';
//     finalText += `<b>Note: "This is an auto generated dunning remainder.
//     If payment is yet to be done, kindly do it within next two days for the Overdue invoices and notify us.
//     Please ignore this email, if payment is already initiated." <br/><br/>`;
//     finalText += '<b>Thanks and Regards <br/>';
//     finalText += '<b>Central Business Finance <br/>';
//     finalText +=
//       "<img src='https://assets.kebs.app/images/KAAR.png' height='75' width='75'/><br/>";
//     // this.finalText += "<b>Kaar Technologies <br/>";
//     finalText += '<b>Kaar Technologies <br/>';
//     finalText += '<b><EMAIL> | www.kaartech.com <br/>';
//     finalText += '<br/><br/>';

//     let mailBody =  bodyText+text+finalText
//     result = {

//       fromMailIds : fromMailIds,
//       toMailIds : toMailIds,
//       ccMailIds : ccMailIds,
//       authorizedMailSenders: fromMailIds,
//       customerId:data.result[0].customer_id,
//       customerName:customerName,
//       bccMailIds: bccMailIds,
//       subjectTxt:subjectTxt,
//       mailBody : mailBody
      
//     }

//     return result

//   }


  /**
   * <AUTHOR> Kumaran S
   * @param file 
   * @returns 
   */
  // Function to read file content as base64
  async readFileContent(file: File): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const content = reader.result as string;
        const base64Content = btoa(content);
        resolve(base64Content);
      };
      reader.onerror = reject;
      reader.readAsBinaryString(file);
    });
  }

   // Function to convert Blob to base64
   async blobToBase64(blob: Blob): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        resolve(base64String.split(',')[1]);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  getMailHistory(body, url, token) {
    const httpOptions = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + token
      })
    };

    return new Promise((resolve, reject) => {
      this._http
        .post(url, body, httpOptions)
        .subscribe(
          res => {
            return resolve(res);
          },
          err => console.log(err)
        );
    });
  }

}
