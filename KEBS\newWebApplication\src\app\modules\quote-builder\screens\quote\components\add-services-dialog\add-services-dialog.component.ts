import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormControl, FormGroup, FormBuilder, Validators, FormArray, AbstractControl } from '@angular/forms';

import { Subject, Subscription } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';

import { QBMasterDataService } from '../../../../services/qb-master-data.service';
import { QuoteMainService } from 'src/app/modules/quote-builder/services/quote-main.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { OpportunityService } from "src/app/modules/opportunities/features/opportunities-detail/services/OpportunityService";

@Component({
  selector: 'app-add-services-dialog',
  templateUrl: './add-services-dialog.component.html',
  styleUrls: ['./add-services-dialog.component.scss']
})
export class AddServicesDialogComponent implements OnInit {

  searchFormControl = new FormControl();
  servicesForm: FormGroup;

  protected _onDestroy = new Subject<void>();
  valueChangeSubscription = new Subscription();
  masterDataSubscription = new Subscription();

  enableAddServiceButton: boolean = false;
  isServiceDataLoading: boolean = false;

  tempServicesList = [];
  selectedServices = [];
  selectedPositions = [];
  positionMetaData = [];

  constructor(
    public matDialogRef: MatDialogRef<AddServicesDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public inData: any,
    private fb: FormBuilder,
    private _masterDataService: QBMasterDataService,
    private _quoteService: QuoteMainService,
    private _toaster: ToasterService,
    private opportunityService: OpportunityService,

  ) {
    this.servicesForm = this.fb.group({
      services: this.fb.array([])
    });
  }

  ngOnInit(): void {

    if (this.inData.opportunityId)
      this.patchServiceData();

    else
      this._toaster.showError("Error", "Opportunity ID not found !", this.opportunityService.longInterval);

    this.resolvePositionMetaData();

    this.valueChangeSubscription.add(this.searchFormControl.valueChanges.pipe(debounceTime(500), takeUntil(this._onDestroy)).subscribe(val => {

      this.resolveServiceFormArr(this.tempServicesList);

      if (val) {

        val = val.toLowerCase();

        const serviceFormArrList = this.servicesFormArr.value;

        for (let i = serviceFormArrList.length - 1; i >= 0; i--) {

          if (!serviceFormArrList[i]['serviceName'] || !serviceFormArrList[i]['serviceName'].toLowerCase().includes(val)) {

            const positionFormArr = this.servicesFormArr.at(i).get('positions') as FormArray;

            for (let j = serviceFormArrList[i]['positions'].length - 1; j >= 0; j--)
              if (!serviceFormArrList[i]['positions'][j]['positionName'] || !serviceFormArrList[i]['positions'][j]['positionName'].toLowerCase().includes(val))
                  positionFormArr.removeAt(j);

            if (positionFormArr.length == 0)
              this.servicesFormArr.removeAt(i);

          }

        }

        for (const serviceItem of this.servicesFormArr.controls)
          this.toggleService(serviceItem, true);

      }

      else
        for (const serviceItem of this.servicesFormArr.controls)
          this.toggleService(serviceItem, false);

    }));

  }

  resolvePositionMetaData = () => {

    if (this.inData.mandatoryFields && this.inData.mandatoryFields.length)
      for (const mandatoryItem of this.inData.mandatoryFields)
        switch (mandatoryItem) {

          case 'work_experience':

            this.positionMetaData.push({
              key: 'experience',
              label: 'Exp',
              masterData: this.inData.experienceList
            });

            break;

          case 'work_location':

            this.positionMetaData.push({
              key: 'workLocation',
              label: 'Loc',
              masterData: this.inData.workLocationList
            });

            break;

          case 'nationality':

            this.positionMetaData.push({
              key: 'nationality',
              label: 'Nat',
              masterData: this.inData.nationalityList
            });

            break;

          case 'entity':

            this.positionMetaData.push({
              key: 'entity',
              label: 'Enty',
              masterData: this.inData.entityList
            });

            break;

          case 'division':

            this.positionMetaData.push({
              key: 'division',
              label: 'Div',
              masterData: this.inData.divisionList
            });

            break;

          case 'sub_division':

            this.positionMetaData.push({
              key: 'subDivision',
              label: 'Sub-div',
              masterData: this.inData.subDivisionList
            });

            break;

          default:
            break;

        }

  }

  /**
   * @description Patches the API data to service form array
   */
  patchServiceData = () => {

    this.isServiceDataLoading = true;

    this.masterDataSubscription = this._quoteService.getServiceList(this.inData.opportunityId, this.inData.mandatoryFields, this.inData.defaultCurrency, this.inData.quoteCurrency, this.inData.conversionTypeId)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(res => {

      if (res["messType"] == "S" && res["data"]) {

        let servicesList = res["data"];

        this.tempServicesList = res["data"];

        this.resolveServiceFormArr(servicesList);

      }

      else
        this._toaster.showError("Error", res["messText"], this.opportunityService.longInterval);

      this.isServiceDataLoading = false;

    },
     err => {
      console.log(err);
      this._toaster.showError("Error", "Error in retrieving Service list", this.opportunityService.longInterval);
     });

  }

  /**
   * @description Pushes value into servicesFormArr
   * @param serviceList 
   */
  resolveServiceFormArr = (serviceList) => {

    if (serviceList.length) {

      this.servicesFormArr.clear();

      serviceList.forEach((serviceItem: any, i) => {

        let servicesFormGroup = this.getServicesFormGroup();

        let positionFormArr = servicesFormGroup.get('positions') as FormArray;

        positionFormArr.clear();

        if (serviceItem['position'].length > 0)
          for (let positionItem of serviceItem['position']) {

            let positionFormGroup = this.getPositionFormGroup();

            positionFormGroup.patchValue({
              servicePositionId: `${serviceItem['service_header_id']}-${positionItem['id']}`,
              positionId: positionItem['id'],
              positionName: positionItem['name'],
              experience: positionItem['work_experience'],
              workLocation: positionItem['work_location'],
              nationality: positionItem['nationality'],
              entity: positionItem['entity'],
              division: positionItem['division'],
              subDivision: positionItem['sub_division'],
              unit: positionItem['unit_id'],
              revenue: positionItem['revenue'],
              cost: positionItem['cost'],
              isSelected: this.selectedPositions.includes(`${serviceItem['service_header_id']}-${positionItem['id']}`),
              isLicense: positionItem['is_license'] === 1
            });

            positionFormArr.push(positionFormGroup);

          }

        servicesFormGroup.patchValue(
          {
            serviceId: serviceItem['service_header_id'],
            serviceName: serviceItem['service_name'],
            serviceTypeId: serviceItem['service_type_id'] || null,
            isFixedRate: serviceItem['is_fixed_rate'] || null,
            isSelected: this.selectedServices.includes(serviceItem['service_header_id']),
          }, 
          { 
            emitEvent: false 
          }
        );

        this.servicesFormArr.push(servicesFormGroup);

      });

    }

  }

  /**
   * @description Returns the services form group
   * @returns FormGroup
   */
  getServicesFormGroup = (): FormGroup => {

    const servicesFormGroup = this.fb.group({
      serviceId: [null],
      serviceName: [null, Validators.required],
      serviceTypeId: [null],
      isFixedRate: [null],
      isInDeterMinate: [false],
      isSelected: [false],
      isExpanded: [false],
      positions: this.fb.array([])
    });

    this.valueChangeSubscription.add(servicesFormGroup.get('isSelected').valueChanges.pipe(debounceTime(10), takeUntil(this._onDestroy)).subscribe(res => {

      const serviceIndex = this.servicesFormArr.controls.findIndex(
        (control) => control.get('serviceId').value === servicesFormGroup.get('serviceId').value
      );

      this.resolveSelectAllUn(serviceIndex);

      const positions = servicesFormGroup.get('positions') as FormArray;

      for (const positionFormGroup of positions.controls)
        this.resolveSelectedPositions(positionFormGroup, res);

      this.resolveSelectedServices(servicesFormGroup, res);

    }));

    return servicesFormGroup;

  }

  get servicesFormArr() {
    return this.servicesForm.get('services') as FormArray;
  }

  /**
   * @description Resolves the service select all state
   * @param serviceIndex
   */
  resolveSelectAll = (serviceIndex) => {

    const currentServiceItem = this.servicesFormArr.at(serviceIndex);

    currentServiceItem.get('isInDeterMinate').patchValue(false, { emitEvent: false });

    (currentServiceItem.get('positions') as FormArray).controls.forEach(positionItem => {

      positionItem.get('isSelected').patchValue(currentServiceItem.get('isSelected').value, { emitEvent: false });

    });

    this.enableAddServiceButton = false;

    for (const serviceItem of this.servicesFormArr.controls) {

      if (serviceItem.get('isSelected').value) {

        this.enableAddServiceButton = true;

        break;

      }

      const selectedPositions = serviceItem.get("positions").value.map(val => val['isSelected']);

      if (selectedPositions.includes(true)) {

        this.enableAddServiceButton = true;

        break;

      }

    }

  }

  /**
 * @description Resolves the service select all state
 * @param serviceIndex
 */
  resolveSelectAllUn = (serviceIndex) => {
    const currentServiceItem = this.servicesFormArr.at(serviceIndex);
    const isServiceSelected = currentServiceItem.get('isSelected').value;

    // If the service is unchecked, uncheck all positions
    if (!isServiceSelected) {
      currentServiceItem.get('isInDeterMinate').patchValue(false, { emitEvent: false });

      (currentServiceItem.get('positions') as FormArray).controls.forEach(positionItem => {
        positionItem.get('isSelected').patchValue(false, { emitEvent: false });
      });
    }

    // Check if any service or any position is selected to enable add button
    this.enableAddServiceButton = false;

    for (const serviceItem of this.servicesFormArr.controls) {

      if (serviceItem.get('isSelected').value) {

        this.enableAddServiceButton = true;

        break;

      }

      const selectedPositions = serviceItem.get("positions").value.map(val => val['isSelected']);

      if (selectedPositions.includes(true)) {

        this.enableAddServiceButton = true;

        break;

      }

    }

  };

  /**
   * @description Returns the position form group
   * @returns FormGroup
   */
  getPositionFormGroup = (): FormGroup => {

    const positionFormGroup = this.fb.group({
      servicePositionId: [null],
      positionId: [null],
      positionName: [null, Validators.required],
      experience: [null],
      workLocation: [null],
      nationality: [null],
      division: [null],
      subDivision: [null],
      entity: [null],
      unit: [null],
      revenue: [null],
      cost: [null],
      isSelected: [false],
      isLicense: [false]
    });

    this.valueChangeSubscription.add(positionFormGroup.get('isSelected').valueChanges.pipe(debounceTime(10), takeUntil(this._onDestroy)).subscribe(res => {

      this.resolveIndeterminateState();

      this.resolveSelectedPositions(positionFormGroup, res);

    }));

    return positionFormGroup;

  }

  /**
   * @description Resolves selectedServices array data
   * @param serviceFormGroup 
   * @param isSelected 
   */
  resolveSelectedServices = (serviceFormGroup: AbstractControl, isSelected: boolean) => {

    const serviceId = serviceFormGroup.get('serviceId').value;
    const existingServiceItem = this.selectedServices.indexOf(serviceId);

    if (isSelected && existingServiceItem == -1)
      this.selectedServices.push(serviceId);

    if (!isSelected && existingServiceItem > -1)
      this.selectedServices.splice(existingServiceItem, 1);

  }

  /**
   * @description Resolves selectedPositions array data
   * @param positionFormGroup 
   * @param isSelected 
   */
  resolveSelectedPositions = (positionFormGroup: AbstractControl, isSelected: boolean) => {

    const servicePositionId = positionFormGroup.get('servicePositionId').value;
    const existingPositionItem = this.selectedPositions.indexOf(servicePositionId);

    if (isSelected && existingPositionItem == -1)
      this.selectedPositions.push(servicePositionId);

    if (!isSelected && existingPositionItem > -1)
      this.selectedPositions.splice(existingPositionItem, 1);

  }

  /**
   * @description Resolves the service indeterminate state
   */
  resolveIndeterminateState = () => {

    this.enableAddServiceButton = false;

    for (const [serviceIndex, serviceItem] of this.servicesFormArr.value.entries()) {

      const selectedPositions = serviceItem['positions'].map(val => val['isSelected']);

      if (selectedPositions.includes(true))
        this.enableAddServiceButton = true;

      if (selectedPositions.includes(true) && selectedPositions.includes(false))
        this.servicesFormArr.at(serviceIndex).get('isInDeterMinate').patchValue(true, { emitEvent: false });

      else
        this.servicesFormArr.at(serviceIndex).get('isInDeterMinate').patchValue(false, { emitEvent: false });

      if (selectedPositions.includes(true) && !selectedPositions.includes(false))
        this.servicesFormArr.at(serviceIndex).get('isSelected').patchValue(true, { emitEvent: false });

      if (selectedPositions.includes(false) && !selectedPositions.includes(true))
        this.servicesFormArr.at(serviceIndex).get('isSelected').patchValue(false, { emitEvent: false });

    }


  }

  closeDialog = () => {

    this.matDialogRef.close({ event: 'close' });

  }

  cancel = () => {

    this.matDialogRef.close({ event: 'cancel' });

  } 

  addService = () => {

    if (this.searchFormControl.value != null && this.searchFormControl.value != "")
      this.resolveServiceFormArr(this.tempServicesList);

    let selectedServices = [];

    for (const serviceItem of this.servicesFormArr.value) {

      let selectedPositions = [];

      for (const positionItem of serviceItem['positions'])
        if (positionItem['isSelected'])
          selectedPositions.push(positionItem);

      if (selectedPositions.length || serviceItem['isSelected'])
        selectedServices.push({
          serviceId: serviceItem['serviceId'],
          serviceName: serviceItem['serviceName'],
          serviceTypeId: serviceItem['serviceTypeId'],
          isFixedRate: serviceItem['isFixedRate'],
          positions: selectedPositions
        });

    }

    if (selectedServices.length)
      this.matDialogRef.close({ event: 'submit', data: selectedServices });
    else this._toaster.showWarning('No Services has been selected!', '', 3000)

  }

  /**
   * @description Toggles service expand/collapse
   * @param serviceItem 
   */
  toggleService = (serviceItem: AbstractControl, toExpand = null) => {

    serviceItem.get('isExpanded').patchValue(toExpand != null ? toExpand : !serviceItem.get('isExpanded').value);

  }

  ngOnDestroy() {

    this._onDestroy.next();
    this._onDestroy.complete();

    if (this.valueChangeSubscription)
      this.valueChangeSubscription.unsubscribe();

    if (this.masterDataSubscription)
      this.masterDataSubscription.unsubscribe();

  }

}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { QuotePipesModule } from 'src/app/modules/quote-builder/quote-pipes/quote-pipes.module';
@NgModule({
  declarations: [
    AddServicesDialogComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatInputModule,
    MatTooltipModule,
    QuotePipesModule
  ],
  exports: [AddServicesDialogComponent]
})

class AddServicesDialogModule { }
