<div class="Main-form">
    <ng-container *ngIf="show == true">
        <div class="row">
            <div class="col-9">

                <!-- Standard Fields -->
                <mat-card [ngClass]="{ 'expanded': showStandardFields }" class="mat-card"
                    style="width: auto;margin-top: 10px; margin-left: -20px; margin-right: -11px;">
                    <div class="expand-icon p-0" (click)="toggleStandardFields()">
                        <mat-icon>{{ showStandardFields ? 'expand_less' : 'expand_more' }}</mat-icon>
                    </div>
                    <!-- <div *ngIf="stepIndex == 0" style="float: right; position: relative; z-index: 1; cursor: pointer;"
                    [matMenuTriggerFor]="menu">
                    <span>
                    <img src="https://assets.kebs.app/images/data_info_alert_FILL0_wght400_GRAD0_opsz24.svg" style="margin-top: 2.5rem;"/>
                    <mat-menu class="my-class label-name" #menu [yPosition]="'above'"
                    [xPosition]="'before'" [overlapTrigger]="true">
                    <div class="container">
                        <div class="row pt-1 label-name" style="font-weight: 500;">
                            <div class="col-6 mt-2 p-0" style="font-size: 15px;">Prefilled Fields</div>
                            <div class="col d-flex p-0 justify-content-end">
                                <button mat-button [matMenuTriggerFor]="menu" #MenuTrigger="matMenuTrigger">
                                    <mat-icon style="color:#cf0001; font-size: 18px;">close</mat-icon>
                                </button>
                            </div>
                        </div>
                        <hr class="p-0 m-0">
                        <div class="row label-name" *ngFor="let fieldName of projectFields">
                            <div class="col-12 mt-2" style="font-size: 13px;">{{ fieldName }}</div>
                        </div>
                        <hr class="p-0 m-0">
                        <div class="row pt-2 pb-1 label-name">
                            <div class="col-12" style="font-size: 15px;">
                                Milestone Updated By
                            </div>
                        </div>
                        <div class="row pb-2 label-name">
                            <div class="col-6" style="font-size: 13px; vertical-align: middle;">{{form?.get('MilestoneUpdatedBy')?.value || "-"}}</div>
                            <div class="col-6 d-flex justify-content-end align-items-center">
                                
                                <mat-icon style="vertical-align: middle;">calendar_today</mat-icon>
                                <span style="vertical-align: middle;">{{form?.get('MilestoneUpdatedDate')?.value || "-"}}</span>
                            </div>
                        </div>

                    </div>
                </mat-menu>
                </span>
                </div> -->

                    <div class="row">
                        <div class="section-title">
                            Milestone Information
                        </div>
                        <div class="col-12 p-0" *ngIf="showStandardFields">
                            <div class="row">
                                <ng-container *ngFor="let field of staticFields; let i = index">
                                    <div [class]="'col-' + field.colSize" *ngIf="field.key_name != 'invoiceNoType' ">
                                        <app-invoice-field-builder [field]="field" [form]="form" [stepIndex]="stepIndex"
                                            [isTenantExistingInvoiceAllowed]="isTenantExistingInvoiceAllowed" [dateFormats]="dateFormats" [milestoneTagDetails]="milestoneTagDetails" [isToShowMilestoneTagOption]="isToShowMilestoneTagOption">
                                        </app-invoice-field-builder>
                                    </div>
                                    <div [class]="'col-' + field.colSize + ' pt-3'" *ngIf="field.key_name == 'invoiceNoType' "  [formGroup]="form">
                                        <div class="row label-name">
                                            {{ field.label_name }}<span *ngIf="field.is_mandatory == 1"class="field-mandatory">&nbsp;*</span>
                                        </div>
                                        <mat-form-field appearance="outline" class="pr-1 invoice-input full-width" style="width: 100%;">
                                            <div class="input-select-container label-name" style="display: flex; align-items: center;">
                                                <input class="pb-1 label-name" style="font-weight: 400 !important;" matInput type="text" [placeholder]="field.label_name"
                                                    formControlName="invoiceNo" [readonly]="isReadonly" [matTooltip]="form.get('invoiceNo')?.value">
                                                <mat-form-field style="width: 50%;" appearance="none" class="custom-dropdown">
                                                    <mat-select formControlName="invoiceNoType" style="text-align: end;" (selectionChange)="onInvoiceTypeChange($event.value)" [matTooltip]="getTooltip(form.get('invoiceNoType')?.value)">
                                                        <mat-option value="new" class="pt-2">Auto Generated</mat-option>
                                                        <mat-option value="manual" class="pt-2" *ngIf="isManualNumberApplicable">Manual</mat-option>
                                                        <mat-option value="existing" class="pt-2" *ngIf="isTenantExistingInvoiceAllowed && isPreviousNumberAvailable">Previous Invoice No</mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                            </div>
                                            <mat-error class="pt-2" *ngIf="form?.get('invoiceNo')?.hasError('maxlength')">
                                                Invoice no cannot exceed {{allowedCharacters}} characters.
                                            </mat-error>
                                        </mat-form-field>
                                        
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                    </div>
                </mat-card>
               <!-- PO Fields -->
                <mat-card [ngClass]="{ 'expanded': showPoDetails }" class="mat-card"
                    style="width: auto; margin-top: 10px; margin-left: -20px; margin-right: -11px;"
                    [formGroup]="form" *ngIf="poDetailsFields?.length > 0">
                    <div class="expand-icon p-0" (click)="togglePoDetails()">
                        <mat-icon>{{ showPoDetails ? 'expand_less' : 'expand_more' }}</mat-icon>
                    </div>
                    <div class="row">
                        <div class="section-po-title">
                            PO Details
                        </div>
                    </div>
                    <div *ngIf="showPoDetails">
                        <div class="row card-title p-0 m-0" style="font-weight: 500; color: #45546E;font-size: 14px;">PO Details</div>
                        <div class="row">
                        <ng-container *ngFor="let field of poDetailsFields; let i = index">
                            <div [class]="'col-' + field.colSize">
                                <app-invoice-field-builder [field]="field" [form]="form" [stepIndex]="stepIndex"
                                    [isTenantExistingInvoiceAllowed]="isTenantExistingInvoiceAllowed" [dateFormats]="dateFormats">
                                </app-invoice-field-builder>
                            </div>
                        </ng-container>
                        </div>
                        <div class="row mt-2 rowData label-name big-bold-total" *ngIf="poError">
                            <div class="col-4"></div>
                            <div class="col-4 alert" style="background-color: #FFDADC; color: #5F6C81; border-radius: 5px; display: flex; align-items: center; height: fit-content; width: fit-content;">
                                <mat-icon>info</mat-icon>
                                <span style="margin-left: 5px; font-family: roboto; font-size: 12px;">
                                  {{poErrorMessage}}
                                </span>
                              </div>
                        </div>
                    </div>
                </mat-card>
                <!-- FTE Fields -->
                <mat-card [ngClass]="{ 'expanded': showfteDetails }" class="mat-card"
                    style="width: auto; margin-top: 10px; margin-left: -20px; margin-right: -11px;"
                    *ngIf="showFteDetails == true" [formGroup]="form">
                    <div class="expand-icon p-0" (click)="toggleFteDetails()">
                        <mat-icon>{{ showfteDetails ? 'expand_less' : 'expand_more' }}</mat-icon>
                    </div>
                    <div class="row">
                        <div class="section-fte-title">
                            FTE Details
                        </div>
                        <div class="col-6 pl-0" *ngIf="showfteDetails">
                            <div class="d-flex align-items-center">
                                <label style="font-weight: 500; color: #45546E;" class="card-title">FTE
                                    Details</label>&nbsp;

                                <span>
                                    <mat-form-field appearance="outline" class="ml-2 full-width label-name"
                                        [class.non-editable]="stepIndex === 1">
                                        <mat-label class="label-name">Units</mat-label>
                                        <mat-select formControlName="unit">
                                            <mat-option value="Hours" (click)="unitClicked('Hours')">Hour</mat-option>
                                            <mat-option value="Days" (click)="unitClicked('Days')">Day</mat-option>
                                            <mat-option value="Month" (click)="unitClicked('Month')">Month</mat-option>
                                            <mat-option value="Page" (click)="unitClicked('Page')">Page</mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                </span>
                            </div>
                        </div>

                    </div>
                    <div *ngIf="showfteDetails" class="row" formArrayName="fteDetails">
                        <div class="col-12 p-0"
                            *ngFor="let fteFormGroup of form.get('fteDetails').controls; let i = index"
                            [formGroupName]="i">
                            <div class="row">
                                <ng-container *ngFor="let field of fteconfig; let i = index">
                                    <ng-container [ngSwitch]="field.field_type">
                                        <div class="col-3 label-name">
                                            <ng-container *ngSwitchCase="'input'">
                                                <mat-form-field appearance="outline" style="width:100%"
                                                    [class.custom-disabled]="stepIndex === 1" class="full-width">
                                                    <mat-label>{{ field.label_name }}</mat-label>
                                                    <input matInput [formControlName]="field.key_name" type="text"
                                                        [placeholder]="field.label_name"
                                                        [required]="field.is_mandatory == 1"
                                                        (input)="formatfInput($event,field.key_name,fteFormGroup,$event)"
                                                        style="text-align: right;"
                                                        (blur)="field.key_name == 'actualWorkingDays' || field.key_name == 'plannedWorkingDays' ? showWarningMsg() : null"
                                                        [readonly]="field.is_editable == 0">
                                                    <mat-error class="pt-2"
                                                        *ngIf="fteFormGroup.get('actualWorkingDays')?.hasError('min') || fteFormGroup.get('perDayRate')?.hasError('min')">
                                                        {{field.label_name}} should not be 0
                                                    </mat-error>
                                                </mat-form-field>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'text'">
                                                <mat-form-field appearance="outline" style="width:100%"
                                                    class="full-width">
                                                    <mat-label>{{ field.label_name }}</mat-label>
                                                    <input matInput [formControlName]="field.key_name"
                                                        [ngClass]="{'big-bold': field.key_name === 'fteTotalAmount'}"
                                                        [placeholder]="field.label_name" readonly
                                                        (input)="formatfInput($event,field.key_name,fteFormGroup,$event)"
                                                        style="text-align: right;" [readonly]="field.is_editable == 0">
                                                </mat-form-field>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'dropdown'">
                                                <mat-form-field *ngIf="field.key_name === 'workLocation'"
                                                    class="full-width" appearance="outline" style="width: 100%;"
                                                    [class.custom-disabled]="stepIndex === 1" [ngStyle]="{'pointer-events': field.is_editable == 0 ? 'none' : 'auto', 'cursor': field.is_editable == 0 ? 'default' : 'pointer'}">
                                                    <mat-label>{{ field.label_name }}</mat-label>
                                                    <mat-select formControlName="workLocation"
                                                        [matTooltip]="getFteFieldValue(field.key_name,fteFormGroup)"
                                                        [required]="field.is_mandatory == 1">
                                                        <mat-option *ngFor="let element of invoiceWorkLocation"
                                                            [value]="element.shift_name">{{element.shift_name}}</mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <mat-form-field *ngIf="field.key_name === 'title'" appearance="outline"
                                                    style="width: 100%;" [class.custom-disabled]="stepIndex === 1"
                                                    class="full-width" [ngStyle]="{'pointer-events': field.is_editable == 0 ? 'none' : 'auto', 'cursor': field.is_editable == 0 ? 'default' : 'pointer'}">
                                                    <mat-label>Title</mat-label>
                                                    <mat-select formControlName="title"
                                                        [matTooltip]="getFteFieldValue(field.key_name,fteFormGroup)"
                                                        [required]="field.is_mandatory == 1" [ngStyle]="{'pointer-events': field.is_editable == 0 ? 'none' : 'auto', 'cursor': field.is_editable == 0 ? 'default' : 'pointer'}">
                                                        <mat-option *ngFor="let element of skillRoleList"
                                                            [value]="element.role_description">{{element.role_description}}</mat-option>
                                                    </mat-select>
                                                </mat-form-field>
                                                <app-input-search *ngIf="field.key_name === 'associateOid'"
                                                    class="custom-input-search" [placeholder]="field.label_name"
                                                    [list]="masterFteData"
                                                    (change)="selectAssignedTo($event,fteFormGroup)"
                                                    formControlName="associateId"
                                                    [required]="field.is_mandatory == 1"
                                                    [ngStyle]="{'pointer-events': field.is_editable == 0 ? 'none' : 'auto', 'cursor': field.is_editable == 0 ? 'default' : 'pointer'}">
                                                </app-input-search>

                                            </ng-container>
                                        </div>
                                    </ng-container>
                                </ng-container>
                                <div class="row" style="margin: 4px;" *ngIf="stepIndex !=1 ">
                                    <!-- <span class="add-new-line" (click)="addFte()">add_circle_outline</span>&nbsp;&nbsp; -->
                                    <button mat-icon-button color="primary" class="mt-2" (click)="addFte()" *ngIf="(invoiceTenantDetails?.add_delete_fte_restricted === undefined || 
                                    !invoiceTenantDetails?.add_delete_fte_restricted)">
                                        <mat-icon style="margin-top: -18px;">add_circle_outline</mat-icon>
                                    </button>&nbsp;&nbsp;
                                    <button *ngIf="form.get('fteDetails').controls.length!=1 && (invoiceTenantDetails?.add_delete_fte_restricted === undefined || 
                                    !invoiceTenantDetails?.add_delete_fte_restricted)" mat-icon-button
                                        color="primary" class="mt-2" class="delete-button"  (click)="deleteFte(i)">
                                        <mat-icon class="delete-icon">remove_circle_outline</mat-icon>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </mat-card>
                <!-- Item Fields -->
                <mat-card [ngClass]="{ 'expanded': showServiceDetails }" class="mat-card"
                    style="width: auto; margin-top: 15px; margin-left: -20px; margin-right: -11px;" [formGroup]="form">
                    <div class="row">
                        <div class="col-10 pl-0" *ngIf="showServiceDetails">
                            <label style="font-weight: 500; color: #45546E; margin-bottom: 15px; font-size: 14px;"
                                class="card-title">Service
                                Details</label>
                        </div>
                        <div class="col-10 pl-0 section-service-title">
                            Service Details
                        </div>
                        <div class="col-2 justify-content-end expand-icon p-0" (click)="toggleServiceDetails()">
                            <mat-icon>{{ showServiceDetails ? 'expand_less' : 'expand_more' }}</mat-icon>
                        </div>

                    </div>

                    <div class="row" *ngIf="showServiceDetails">
                        <div [class]="'col-' + column.width" *ngFor="let column of itemConfig" class="label-name"
                            [style.text-align]="column.text_align" [ngStyle]="{'max-width': column.key_name === 'lineItemNo' ? '50px' : ''}">
                            <ng-container *ngIf="column.active_status === 1" class="label-name">{{
                                column.label_name }} <span *ngIf="column.is_mandatory == 1" style="color: red;">*</span>
                            </ng-container>
                        </div>
                    </div>
                    <ng-container formArrayName="workSummary" *ngIf="showServiceDetails">
                        <div class="row"
                            *ngFor="let workSummaryFormGroup of form.get('workSummary').controls; let i = index"
                            [formGroupName]="i">
                            <div [class]="'col-' + column.width" *ngFor="let column of itemConfig" [ngStyle]="{'max-width': column.key_name === 'lineItemNo' ? '50px' : ''}">
                                <ng-container *ngIf="column.active_status === 1">
                                    <ng-container [ngSwitch]="column.field_type" [style.width]="column.width + '%'">
                                        <ng-container *ngSwitchCase="'input'" class="label-name">
                                            <mat-form-field *ngIf="column.key_name !='description'" appearance="outline"
                                                style="width: 100%;">
                                                <div class="input-with-currency label-name">
                                                    <input matInput [formControlName]="column.key_name"
                                                        [type]="column.data_type" class="clickable-input"
                                                        [readonly]="stepIndex == 1"
                                                        [placeholder]="column.data_type === 'number' ? '0.00' : ''"
                                                        (input)="formatfInput($event,column.key_name,workSummaryFormGroup,$event)"
                                                        [required]="column.is_mandatory == 1"
                                                        [style.text-align]="column.text_align">
                                                    <span *ngIf="column.key_name == 'rate'"
                                                        class="currency-indicator">{{this.form.get("currency").value}}</span>
                                                </div>
                                            </mat-form-field>


                                            <mat-form-field *ngIf="column.key_name=='description'" appearance="outline"
                                                style="width: 100%;" class="label-name">
                                                <input matInput [formControlName]="column.key_name" type="text"
                                                    class="clickable-input" [readonly]="stepIndex == 1"
                                                    [placeholder]="column.data_type === 'number' ? '0.00' : ''"
                                                    [matTooltip]="getWsFieldValue(column.key_name,workSummaryFormGroup)"
                                                    [required]="column.is_mandatory == 1" pattern="^(?=.*\S)[\s\S]*$">
                                            </mat-form-field>

                                        </ng-container>

                                        <ng-container *ngSwitchCase="'dropdown'">
                                            

                                            <!-- Dropdown for productServiceType -->
                                            <mat-form-field appearance="outline" class="full-width label-name" *ngIf="column.key_name === 'productServiceType'"
                                                [class.custom-disabled]="stepIndex === 1" style="width: 100%;">
                                                <mat-select formControlName="productServiceType"
                                                    [matTooltip]="getWsFieldValue(column.key_name, workSummaryFormGroup)" [required]="column.is_mandatory == 1"
                                                    #onScroll (openedChange)="onOpenedChange($event, 'onScroll')" (selectionChange)="selectionChanged($event.value, workSummaryFormGroup)">
                                                    <div style="width:100%">
                                                        <span class="pl-1" style="color: #a1a1a2;font-size: 20px;vertical-align: middle;"><mat-icon matPrefix>search</mat-icon></span>
                                                        <input [formControl]="searchCtrl"
                                                        placeholder="Search"
                                                        noEntriesFoundLabel="No results found"  class="p-3 pl-0" style="height: 5%; border: none; width: 78%;" (keydown)="$event.stopPropagation()">
                                                        <span style="color: #a1a1a2;font-size: 20px;cursor: pointer;vertical-align: middle;" (click)="cancelSearchValue()"><mat-icon matSuffix class="mr-1">highlight_off</mat-icon></span>
                                                    </div>
                                                    <mat-option (click) = "unSelectProductService(workSummaryFormGroup)">None</mat-option>
                                                    <mat-option *ngFor="let item of productServiceOptions" [value]="item.name">
                                                        {{ item.name }}
                                                    </mat-option>
                                                </mat-select>
                                            </mat-form-field>
                                            <mat-form-field appearance="outline" *ngIf="column.key_name === 'tax'"
                                            class="full-width label-name" style="width: 100%;">
                                            <mat-select formControlName="taxDetails"
                                                [matTooltip]="getWsFieldValue(column.key_name,workSummaryFormGroup)"
                                                [panelClass]="customPanelClass"
                                                (selectionChange)="onTaxSelectionChange($event, workSummaryFormGroup, i+1)">
                                                <mat-option [value]="{ id: null, type: 'none' }">None</mat-option>
                                                <mat-optgroup label="Tax Group" *ngIf="filterTaxGroup?.length">
                                                    <mat-option *ngFor="let item of filterTaxGroup"
                                                        [value]="item">
                                                        {{ item.description}}
                                                    </mat-option>
                                                </mat-optgroup>
                                                <mat-optgroup label="Tax" *ngIf="taxMaster?.length">
                                                    <mat-option *ngFor="let item of taxMaster"
                                                        [value]="item">
                                                        {{ item.description}} - {{item.tax_percentage}}%
                                                    </mat-option>
                                                </mat-optgroup>
                                            </mat-select>
                                        </mat-form-field>

                                         <!-- Dropdown for Charge Type -->
                                         <mat-form-field appearance="outline" *ngIf="column.key_name === 'chargeType'" class="full-width label-name"
                                         style="width: 100%;">
                                         <mat-select formControlName="chargeType" [panelClass]="customPanelClass"
                                             (selectionChange)="onChargeTypeSelectionChange($event, workSummaryFormGroup, i+1)">
                                             <mat-option [value]="{ id: null, type: 'none' }">None</mat-option>
                                             <mat-option *ngFor="let item of chargeTypes" [value]="item">
                                                 {{item.description}}
                                             </mat-option>
                                         </mat-select>
                                     </mat-form-field>

                                        </ng-container>
                                        <ng-container *ngSwitchCase="'text'">
                                            <!-- <mat-form-field appearance="outline"
                                            [ngClass]="{'big-bold': column.key_name == 'amount'}" style="width: 100%;">
                                            <div class="input-with-currency label-name">
                                                <input matInput [formControlName]="column.key_name" class="full-width"
                                                    type="text"
                                                    [matTooltip]="getWsFieldValue(column.key_name,workSummaryFormGroup)"
                                                    readonly>
                                                <span *ngIf="column.key_name == 'amount' || column.key_name == 'rate'"
                                                    class="currency-indicator">{{this.form.get("currency").value}}</span>
                                            </div>
                                        </mat-form-field> -->
                                            <div class="input-with-currency label-name">
                                                <input matInput [formControlName]="column.key_name"
                                                    class=" mt-2 full-width" type="text"
                                                    [matTooltip]="getWsFieldValue(column.key_name,workSummaryFormGroup)"
                                                    readonly [style.text-align]="column.text_align">

                                                <span *ngIf="column.key_name == 'amount' || column.key_name == 'rate'"
                                                    class="mt-2 currency-indicator">{{this.form.get("currency").value}}</span>
                                            </div>
                                        </ng-container>

                                    </ng-container>
                                </ng-container>
                            </div>
                            <ng-container *ngIf="form.get('workSummary').controls.length!=1">
                                <div *ngIf="stepIndex != 1">
                                    <button *ngIf="form.get('workSummary').controls.length!=1" mat-icon-button 
                                        class="delete-button" (click)="deleteWorkSummary(i)"  style="display: flex;
                                        justify-content: end;
                                        align-items: flex-end;
                                        z-index: 1;
                                        margin-top: -4px;
                                        position: absolute">
                                        <mat-icon class="delete-icon">delete_outline</mat-icon>
                                    </button>
                                </div>
                            </ng-container>

                        </div>
                    </ng-container>


                    <div *ngIf="(form.get('serviceTypeGroupId').value==4 && stepIndex != 1) || (this.invoiceTenantDetails && this.invoiceTenantDetails?.add_delete_fte_restricted &&form.get('serviceTypeGroupId').value == 1 )" class="row"
                        style="margin: 15px;">
                        <span class="add-new-line" (click)="addWorkSummary()">+ Add New Line</span>
                    </div>
                    <hr>
                    <div class="row" [formGroup]="form">
                        <ng-container>
                            <div class="col-4">
                                <div class="row label-name">
                                    <span>Note</span><br>
                                </div>
                                <div class="row">
                                    <mat-form-field appearance="outline" class="textArea label-name">
                                        <textarea matInput cdkTextareaAutosize #autosize="cdkTextareaAutosize"
                                            cdkAutosizeMinRows="7" cdkAutosizeMaxRows="" formControlName="note"
                                            class="label-name"></textarea>
                                    </mat-form-field>
                                </div>
                            </div>
                            <div class="col-8" style="display: flex;flex-direction: column;flex-wrap: wrap;">
                                <div class="row rowData label-name">
                                    <div class="col-2"></div>
                                    <div class="col-6 p-0">
                                        <span class="d-flex justify-content-end" style="display: block;">Sub
                                            Total</span>
                                        <div class="p-0 d-flex justify-content-end" style="display: block;">(Tax
                                            Exclusive)</div>
                                    </div>
                                    <div class="col-3 ml-2 p-0 d-flex justify-content-end">
                                        <span
                                            class="pl-3 p-0 d-flex justify-content-end">{{formatNumberForDisplay(form.get('subTotal').value
                                            ||
                                            0,form.get('currency').value)}}</span>
                                    </div>
                                    <div class="col-1"></div>
                                </div>
                                <ng-container *ngFor="let taxItem of taxresultArray">
                                    <div class="row mt-2 rowData label-name">
                                        <div class="col-2"></div>
                                        <div class="col-6 p-0 d-flex justify-content-end">
                                            <span>{{ taxItem.tax_description }}</span>
                                        </div>
                                        <div class="col-3 ml-2 p-0 d-flex justify-content-end">
                                            <span
                                                class="pl-3 p-0 d-flex justify-content-end">{{formatNumberForDisplay(taxItem.amount,form.get('currency').value)
                                                }}</span>
                                        </div>
                                        <div class="col-1"></div>
                                    </div>
                                </ng-container>
                                <div class="row d-flex mt-2 rowData label-name">
                                    <div class="col-2">
                                    </div>
                                    <!-- <div class="col-3 p-0">
                                    <span class="d-flex justify-content-end" style="display: block;">Discount</span>
                                    <div class="p-0 d-flex justify-content-start" style="display: block;"> (Applied On
                                        {{ fixNumberOnUI(form.get('subTotal')?.value || 0.00,
                                        form.get('currency')?.value) }})</div>
                                </div> -->
                                    <div class="col-6 p-0 d-flex justify-content-end">
                                        <mat-form-field appearance="outline" class="pr-1 discount-input full-width">
                                            <mat-label>Discount</mat-label>
                                            <div class="input-select-container">
                                                <input matInput type="number" placeholder="0.00"
                                                    formControlName="discountInputAmount" (blur)="clearInvalidValue()" (input)="enforceMaxDiscountPercentage($event)" >
                                                <mat-select matSuffix formControlName="discountAmountType">
                                                    <mat-option value="percentage">%</mat-option>
                                                    <mat-option
                                                        value="rupees">{{getCurrencySymbol(this.form.get('currency').value)}}</mat-option>
                                                </mat-select>
                                            </div>
                                        </mat-form-field>
                                        <!-- <div class="mt-2 mb-3">
                                        <mat-error class="error-margin-bottom"
                                            *ngIf="form.get('discountInputAmount').invalid && form.get('discountInputAmount').touched">
                                            {{ getDiscountErrorMessage() }}
                                        </mat-error>
                                    </div> -->

                                    </div>
                                    <div class="col-3 ml-2 p-0 d-flex justify-content-end" style="margin-top:16px;">
                                        <span class="pl-3 p-0 d-flex justify-content-end">-
                                            {{formatNumberForDisplay(form.get('discountAmount').value
                                            ||
                                            0,form.get('currency').value)}}</span>
                                    </div>
                                    <div class="col-1"></div>
                                </div>
                                <div class="row mt-2 rowData label-name">
                                    <div class="col-2"></div>
                                    <!-- <div class="col-3 p-0">
                                    <span class="d-flex justify-content-start" style="display: block;">Retention</span>
                                    <div class="p-0 d-flex justify-content-start" style="display: block;">(Applied On
                                        {{ fixNumberOnUI(form.get('subTotal')?.value || 0.00,
                                        form.get('currency')?.value) }})
                                    </div>
                                </div> -->
                                    <div class="col-6 p-0 d-flex justify-content-end">
                                        <mat-form-field appearance="outline" class="pr-1 discount-input full-width">
                                            <mat-label>Retention</mat-label>
                                            <div class="input-select-container">
                                                <input matInput type="number" placeholder="0.00"
                                                    formControlName="retentionInputAmount"
                                                    (blur)="clearRetentionInvalidValue()" (input)="enforceMaxRetentionPercentage($event)">
                                                <mat-select matSuffix formControlName="retentionAmountType">
                                                    <mat-option value="percentage">%</mat-option>
                                                    <mat-option
                                                        value="rupees">{{getCurrencySymbol(this.form.get('currency').value)}}</mat-option>
                                                </mat-select>
                                            </div>
                                        </mat-form-field>
                                        <!-- <div class="mt-2 mb-3">
                                        <mat-error class="mt-2"
                                            *ngIf="form.get('retentionInputAmount').invalid && form.get('retentionInputAmount').touched">
                                            {{ getRetentionErrorMessage() }}
                                        </mat-error>
                                    </div> -->

                                    </div>
                                    <div class="col-3 ml-2 p-0 d-flex justify-content-end" style="margin-top:16px;">
                                        <span
                                            class="pl-3 p-0 d-flex justify-content-end">{{formatNumberForDisplay(form.get('retentionAmount').value
                                            ||
                                            0,form.get('currency').value)}}</span>
                                    </div>

                                    <div class="col-1"></div>
                                </div>
                                <div class="row rowData label-name">
                                    <div class="col-2"></div>
                                    <div class="col-6 p-0">
                                        <span class="d-flex justify-content-end" style="display: block;">Total</span>
                                        <div class="p-0 d-flex justify-content-end" style="display: block;">(TDS/TCS
                                            Exclusive)</div>
                                    </div>
                                    <div class="col-3 ml-2 p-0 d-flex justify-content-end">
                                        <span
                                            class="pl-3 p-0 d-flex justify-content-end">{{formatNumberForDisplay(form.get('totalTdsTcsExclusive').value
                                            ||
                                            0,form.get('currency').value)}}</span>
                                    </div>
                                    <div class="col-1"></div>
                                </div>
                                <div class="row mt-2 rowData label-name">
                                    <div class="col-2"></div>
                                    <div class="col-6 p-0 d-flex justify-content-end">
                                        <mat-radio-group class="mr-2 d-flex" style="margin-top: 0.7rem;"
                                            formControlName="selectedOption" (change)="onRadioChange()">
                                            <mat-radio-button color="warn" class="mr-2"
                                                value="tds">TDS</mat-radio-button>
                                            <mat-radio-button color="warn" value="tcs">TCS</mat-radio-button>
                                        </mat-radio-group>

                                        <mat-form-field class="justify-content-end" appearance="outline"
                                            style="width:80%">
                                            <mat-label>Select a Tax</mat-label>
                                            <mat-select formControlName="selectedDropdownOption">
                                                <mat-option *ngFor="let option of getDropdownOptions()"
                                                    [value]="option.id" (click)="calculateTdsOrTCS(option)">
                                                    {{ option.description }} - {{option.tax_percentage}} %
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                    <div class="col-3 ml-2 p-0 d-flex justify-content-end">
                                        <span class="mt-3" *ngIf="form.get('selectedOption')?.value == 'tds'">
                                            - {{formatNumberForDisplay(form.get('tdsValue').value ||
                                            0,form.get('currency').value)}}
                                        </span>
                                        <span class="mt-3" *ngIf="form.get('selectedOption')?.value == 'tcs'">
                                            - {{formatNumberForDisplay(form.get('tcsValue').value ||
                                            0,form.get('currency').value)}}
                                        </span>
                                    </div>
                                    <div class="col-1"></div>
                                </div>
                                <div class="row mt-2 rowData label-name big-bold-total">
                                    <div class="col-2"></div>
                                    <div class="col-6 p-0 d-flex justify-content-end">
                                        <span>Total</span>
                                    </div>
                                    <div class="col-3 ml-2 p-0 d-flex justify-content-end">
                                        <span
                                            class="pl-3 d-flex justify-content-end">{{formatNumberForDisplay(form.get('totalAmount').value
                                            ||
                                            0,form.get('currency').value)}} {{form.get('currency').value}}</span>
                                        <!-- <span>{{form.get('currency').value}} </span> -->
                                    </div>
                                    <div class="col-1"></div>
                                </div>
                                <div class="mt-4 row rowData">
                                    <div class="col-2"></div>
                                    <div class="col-6 label-name d-flex justify-content-end">
                                        <span>Amount chargeable in words</span>
                                    </div>
                                    <div class="col-4 label-name d-flex justify-content-center">
                                        <span>{{form.get('totalAmountInwords')?.value || "-"}}</span>
                                    </div>
                                </div>
                                <div class="row mt-2 rowData label-name big-bold-total" *ngIf="showPoAlert">
                                    <div class="col-4"></div>
                                    <div class="col-8 alert" style="background-color: #FFDADC; color: #5F6C81; padding: 10px; border-radius: 5px; display: flex; align-items: center; height: fit-content;">
                                        <mat-icon>info</mat-icon>
                                        <span style="margin-left: 5px; font-family: roboto; font-size: 12px;">
                                            {{ poAlertText }} {{form.get('remainingPoValue')?.value || "-" }} {{this.form.get("currency").value}}
                                        </span>
                                      </div>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                    <div class="row" *ngIf="stepIndex == 0">
                        <div class="col">
                            <div class="row label-name d-flex align-items-center" style="margin-top: 10px;">
                                <span>Attached File(s) From Billing Advice</span>
                                <span class="material-icons" (click)="DisplayAttachment()"  [style.color]="prefilldata?.is_project_attachment ? '#ff3a46' : ''"  style="cursor: pointer; font-size: 19px !important; margin-left: 4px;" matTooltip="Attachment Preview">
                                    visibility
                                </span>
                                <span *ngIf="projectAttachmentsCount > 0" class="badge-circle">
                                    {{ projectAttachmentsCount }}
                                </span>
                            </div>
                            <div class="row label-name d-flex align-items-center" style="margin-top: 10px;">
                                <span>Attach File(s) to Invoice</span>
                                <span class="material-icons" (click)="addAttachment()" [style.color]="prefilldata?.is_attachment ? '#ff3a46' : ''" style="cursor: pointer;" matTooltip="Add attachment">
                                    attach_file
                                </span>
                                <span *ngIf="invoiceAttachmentsCount > 0" class="badge-circle-a ">
                                    {{ invoiceAttachmentsCount }}
                                  </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- <div class="p-0 label-name" style="display: block;" *ngIf="stepIndex == 0">You can upload a maximum of 10MB each file
                    </div> -->
                </mat-card>

                <!-- Footer Section -->
                <mat-card [ngClass]="{ 'expanded': true }" class="mat-card"
                    style="width: auto;margin-top: 15px; margin-left: -20px; margin-right: -11px;">
                    <div class="row" [formGroup]="form">
                        <div class="col-6 p-0">
                            <div class="row p-0">
                                <div class="col-12 heading pt-1 p-0 card-title">Terms and Condition</div>
                            </div>
                            <ng-container *ngIf="!isTSTextarea">
                                <div class="row pt-2 p-0"
                                    *ngIf="termsConditionsConfig && termsConditionsConfig.terms_and_condition.length>0">
                                    <div class="col-12 p-0 d-flex"
                                        *ngFor="let item of termsConditionsConfig.terms_and_condition;let k = index">
                                        <div class="col-5 d-flex" *ngIf="item.label!='' && item.isShow">
                                            <span [ngStyle]="{
                          'font-size':(item.cssData&&item.cssData['font_size'])?item.cssData['font_size']:'12px',
                          'font-weight':(item.cssData&&item.cssData['font_weight'])?item.cssData['font_weight']:'400',
                          'color':'#6f7c90'
                        }">{{item.label}}</span>
                                            <span *ngIf="item.showEditButton" class=" ml-auto mr-3">
                                                <button mat-icon-button class="icon-tray-button"
                                                    (click)="openBottomSheet('bank')" matTooltip="Edit Bank">
                                                    <mat-icon class="smallCardIcon mb-5">create</mat-icon>
                                                </button>
                                            </span>
                                            <span class="ml-auto"> : </span>
                                        </div>

                                        <div class="col-7" *ngIf="item.label!='' && item.type == 'text' && item.isShow">
                                            <span [ngStyle]="{
                          'font-size':(item.cssData&&item.cssData['font_size'])?item.cssData['font_size']:'12px',
                          'font-weight':(item.cssData&&item.cssData['font_weight'])?item.cssData['font_weight']:'500',
                          'color':'#6f7c90'
                        }" class="terms-text">{{this.form.get(item.value)?.value?this.form.get(item.value).value
                                                : '_'}}</span>
                                        </div>

                                        <div class="col-7"
                                            *ngIf="item.label!='' && item.type == 'address' && item.isShow">
                                            <div class="col-12 p-0" *ngFor="let address of item.value;let j=index">
                                                <span [ngStyle]="{
                            'font-size':(item.cssData&&item.cssData['font_size'])?item.cssData['font_size']:'12px',
                            'font-weight':(item.cssData&&item.cssData['font_weight'])?item.cssData['font_weight']:'500',
                            'color':'#6f7c90'
                          }">{{address.addr?this.form.get(address.addr)?.value:'_'}}</span>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </ng-container>
                            <div class="row" *ngIf="isTSTextarea">
                                <mat-form-field appearance="outline" class="textArea label-name">
                                    <textarea maxlength="1000" matInput cdkTextareaAutosize
                                        #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="10" cdkAutosizeMaxRows=""
                                        formControlName="termsAndConditions"></textarea>
                                </mat-form-field>
                            </div>

                        </div>
                        <ng-container *ngFor="let field of footerFields">
                            <div class="col-6" *ngIf="field.key_name == 'narration' && field.active_status == 1">
                                <div class="row label-name">
                                    <span>Narration</span><br>
                                </div>
                                <div class="row">
                                    <mat-form-field appearance="outline" class="textArea label-name">
                                        <textarea matInput cdkTextareaAutosize #autosize="cdkTextareaAutosize"
                                            cdkAutosizeMinRows="10" cdkAutosizeMaxRows=""
                                            formControlName="narration"></textarea>
                                    </mat-form-field>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </mat-card>

            </div>
            <div class="col-3">
                <mat-card [ngClass]="{ 'expanded': true }"
                    style="width: auto; margin-top: 10px; margin-left: 16px; margin-right: -30px;">
                    <div class="scrollable-container">
                        <div class="row">
                            <div class="col-12">
                                <div class="row">
                                    <div class="col-2 label p-0 label-name" style="font-weight: 500;">
                                        From
                                    </div>
                                    <div class="col-10 label p-0 label-name d-flex justify-content-end">
                                        <span>
                                            <button mat-icon-button class="ml-5 icon-tray-button "
                                                (click)="editFromAddress()" matTooltip="Edit Address">
                                                <mat-icon class="smallCardIcon">create</mat-icon>
                                            </button>
                                        </span>
                                    </div>
                                </div>
                                <!-- <label style="font-weight: bold;">{{form.get('From_company_code')?.value}}</label><br> -->
                                <div class="row address">{{form.get('fromLineOneAddress')?.value}}</div>
                                <div class="row address">{{form.get('fromLineTwoAddress')?.value}}</div>
                                <div class="row address">{{form.get('fromLineThreeAddress')?.value}}</div>
                                <div class="row address">{{form.get('fromLineFourAddress')?.value}}</div>
                                <div class="row address">{{form.get('fromLineFiveAddress')?.value}}</div>
                            </div>
                        </div>
                        <div class="row" style="margin-top: 5%;">
                            <div class="col-12">
                                <div class="row">
                                    <div class="col-2 label p-0 label-name" style="font-weight: 500;">
                                        To
                                    </div>
                                    <div class="col label p-0 label-name d-flex justify-content-end"
                                        style="font-weight: bold;">
                                        <span>
                                            <button mat-icon-button class="ml-5 icon-tray-button"
                                                (click)="editToAddress()" matTooltip="Edit Address">
                                                <mat-icon class="smallCardIcon">create</mat-icon>
                                            </button>
                                        </span>
                                    </div>
                                </div>
                                <!-- <label style="font-weight: bold;">{{form.get('To_company_code')?.value}}</label><br> -->
                                <ng-container *ngIf="isAnyToAddressAvailable(); else noAddress">
                                    <div class="row address">{{form.get('toLineOneAddress')?.value}}</div>
                                    <div class="row address">{{form.get('toLineTwoAddress')?.value}}</div>
                                    <div class="row address">{{form.get('toLineThreeAddress')?.value}}</div>
                                    <div class="row address">{{form.get('toLineFourAddress')?.value}}</div>
                                    <div class="row address">{{form.get('toLineFiveAddress')?.value}}</div>
                                    <div class="row address" *ngIf="form.get('customerGSTIN')?.value !== null && form.get('customerGSTIN')?.value !== ''">GSTIN: {{ form.get('customerGSTIN')?.value }}</div>
                                </ng-container>
                                <ng-template #noAddress>
                                    <div class="row label-name">No address found</div>
                                </ng-template>
                            </div>
                        </div>
                        <!-- Header Fields -->
                        <div class="row">
                            <ng-container *ngFor="let field of headerFields; let i = index">
                                <div class="col-12">
                                    <app-invoice-field-builder [field]="field" [form]="form" [prefilldata]="prefilldata"
                                        [projectCurrencyMaster]="projectCurrencyMaster" [legalEntityId]="legalEntityid" [dateFormats]="dateFormats">
                                    </app-invoice-field-builder>
                                </div>
                            </ng-container>


                        </div>
                    </div>
                </mat-card>
            </div>
        </div>

    </ng-container>
</div>
<!-- <ng-container *ngIf="showSpinner == true">
    <ngx-spinner bdColor="rgb(245,245,245,0.6)" size="medium" color="#cf0001" type="ball-clip-rotate"
        [fullScreen]="true">
        <p style="color: #cf0001"> Getting Things Ready ... </p>
    </ngx-spinner>
</ng-container> -->