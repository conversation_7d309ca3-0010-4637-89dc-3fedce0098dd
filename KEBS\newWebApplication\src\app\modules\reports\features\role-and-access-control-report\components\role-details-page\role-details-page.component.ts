import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { RoleAccessServiceService } from '../../service/role-access-service.service';
import { FormControl, FormBuilder, FormGroup, FormArray, Validators, AbstractControl } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Subject, Subscription, of } from 'rxjs';
import { catchError, takeUntil, tap } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import moment from 'moment';
import { DxDataGridComponent } from 'devextreme-angular';
import * as _ from 'underscore';
import { saveAs } from 'file-saver';
import * as ExcelJS from 'exceljs';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Router } from '@angular/router';
@Component({
  selector: 'app-role-details-page',
  templateUrl: './role-details-page.component.html',
  styleUrls: ['./role-details-page.component.scss']
})
export class RoleDetailsPageComponent implements OnInit {

  rolePermissionList: any
  permissionTableData: any
  userTableData: any

  isPermisisonDataLoading = false
  isUserDataLoading = false

  protected $onDestroy = new Subject<void>();
  protected $onAppApiCalled = new Subject<void>();
  applicationObjectList: any
  applicationList: any

  allMode = "allPages";
  checkBoxesMode = 'always';
  selectedPermissionRowKeys: any[] = [];
  allPermissionSelected: boolean = false;

  fieldVisibleConfig: any

  accessObjectValue = [{
    "value": "*",
    "label": "No Restriction"
  }, {
    "value": "True",
    "label": "Restricted"
  }, {
    "value": "False",
    "label": "No Access"
  }, {
    "value": "User",
    "label": "User"
  }]

  operationValue = [{
    "value": "*",
    "label": "All Access"
  }, {
    "value": "Create",
    "label": "Create"
  }, {
    "value": "Delete",
    "label": "Delete"
  }, {
    "value": "Read",
    "label": "Read"
  },
  {
    "value": "Update",
    "label": "Update"
  }]

  addPermissionFormGroup: FormGroup

  displayAddPermissionforRole = false
  isLoadingforFromDisplay = false
  isDeletePermissionsLoading = false

  accessOperation = [{
    "value": "*",
    "label": "All Access"
  }, {
    "value": "Create",
    "label": "Create"
  }, {
    "value": "Delete",
    "label": "Delete"
  }, {
    "value": "Read",
    "label": "Read"
  },
  {
    "value": "Update",
    "label": "Update"
  }]

  accessOperationTemp = []
  isOperationValueDisabled = false
  isLoadingOnPermissionCreation = false

  rolebandLableConfig: any

  accessOperationTempList = [];
  isOperationValueDisabledList = []
  isPermissionNameDisabledList = []

  @ViewChild(DxDataGridComponent, { static: false }) dataGridPermission: DxDataGridComponent;
  @ViewChild('premissionListGrid', { static: false }) premissionListGrid: DxDataGridComponent;
  @ViewChild('usersListGrid', { static: false }) usersListGrid: DxDataGridComponent;

  constructor(private api: RoleAccessServiceService, private fb: FormBuilder,
    private http: HttpClient,
    private _toaster: ToasterService,
    private snackBar: MatSnackBar,
    public dialogRef: MatDialogRef<RoleDetailsPageComponent>,
    private router: Router,
    @Inject(MAT_DIALOG_DATA) public data: any) { }

  async ngOnInit() {
    await this.loadRolePerimissionLits()
    await this.loadRoleUsersList()
  }

  async loadRolePerimissionLits() {
    this.isPermisisonDataLoading = false
    this.permissionTableData = []

    await this.api.getApplicationObjectList().subscribe((res: any) => {
      if (res.messType == "S") {
        this.applicationObjectList = res["data"]
        _.each(this.applicationObjectList, (item) => {
          item.object_name = item.name
        })
      }
      else {
        this.applicationObjectList = []
      }
    }, err => {
      console.error(err);
      this.applicationObjectList = []
      this._toaster.showError("Error on Retrieving Application Object List", err, 5000);
    })

    await this.api.getApplicationList().subscribe((res: any) => {
      if (res.messType == "S") {
        this.applicationList = res["data"]
        _.each(this.applicationList, (item) => {
          item.application_name = item.name
        })
      }
      else {
        this.applicationList = []
      }
    }, err => {
      console.error(err);
      this.applicationList = []
      this._toaster.showError("Error on Retrieving Application List", err, 5000);
    })

    await this.api.getRolePermissionDetails(this.data.role_id).pipe(takeUntil(this.$onDestroy))
      .subscribe((res: any) => {
        if (res.messType == "S") {
          this.isPermisisonDataLoading = true
          this.permissionTableData = res["data"]
        }
        else {
          this.permissionTableData = []
          this.isPermisisonDataLoading = true

        }
      }, err => {
        console.error(err);
        this.isPermisisonDataLoading = true
        this.permissionTableData = []
      })
  }

  async loadRoleUsersList() {
    this.isUserDataLoading = false
    this.userTableData = []

    await this.api.getRoleUsersDetails(this.data.role_id).pipe(takeUntil(this.$onDestroy))
      .subscribe((res: any) => {
        if (res.messType == "S") {
          this.isUserDataLoading = true
          this.userTableData = res["data"]
        }
        else {
          this.userTableData = []
          this.isUserDataLoading = true

        }
      }, err => {
        console.error(err);
        this.isUserDataLoading = true
        this.userTableData = []
      })
  }

  openColumnChooserPermission() {
    this.premissionListGrid.instance.showColumnChooser();
  }

  exportGridToExcelPermissionList() {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Main sheet');

    exportDataGrid({
      component: this.premissionListGrid.instance,
      worksheet: worksheet,
      autoFilterEnabled: true
    }).then(() => {
      workbook.xlsx.writeBuffer().then((buffer) => {
        saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Permission List_Role ID ${this.data.role_id}.xlsx`);
      });
    });
  }

  openColumnChooserUsers() {
    this.usersListGrid.instance.showColumnChooser();
  }

  exportGridToExcelUsersList() {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Main sheet');

    
    this.usersListGrid.instance.columnOption('actionColumn', 'edit_user_role', false);

    exportDataGrid({
      component: this.usersListGrid.instance,
      worksheet: worksheet,
      autoFilterEnabled: true
    }).then(() => {
      this.usersListGrid.instance.columnOption('actionColumn', 'edit_user_role', true);
      
      workbook.xlsx.writeBuffer().then((buffer) => {
        saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Users List_Role ID ${this.data.role_id}.xlsx`);
      });
    });
  }

  editUserRole(data) {
    console.log("Clicked Role Details  : ", data)
    const url = `/main/employee-central/employeeCentralDetail/${data.associate_id}/overview/organisation`;
    window.open(url, '_blank');
  }

  closePage() {
    this.dialogRef.close();
  }
}
