import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { read, WorkBook, WorkSheet, utils } from 'xlsx';

@Injectable({
  providedIn: 'root',
})
export class LeaveUploadService {
  data: any;
  excelData: any;
  constructor(private http: HttpClient) {}

  leaveDataUpload(evt, associateId, fileName) {
    return new Promise((resolve, reject) => {
      let items_param;
      const target: DataTransfer = <DataTransfer>evt.target;
      const reader: FileReader = new FileReader();
      reader.onload = (e: any) => {
        const bstr: string = e.target.result;
        const wb: WorkBook = read(bstr, {
          type: 'binary',
          cellDates: true,
          cellNF: false,
          cellText: false,
        });
        const wsname: string = wb.SheetNames[0];
        const ws: WorkSheet = wb.Sheets[wsname];
        this.data = utils.sheet_to_json(ws, {
          dateNF: 'YYYY-MM-DD',
          raw: false,
          header: 1,
        });
        let columns,
          data = [];
        data.push(this.data);
        this.http
          .post('/api/tsNodeV2Primary/uploadLeaveData', {
            excelData: data,
            associateId: associateId,
            fileDetails: fileName,
          })
          .subscribe(
            (res) => {
              this.excelData = [];
              return resolve(res);
            },
            (err) => {
              return reject(err);
            }
          );
      };

      reader.readAsBinaryString(target.files[0]);
    });
  }

  insertLeaveData(data, sCount, eCount, totalPage, currentPage, fileName, stagingData) {
    return this.http.post('/api/tsNodeV2Primary/insertLeaveRecords', {
      leaveData: data,
      sCount: sCount,
      eCount: eCount,
      totalPage: totalPage,
      currentPage: currentPage,
      fileName: fileName,
      stagingData: stagingData
    });
  }

  getLeaveUploadLogs() {
    return this.http.post('/api/tsNodeV2Primary/getLeaveUploadLogs', {});
  }
}
