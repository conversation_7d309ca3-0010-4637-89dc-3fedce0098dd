const express = require("express");
const router = express.Router();


/**
 * Health check api for monitoring
 */
router.get("/getHealthStatus",
  async (req, res) => {
    try {
      res.status(200).send("success")
    } catch (err) {
      console.log(err);
      res.status(500).json({ 
        message: err.message,
        stack: err.stack,
        status: "failure"
      });
    }
  });

module.exports = router;