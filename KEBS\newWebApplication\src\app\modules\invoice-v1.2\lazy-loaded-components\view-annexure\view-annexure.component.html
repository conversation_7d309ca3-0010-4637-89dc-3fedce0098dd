<div class="view-annexure-styles">
  <ng-container *ngFor="let element of annexureData; let i = index" >
    <div class="row"
    style="font-size: 14px !important; border-bottom: #b3b3b3 1px solid; margin-left: 15px !important; margin-right: 15px !important;">
    <div class="col-10 d-flex">
      <span *ngIf="serviceTypeId == 1 || serviceTypeId == 3 || serviceTypeId == 4 " class="mx-auto pt-2 "
        style="font-weight: 500 !important; color: #9f2825 ;  max-width: 900px; text-overflow: ellipsis; overflow: hidden;" matTooltip="{{ element.to }}" matTooltipClass="my-tooltip-max-width">
        {{ element.to }} Annexure
      </span>
      <span *ngIf="serviceTypeId == 2" class="mx-auto pt-2" style="font-weight: 500 !important; color: #9f2825 ;  max-width: 900px; text-overflow: ellipsis; overflow: hidden;" matTooltip="
      {{ element.currentMilestone.to }}" matTooltipClass="my-tooltip-max-width">
        {{ element.currentMilestone.to }} Annexure
      </span>
    </div>
    <div class="col-2 d-flex">
      <span class="ml-auto">
        <button mat-icon-button matTooltip="Download" 
          class="icon-tray-button"
          (click)="generatePdfAnnex(i, serviceTypeId == 2 ? element.currentMilestone.from  : element.from, serviceTypeId == 2 ? element.currentMilestone.to : element.to )">
          <mat-icon class="smallCardIcon">save_alt</mat-icon>
        </button>
        <button mat-icon-button matTooltip="Close" class="icon-tray-button ml-1" (click)="close()">
          <mat-icon class="smallCardIcon">close</mat-icon>
        </button>
      </span>
    </div>
  </div>
  <div class="container-fluid pl-2 pr-2" id="{{ i }}" *ngIf="serviceTypeId == 1">
    <div class="row row-body pdf-border pt-3">
      <div class="col-12 pl-1 pr-1">
        <div class="row">
          <div class="col-6 pl-2" style=" color: #9f2825;
          padding-top: 0;
          padding-left: 16px;
          font-size: 16px;
          font-weight: 500;">
            Annexure
          </div>
        </div>
        <div class="row pt-3">
          <div class="col-2  pl-2 d-flex" style="color: #000000;
          font-size: 13px !important;">
            Invoice No
            <span class="ml-auto" style="color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9" style="color: #000000;
          font-size: 14px !important;">
            <!-- <span>{{element.invoiceNo != 'XX/XX-XX/XXXX' ? element.invoiceNo : getInvoiceNo(element.from)}}</span> -->
            <span *ngIf="element.invoiceNo !== 'XXX/XX-XX/XXXX'; else getInvoiceNoTemplate">{{element.invoiceNo}}</span>
            <ng-template #getInvoiceNoTemplate>
            <span>{{getInvoiceNo(element.from)}}</span>
            </ng-template>
          </div>
        </div>
        <div class="row pt-1">
          <div class="col-2 pl-2 d-flex" style="color: #000000;
          font-size: 13px !important;">
            Invoice Date
            <span class="ml-auto" style="color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9" style="color: #000000;
          font-size: 14px !important;">
            {{ getDate(element.invoiceDate) }}
          </div>
        </div>
        <div class="row pt-1" *ngIf="element.milestoneName">
          <div class="col-2 pl-2 d-flex" style="color: #000000;
          font-size: 13px !important;">
            Milestone Name
            <span class="ml-auto" style="color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9" style="color: #000000;
          font-size: 14px !important;">
            {{ element.milestoneName }}
          </div>
        </div>
        <div class="row pt-1" *ngIf="element.projectName">
          <div class="col-2 pl-2 d-flex" style="color: #000000;
          font-size: 13px !important;">
            Portfolio Name
            <span class="ml-auto" style="color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9" style="color: #000000;
          font-size: 14px !important;">
            {{ element.projectName }}
          </div>
        </div>
        <!-- <div class="row pt-1 pb-3">
          <div
            class="col-2 d-flex pl-2"
            style="color: #000000;
          font-size: 13px !important;"
          >
            PO Value in {{ element.selectedCurrency }}
            <span
              class="ml-auto"
              style="color: #000000;
            font-size: 13px !important;"
            >
              :
            </span>
          </div>
          <div
            class="col-9"
            style="color: #000000;
          font-size: 14px !important;"
          >
            {{ fixNumberOnUI(element.milestoneValue) }}
          </div>
        </div> -->
      </div>
    </div>
    <div class="row pt-3">
      <div class="col-12 pl-1 pr-1">
        <div class="row">
          <div class="col-6 pl-2 pb-3" style=" color: #9f2825;
          padding-top: 0;
          padding-left: 16px;
          font-size: 16px;
          font-weight: 500;">
            Milestone phase particulars
          </div>
          <table class="table">
            <thead style="color: #000000;
            font-size: 14px !important;">
              <tr>
                <th scope="col">Consultant name</th>
                <th scope="col">Work location</th>
                <th scope="col">Title</th>
                <!-- <th scope="col">Planned Working {{element.unit == 'Month'? 'Day' : element.unit }}s</th>
                <th scope="col">Actual Worked {{element.unit == 'Month'? 'Day' : element.unit }}s</th> -->
                <th scope="col">
                  Planned {{ element.unit == 'Month' ? 'Working Month' : element.unit == 'Days' ? 'Working Day' : element.unit == 'Hours' ? 'Working Hours' : 'Pages' }}
                </th>
                <th scope="col">
                  Actual {{ element.unit == 'Month' ? 'Worked Month' : element.unit == 'Days' ? 'Worked Day' : element.unit == 'Hours' ? 'Worked Hours' : element.unit == 'Page' ? 'Pages' : 'Pages' }}
                </th>                  
                <th scope="col">
                  Per {{ element.unit }} rate in
                  {{ element.selectedCurrency }}
                </th>
                <th scope="col">Eligible Invoice value</th>
              </tr>
            </thead>
            <tbody style="color: #000000;
            font-size: 14px !important;">
              <tr *ngFor="let item of element.consultantDetail">
                <td>
                  {{ item.consultantName }}
                </td>
                <td>
                  {{ item.workLocation }}
                </td>
                <td>
                  {{ item.title }}
                </td>
                <td>
                  {{fixNumberOnUI(parseValue( item.plannedWorkingDays),element.selectedCurrency) }}
                </td>
                <td>
                  {{ fixNumberOnUI(parseValue(item.actualWorkingDays),element.selectedCurrency) }}
                </td>
                <td>
                  {{ fixNumberOnUI(parseValue(item.perDayRate),element.selectedCurrency ) }}
                </td>
                <td *ngIf="item.fteTotalAmount">
                  {{ fixNumberOnUI(parseValue(item.fteTotalAmount),element.selectedCurrency) }}
                </td>
                <td *ngIf="item.totalAmount">
                  {{ fixNumberOnUI(parseValue(item.totalAmount),element.selectedCurrency) }}
                </td>
              </tr>
              <tr style=" font-weight: 600 !important;
              color: #9f2825 !important;">
                <td colspan="5"></td>
                <td>Total</td>
                <td *ngIf="!element?.milestoneValue">{{ fixNumberOnUI(parseValue(element.subTotal),element.selectedCurrency) }}</td>
                <td *ngIf="element?.milestoneValue">{{ fixNumberOnUI(parseValue(element.milestoneValue),element.selectedCurrency) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div id="{{ i }}" class="container-fluid pl-2 pr-2" style="margin-top: 25px !important;"
    *ngIf="serviceTypeId == 2">
    <div class="row row-body pdf-border pt-3">
      <div class="col-12 pl-1 pr-1">
        <div class="row">
          <div class="col-6 pl-2" style=" color: #9f2825;
              padding-top: 0;
              padding-left: 16px;
              font-size: 16px;
              font-weight: 500;">
            Annexure
          </div>
        </div>
        <div class="row pt-3">
          <div class="col-2 pl-2 d-flex" style=" color: #000000;
              font-size: 13px !important;">
            Invoice No
            <span class=" ml-auto" style=" color: #000000;
                font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9" style="  color: #000000;
              font-size: 14px !important;">
            <span>{{element.currentMilestone.invoiceNo || getInvoiceNo(element.currentMilestone.from )}}</span>
          </div>
        </div>
        <div class="row pt-1">
          <div class="col-2  pl-2 d-flex" style=" color: #000000;
              font-size: 13px !important;">
            Invoice Date
            <span class=" ml-auto" style=" color: #000000;
                font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9 " style="  color: #000000;
              font-size: 14px !important;">
            {{ getDate(element.invoiceDate) }}
          </div>
        </div>
        <div class="row pt-1 " *ngIf="element.milestoneName">
          <div class="col-2  pl-2 d-flex" style=" color: #000000;
              font-size: 13px !important;">
            Milestone Name
            <span class=" ml-auto" style=" color: #000000;
                font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9 " style="  color: #000000;
              font-size: 14px !important;">
            {{ element.milestoneName }}
          </div>
        </div>
        <div class="row pt-1 " *ngIf="element.projectName">
          <div class="col-2  pl-2 d-flex" style=" color: #000000;
              font-size: 13px !important;">
            Portfolio Name
            <span class=" ml-auto" style=" color: #000000;
                font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9 " style="  color: #000000;
              font-size: 14px !important;">
            {{ element.projectName }}
          </div>
        </div>
        <div class="row pt-1 pb-3">
          <div class="col-2 d-flex  pl-2" style=" color: #000000;
              font-size: 13px !important;">
            PO Value in {{ element.currentMilestone.selectedCurrency }}
            <span class=" ml-auto">
              :
            </span>
          </div>
          <div class="col-9 " style="  color: #000000;
              font-size: 14px !important;">
            {{ fixNumberOnUI(parseValue(element.poValue),element.currentMilestone.selectedCurrency ) }}
          </div>
        </div>
      </div>
    </div>
    <div class="row pt-3">
      <div class="col-12 pl-1 pr-1">
        <div class="row">
          <div class="col-6  pl-2 pb-3" style=" color: #9f2825;
              padding-top: 0;
              padding-left: 16px;
              font-size: 16px;
              font-weight: 500;">
            Milestone phase particulars
          </div>
          <table class="table">
            <thead style="color: #000000;
                font-size: 14px !important;">
              <tr>
                <th scope="col" style="width:50% !important">Milestone name</th>
                <th scope="col">Due %</th>
                <th scope="col">Date</th>
                <th scope="col">Status</th>
                <th scope="col">
                  Value in {{ element.currentMilestone.selectedCurrency }}
                </th>
              </tr>
            </thead>
            <tbody style="color: #000000;
                font-size: 14px !important;">
              <tr *ngFor="let item of (element?.data)[0]" [style.background-color]="
                  element.currentMilestone == 'true' ? '#f6f6f6' : 'white'
                " [style.font-weight]="
                  element.currentMilestone == 'true' ? '600' : ''
                ">
                <td style="width:50% !important">{{ item.milestone_name }}</td>
                <td>{{ item.milestone_percentage }}%</td>
                <td>
                  {{
                    item.actual_end_date && item.actual_end_date != '0000-00-00 00:00:00'
                      ? getDate(item.actual_end_date)
                      : "-"
                  }}
                </td>
                <td>{{ item.status }}</td>
                <td>
                  {{ fixNumberOnUI(parseValue(item.milestone_value[0].value),element.currentMilestone.selectedCurrency ) }}
                </td>
              </tr>

              <tr style=" font-weight: 600 !important;
              color: #9f2825 !important;">
                <td colspan="3"></td>
                <td>Total</td>
                <td>{{ fixNumberOnUI(getTotal((element?.data)[0]),element.currentMilestone.selectedCurrency ) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div id="{{ i }}" class="container-fluid pl-2 pr-2" *ngIf="serviceTypeId == 3">
    <div class="row row-body pdf-border pt-3">
      <div class="col-12 pl-1 pr-1">
        <div class="row">
          <div class="col-6 pl-2" style=" color: #9f2825;
          padding-top: 0;
          padding-left: 16px;
          font-size: 16px;
          font-weight: 500;">
            Annexure
          </div>
        </div>
        <div class="row pt-3">
          <div class="col-2 pl-2 d-flex" style=" color: #000000;
          font-size: 13px !important;">
            Invoice No
            <span class=" ml-auto" style=" color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9" style=" color: #000000;
          font-size: 14px !important;">
            <span>{{element.invoiceNo || getInvoiceNo(element.from)}}</span>
          </div>
        </div>
        <div class="row pt-1">
          <div class="col-2 pl-2 d-flex" style=" color: #000000;
          font-size: 13px !important;">
            Invoice Date
            <span class="ml-auto" style=" color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9 " style=" color: #000000;
          font-size: 14px !important;">
            {{ getDate(element.invoiceDate) }}
          </div>
        </div>
        <div class="row pt-1" *ngIf="element.milestoneName">
          <div class="col-2 pl-2 d-flex" style=" color: #000000;
          font-size: 13px !important;">
            Milestone Name
            <span class=" ml-auto" style=" color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9 " style=" color: #000000;
          font-size: 14px !important;">
            {{ element.milestoneName }}
          </div>
        </div>
        <div class="row pt-1" *ngIf="element.projectName">
          <div class="col-2 pl-2 d-flex" style=" color: #000000;
          font-size: 13px !important;">
            Portfolio Name
            <span class=" ml-auto" style=" color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9 " style=" color: #000000;
          font-size: 14px !important;">
            {{ element.projectName }}
          </div>
        </div>
        <!-- <div class="row pt-1 pb-3">
          <div
            class="col-2 d-flex  pl-2"
            style=" color: #000000;
          font-size: 13px !important;"
          >
            PO Value in {{ element.selectedCurrency }}
            <span
              class="ml-auto "
              style=" color: #000000;
            font-size: 13px !important;"
            >
              :
            </span>
          </div>
          <div
            class="col-9"
            style=" color: #000000;
          font-size: 14px !important;"
          >
            {{ fixNumberOnUI(element.milestoneValue) }}
          </div>
        </div> -->
      </div>
    </div>
    <div class="row pt-3">
      <div class="col-12 pl-1 pr-1">
        <div class="row">
          <div class="col-6 pl-2 pb-3" style=" color: #9f2825;
          padding-top: 0;
          padding-left: 16px;
          font-size: 16px;
          font-weight: 500;">
            Milestone phase particulars
          </div>
          <table class="table">
            <thead style=" color: #000000;
            font-size: 14px !important;">
              <tr>
                <th scope="col">Milestone name</th>
                <th scope="col">Actual No of Hours</th>
                <th scope="col">Monthly Billable amount</th>
                <th scope="col" *ngIf="element?.extraHoursCharged">Extra hours charged</th>
                <th scope="col">Value in {{ element.selectedCurrency }}</th>
              </tr>
            </thead>
            <tbody style="color: #000000;
            font-size: 14px !important;">
              <tr>
                <td>{{ element.milestoneName }}</td>
                <td>{{fixNumberOnUI(parseValue( element.actualWorkingHours),element.selectedCurrency) }}</td>
                <td>
                  {{fixNumberOnUI((parseValue(element.actualWorkingHours) * parseValue(element.perHourRate)),element.selectedCurrency)}}
                </td>
                <td *ngIf="element?.extraHoursCharged">{{fixNumberOnUI(parseValue(element.extraHoursCharged),element.selectedCurrency) }}</td>
                <td>{{ fixNumberOnUI(parseValue(element.milestoneValue),element.selectedCurrency) }}</td>
              </tr>
              <tr style=" font-weight: 600 !important;
              color: #9f2825 !important;">
                <td [attr.colspan]="element?.extraHoursCharged ?  3 : 2"></td>
                <td>Total</td>
                <td>{{ fixNumberOnUI(parseValue(element.total,element.selectedCurrency) )}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div id="{{ i }}" class="container-fluid pl-2 pr-2" *ngIf="serviceTypeId == 4">
    <div class="row row-body pdf-border pt-3">
      <div class="col-12 pl-1 pr-1">
        <div class="row">
          <div class="col-6 pl-2" style=" color: #9f2825;
          padding-top: 0;
          padding-left: 16px;
          font-size: 16px;
          font-weight: 500;">
            Annexure
          </div>
        </div>
        <div class="row pt-3">
          <div class="col-2 pl-2 d-flex" style=" color: #000000;
          font-size: 13px !important;">
            Invoice No
            <span class=" ml-auto" style=" color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9" style=" color: #000000;
          font-size: 14px !important;">
            <span>{{element.invoiceNo || getInvoiceNo(element.from)}}</span>
          </div>
        </div>
        <div class="row pt-1">
          <div class="col-2 pl-2 d-flex" style=" color: #000000;
          font-size: 13px !important;">
            Invoice Date
            <span class="ml-auto" style=" color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9 " style=" color: #000000;
          font-size: 14px !important;">
            {{ getDate(element.invoiceDate) }}
          </div>
        </div>
        <div class="row pt-1" *ngIf="element.milestoneName">
          <div class="col-2 pl-2 d-flex" style=" color: #000000;
          font-size: 13px !important;">
            Milestone Name
            <span class=" ml-auto" style=" color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9 " style=" color: #000000;
          font-size: 14px !important;">
            {{ element.milestoneName }}
          </div>
        </div>
        <div class="row pt-1" *ngIf="element.projectName">
          <div class="col-2 pl-2 d-flex" style=" color: #000000;
          font-size: 13px !important;">
            Portfolio Name
            <span class=" ml-auto" style=" color: #000000;
            font-size: 13px !important;">
              :
            </span>
          </div>
          <div class="col-9 " style=" color: #000000;
          font-size: 14px !important;">
            {{ element.projectName }}
          </div>
        </div>
      </div>
    </div>
    <div class="row pt-3">
      <div class="col-12 pl-1 pr-1">
        <div class="row">
          <div class="col-6 pl-2 pb-3" style=" color: #9f2825;
          padding-top: 0;
          padding-left: 16px;
          font-size: 16px;
          font-weight: 500;">
            Milestone phase particulars
          </div>
          <table class="table">
            <thead style=" color: #000000;
            font-size: 14px !important;">
              <tr>
                <th scope="col" *ngIf="invoiceTenantDetails?.is_to_show_item_type == 1 && element.sublineItem[0]?.productServiceType" style="width: 30%; max-width: 30%;">Item Type</th>
                <th scope="col">Item Description</th>
                <th scope="col">Quantity</th>             
                <th scope="col">Rate</th>
                <th scope="col">Monthly Billable amount in {{ element.selectedCurrency }}</th>
                <!-- <th scope="col">Extra hours charged</th>
                <th scope="col">Value in {{ element.selectedCurrency }}</th> -->
              </tr>
            </thead>
            <tbody style="color: #000000;
            font-size: 14px !important;">
            <ng-container *ngFor="let ele of element.sublineItem">
              <tr>
                <td *ngIf="invoiceTenantDetails?.is_to_show_item_type == 1 && element.sublineItem[0]?.productServiceType">{{ ele.productServiceType}}</td>
                <td>{{ ele.description }}</td>
                <td>{{ fixNumberOnUI(parseValue(ele.quantity),element.selectedCurrency) }}</td>
                <td>{{ fixNumberOnUI(parseValue(ele.rate),element.selectedCurrency) }}</td>
                <td>{{ fixNumberOnUI(parseValue(ele.amount),element.selectedCurrency)}}</td>
              </tr>
            </ng-container>
              <tr style=" font-weight: 600 !important;
              color: #9f2825 !important;">
                <td [attr.colspan]="invoiceTenantDetails?.is_to_show_item_type == 1 && element.sublineItem[0]?.productServiceType ?  3 : 2"></td>
                <td>Total</td>
                <td>{{ fixNumberOnUI(parseValue(element.total),element.selectedCurrency) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  </ng-container>
</div>