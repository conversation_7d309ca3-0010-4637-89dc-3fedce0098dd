

<div style="display: block; width: 100%; height: 100%; overflow: hidden;">
<ng-container *ngIf="type=='single'">
<div class="row ml-3 mr-3 mt-3">
  <div class="col">
    <div class="ml-3 mr-3 mt-3" style="font-size: 17px; float: left;" >Select invoices to mail</div>
    <button mat-icon-button color="primary" (click)="closeDialog()"  style="float: right;">
      <mat-icon>close</mat-icon>
    </button>
  </div>
</div>
<div class="grid-container">
<dx-data-grid
  #dataGrid
  [dataSource]="dataSource"
  [showBorders]="false"
  [columnAutoWidth]="true"
  [selectedRowKeys]="selectedRows"
  (onSelectionChanged)="onSelectionChanged($event)"
  (onRowClick)="rowClick($event)"
  >
  <!-- <dxo-paging [pageSize]="10" [pageIndex]="0"> </dxo-paging> -->
  <dxo-sorting mode="single"></dxo-sorting>
  <dxo-selection  [showCheckBoxesMode]="checkBoxesMode" mode="multiple"> </dxo-selection>
  <dxi-column dataField="invoice_no" caption="Invoice Number" [width]="150"></dxi-column>
  <dxi-column dataField="milestone_name" caption="Description" [width]="200"></dxi-column>
  <dxi-column dataField="invoice_date" caption="Invoice Date" [width]="150"></dxi-column>
  <dxi-column dataField="due_date" caption="Due Date" [width]="150"></dxi-column>  
</dx-data-grid>
</div>

<div class="m-3 bottom-container">
  <div class="col-2 selected-invoices">{{ selectedRows.length + (selectedRows.length === 1 || selectedRows.length === 0 ? ' Invoice Selected' : ' Invoices Selected') }}</div>

  <div class="col-10 button-container">
    <button mat-stroked-button *ngFor="let option of templateOptions" class="light-button" (click)="selectTemplate(option)" [ngClass]="{'disabled': selectedRows.length === 0}">
      <span>{{ option.template_name }}</span>
      <span class="restriction-text">Choose at least one to proceed further</span>
    </button>
  </div>
  
</div>
</ng-container>
</div>

<ng-container *ngIf="type=='multiple'">
<h2 mat-dialog-title class="template-dialog-title">Choose a Template</h2>
<mat-dialog-content class="m-0 pr-2 pl-2">
  <mat-list>
    <mat-list-item *ngFor="let template of templateOptions let i = index" (click)="selectTemplateForMultiple(template)" class="template-list-item mb-3" style="justify-content: center !important; display: flex; border:2px solid #cf0001; border-radius: 20px;">
      <span class="pb-2">{{ i + 1 }}. {{ template.template_name }}</span>
    </mat-list-item>
  </mat-list>
</mat-dialog-content>
<mat-dialog-actions class="template-dialog-actions pt-3" style="justify-content: center !important; border-radius: 15px;">
  <button mat-button color="warn" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>
</ng-container>

