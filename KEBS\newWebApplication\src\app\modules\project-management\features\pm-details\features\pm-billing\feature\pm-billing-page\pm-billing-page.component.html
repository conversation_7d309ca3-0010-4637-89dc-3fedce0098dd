<div *ngIf = "oldScreen">
  <div class="pm-billing-page" *ngIf="loading">
    <div class="loader-container">
      <mat-spinner class="green-spinner loader" diameter="40"></mat-spinner>
    </div>
  </div>
  <div class="pm-billing-page" [ngStyle]="{'overflow-y':listView ? 'hidden' :'auto'}" *ngIf="!loading">
    <div class="header row">
      <!-- <div class="tab-name col-6">Milestone</div> -->
      <div calss="project-finacial-details" style="margin-top: -47px;position: absolute; right: 20%">
        <div
          style="width: 118px; height: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div class="font-family"
            style="color: #6E7B8F; font-size: 9px;font-weight: 500; text-transform: capitalize; line-height: 16px; letter-spacing: 0.24px;">
            {{('overall_milestone_value' | checkLabel : this.formConfig: 'milestone-landing': 'Milestone Gross Value')}}
          </div>
          <app-currency [toDisplay]="projectValueAccess" [currencyList]="projectMilestoneGrossValue" [code]="code" [font_size]="font_size_currency_header" class="flex-1" type="big">
          </app-currency>
        </div>
        <div
          style="width: 100px; height: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div class="font-family"
            style="color: #6E7B8F; font-size: 9px;font-weight: 500; text-transform: capitalize; line-height: 16px; letter-spacing: 0.24px;">
            {{('po_value' | checkLabel : this.formConfig: 'milestone-landing': 'Order Value')}}</div>
          <app-currency [toDisplay]="projectValueAccess"  [currencyList]="projectOrderValue" [code]="code" [font_size]="font_size_currency_header" class="flex-1" type="big">
          </app-currency>
        </div>
        <div
        style="width: 100px; height: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
        <div class="font-family"
          style="color: #6E7B8F; font-size: 9px;font-weight: 500; text-transform: capitalize; line-height: 16px; letter-spacing: 0.24px;">
          {{('consumed_value' | checkLabel : this.formConfig: 'milestone-landing': 'Consumed Value')}}</div>
        <app-currency [toDisplay]="projectValueAccess"  [currencyList]="projectConsumedValue" [code]="code" [font_size]="font_size_currency_header" class="flex-1" type="big">
        </app-currency>
        </div>
        <div
        style="width: 160px; height: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
        <div class="font-family"
          style="color: #6E7B8F; font-size: 9px;font-weight: 500; text-transform: capitalize; line-height: 16px; letter-spacing: 0.24px;">
          {{('remaining_value' | checkLabel : this.formConfig: 'milestone-landing': 'Remaining Value')}}</div>
        <app-currency [toDisplay]="projectValueAccess"  [currencyList]="projectRemainingValue" [code]="code" [font_size]="font_size_currency_header" class="flex-1" type="big">
        </app-currency>
        </div>
      </div>
      <div class="icons">
        <mat-icon [ngStyle]="{'margin-right':lumpsum_left}"
          style="margin-top: 11px;font-size: 18px;color: #7D838B;cursor: pointer;"
          *ngIf="billingAdviceAccess && lumpsumBillingAdviceProject"
          class="pending_actions" tooltip="Billing Advice"
          (click)="openLumpsumAdvise()">pending_actions</mat-icon>
        <div class="all" [ngStyle]="{'margin-left':left}" [matMenuTriggerFor]="menu">{{status_filter_name}} <mat-icon
            class="all-icon">keyboard_arrow_down</mat-icon>
        </div>
        <!----------------------------Filter Change-------------------------------->
        <!-- <div class="filter">
          <mat-icon class="filter-icon" tooltip="Filter" [satPopoverAnchor]="showFilterPopup" (click)="showFilterPopup.toggle()">filter_list</mat-icon>
        </div>
        <sat-popover #showFilterPopup horizontalAlign="after" verticalAlign="below" hasBackdrop>
  
          <div class="card" style="width: 500px;">
            <div class="row filter-header">
              <div class="filter-text">Filters</div>
            </div>
            <div class="row" *ngIf="filterTags?.length > 0" class="selected-tags">
              <div *ngFor="let chip of filterTags">
                <div class="tag-chip">
                  <mat-icon class="tag-preffix" [style.color]="(chip.color ? chip.color : 'grey')">{{chip.icon ? chip.icon
                    : 'cirlce'}}</mat-icon>
                  <div class="tag-text" tooltip="{{chip.name}}">
                    {{ chip.name | maxellipsis: 5 }}
                  </div>
                  <mat-icon class="tag-clear-icon"
                    (click)="closeTagFilter(chip.name,chip.type,chip.color,chip.i,chip.icon)">clear</mat-icon>
                </div>
              </div>
            </div>
            <div class="row selected-tags" *ngIf="filterTags.length == 0" style="padding-left: 15px; color:'grey'">
              {{('noFilter_selected' | checkLabel : this.formConfig: 'risk-register-filter': 'No Filter selected')}}
            </div>
            <div class="row filter-footer">
              <div class="button">
                <div class="clear-button" (click)="clearFilter()">
                  <div class="clear-button-text">
                    Clear
                  </div>
                </div>
              </div>
            </div>
          </div>
  
  
        </sat-popover> -->
        <!----------------------------Filter Change-------------------------------->
        <mat-menu #menu="matMenu" style="overflow: hidden !important;">
          <div *ngFor="let item of milestone_filter_status_list">
  
            <button *ngIf="item.id!=-1" (click)="getStatusFilterData(item.id,item.name)" mat-menu-item
              class="font-family" style="color:rgb(125, 131, 139);font-weight: 400;text-transform: capitalize;letter-spacing: 0.24px;line-height: 31px !important; height: 31px !important; font-size: 13px !important;padding-left: 25px;">
              <div
                style="width: 6px; height: 6px; border-radius: 9999px;margin-top: 12px;position: absolute;margin-left: -14px;"
                [ngStyle]="{'background':item.color}">
              </div>{{item.name}}
            </button>
            <button *ngIf="item.id==-1" (click)="getStatusFilterData(item.id,item.name)" mat-menu-item
              class="font-family" style="color:rgb(125, 131, 139);font-weight: 400;text-transform: capitalize;letter-spacing: 0.24px;line-height: 31px !important; height: 31px !important; font-size: 13px !important;padding-left: 25px;border-top: 1px solid rgb(125, 131, 139);">
              <div
                style="width: 6px; height: 6px; border-radius: 9999px;margin-top: 12px;position: absolute;margin-left: -14px;"
                [ngStyle]="{'background':item.color}">
              </div>{{item.name}}
            </button>
          </div>
        </mat-menu>

        <!-- Quote Filter Dropdown -->
        <div *ngIf="opportunity_status === 1" class="all" style="margin-left: 10px;" [matMenuTriggerFor]="quoteMenu">{{quote_filter_name}} <mat-icon
            class="all-icon">keyboard_arrow_down</mat-icon>
        </div>
        <mat-menu #quoteMenu="matMenu" style="overflow: hidden !important;">
          <div *ngFor="let item of quote_filter_list">
            <button *ngIf="item.id!=-1" (click)="getQuoteFilterData(item.id,item.name)" mat-menu-item
              class="font-family" style="color:rgb(125, 131, 139);font-weight: 400;text-transform: capitalize;letter-spacing: 0.24px;line-height: 31px !important; height: 31px !important; font-size: 13px !important;padding-left: 25px;">
              {{item.name}}
            </button>
            <button *ngIf="item.id==-1" (click)="getQuoteFilterData(item.id,item.name)" mat-menu-item
              class="font-family" style="color:rgb(125, 131, 139);font-weight: 400;text-transform: capitalize;letter-spacing: 0.24px;line-height: 31px !important; height: 31px !important; font-size: 13px !important;padding-left: 25px;border-top: 1px solid rgb(125, 131, 139);">
              {{item.name}}
            </button>
          </div>
        </mat-menu>

        <mat-button-toggle-group class="button-toggle-class">
          <mat-button-toggle value="list" tooltip="List view" [checked]="listView" (click)="toggleList('list')">
            <mat-icon class="list-view" [ngStyle]="listView ? {'color': button} : null">list</mat-icon>
          </mat-button-toggle>
          <mat-button-toggle value="card" tooltip="Card view" [checked]="!listView" (click)="toggleList('card')">
            <mat-icon class="card-view" [ngStyle]="!listView ? {'color': button} : null">dashboard</mat-icon>
          </mat-button-toggle>
        </mat-button-toggle-group>
  
        <div tooltip="{{sort_tooltip}}">
          <mat-icon id='filter' class="sort" (click)="openFilter()">sort</mat-icon>
          <mat-icon id='filter' (click)="openFilter()"
            style="cursor: pointer;font-size: 14px;margin-top: 17px;margin-left: -12px;color: #7D838B;"
            *ngIf="duedate">arrow_upward</mat-icon>
          <mat-icon id='filter' (click)="openFilter()"
            style="cursor: pointer;font-size: 14px;margin-top: 17px;margin-left: -12px;color: #7D838B;"
            *ngIf="!duedate">arrow_downward</mat-icon>
        </div>
  
        <!-- <mat-icon class="filter" matTooltip="Filter">filter_list</mat-icon> -->
  
        <mat-icon *ngIf="calendarIcon" class="date-range" tooltip="Calendar View">date_range</mat-icon>
  
        <mat-icon class="delete" tooltip="Delete" (click)="deleteMilestone()" *ngIf="check && deleteMilestoneAccess">delete_outline</mat-icon>
  
        <mat-icon class="edit" tooltip="Edit" *ngIf="check" (click)="openEdit()">edit</mat-icon>
  
        <span class="material-icons color" tooltip="Generate Milestones" (click)="generateMilestone()" *ngIf="generateMilestoneAccess"
          >smart_toy</span>
  
        <mat-icon class="multiple" tooltip="Create Multiple Milestones" (click)="multipleMilestone()" *ngIf="multipleMilestoneAccess"
          >storage</mat-icon>
  
        <mat-icon class="create" tooltip="Create Milestone" (click)="create()"
          >add_box</mat-icon>
          
        <mat-icon *ngIf="check && ((edit_data.financial_status_id==8 && milestone_status_ytb_revert) || (edit_data.financial_status_id==15 && milestone_status_accrued_revert))" class="revert" tooltip="Revert Status"  (click)="revertStatus()">undo</mat-icon>
  
        <!-- <mat-icon class="list-view" matTooltip="List view" (click)="toggleList()" [ngStyle]="listView ? {'color': '#EE4961'} : null">list</mat-icon>
        
        <mat-icon class="card-view" matTooltip="Card view" (click)="toggleList()" [ngStyle]="!listView ? {'color': '#EE4961'} : null">dashboard</mat-icon> -->
      </div>
    </div>
    <div *ngIf="data.length>0 && !listView" style="padding-top: 10px;padding-bottom: 20px;">
      <div *ngFor="let p of data; let i=index">
        <div class="card-m pl-2">
          <div *ngIf="p.month_check"
            style="width: 100%;margin-top: 5px;height: 32px; padding: 8px; background: #F6F6F6; border-radius: 4px; justify-content: flex-start; align-items: flex-start; gap: 10px; display: inline-flex">
            <div class="font-family"
              style="color: #272A47; font-size: 14px; font-weight: 500; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
              {{p.month}}</div>
          </div>
          <div [ngStyle]="{'box-shadow':p.box,'margin-top': '5px'}">
            <div class="calender-icon">
              <app-date-display class="row" [date]="p.end_date" [color]="p.color"></app-date-display>
            </div>
            <div class="details" (dblclick)="editMilestone(p)">
              <mat-icon class="check" [ngStyle]="{'color':p.color}">check_circle_outline</mat-icon>
              <div class="inside-details">
                <div class="milestone-details">
                  <div (click)="openEditName(i,p.month_check)" id="myDiv1" class="milestone-inside-details">
                    <div style="display: flex;">
                      <div class="milestone-id"
                        *ngIf="('milestone_id' | checkActive : this.formConfig: 'milestone-landing')">
                        {{('id' | checkLabel : this.formConfig: 'milestone-landing': 'ID')}}:{{p.id}}
                      </div>
                      <div class="milestone-name"
                        *ngIf="('milestone_name' | checkActive : this.formConfig: 'milestone-landing')"
                        tooltip="{{p.label}}" placement="top" showDelay="500">
                        {{ p.label | maxellipsis: 80 }}
                      </div>
                    </div>
                    <div style="display: flex;margin-left: 2px;">
                      <div class="milestone-value"
                        *ngIf="('milestone_value' | checkActive : this.formConfig: 'milestone-landing')">
                        <app-currency [toDisplay]="projectValueAccess"  [currencyList]="p.currencyListJSON" [code]="code"  [font_size]="font_size_currency_card" class="flex-1" type="big">
                        </app-currency>
                      </div>

                      
                      <div class="tags" *ngIf="('milestone_tags' | checkActive : this.formConfig: 'milestone-landing')">
                        <div class="chips-container" *ngIf="p.tagActive">
                          <div *ngFor="let chip of p.Tags">
                            <!-- <mat-chip  *ngIf="chip.unique_id_2==p.id" class="custom-chip">
                    <div class="dot" [style.background-color]="chip.color"></div>
                    <div class="tag-text">{{ chip.name }}</div>
                  </mat-chip> -->
                            <div *ngIf="chip.unique_id_2==p.id"
                              style="margin-left: 10px;width: 100%;height: 15px;padding-left: 8px;padding-right: 1px;padding-top: 2px;padding-bottom: 2px;background: #F6F6F6;border-radius: 16px;border: 1px #E8E9EE solid;justify-content: flex-start;align-items: center;gap: 4px;display: inline-flex;">
                              <div style="width: 6px; height: 6px; border-radius: 9999px"
                                [style.background-color]="chip.color">
                              </div>
                              <div tooltip="{{chip.name}}" class="font-family"
                                style="color: #7D838B; font-size: 12px; font-weight: 400; line-height: 16px;overflow: hidden;text-overflow: ellipsis;width: 65px;white-space: nowrap;">
                                {{ chip.name }}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
  
                </div>
                <!-- <div class="progress"> -->
  
                <!-- </div> -->
                <div style="display: flex;padding-top: 10px;">
                  <div class="inside-details-footer">
                    <mat-icon *ngIf="billingAdviceAccess && p.financial_status_id!=6"
                      class="pending_actions" [ngStyle]="{'color':p.color}" tooltip="Billing Advice"
                      (click)="openBillingAdivise(p.id,p.start_date,p.end_date,p.financial_status_id,p.currencyListJSON,p.financial_status,p.background,p.border,p.color,p.label,i)">pending_actions</mat-icon>
                    <mat-chip *ngIf="('milestone_status' | checkActive : this.formConfig: 'milestone-landing')"
                      class="status-chip" [ngStyle]="{'border':p.border,background:p.background}"
                      (click)="togglePopover(p.financial_status_id,i)"
                      >
                      <div class="status-text" [ngStyle]="{'color':p.color}">{{p.financial_status}}</div>
                    </mat-chip>
                    <!-- <div *ngIf="isPopoverOpen"> -->
                    <!-- <sat-popover   horizontalAlign="after" verticalAlign="above" hasBackdrop> -->
                    <div *ngIf="p.isPopoverOpen && divVisible" id="myDiv" class="pop-up">
                      <div class="column-list">
                        <label *ngFor="let column of status;let j=index" class="column-item">
                          <mat-chip class="status-chip"
                            [ngStyle]="{'border':column['border'],background:column['background']}"
                            (click)="statusChange(i,column['id'])">
                            <div class="status-text" [ngStyle]="{'color':column['color']}">{{column['name']}}</div>
                          </mat-chip>
                        </label>
                      </div>
                    </div>
  
                    <!-- </sat-popover> -->
                    <!-- </div> -->
                  </div>
                  <div style="display:flex;margin-top: 9px;padding-left: 10px;"
                    *ngIf="('milestone_percentage' | checkActive : this.formConfig: 'milestone-landing')">
                    <mat-progress-bar class="custom-progress-bar" mode="determinate"
                      [value]="p.completion_percentage"></mat-progress-bar>
                    <div class="progress-name font-family" style="margin-top: -2.5px;
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              line-height: 14px;
              letter-spacing: 0.02em;
              text-align: center;
              color: #5F6C81;">{{p.completion_percentage}}% complete </div>
                  </div>
                  <div class="type-col-1" *ngIf="('milestone_type' | checkActive : this.formConfig: 'milestone-landing-list')">
                    <div class="type-chip-1" [ngStyle]="{'background':p.milestone_type_color}">
                      <div class="type-text-1">{{p.milestone_type_name}}</div>
                    </div>
                  </div>
                </div>
              
                <div class="status-delete">
                  <div class="status" *ngIf="(p.financial_status_id==4 && this.currentDate > p.end_date)"
                    style="background:#F27A6C">
                    <div class="status-name">
                      {{('overdue_status' | checkLabel : this.formConfig: 'milestone-landing': 'Overdue')}}
                    </div>
                  </div>
                  <!-- <div class="status" *ngIf="p.financial_status_id==9" style="background:#F27A6C">
                    <div class="status-name">
                      {{('ardue_status' | checkLabel : this.formConfig: 'milestone-landing': 'AR Due')}}
                    </div>
                  </div> -->
                  <div class="status-empty"
                    *ngIf="!((p.financial_status_id==4 && this.currentDate > p.end_date) || p.financial_status_id==9)">
                  </div>
                  <!-- <div class="delete">
                <mat-icon class="delete-icon">delete_outline</mat-icon>
              </div> -->
                </div>
              </div>
  
              <div class="d-flex" style="justify-content: space-between;">
                <div class="actions">
                  <div class="add-notes" *ngIf="('milestone_notes' | checkActive : this.formConfig: 'milestone-landing')">
                    <mat-icon class="notes-icon" (click)="addNotes(i)"> chat_bubble_outline</mat-icon>
                    <div class="notes-name" (click)="addNotes(i)">{{('comments' | checkLabel : this.formConfig:
                      'milestone-landing': 'Comments')}}</div>
                  </div>
                  <div class="add-tags" *ngIf="('milestone_add_tags' | checkActive : this.formConfig: 'milestone-landing')">
                    <mat-icon id="displayTagDiv" (click)="addTag(i)" class="tags-icon">outlined_flag</mat-icon>
                    <div id="displayTagDiv" class="tags-name" (click)="addTag(i)">{{('add_tags' | checkLabel :
                      this.formConfig: 'milestone-landing': 'Add Tags')}}</div>
                  </div>
                  <form [formGroup]="stepperFormGroup">
                    <div *ngIf="p.tagPopUpOpen && isDisplayVisible" id="displayTagDiv"
                      style="width: 166px;margin-left: 527px; height: 200px;padding-bottom: 0px !important;padding: 16px; background: white; box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); border-radius: 4px; border: 1px #E8E9EE solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex;margin-top: -356px;width: 166px;margin-left: -189px;height: 200px;padding-bottom: 0px !important;padding: 16px;background: white;box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);border-radius: 4px;border: 1px #E8E9EE solid;flex-direction: column;justify-content: flex-start;align-items: flex-start;gap: 8px;display: inline-flex;margin-top: -202px;position: fixed;">
                      <div style="justify-content: flex-start; align-items: center; gap: 2px; display: inline-flex">
                        <div style="width: 14px; height: 14px; position: relative">
                          <div style="width: 14px; height: 14px; left: 0px; top: 0px; position: absolute;"></div>
                          <div style="width: 11.07px; height: 11.07px; left: 1.46px; top: 1.46px; position: absolute;">
                            <mat-icon style="color: #7D838B;font-size: 17px;top: 10px;margin-top: -10px;margin-left: -5px;">
                              sell
                            </mat-icon>
                          </div>
                        </div>
                        <!-- <div style="color: #7D838B; font-size: 11px; font-family: Roboto; font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.22px; word-wrap: break-word"> Add Tag..</div> -->
                        <input style="border: none;width: 130px;outline: none;font-size: 12px;" minlength='3' maxlength="30"
                          formControlName="textInputControlDisplay" type="text" placeholder="Search" />
                      </div>
                      <div style="align-self: stretch; height: 0px; border: 1px #E8E9EE solid"></div>
                      <div
                        style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
                        <div class="font-family"
                          style="color: #B9C0CA; font-size: 11px; font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.22px; word-wrap: break-word">
                          All projects</div>
                        <div style="width: 16px; height: 16px; position: relative">
                          <div style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute;"></div>
                          <div style="width: 13.33px; height: 13.33px; left: 1.33px; top: 1.33px; position: absolute;">
                            <mat-icon style="color: #B9C0CA;font-size: 15px;"
                              tooltip="Add tag by click">info_outline</mat-icon>
                          </div>
                        </div>
                      </div>
                      <div style="height: 700px;
                overflow-y: auto;
                overflow-x: hidden;
                width: 149px;">
                        <div *ngFor="let p of tagsPopUp;let k=index" style="padding-bottom: 5px;">
                          <div (click)="getDisplayTag(k,i)"
                            style="padding-left: 8px;cursor: pointer; padding-right: 8px; background: #F6F6F6; border-radius: 4px; border: 1px #E8E9EE solid; justify-content: flex-start; align-items: center; gap: 4px; display: inline-flex">
                            <div style="width: 4px; height: 4px; border-radius: 9999px" [ngStyle]="{'background':p.color}">
                            </div>
                            <div *ngIf="18>p.name.length" tooltip="{{p.name}}"
                              class="font-family" style="color: #7D838B; font-size: 11px; font-weight: 400; line-height: 16px; word-wrap: break-word">
                              {{p.name}}</div>
                            <div *ngIf="p.name.length>=18" tooltip="{{p.name}}"
                              class="font-family" style="color: #7D838B;font-size: 11px;font-weight: 400;line-height: 16px;/* word-wrap: break-word; */overflow: hidden;text-overflow: ellipsis;width: 105px;white-space: nowrap;">
                              {{p.name}}</div>
                          </div>
                        </div>
                      </div>
    
                    </div>
                  </form>
                  <div class="attach-task" *ngIf="('milestone_task' | checkActive : this.formConfig: 'milestone-landing')"
                    (click)="openAttachTask(p.id,p.start_date,p.end_date,p.financial_status_id,p.currencyList)">
                    <mat-icon class="task-icon">playlist_add</mat-icon>
                    <div class="task-name">{{('assign_dependency' | checkLabel : this.formConfig: 'milestone-landing':
                      'Assign Dependencies')}}</div>
                  </div>
                  <div class="add-attachment" (click)="addAttachment(p.id,p.gantt_id)">
                    <mat-icon class="attachment-icon">attachment</mat-icon>
                    <div class="attachment-name">{{('add_attachments' | checkLabel : this.formConfig: 'milestone-landing':
                      'Add Attachments')}}</div>
                  </div>
                  <!-- <div class="add-dependencies">
                <mat-icon class="dependencies-icon">remove_red_eye</mat-icon>
                <div class="dependencies-name">view Dependencies</div>
              </div> -->
                </div>
                <div class="mr-4 d-flex">
                  <div class="d-flex" *ngIf="('scheduled_date' | checkActive : this.formConfig: 'milestone-landing-card') && this.applicableScheduleDateStatus.includes(p.financial_status_id)">
                  <div class="card-date-label">{{('scheduled_date' | checkLabel :
                    this.formConfig: 'milestone-landing-card': 'Scheduled Date')}}</div>
                  <div class="ml-1 card-date-value">{{p.scheduled_date}}</div>
                  </div>
                  <div class="ml-3 d-flex" style = "width:180px" *ngIf="('accrual_reversal_date' | checkActive : this.formConfig: 'milestone-landing-card') && this.applicableAccrualDateStatus.includes(p.financial_status_id)">
                  <div class="card-date-label">{{('accrual_reversal_date' | checkLabel :
                    this.formConfig: 'milestone-landing-card': 'Accrua Reversal Date')}}</div>
                  <div class="ml-1 card-date-value">
                    <ng-container *ngIf="p.accrual_reversal_date !== 'NA'; else naTemplate">
                      {{p.accrual_reversal_date}}
                    </ng-container>
                    <ng-template #naTemplate>NA</ng-template>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="column-row sticky-top" *ngIf="data.length>0 && listView">
      
        <div class="select-header" *ngIf="('milestone_select' | checkActive : this.formConfig: 'milestone-landing-list')">
  
        </div>
        <div class="id-header" *ngIf="('milestone_id' | checkActive : this.formConfig: 'milestone-landing-list')">
          {{('milestone_id' | checkLabel : this.formConfig: 'milestone-landing-list': 'Milestone id')}}
        </div>
        <div class="name-header" *ngIf="('milestone_name' | checkActive : this.formConfig: 'milestone-landing-list')">
          {{('milestone_name' | checkLabel : this.formConfig: 'milestone-landing-list': 'Milestone Name')}}
        </div>
        <div class="type-header" *ngIf="('milestone_type' | checkActive : this.formConfig: 'milestone-landing-list')">
          {{('milestone_type' | checkLabel : this.formConfig: 'milestone-landing-list': 'Milestone Type')}}
        </div>
        <div class="value-header" *ngIf="('milestone_value' | checkActive : this.formConfig: 'milestone-landing-list')">
          {{('milestone_value' | checkLabel : this.formConfig: 'milestone-landing-list': 'Milestone Value')}}
        </div>
     
      
        <div class="status-header" *ngIf="('milestone_status' | checkActive : this.formConfig: 'milestone-landing-list')">
          {{('milestone_status' | checkLabel : this.formConfig: 'milestone-landing-list': 'Milestone Status')}}
        </div>
        <div class="startDate-header"
          *ngIf="('milestone_startDate' | checkActive : this.formConfig: 'milestone-landing-list')">
          {{('milestone_startDate' | checkLabel : this.formConfig: 'milestone-landing-list': 'Start Date')}}
        </div>
        <div class="endDate-header"
          *ngIf="('milestone_endDate' | checkActive : this.formConfig: 'milestone-landing-list')">
          {{('milestone_endDate' | checkLabel : this.formConfig: 'milestone-landing-list': 'Due Date')}}
        </div>
        <div class="percentage-header"
          *ngIf="('milestone_percentage' | checkActive : this.formConfig: 'milestone-landing-list')">
          {{('milestone_percentage' | checkLabel : this.formConfig: 'milestone-landing-list': 'Completion Percentage')}}
        </div>
        <div class="actions-header" *ngIf="('actions-header' | checkActive : this.formConfig: 'milestone-landing-list')">
          {{('actions-header' | checkLabel : this.formConfig: 'milestone-landing-list': 'Actions')}}
        </div>
      
    </div>
    <div class="overall-list" *ngIf="data.length>0 && listView">
      <div class="data-row">
        <div *ngFor="let p of data; let i=index">
          <div *ngIf="p.parent==0 && p.child==0">
          <div class="list-card row"
            [ngStyle]="{'border-left':'2px solid' + p.color, 'background-color': 'white'}">
            
              <div class="icon-col"
                *ngIf="('milestone_select' | checkActive : this.formConfig: 'milestone-landing-list')">
                <mat-checkbox class="checkbox-input" [checked]="p.selected"
                  (change)="getSelectedRow(p.id, p.month_check)">
                </mat-checkbox>
              </div>
              <div class="id-col" *ngIf="('milestone_id' | checkActive : this.formConfig: 'milestone-landing-list')">
                {{ p.id}}
              </div>
              <div class="name-col" *ngIf="('milestone_name' | checkActive : this.formConfig: 'milestone-landing-list')">
                <div class="name-text" tooltip="{{p.label}}">{{ p.label | maxellipsis: 30 }}</div>
              </div>
              <div class="type-col" *ngIf="('milestone_type' | checkActive : this.formConfig: 'milestone-landing-list')">
                <div class="type-chip" [ngStyle]="{'background':p.milestone_type_color}">
                  <div class="type-text">{{p.milestone_type_name}}</div>
                </div>
              </div>
              <div class="value-col"
              *ngIf="('milestone_value' | checkActive : this.formConfig: 'milestone-landing-list')">
              <app-currency [toDisplay]="projectValueAccess"  [currencyList]="p.currencyListJSON" [code]="code" [font_size]="font_size_currency_card" class="flex-1" type="big">
              </app-currency>
            </div>
  
            
           
                          <!-- <div class="status-col"> -->
                            <div style="width: 146px;">
                            <mat-chip *ngIf="('milestone_status' | checkActive : this.formConfig: 'milestone-landing-list')"
                            class="status-chip" [ngStyle]="{'border':p.border,background:p.background}"
                            (click)="togglePopover(p.financial_status_id,i)">
                            <div class="status-text" [ngStyle]="{'color':p.color}">{{p.financial_status}}</div>
                          </mat-chip>
                        </div>
              
                          <div *ngIf="p.isPopoverOpen && divVisible" id="myDiv"
                            [ngClass]="{'pop-up-container': (i !== 0 && i !== 1 && i !== 2), 'pop-up-container1': (i === 0 || i === 1 || i === 2)}">
                            <div class="column-list">
                              <label *ngFor="let column of status;let j=index" class="column-item">
                                <mat-chip class="status-chip" [ngStyle]="{'border':column['border'],background:column['background']}"
                                  (click)="statusChange(i,column['id'])">
                                  <div class="status-text" [ngStyle]="{'color':column['color']}">{{column['name']}}</div>
                                </mat-chip>
                              </label>
                            </div>
                          </div>
                          <!-- </div> -->
              <div class="date-col"
                *ngIf="('milestone_startDate' | checkActive : this.formConfig: 'milestone-landing-list')">
                {{ p.show_start_date }}
              </div>
              <div class="date-col"
                *ngIf="('milestone_endDate' | checkActive : this.formConfig: 'milestone-landing-list')">
                {{ p.show_end_date }}
              </div>
              <div class="percentage-col"
                *ngIf="('milestone_percentage' | checkActive : this.formConfig: 'milestone-landing-list')">
                {{ p.completion_percentage }}
              </div>
              <div class="view-hierarchy-col"
                *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')">
                <div *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')"
                  class="billing-advice-button" [ngStyle]="{'color':p.color}"
                  tooltip="{{('milestone_view_heirarchy' | checkTooltip : this.formConfig: 'milestone-landing-list': 'View Hierarchy')}}"
                  (click)="openBillingAdivise(p.id,p.start_date,p.end_date,p.financial_status_id,p.currencyListJSON,p.financial_status,p.background,p.border,p.color,p.label,i)">{{('milestone_view_heirarchy' | checkLabel : this.formConfig: 'milestone-landing-list': 'Billing Advice')}}
                </div>
              </div>
              <div class="view-hierarchy-col"
                *ngIf="('milestone_task' | checkActive : this.formConfig: 'milestone-landing-list')"
                (click)="openAttachTask(p.id,p.start_date,p.end_date,p.financial_status_id,p.currencyList)">
                <mat-icon class="actions-icon" [ngStyle]="{'color':p.color}"
                  tooltip="{{('milestone_task' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach tasks')}}">playlist_add</mat-icon>
              </div>
              <div class="view-hierarchy-col" (click)="addAttachment(p.id,p.gantt_id)"
                *ngIf="('milestone_attachments' | checkActive : this.formConfig: 'milestone-landing-list')">
                <mat-icon class="actions-icon" [ngStyle]="{'color':p.color}"
                  tooltip="{{('milestone_attachments' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach files')}}">attachment</mat-icon>
              </div>
  
            
          </div>
          </div>
          <div *ngIf="p.parent==1">
            <div class="list-card row"
              [ngStyle]="{'border-left':'2px solid' + p.color, 'background-color':'whitesmoke'}">
             
                <div class="icon-col"
                  *ngIf="('milestone_select' | checkActive : this.formConfig: 'milestone-landing-list')">
                  <mat-checkbox class="checkbox-input" [checked]="p.selected"
                    (change)="getSelectedRow(p.id, p.month_check)">
                  </mat-checkbox>
                  <mat-icon *ngIf ="(!p.expand)" class="expand-icon-1" (click)="showChild(i)">
                    chevron_right
                </mat-icon>
                <mat-icon *ngIf ="p.expand" class="expand-icon-1" (click)="showChild(i)">
                    expand_more
                </mat-icon>
                </div>
                  
                <div class="id-col" *ngIf="('milestone_id' | checkActive : this.formConfig: 'milestone-landing-list')">
                  {{ p.id}}
                </div>
                <div class="name-col" *ngIf="('milestone_name' | checkActive : this.formConfig: 'milestone-landing-list')">
                  <div class="name-text" tooltip="{{p.label}}">{{ p.label | maxellipsis: 30 }}</div>
                </div>
                <div class="type-col" *ngIf="('milestone_type' | checkActive : this.formConfig: 'milestone-landing-list')">
                  <div class="type-chip" [ngStyle]="{'background':p.milestone_type_color}">
                    <div class="type-text">{{p.milestone_type_name}}</div>
                  </div>
                </div>
                <div class="value-col"
                *ngIf="('milestone_value' | checkActive : this.formConfig: 'milestone-landing-list')">
                <app-currency [toDisplay]="projectValueAccess"  [currencyList]="p.currencyListJSON" [code]="code" [font_size]="font_size_currency_card" class="flex-1" type="big">
                </app-currency>
              </div>
    
              
              
                            <!-- <div class="status-col"> -->
                              <div style="width: 146px;">
                              <mat-chip *ngIf="('milestone_status' | checkActive : this.formConfig: 'milestone-landing-list')"
                              class="status-chip" [ngStyle]="{'border':p.border,background:p.background}"
                              (click)="togglePopover(p.financial_status_id,i)">
                              <div class="status-text" [ngStyle]="{'color':p.color}">{{p.financial_status}}</div>
                            </mat-chip>
                          </div>
                
                            <div *ngIf="p.isPopoverOpen && divVisible" id="myDiv"
                              [ngClass]="{'pop-up-container': (i !== 0 && i !== 1 && i !== 2), 'pop-up-container1': (i === 0 || i === 1 || i === 2)}">
                              <div class="column-list">
                                <label *ngFor="let column of status;let j=index" class="column-item">
                                  <mat-chip class="status-chip" [ngStyle]="{'border':column['border'],background:column['background']}"
                                    (click)="statusChange(i,column['id'])">
                                    <div class="status-text" [ngStyle]="{'color':column['color']}">{{column['name']}}</div>
                                  </mat-chip>
                                </label>
                              </div>
                            </div>
                            <!-- </div> -->
                <div class="date-col"
                  *ngIf="('milestone_startDate' | checkActive : this.formConfig: 'milestone-landing-list')">
                  {{ p.start_date | dateFormat: 'DD-MMM-YYYY'}}
                </div>
                <div class="date-col"
                  *ngIf="('milestone_endDate' | checkActive : this.formConfig: 'milestone-landing-list')">
                  {{ p.end_date | dateFormat: 'DD-MMM-YYYY'}}
                </div>
                <div class="percentage-col"
                  *ngIf="('milestone_percentage' | checkActive : this.formConfig: 'milestone-landing-list')">
                  {{ p.completion_percentage }}
                </div>
                <div class="view-hierarchy-col"
                  *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')">
                  <div *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')"
                  class="billing-advice-button" [ngStyle]="{'color':p.color}"
                  tooltip="{{('milestone_view_heirarchy' | checkTooltip : this.formConfig: 'milestone-landing-list': 'View Hierarchy')}}"
                  (click)="openBillingAdivise(p.id,p.start_date,p.end_date,p.financial_status_id,p.currencyListJSON,p.financial_status,p.background,p.border,p.color,p.label,i)">{{('milestone_view_heirarchy' | checkLabel : this.formConfig: 'milestone-landing-list': 'Billing Advice')}}
                </div>
                </div>
                <div class="view-hierarchy-col"
                  *ngIf="('milestone_task' | checkActive : this.formConfig: 'milestone-landing-list')"
                  (click)="openAttachTask(p.id,p.start_date,p.end_date,p.financial_status_id,p.currencyList)">
                  <mat-icon class="actions-icon" [ngStyle]="{'color':p.color}"
                    tooltip="{{('milestone_task' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach tasks')}}">playlist_add</mat-icon>
                </div>
                <div class="view-hierarchy-col" (click)="addAttachment(p.id,p.gantt_id)"
                  *ngIf="('milestone_attachments' | checkActive : this.formConfig: 'milestone-landing-list')">
                  <mat-icon class="actions-icon" [ngStyle]="{'color':p.color}"
                    tooltip="{{('milestone_attachments' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach files')}}">attachment</mat-icon>
                </div>
    
              
            </div>
            <div *ngIf="p.expand">
            <div *ngFor="let p of p.child_data;let i=index">
              <!-- Month header for child data -->
              <div *ngIf="p.month_check" class="col-12 month-header" style="background: #F6F6F6; padding: 8px; margin: 5px 0; border-radius: 4px;">
                <div class="ml-2 font-family" style="color: #272A47; font-size: 14px; font-weight: 500; text-transform: capitalize;">{{p.month}}</div>
              </div>
              <div class="list-card row"
                [ngStyle]="{'border-left':'2px solid' + p.color, 'background-color': (i % 2 !== 0) ? 'whitesmoke' : 'white'}">
                  <div class="icon-col"
                    *ngIf="('milestone_select' | checkActive : this.formConfig: 'milestone-landing-list')">
                    <mat-checkbox class="checkbox-input" [checked]="p.selected"
                      (change)="getSelectedRow(p.id, p.month_check)">
                    </mat-checkbox>
                  </div>
                <div class="id-col" *ngIf="('milestone_id' | checkActive : this.formConfig: 'milestone-landing-list')">
                  {{ p.id}}
                </div>
                <div class="name-col" *ngIf="('milestone_name' | checkActive : this.formConfig: 'milestone-landing-list')">
                  <div class="name-text" tooltip="{{p.label}}">{{ p.label | maxellipsis: 30 }}</div>
                </div>
                <div class="type-col" *ngIf="('milestone_type' | checkActive : this.formConfig: 'milestone-landing-list')">
                  <div class="type-chip" [ngStyle]="{'background':p.milestone_type_color}">
                    <div class="type-text">{{p.milestone_type_name}}</div>
                  </div>
                </div>
                <div class="value-col"
                *ngIf="('milestone_value' | checkActive : this.formConfig: 'milestone-landing-list')">
                <app-currency [toDisplay]="projectValueAccess"  [currencyList]="p.currencyListJSON" [code]="code" [font_size]="font_size_currency_card" class="flex-1" type="big">
                </app-currency>
              </div>
    
              
              
                            <!-- <div class="status-col"> -->
                              <div style="width: 146px;">
                              <mat-chip *ngIf="('milestone_status' | checkActive : this.formConfig: 'milestone-landing-list')"
                              class="status-chip" [ngStyle]="{'border':p.border,background:p.background}"
                              (click)="togglePopover(p.financial_status_id,i)">
                              <div class="status-text" [ngStyle]="{'color':p.color}">{{p.financial_status}}</div>
                            </mat-chip>
                          </div>
                
                            <div *ngIf="p.isPopoverOpen && divVisible" id="myDiv"
                              [ngClass]="{'pop-up-container': (i !== 0 && i !== 1 && i !== 2), 'pop-up-container1': (i === 0 || i === 1 || i === 2)}">
                              <div class="column-list">
                                <label *ngFor="let column of status;let j=index" class="column-item">
                                  <mat-chip class="status-chip" [ngStyle]="{'border':column['border'],background:column['background']}"
                                    (click)="statusChange(i,column['id'])">
                                    <div class="status-text" [ngStyle]="{'color':column['color']}">{{column['name']}}</div>
                                  </mat-chip>
                                </label>
                              </div>
                            </div>
                            <!-- </div> -->
                <div class="date-col"
                  *ngIf="('milestone_startDate' | checkActive : this.formConfig: 'milestone-landing-list')">
                  {{ p.start_date | dateFormat: 'DD-MMM-YYYY'}}
                </div>
                <div class="date-col"
                  *ngIf="('milestone_endDate' | checkActive : this.formConfig: 'milestone-landing-list')">
                  {{ p.end_date | dateFormat: 'DD-MMM-YYYY'}}
                </div>
                <div class="percentage-col"
                  *ngIf="('milestone_percentage' | checkActive : this.formConfig: 'milestone-landing-list')">
                  {{ p.completion_percentage }}
                </div>
                <div class="view-hierarchy-col"
                  *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')">
                  <div *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')"
                    class="billing-advice-button" [ngStyle]="{'color':p.color}"
                    tooltip="{{('milestone_view_heirarchy' | checkTooltip : this.formConfig: 'milestone-landing-list': 'View Hierarchy')}}"
                    (click)="openBillingAdivise(p.id,p.start_date,p.end_date,p.financial_status_id,p.currencyListJSON,p.financial_status,p.background,p.border,p.color,p.label,i)">{{('milestone_view_heirarchy' | checkLabel : this.formConfig: 'milestone-landing-list': 'Billing Advice')}}
                  </div>
                </div>
                <div class="view-hierarchy-col"
                  *ngIf="('milestone_task' | checkActive : this.formConfig: 'milestone-landing-list')"
                  (click)="openAttachTask(p.id,p.start_date,p.end_date,p.financial_status_id,p.currencyList)">
                  <mat-icon class="actions-icon" [ngStyle]="{'color':p.color}"
                    tooltip="{{('milestone_task' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach tasks')}}">playlist_add</mat-icon>
                </div>
                <div class="view-hierarchy-col" (click)="addAttachment(p.id,p.gantt_id)"
                  *ngIf="('milestone_attachments' | checkActive : this.formConfig: 'milestone-landing-list')">
                  <mat-icon class="actions-icon" [ngStyle]="{'color':p.color}"
                    tooltip="{{('milestone_attachments' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach files')}}">attachment</mat-icon>
                </div>


              </div>
            </div>
            </div>
          </div>
        </div>
      </div>
  
    </div>
    <div *ngIf="data.length==0">
      <div class="content">
        <div class="img">
          <img [src]="noDataImage ? noDataImage : 'https://assets.kebs.app/No-milestone-image.png'" class="img-data" />
        </div>
        <div class="inner-content pt-1">
          <div class="tittle pt-2">No Milestone Here</div>
          <div class="description pt-2" *ngIf="milestoneWriteAccess">Start Adding Milestones to mark your progress
          </div>
        </div>
        <button mat-raised-button class="save-button" *ngIf="milestoneWriteAccess" (click)="createMilestone()">Add
          Milestone</button>
      </div>
    </div>
  
  </div>

</div>

<!--------------------------------------------------------new screen ------------------------------------------>
<div *ngIf="!oldScreen">
  <div class="pm-billing-page" *ngIf="loading">
    <div class="loader-container">
      <mat-spinner class="green-spinner loader" diameter="40"></mat-spinner>
    </div>
  </div>
  <div class="pm-billing-page" *ngIf="!loading">
    <div class="header row">
      <div class="icons">
        <mat-icon [ngStyle]="{'margin-right':lumpsum_left}"
          style="margin-top: 11px;font-size: 18px;color: #7D838B;cursor: pointer;"
          *ngIf="billingAdviceAccess && lumpsumBillingAdviceProject" class="pending_actions" tooltip="Billing Advice"
          (click)="openLumpsumAdvise()">pending_actions</mat-icon>
        <div class="all" [ngStyle]="{'margin-left':left}" [matMenuTriggerFor]="menu">{{status_filter_name}} <mat-icon
            class="all-icon">keyboard_arrow_down</mat-icon>
        </div>
        <!----------------------------Filter Change-------------------------------->
        <mat-menu #menu="matMenu" style="overflow: hidden !important;">
          <div *ngFor="let item of milestone_filter_status_list">

            <button *ngIf="item.id!=-1" (click)="getStatusFilterData(item.id,item.name)" mat-menu-item
              class="font-family"
              style="color:rgb(125, 131, 139);font-weight: 400;text-transform: capitalize;letter-spacing: 0.24px;line-height: 31px !important; height: 31px !important; font-size: 13px !important;padding-left: 25px;">
              <div
                style="width: 6px; height: 6px; border-radius: 9999px;margin-top: 12px;position: absolute;margin-left: -14px;"
                [ngStyle]="{'background':item.color}">
              </div>{{item.name}}
            </button>
            <button *ngIf="item.id==-1" (click)="getStatusFilterData(item.id,item.name)" mat-menu-item
              class="font-family"
              style="color:rgb(125, 131, 139);font-weight: 400;text-transform: capitalize;letter-spacing: 0.24px;line-height: 31px !important; height: 31px !important; font-size: 13px !important;padding-left: 25px;border-top: 1px solid rgb(125, 131, 139);">
              <div
                style="width: 6px; height: 6px; border-radius: 9999px;margin-top: 12px;position: absolute;margin-left: -14px;"
                [ngStyle]="{'background':item.color}">
              </div>{{item.name}}
            </button>
          </div>
        </mat-menu>

        <!-- Quote Filter Dropdown -->
        <div *ngIf="opportunity_status === 1" class="all" style="margin-left: 10px;" [matMenuTriggerFor]="quoteMenu2">{{quote_filter_name}} <mat-icon
            class="all-icon">keyboard_arrow_down</mat-icon>
        </div>
        <mat-menu #quoteMenu2="matMenu" style="overflow: hidden !important;">
          <div *ngFor="let item of quote_filter_list">
            <button *ngIf="item.id!=-1" (click)="getQuoteFilterData(item.id,item.name)" mat-menu-item
              class="font-family" style="color:rgb(125, 131, 139);font-weight: 400;text-transform: capitalize;letter-spacing: 0.24px;line-height: 31px !important; height: 31px !important; font-size: 13px !important;padding-left: 25px;">
              {{item.name}}
            </button>
            <button *ngIf="item.id==-1" (click)="getQuoteFilterData(item.id,item.name)" mat-menu-item
              class="font-family" style="color:rgb(125, 131, 139);font-weight: 400;text-transform: capitalize;letter-spacing: 0.24px;line-height: 31px !important; height: 31px !important; font-size: 13px !important;padding-left: 25px;border-top: 1px solid rgb(125, 131, 139);">
              {{item.name}}
            </button>
          </div>
        </mat-menu>

        <mat-button-toggle-group class="button-toggle-class">
          <mat-button-toggle value="list" tooltip="List view" [checked]="listView" (click)="toggleList('list')">
            <mat-icon class="list-view" [ngStyle]="listView ? {'color': button} : null">list</mat-icon>
          </mat-button-toggle>
          <mat-button-toggle value="card" tooltip="Card view" [checked]="!listView" (click)="toggleList('card')">
            <mat-icon class="card-view" [ngStyle]="!listView ? {'color': button} : null">dashboard</mat-icon>
          </mat-button-toggle>
        </mat-button-toggle-group>

        <div tooltip="{{sort_tooltip}}">
          <mat-icon id='filter' class="sort" (click)="openFilter()">sort</mat-icon>
          <mat-icon id='filter' (click)="openFilter()"
            style="cursor: pointer;font-size: 14px;margin-top: 17px;margin-left: -12px;color: #7D838B;"
            *ngIf="duedate">arrow_upward</mat-icon>
          <mat-icon id='filter' (click)="openFilter()"
            style="cursor: pointer;font-size: 14px;margin-top: 17px;margin-left: -12px;color: #7D838B;"
            *ngIf="!duedate">arrow_downward</mat-icon>
        </div>

        <!-- <mat-icon class="filter" matTooltip="Filter">filter_list</mat-icon> -->

        <mat-icon *ngIf="calendarIcon" class="date-range" tooltip="Calendar View">date_range</mat-icon>

        <mat-icon class="delete" tooltip="Delete" (click)="deleteMilestone()" *ngIf="check && deleteMilestoneAccess">delete_outline</mat-icon>

        <mat-icon class="edit" tooltip="Edit" *ngIf="check" (click)="openEdit()">edit</mat-icon>

        <span class="material-icons color" tooltip="Generate Milestones" (click)="generateMilestone()" *ngIf="generateMilestoneAccess">smart_toy</span>

        <mat-icon class="multiple" tooltip="Create Multiple Milestones" (click)="multipleMilestone()" *ngIf="multipleMilestoneAccess">storage</mat-icon>

        <mat-icon *ngIf="data.length>0 && listView" class="material-icons customize-icon" (click)="columnConfigToggle.toggle()" [satPopoverAnchor]="columnConfigToggle" tooltip="Column Selection">display_settings</mat-icon>

        <mat-icon class="create" tooltip="Create Milestone" (click)="create()">add_box</mat-icon>

        <mat-icon
          *ngIf="check && ((edit_data.financial_status_id==8 && milestone_status_ytb_revert) || (edit_data.financial_status_id==15 && milestone_status_accrued_revert))"
          class="revert" tooltip="Revert Status" (click)="revertStatus()">undo</mat-icon>

        <!-- <mat-icon class="list-view" matTooltip="List view" (click)="toggleList()" [ngStyle]="listView ? {'color': '#EE4961'} : null">list</mat-icon>
        
        <mat-icon class="card-view" matTooltip="Card view" (click)="toggleList()" [ngStyle]="!listView ? {'color': '#EE4961'} : null">dashboard</mat-icon> -->

        <!-- Column Selection Pop-over -->

        <sat-popover #columnConfigToggle horizontalAlign="center" verticalAlign="below" hasBackdrop class="overlay-popover" >
          <div class="card-column card" >
            <div class="column-config-popup">
              <div class="column-list">
                          <span  *ngFor="let column of columns;let i=index">
                      <div class="row" *ngIf="column.isActive">
                          <label class="toggle-switch" [ngClass]="{ 'grayed-out-toggle': column.isDisabled }" [style.background-color]="column.isDisabled ? 'grey' : (column.isVisible ? button : 'lightgrey')">
                              <input 
                              type="checkbox" 
                              [(ngModel)]="column.isVisible"
                              [disabled]="column.isDisabled"
                              (click)="saveISALandingPageUserConfig()">
                              <span class="slider"></span>
                          </label>
                          <span class="column-header">{{ column.column_name }}</span>
                      </div>
                  </span>
              </div>
          </div>
          </div>
        </sat-popover>

      </div>
    </div>
    <div class="data-header">
      <div class="project-value-header d-flex">
        <ng-container *ngFor = "let item of projectFinancialValueConfig; let itemIndex = index">
          <div class="ml-3 mr-3 divider"
          *ngIf="itemIndex > 0">
        </div>
          <div class="value-card">
            <div class="value-label">
              {{ item.column_name }}
            </div>
            <div class="value-currency">
              <app-currency [toDisplay]="projectValueAccess" [currencyList]="projectFinanicalValue[item.column_value_key]" [code]="code"
                [font_size]="font_size_currency_header" class="flex-1" type="big">
              </app-currency>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
    <div class="mr-4 milestone-data">
      <div *ngIf="data.length>0 && !listView">
        <div *ngFor="let p of data; let i=index">
          <div class="mt-2 col-12 new-card">
            <div *ngIf="p.month_check" class="col-12 month-header">
              <div class="ml-2">{{p.month}}</div>
            </div>
            <div class="col-12 d-flex milestone-values" [ngStyle]="{'box-shadow':p.box,'margin-top': '5px'}" >
              <div class="col-2 d-flex calender-data">
                <app-date-display class="row" [date]="p.end_date" [color]="p.color"></app-date-display>
                <mat-icon (dblclick)="editMilestone(p)"  class="selection-check-circle" [ngStyle]="{'color':p.color}">check_circle_outline</mat-icon>
              </div>
              <div class="col-10 ml-2 milestone-content">
                <div class="first-row-content d-flex">
                  <div class="milestone-header-details d-flex" (click)="openEditName(i,p.month_check)" id="myDiv1">
                    <div *ngIf="('id' | checkActive : this.formConfig: 'milestone-landing')" class="mr-2 milestone-id">
                      {{('id' | checkLabel : this.formConfig: 'milestone-landing': 'ID')}}:{{p.id}}
                    </div>
                    <div (dblclick)="editMilestone(p)" *ngIf="('milestone_name' | checkActive : this.formConfig: 'milestone-landing')"
                      class="milestone-name" tooltip="{{p.label}}" placement="top" showDelay="500">
                      {{ p.label | maxellipsis: 80 }}
                    </div>
                  </div>
                  <div class="milestone-header-actions d-flex mr-2">
                    <div *ngIf="p.cancelButtonEnabled" class="ml-3"
                      tooltip="{{('cancel' | checkLabel : this.formConfig:'milestone-landing': 'Cancel')}}" (click)="cancelMilestone(p)" style="cursor: pointer;">
                      <svg width="18" height="18" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <mask id="mask0_20530_80140" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                        <rect width="16" height="16" fill="#D9D9D9"/>
                        </mask>
                        <g mask="url(#mask0_20530_80140)">
                        <path d="M6.46667 12.4668L5.53333 11.5335L7.06667 10.0002L5.53333 8.46683L6.46667 7.5335L8 9.06683L9.53333 7.5335L10.4667 8.46683L8.93333 10.0002L10.4667 11.5335L9.53333 12.4668L8 10.9335L6.46667 12.4668ZM3.33333 14.6668C2.96667 14.6668 2.65278 14.5363 2.39167 14.2752C2.13056 14.0141 2 13.7002 2 13.3335V4.00016C2 3.6335 2.13056 3.31961 2.39167 3.0585C2.65278 2.79738 2.96667 2.66683 3.33333 2.66683H4V1.3335H5.33333V2.66683H10.6667V1.3335H12V2.66683H12.6667C13.0333 2.66683 13.3472 2.79738 13.6083 3.0585C13.8694 3.31961 14 3.6335 14 4.00016V13.3335C14 13.7002 13.8694 14.0141 13.6083 14.2752C13.3472 14.5363 13.0333 14.6668 12.6667 14.6668H3.33333ZM3.33333 13.3335H12.6667V6.66683H3.33333V13.3335ZM3.33333 5.3335H12.6667V4.00016H3.33333V5.3335Z" fill="#45546E"/>
                        </g>
                      </svg>
                        
                    </div>
                    <div *ngIf="p.reversalButtonEnabled" class="ml-3"
                      tooltip="{{('revert' | checkLabel : this.formConfig:'milestone-landing': 'Revert')}}" (click)="revertMilestone(p)" style="cursor: pointer;">
                      <svg width="18" height="18" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2.6407 1.95599C3.8504 0.907764 5.39802 0.331758 6.9987 0.333991C10.6807 0.333991 13.6654 3.31866 13.6654 7.00066C13.6654 8.42466 13.2187 9.74466 12.4587 10.8273L10.332 7.00066H12.332C12.3321 5.95507 12.0249 4.93253 11.4485 4.06016C10.8721 3.1878 10.052 2.5041 9.09015 2.09407C8.12832 1.68405 7.06717 1.56579 6.03867 1.754C5.01016 1.94221 4.05967 2.42859 3.30536 3.15266L2.6407 1.95599ZM11.3567 12.0453C10.147 13.0935 8.59937 13.6696 6.9987 13.6673C3.3167 13.6673 0.332031 10.6827 0.332031 7.00066C0.332031 5.57666 0.778698 4.25666 1.5387 3.17399L3.66536 7.00066H1.66536C1.66528 8.04624 1.97253 9.06879 2.54892 9.94115C3.12531 10.8135 3.94541 11.4972 4.90725 11.9072C5.86908 12.3173 6.93022 12.4355 7.95873 12.2473C8.98723 12.0591 9.93773 11.5727 10.692 10.8487L11.3567 12.0453Z" fill="#45546E"/>
                      </svg>
                    </div>
                    <div  *ngIf="superadminaccess" class="ml-2" tooltip="Change Invoice Date">
                      <mat-icon class="milestone-action-icons" style="padding-top: 0px;" (click)="openInvoice(p)">
                        calendar_today</mat-icon>
                    </div>
                    <div class="ml-2"
                      *ngIf="('milestone_add_tags' | checkActive : this.formConfig: 'milestone-landing')" [ngStyle]="{'color':p.is_attachment ? this.button : ''}">
                      <mat-icon id="displayTagDiv" (click)="addTag(i)" tooltip="{{('add_tags' | checkLabel :
                      this.formConfig: 'milestone-landing': 'Add Tags')}}" class="milestone-action-icons"
                        style="margin-top: -1px;">outlined_flag</mat-icon>
                    </div>
                    <div *ngIf="p.creditNoteButtonEnabled" class="ml-3"
                      tooltip="{{('credit_note' | checkLabel : this.formConfig:'milestone-landing': 'Credit Note')}}"  (click)="raiseCreditNote(p)" style="cursor: pointer;">
                      <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" *ngIf="!this.creditNoteSelected">
                        <path d="M13.3333 14.6673H4.33333C3.71449 14.6673 3.121 14.4215 2.68342 13.9839C2.24583 13.5463 2 12.9528 2 12.334V3.33398C2 2.80355 2.21071 2.29484 2.58579 1.91977C2.96086 1.5447 3.46957 1.33398 4 1.33398H13.3333C13.5101 1.33398 13.6797 1.40422 13.8047 1.52925C13.9298 1.65427 14 1.82384 14 2.00065V14.0007C14 14.1775 13.9298 14.347 13.8047 14.4721C13.6797 14.5971 13.5101 14.6673 13.3333 14.6673ZM12.6667 13.334V11.334H4.33333C4.06812 11.334 3.81376 11.4393 3.62623 11.6269C3.43869 11.8144 3.33333 12.0688 3.33333 12.334C3.33333 12.5992 3.43869 12.8536 3.62623 13.0411C3.81376 13.2286 4.06812 13.334 4.33333 13.334H12.6667ZM6.66667 2.66732V8.00065L9 6.66732L11.3333 8.00065V2.66732H6.66667Z" fill="#45546E"/>
                      </svg>
                    </div>
                    <div *ngIf="p.partialInvoiceButtonEnabled" class="ml-3"
                      tooltip="{{('partial_invoice' | checkLabel : this.formConfig:'milestone-landing': 'Partial Invoice')}}" (click)="raisePartialInvoice(p)" style="cursor: pointer;">
                      <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" *ngIf="!this.creditNoteSelected">
                        <g clip-path="url(#clip0_20553_17093)">
                        <path d="M13.3333 14.6663H2.66667C2.48986 14.6663 2.32029 14.5961 2.19526 14.4711C2.07024 14.3461 2 14.1765 2 13.9997V1.99967C2 1.82286 2.07024 1.65329 2.19526 1.52827C2.32029 1.40325 2.48986 1.33301 2.66667 1.33301H13.3333C13.5101 1.33301 13.6797 1.40325 13.8047 1.52827C13.9298 1.65329 14 1.82286 14 1.99967V13.9997C14 14.1765 13.9298 14.3461 13.8047 14.4711C13.6797 14.5961 13.5101 14.6663 13.3333 14.6663ZM12.6667 13.333V2.66634H3.33333V13.333H12.6667ZM5.33333 5.99967H10.6667V7.33301H5.33333V5.99967ZM5.33333 8.66634H10.6667V9.99967H5.33333V8.66634Z" fill="#45546E"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_20553_17093">
                        <rect width="16" height="16" fill="white"/>
                        </clipPath>
                        </defs>
                      </svg>
                    </div>
                    <div *ngIf="('attach_dependencies' | checkActive : this.formConfig: 'milestone-landing')" class="ml-3"
                      tooltip="{{('attach_dependencies' | checkLabel : this.formConfig:'milestone-landing': 'Add Dependencies')}}"
                      (click)="addDependency(p)" style="cursor: pointer;">
                      <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_9089_30926)">
                          <path
                            d="M12 10.4998L11.9993 12.4998H14V13.8332H11.9993L12 15.8332H10.6667L10.666 13.8332H8.66667V12.4998H10.666L10.6667 10.4998H12ZM7.33333 12.4998V13.8332H2V12.4998H7.33333ZM14 7.83317V9.1665H2V7.83317H14ZM14 3.1665V4.49984H2V3.1665H14Z"
                            fill="#45546E" />
                        </g>
                        <defs>
                          <clipPath id="clip0_9089_30926">
                            <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
                          </clipPath>
                        </defs>
                      </svg>
                    </div>
                    <div *ngIf="('milestone_notes' | checkActive : this.formConfig: 'milestone-landing')" class="ml-3"
                      tooltip="{{('comments' | checkLabel : this.formConfig:'milestone-landing': 'Comments')}}" (click)="addNotes(i)" style="cursor: pointer;">
                      <svg width="18" height="18" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" *ngIf="p.is_comment">
                        <path d="M0.333984 13.6654V1.66536C0.333984 1.2987 0.46454 0.984809 0.725651 0.723698C0.986762 0.462587 1.30065 0.332031 1.66732 0.332031H12.334C12.7007 0.332031 13.0145 0.462587 13.2757 0.723698C13.5368 0.984809 13.6673 1.2987 13.6673 1.66536V9.66536C13.6673 10.032 13.5368 10.3459 13.2757 10.607C13.0145 10.8681 12.7007 10.9987 12.334 10.9987H3.00065L0.333984 13.6654ZM2.43398 9.66536H12.334V1.66536H1.66732V10.4154L2.43398 9.66536Z" fill="#EE4961"/>
                      </svg>
                      <svg width="18" height="18" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" *ngIf="!p.is_comment">
                        <path d="M0.333984 13.6654V1.66536C0.333984 1.2987 0.46454 0.984809 0.725651 0.723698C0.986762 0.462587 1.30065 0.332031 1.66732 0.332031H12.334C12.7007 0.332031 13.0145 0.462587 13.2757 0.723698C13.5368 0.984809 13.6673 1.2987 13.6673 1.66536V9.66536C13.6673 10.032 13.5368 10.3459 13.2757 10.607C13.0145 10.8681 12.7007 10.9987 12.334 10.9987H3.00065L0.333984 13.6654ZM2.43398 9.66536H12.334V1.66536H1.66732V10.4154L2.43398 9.66536Z" fill="#45546E"/>
                      </svg>
                        
                    </div>
                   
                    <form [formGroup]="stepperFormGroup">
                      <div *ngIf="p.tagPopUpOpen && isDisplayVisible" id="displayTagDiv"
                        style="width: 166px;margin-left: 527px; height: 200px;padding-bottom: 0px !important;padding: 16px; background: white; box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); border-radius: 4px; border: 1px #E8E9EE solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex;margin-top: -356px;width: 166px;margin-left: -189px;height: 200px;padding-bottom: 0px !important;padding: 16px;background: white;box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);border-radius: 4px;border: 1px #E8E9EE solid;flex-direction: column;justify-content: flex-start;align-items: flex-start;gap: 8px;display: inline-flex;margin-top: -202px;position: fixed;">
                        <div style="justify-content: flex-start; align-items: center; gap: 2px; display: inline-flex">
                          <div style="width: 14px; height: 14px; position: relative">
                            <div style="width: 14px; height: 14px; left: 0px; top: 0px; position: absolute;"></div>
                            <div
                              style="width: 11.07px; height: 11.07px; left: 1.46px; top: 1.46px; position: absolute;">
                              <mat-icon
                                style="color: #7D838B;font-size: 17px;top: 10px;margin-top: -10px;margin-left: -5px;">
                                sell
                              </mat-icon>
                            </div>
                          </div>
                          <!-- <div style="color: #7D838B; font-size: 11px; font-family: Roboto; font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.22px; word-wrap: break-word"> Add Tag..</div> -->
                          <input style="border: none;width: 130px;outline: none;font-size: 12px;" minlength='3'
                            maxlength="30" formControlName="textInputControlDisplay" type="text" placeholder="Search" />
                        </div>
                        <div style="align-self: stretch; height: 0px; border: 1px #E8E9EE solid"></div>
                        <div
                          style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
                          <div class="font-family"
                            style="color: #B9C0CA; font-size: 11px; font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.22px; word-wrap: break-word">
                            All projects</div>
                          <div style="width: 16px; height: 16px; position: relative">
                            <div style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute;"></div>
                            <div
                              style="width: 13.33px; height: 13.33px; left: 1.33px; top: 1.33px; position: absolute;">
                              <mat-icon style="color: #B9C0CA;font-size: 15px;"
                                tooltip="Add tag by click">info_outline</mat-icon>
                            </div>
                          </div>
                        </div>
                        <div style="height: 700px;
                  overflow-y: auto;
                  overflow-x: hidden;
                  width: 149px;">
                          <div *ngFor="let p of tagsPopUp;let k=index" style="padding-bottom: 5px;">
                            <div (click)="getDisplayTag(k,i)"
                              style="padding-left: 8px;cursor: pointer; padding-right: 8px; background: #F6F6F6; border-radius: 4px; border: 1px #E8E9EE solid; justify-content: flex-start; align-items: center; gap: 4px; display: inline-flex">
                              <div style="width: 4px; height: 4px; border-radius: 9999px"
                                [ngStyle]="{'background':p.color}">
                              </div>
                              <div *ngIf="18>p.name.length" tooltip="{{p.name}}" class="font-family"
                                style="color: #7D838B; font-size: 11px; font-weight: 400; line-height: 16px; word-wrap: break-word">
                                {{p.name}}</div>
                              <div *ngIf="p.name.length>=18" tooltip="{{p.name}}" class="font-family"
                                style="color: #7D838B;font-size: 11px;font-weight: 400;line-height: 16px;/* word-wrap: break-word; */overflow: hidden;text-overflow: ellipsis;width: 105px;white-space: nowrap;">
                                {{p.name}}</div>
                            </div>
                          </div>
                        </div>

                      </div>
                    </form>
                    <div class="ml-3" *ngIf="('milestone_task' | checkActive : this.formConfig: 'milestone-landing')">
                      <mat-icon class="milestone-action-icons"
                        (click)="openAttachTask(p.id,p.start_date,p.end_date,p.financial_status_id,p.currencyList)"
                        tooltip="{{('milestone_task' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach tasks')}}">playlist_add</mat-icon>
                    </div>
                    <div class="ml-3"
                      *ngIf="('milestone_attachments' | checkActive : this.formConfig: 'milestone-landing-list')"
                      tooltip="{{('milestone_attachments' | checkLabel : this.formConfig:'milestone-landing': 'Attachments')}}" (click)="addAttachment(p.id, p.gantt_id)" style="cursor: pointer;">
                      <svg width="15" height="18" viewBox="0 0 15 20" fill="none" xmlns="http://www.w3.org/2000/svg" *ngIf="p.is_attachment">
                        <path d="M5.85553 19.5C4.23153 19.5 2.85286 18.93 1.71953 17.79C0.586198 16.6502 0.0195312 15.2683 0.0195312 13.6443V4.65375C0.0195312 3.49992 0.420198 2.51917 1.22153 1.7115C2.0227 0.903834 3.0002 0.5 4.15403 0.5C5.30786 0.5 6.28545 0.903834 7.08678 1.7115C7.88811 2.51917 8.28878 3.49992 8.28878 4.65375V11.25C8.28878 11.4625 8.21686 11.6407 8.07303 11.7845C7.9292 11.9282 7.75103 12 7.53853 12C7.32587 12 7.1477 11.9282 7.00403 11.7845C6.86053 11.6407 6.78878 11.4625 6.78878 11.25V4.64425C6.77845 3.90575 6.52195 3.28042 6.01928 2.76825C5.51661 2.25608 4.89486 2 4.15403 2C3.41636 2 2.79286 2.25767 2.28353 2.773C1.7742 3.2885 1.51953 3.91542 1.51953 4.65375V13.6443C1.5092 14.8533 1.92711 15.8814 2.77328 16.7288C3.61945 17.5763 4.64703 18 5.85603 18C6.13936 18 6.41245 17.9708 6.67528 17.9125C6.93811 17.8542 7.18936 17.7802 7.42903 17.6905C7.6252 17.616 7.81895 17.6186 8.01028 17.6982C8.20145 17.7779 8.33528 17.9157 8.41178 18.1115C8.48611 18.3077 8.48195 18.5019 8.39928 18.6943C8.31662 18.8866 8.1772 19.0199 7.98103 19.0943C7.6502 19.2276 7.30695 19.3285 6.95128 19.397C6.59545 19.4657 6.2302 19.5 5.85553 19.5ZM10.923 18.5C10.7104 18.5 10.5323 18.4281 10.3888 18.2843C10.2451 18.1406 10.1733 17.9625 10.1733 17.75V15.6923H8.11578C7.90328 15.6923 7.72511 15.6203 7.58128 15.4765C7.43761 15.3327 7.36578 15.1545 7.36578 14.942C7.36578 14.7293 7.43761 14.5513 7.58128 14.4078C7.72511 14.2641 7.90328 14.1923 8.11578 14.1923H10.1733V12.1348C10.1733 11.9223 10.2452 11.7441 10.389 11.6003C10.5329 11.4566 10.7111 11.3848 10.9238 11.3848C11.1363 11.3848 11.3144 11.4566 11.458 11.6003C11.6015 11.7441 11.6733 11.9223 11.6733 12.1348V14.1923H13.731C13.9435 14.1923 14.1216 14.2642 14.2653 14.408C14.4091 14.5518 14.481 14.7301 14.481 14.9427C14.481 15.1552 14.4091 15.3333 14.2653 15.477C14.1216 15.6205 13.9435 15.6923 13.731 15.6923H11.6733V17.75C11.6733 17.9625 11.6014 18.1406 11.4575 18.2843C11.3137 18.4281 11.1355 18.5 10.923 18.5ZM4.15378 14.9423C3.94128 14.9423 3.7632 14.8704 3.61953 14.7268C3.47603 14.5829 3.40428 14.4047 3.40428 14.1923V5.13475C3.40428 4.92225 3.47611 4.74408 3.61978 4.60025C3.76361 4.45658 3.94186 4.38475 4.15453 4.38475C4.36703 4.38475 4.54511 4.45658 4.68878 4.60025C4.83228 4.74408 4.90403 4.92225 4.90403 5.13475V14.1923C4.90403 14.4047 4.83211 14.5829 4.68828 14.7268C4.54461 14.8704 4.36645 14.9423 4.15378 14.9423ZM10.923 9.19225C10.7104 9.19225 10.5323 9.12042 10.3888 8.97675C10.2451 8.83292 10.1733 8.65475 10.1733 8.44225V5.13475C10.1733 4.92225 10.2452 4.74408 10.389 4.60025C10.5329 4.45658 10.7111 4.38475 10.9238 4.38475C11.1363 4.38475 11.3144 4.45658 11.458 4.60025C11.6015 4.74408 11.6733 4.92225 11.6733 5.13475V8.44225C11.6733 8.65475 11.6014 8.83292 11.4575 8.97675C11.3137 9.12042 11.1355 9.19225 10.923 9.19225Z" fill="#EE4961"/>
                      </svg>
                      <svg width="15" height="18" viewBox="0 0 15 20" fill="none" xmlns="http://www.w3.org/2000/svg" *ngIf="!p.is_attachment">
                        <path d="M5.85553 19.5C4.23153 19.5 2.85286 18.93 1.71953 17.79C0.586198 16.6502 0.0195312 15.2683 0.0195312 13.6443V4.65375C0.0195312 3.49992 0.420198 2.51917 1.22153 1.7115C2.0227 0.903834 3.0002 0.5 4.15403 0.5C5.30786 0.5 6.28545 0.903834 7.08678 1.7115C7.88811 2.51917 8.28878 3.49992 8.28878 4.65375V11.25C8.28878 11.4625 8.21686 11.6407 8.07303 11.7845C7.9292 11.9282 7.75103 12 7.53853 12C7.32587 12 7.1477 11.9282 7.00403 11.7845C6.86053 11.6407 6.78878 11.4625 6.78878 11.25V4.64425C6.77845 3.90575 6.52195 3.28042 6.01928 2.76825C5.51661 2.25608 4.89486 2 4.15403 2C3.41636 2 2.79286 2.25767 2.28353 2.773C1.7742 3.2885 1.51953 3.91542 1.51953 4.65375V13.6443C1.5092 14.8533 1.92711 15.8814 2.77328 16.7288C3.61945 17.5763 4.64703 18 5.85603 18C6.13936 18 6.41245 17.9708 6.67528 17.9125C6.93811 17.8542 7.18936 17.7802 7.42903 17.6905C7.6252 17.616 7.81895 17.6186 8.01028 17.6982C8.20145 17.7779 8.33528 17.9157 8.41178 18.1115C8.48611 18.3077 8.48195 18.5019 8.39928 18.6943C8.31662 18.8866 8.1772 19.0199 7.98103 19.0943C7.6502 19.2276 7.30695 19.3285 6.95128 19.397C6.59545 19.4657 6.2302 19.5 5.85553 19.5ZM10.923 18.5C10.7104 18.5 10.5323 18.4281 10.3888 18.2843C10.2451 18.1406 10.1733 17.9625 10.1733 17.75V15.6923H8.11578C7.90328 15.6923 7.72511 15.6203 7.58128 15.4765C7.43761 15.3327 7.36578 15.1545 7.36578 14.942C7.36578 14.7293 7.43761 14.5513 7.58128 14.4078C7.72511 14.2641 7.90328 14.1923 8.11578 14.1923H10.1733V12.1348C10.1733 11.9223 10.2452 11.7441 10.389 11.6003C10.5329 11.4566 10.7111 11.3848 10.9238 11.3848C11.1363 11.3848 11.3144 11.4566 11.458 11.6003C11.6015 11.7441 11.6733 11.9223 11.6733 12.1348V14.1923H13.731C13.9435 14.1923 14.1216 14.2642 14.2653 14.408C14.4091 14.5518 14.481 14.7301 14.481 14.9427C14.481 15.1552 14.4091 15.3333 14.2653 15.477C14.1216 15.6205 13.9435 15.6923 13.731 15.6923H11.6733V17.75C11.6733 17.9625 11.6014 18.1406 11.4575 18.2843C11.3137 18.4281 11.1355 18.5 10.923 18.5ZM4.15378 14.9423C3.94128 14.9423 3.7632 14.8704 3.61953 14.7268C3.47603 14.5829 3.40428 14.4047 3.40428 14.1923V5.13475C3.40428 4.92225 3.47611 4.74408 3.61978 4.60025C3.76361 4.45658 3.94186 4.38475 4.15453 4.38475C4.36703 4.38475 4.54511 4.45658 4.68878 4.60025C4.83228 4.74408 4.90403 4.92225 4.90403 5.13475V14.1923C4.90403 14.4047 4.83211 14.5829 4.68828 14.7268C4.54461 14.8704 4.36645 14.9423 4.15378 14.9423ZM10.923 9.19225C10.7104 9.19225 10.5323 9.12042 10.3888 8.97675C10.2451 8.83292 10.1733 8.65475 10.1733 8.44225V5.13475C10.1733 4.92225 10.2452 4.74408 10.389 4.60025C10.5329 4.45658 10.7111 4.38475 10.9238 4.38475C11.1363 4.38475 11.3144 4.45658 11.458 4.60025C11.6015 4.74408 11.6733 4.92225 11.6733 5.13475V8.44225C11.6733 8.65475 11.6014 8.83292 11.4575 8.97675C11.3137 9.12042 11.1355 9.19225 10.923 9.19225Z" fill="#45546E"/>
                      </svg>
                        
                    </div>
                    <!-- <div class="ml-2"
                    *ngIf="finaceApprovalPending && p.financial_status_id == 17"  (click)="openApprovalsDeatils(p.id)" tooltip="Approvals" style="cursor: pointer;">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M18 13C17.06 13 16.19 13.33 15.5 13.88C14.58 14.61 14 15.74 14 17C14 17.75 14.21 18.46 14.58 19.06C15.27 20.22 16.54 21 18 21C19.01 21 19.93 20.63 20.63 20C20.94 19.74 21.21 19.42 21.42 19.06C21.79 18.46 22 17.75 22 17C22 14.79 20.21 13 18 13ZM20.07 16.57L17.94 18.54C17.8 18.67 17.61 18.74 17.43 18.74C17.24 18.74 17.05 18.67 16.9 18.52L15.91 17.53C15.62 17.24 15.62 16.76 15.91 16.47C16.2 16.18 16.68 16.18 16.97 16.47L17.45 16.95L19.05 15.47C19.35 15.19 19.83 15.21 20.11 15.51C20.39 15.81 20.37 16.28 20.07 16.57Z" fill="#7D838B"/>
                      <path opacity="0.4" d="M21.0901 21.5C21.0901 21.78 20.8701 22 20.5901 22H3.41016C3.13016 22 2.91016 21.78 2.91016 21.5C2.91016 17.36 6.99015 14 12.0002 14C13.0302 14 14.0302 14.14 14.9502 14.41C14.3602 15.11 14.0002 16.02 14.0002 17C14.0002 17.75 14.2101 18.46 14.5801 19.06C14.7801 19.4 15.0401 19.71 15.3401 19.97C16.0401 20.61 16.9702 21 18.0002 21C19.1202 21 20.1302 20.54 20.8502 19.8C21.0102 20.34 21.0901 20.91 21.0901 21.5Z" fill="#000000"/>
                      <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="#7D838B"/>
                    </svg>
                  </div> -->
                    <div *ngIf="('milestone_type' | checkActive : this.formConfig: 'milestone-landing-list')"
                      class="ml-4 milestone-type" [ngStyle]="{'background':p.milestone_type_color}">
                      {{p.milestone_type_name}}
                    </div>
                  </div>
                </div>
                <div class="second-row-content d-flex">
                  <div class="milestone-value"
                    *ngIf="('milestone_value' | checkActive : this.formConfig: 'milestone-landing')">
                    <span>Milestone Value</span>
                    <app-currency [toDisplay]="projectValueAccess" [currencyList]="p.currencyListJSON" [code]="code"
                      [font_size]="font_size_currency_card" class="flex-1" type="big">
                    </app-currency>
                  </div>
                  <div class="ml-3 mr-3 divider" *ngIf="p?.billed_value && p?.billed_value.length > 0 && p.billing_advice_stepper"></div>
                  <div class="billed-value"  *ngIf="p?.billed_value && p?.billed_value.length > 0 && p.billing_advice_stepper">
                  <span>Billing Advice Value</span>
                  <app-currency [toDisplay]="projectValueAccess" [currencyList]="p.billed_value" [code]="code"
                      [font_size]="font_size_currency_card" class="flex-1" type="big">
                    </app-currency>
                  </div>
                  <!-- <div class="milestone-parent-id">
                    <div class="chip">
                      <div class="circle"></div>
                      <div>MID:2784378</div>
                    </div>
                  </div> -->
                  <div class="milestone-parent-id" *ngIf = "('milestone_parent_id' | checkActive: this.formConfig : 'milestone-landing') && p.parent_milestone_id"
                  tooltip="{{('milestone_parent_id' | checkLabel: this.formConfig : 'milestone-landing')}}">
                      <div class="chip">
                          <div class="circle"></div>
                          <div>ID:{{p.parent_milestone_id}}</div>
                       </div>
                  </div>
                  <div class="milestone-parent-id" *ngIf = "('milestone_po_number' | checkActive: this.formConfig : 'milestone-landing') && p.po_number"
                  tooltip="{{('milestone_po_number' | checkLabel: this.formConfig : 'milestone-landing')}}">
                      <div class="chip">
                          <div class="circle"></div>
                          <div>PO:{{p.po_number}}</div>
                       </div>
                  </div>
                  <div *ngIf="('milestone_tags' | checkActive : this.formConfig: 'milestone-landing') && p.tagActive"
                    class="tag-sections">
                    <div *ngFor="let chip of p.Tags" class="tags">
                      <div *ngIf="chip.unique_id_2==p.id" class="chip">
                        <div class="circle" [style.background-color]="chip.color"></div>
                        <div tooltip="{{chip.name}}">{{ chip.name }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="last-row-content d-flex">
                  <div class="actions-bar d-flex">
                    <div class="billing-advice-icon mr-3">
                      <mat-icon *ngIf="billingAdviceAccess && p.financial_status_id!=6" class="pending_actions"
                        [ngStyle]="{'color':p.color}" tooltip="Billing Advice"
                        (click)="openBillingAdivise(p.id,p.start_date,p.end_date,p.financial_status_id,p.currencyListJSON,p.financial_status,p.background,p.border,p.color,p.label,i)">pending_actions</mat-icon>
                    </div>
                    <div class="milestone-status">
                      <mat-chip *ngIf="('milestone_status' | checkActive : this.formConfig: 'milestone-landing')"
                        class="status-chip" [ngStyle]="{'border':p.border,background:p.background}"
                        (click)="togglePopover(p.financial_status_id,i,'','parent')">
                        <div class="status-text" [ngStyle]="{'color':p.color}">{{p.financial_status}}</div>
                      </mat-chip>
                      <div *ngIf="p.isPopoverOpen && divVisible" id="myDiv" class="pop-up">
                        <div class="column-list">
                          <label *ngFor="let column of status;let j=index" class="column-item">
                            <mat-chip class="status-chip"
                              [ngStyle]="{'border':column['border'],background:column['background']}"
                              (click)="statusChange(i,column['id'],'','parent')">
                              <div class="status-text" [ngStyle]="{'color':column['color']}">{{column['name']}}</div>
                            </mat-chip>
                          </label>
                        </div>
                      </div>
                    </div>
                    <div class="milestone-progress-bar"
                      *ngIf="('milestone_percentage' | checkActive : this.formConfig: 'milestone-landing')">
                      <mat-progress-bar class="custom-progress-bar" mode="determinate"
                        [value]="p.completion_percentage"></mat-progress-bar>
                      <div class="progress-name font-family" style="margin-top: -2.5px;
                      margin-left: 10px;
                      font-size: 12px;
                      font-weight: 400;
                      line-height: 14px;
                      letter-spacing: 0.02em;
                      text-align: center;
                      color: #5F6C81;">{{p.completion_percentage}}% complete </div>
                    </div>

                  </div>
                  <div class="date-info-section">
                    <div class="mr-3 d-flex">
                      <div class="d-flex"
                        *ngIf="('invoice_date' | checkActive : this.formConfig: 'milestone-landing-card')  && (p.milestone_type | milestoneMatrix: this.milestoneMatrix: p.financial_status_id :'accrual_display')">
                        <div class="card-date-label">{{('Invoice Date' | checkLabel :
                          this.formConfig: 'milestone-landing-card': 'Invoice Date')}}</div>
                        <div class="ml-1 card-date-value">{{p.formatted_invoice_date}}</div>
                      </div>
                      <div class="ml-3 d-flex" style="width:210px"
                        *ngIf="('accrual_reversal_date' | checkActive : this.formConfig: 'milestone-landing-card') && (p.milestone_type | milestoneMatrix: this.milestoneMatrix: p.financial_status_id :'accrual_display')">
                        <div class="card-date-label">{{('accrual_reversal_date' | checkLabel :
                          this.formConfig: 'milestone-landing-card': 'Accrua Reversal Date')}}</div>
                        <div class="ml-1 card-date-value">
                          <ng-container *ngIf="p.accrual_reversal_date !== 'NA'; else naTemplate">
                            {{ p.accrual_reversal_date}}
                          </ng-container>
                          <ng-template #naTemplate>NA</ng-template>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="new-list-view" *ngIf="data.length>0 && listView">
        <div class="col-12 list-header">
          <ng-container *ngFor="let column of columns; let columnIndex = index">
            <div *ngIf="column.isActive && column.isVisible && column.col_type === 'check-box'" [class] = "column.col"></div>
            <div *ngIf="column.isActive && column.isVisible && column.col_type !== 'check-box'" [class] = "column.col">
              {{column.column_name}}
            </div>
          </ng-container>
        </div>
        <div class="col-12 list-body">
          <ng-container *ngFor="let item of data; let itemIndex = index">
            <div class="col-12 line-item" *ngIf="item.parent==0 && item.child==0">
                <ng-container *ngFor="let column of columns; let columnIndex = index">
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'check-box'" [class] = "column.col">
                        <mat-checkbox class="checkbox-input" [checked]="item.selected" (change)="getSelectedRow(item.id, item.month_check)">
                      </mat-checkbox>
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'text'" [class] = "column.col" tooltip="{{item[column.column_value_key]}}">
                        {{item[column.column_value_key] | maxellipsis: 30 }}
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'type'" [class] = "column.col" tooltip="{{item[column.column_value_key]}}">
                        <div class="line-item-status" [ngStyle]="{'background':item.milestone_type_color}">
                          {{item[column.column_value_key] || "-"}}
                        </div>
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'status'" [class] = "column.col">
                        <mat-chip
                              class="line-item-status" [ngStyle]="{'border':item.border,background:item.background}"
                              (click)="togglePopover(item.financial_status_id,itemIndex,'','parent')">
                              <div class="item-status-text" [ngStyle]="{'color':item.color}">{{item[column.column_value_key] || "-"}}</div>
                        </mat-chip>
                        <div *ngIf="item.isPopoverOpen && divVisible" id="myDiv" class="pop-up">
                          <div class="column-list">
                            <label *ngFor="let statusItem of status;let j=index" class="column-item">
                              <mat-chip class="status-chip"
                                [ngStyle]="{'border':statusItem['border'],background:statusItem['background']}"
                                (click)="statusChange(itemIndex,statusItem['id'],'','parent')">
                                <div class="status-text" [ngStyle]="{'color':statusItem['color']}">{{statusItem['name']}}</div>
                              </mat-chip>
                            </label>
                          </div>
                        </div>
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'date'" [class] = "column.col" tooltip="{{item[column.column_value_key]}}">
                        {{item[column.column_value_key] | maxellipsis: 30 }}
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'currency'" [class] = "column.col">
                        <app-currency [toDisplay]="projectValueAccess" [currencyList]="item[column.column_value_key]" [code]="code" 
                        [font_size]="font_size_currency" [color]="'#45546E'" class="flex-1" type="big" [ngStyle]="{'display': 'flex', 'justify-content': 'flex-end'}">
                      </app-currency>
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'actions'" [class] = "column.col">
                        <div class="view-hierarchy-col"
                        *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')">
                        <div *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')"
                          class="billing-advice-button" [ngStyle]="{'color':item.color}"
                          tooltip="{{('milestone_view_heirarchy' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Billing Advice')}}"
                          (click)="openBillingAdivise(item.id,item.start_date,item.end_date,item.financial_status_id,item.currencyListJSON,item.financial_status,item.background,item.border,item.color,item.label,itemIndex)">
                          {{('milestone_view_heirarchy' | checkLabel : this.formConfig: 'milestone-landing-list': 'Billing
                          Advice')}}
                        </div>
                      </div>
                        <div class="view-hierarchy-col"
                          *ngIf="('milestone_task' | checkActive : this.formConfig: 'milestone-landing-list')"
                          (click)="openAttachTask(item.id,item.start_date,item.end_date,item.financial_status_id,item.currencyList)">
                        <mat-icon class="actions-icon" [ngStyle]="{'color':item.color}"
                          tooltip="{{('milestone_task' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach tasks')}}">playlist_add</mat-icon>
                      </div>
                      <div class="view-hierarchy-col" (click)="addAttachment(item.id,item.gantt_id)"
                        *ngIf="('milestone_attachments' | checkActive : this.formConfig: 'milestone-landing-list')">
                        <mat-icon class="actions-icon" [ngStyle]="{'color':item.color}"
                          tooltip="{{('milestone_attachments' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach files')}}">attachment</mat-icon>
                      </div>
                      </div>
                </ng-container>
            </div>
            <div class="col-12 line-item" *ngIf="item.parent==1">
                <ng-container *ngFor="let column of columns; let columnIndex = index">
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'check-box'" [class] = "column.col">
                      <mat-icon *ngIf="(!item.expand)" class="expand-input" (click)="showChild(itemIndex)">
                        chevron_right
                      </mat-icon>
                      <mat-icon *ngIf="item.expand" class="expand-input" (click)="showChild(itemIndex)">
                        expand_more
                      </mat-icon>
                      <mat-checkbox class="checkbox-input" [checked]="item.selected" (change)="getSelectedRow(item.id, item.month_check)">
                    </mat-checkbox>
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'text'" [class] = "column.col" tooltip="{{item[column.column_value_key]}}">
                      {{item[column.column_value_key] | maxellipsis: 30 }}
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'type'" [class] = "column.col" tooltip="{{item[column.column_value_key]}}">
                      <div class="line-item-status" [ngStyle]="{'background':item.milestone_type_color}">
                        {{item[column.column_value_key] || "-"}}
                      </div>
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'status'" [class] = "column.col">
                      <mat-chip
                            class="line-item-status" [ngStyle]="{'border':item.border,background:item.background}"
                            (click)="togglePopover(item.financial_status_id,itemIndex,'','parent')">
                            <div class="item-status-text" [ngStyle]="{'color':item.color}">{{item[column.column_value_key] || "-"}}</div>
                      </mat-chip>
                      <div *ngIf="item.isPopoverOpen && divVisible" id="myDiv" class="pop-up">
                        <div class="column-list">
                          <label *ngFor="let statusItem of status;let j=index" class="column-item">
                            <mat-chip class="status-chip"
                              [ngStyle]="{'border':statusItem['border'],background:statusItem['background']}"
                              (click)="statusChange(itemIndex,statusItem['id'],'','parent')">
                              <div class="status-text" [ngStyle]="{'color':statusItem['color']}">{{statusItem['name']}}</div>
                            </mat-chip>
                          </label>
                        </div>
                      </div>
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'date'" [class] = "column.col" tooltip="{{item[column.column_value_key]}}">
                      {{item[column.column_value_key] | maxellipsis: 30 }}
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'currency'" [class] = "column.col">
                      <app-currency [toDisplay]="projectValueAccess" [currencyList]="item[column.column_value_key]" [code]="code" 
                      [font_size]="font_size_currency" [color]="'#45546E'" class="flex-1" type="big" [ngStyle]="{'display': 'flex', 'justify-content': 'flex-end'}">
                    </app-currency>
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'actions'" [class] = "column.col">
                      <div class="view-hierarchy-col"
                      *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')">
                      <div *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')"
                        class="billing-advice-button" [ngStyle]="{'color':item.color}"
                        tooltip="{{('milestone_view_heirarchy' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Billing Advice')}}"
                        (click)="openBillingAdivise(item.id,item.start_date,item.end_date,item.financial_status_id,item.currencyListJSON,item.financial_status,item.background,item.border,item.color,item.label,itemIndex)">
                        {{('milestone_view_heirarchy' | checkLabel : this.formConfig: 'milestone-landing-list': 'Billing
                        Advice')}}
                      </div>
                    </div>
                      <div class="view-hierarchy-col"
                        *ngIf="('milestone_task' | checkActive : this.formConfig: 'milestone-landing-list')"
                        (click)="openAttachTask(item.id,item.start_date,item.end_date,item.financial_status_id,item.currencyList)">
                      <mat-icon class="actions-icon" [ngStyle]="{'color':item.color}"
                        tooltip="{{('milestone_task' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach tasks')}}">playlist_add</mat-icon>
                    </div>
                    <div class="view-hierarchy-col" (click)="addAttachment(item.id,item.gantt_id)"
                      *ngIf="('milestone_attachments' | checkActive : this.formConfig: 'milestone-landing-list')">
                      <mat-icon class="actions-icon" [ngStyle]="{'color':item.color}"
                        tooltip="{{('milestone_attachments' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach files')}}">attachment</mat-icon>
                    </div>
                    </div>
                </ng-container>
            </div>
            <ng-container *ngIf="item.expand">
                <ng-container *ngFor="let childItem of item.child_data; let childItemIndex = index">
                  <!-- Month header for child data -->
                  <div *ngIf="childItem.month_check" class="col-12 month-header" style="background: #F6F6F6; padding: 8px; margin: 5px 0; border-radius: 4px;">
                    <div class="ml-2 font-family" style="color: #272A47; font-size: 14px; font-weight: 500; text-transform: capitalize;">{{childItem.month}}</div>
                  </div>
                  <div  class="col-12 line-item" [ngStyle]="{'border-left':'2px solid' + childItem.color, 'background-color': 'white'}">
                      <ng-container *ngFor="let column of columns; let columnIndex = index">
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'check-box'" [class] = "column.col">
                        <mat-checkbox class="checkbox-input" [checked]="childItem.selected" (change)="getSelectedRow(childItem.id, childItem.month_check)">
                      </mat-checkbox>
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'text'" [class] = "column.col" tooltip="{{childItem[column.column_value_key]}}">
                        {{childItem[column.column_value_key] | maxellipsis: 30 }}
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'type'" [class] = "column.col" tooltip="{{childItem[column.column_value_key]}}">
                        <div class="line-item-status" [ngStyle]="{'background':childItem.milestone_type_color}">
                          {{childItem[column.column_value_key] || "-"}}
                        </div>
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'status'" [class] = "column.col">
                        <mat-chip
                              class="line-item-status" [ngStyle]="{'border':childItem.border,background:childItem.background}"
                              (click)="togglePopover(childItem.financial_status_id,itemIndex,childItemIndex,'child')">
                              <div class="item-status-text" [ngStyle]="{'color':childItem.color}">{{childItem[column.column_value_key] || "-"}}</div>
                        </mat-chip>
                        <div *ngIf="childItem.isPopoverOpen && divVisible" id="myDiv" class="pop-up">
                          <div class="column-list">
                            <label *ngFor="let statusItem of status;let j=index" class="column-item">
                              <mat-chip class="status-chip"
                                [ngStyle]="{'border':statusItem['border'],background:statusItem['background']}"
                                (click)="statusChange(itemIndex,statusItem['id'],childItemIndex,'child')">
                                <div class="status-text" [ngStyle]="{'color':statusItem['color']}">{{statusItem['name']}}</div>
                              </mat-chip>
                            </label>
                          </div>
                        </div>
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'date'" [class] = "column.col" tooltip="{{childItem[column.column_value_key]}}">
                        {{childItem[column.column_value_key] | maxellipsis: 30 }}
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'currency'" [class] = "column.col">
                        <app-currency [toDisplay]="projectValueAccess" [currencyList]="childItem[column.column_value_key]" [code]="code" 
                        [font_size]="font_size_currency" [color]="'#45546E'" class="flex-1" type="big" [ngStyle]="{'display': 'flex', 'justify-content': 'flex-end'}">
                      </app-currency>
                      </div>
                      <div *ngIf="column.isActive && column.isVisible && column.col_type === 'actions'" [class] = "column.col">
                        <div class="view-hierarchy-col"
                        *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')">
                        <div *ngIf="('milestone_view_heirarchy' | checkActive : this.formConfig: 'milestone-landing-list')"
                          class="billing-advice-button" [ngStyle]="{'color':childItem.color}"
                          tooltip="{{('milestone_view_heirarchy' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Billing Advice')}}"
                          (click)="openBillingAdivise(childItem.id,childItem.start_date,childItem.end_date,childItem.financial_status_id,childItem.currencyListJSON,childItem.financial_status,childItem.background,childItem.border,childItem.color,childItem.label,childItemIndex)">
                          {{('milestone_view_heirarchy' | checkLabel : this.formConfig: 'milestone-landing-list': 'Billing
                          Advice')}}
                        </div>
                      </div>
                        <div class="view-hierarchy-col"
                          *ngIf="('milestone_task' | checkActive : this.formConfig: 'milestone-landing-list')"
                          (click)="openAttachTask(childItem.id,childItem.start_date,childItem.end_date,childItem.financial_status_id,childItem.currencyList)">
                        <mat-icon class="actions-icon" [ngStyle]="{'color':childItem.color}"
                          tooltip="{{('milestone_task' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach tasks')}}">playlist_add</mat-icon>
                      </div>
                      <div class="view-hierarchy-col" (click)="addAttachment(childItem.id,childItem.gantt_id)"
                        *ngIf="('milestone_attachments' | checkActive : this.formConfig: 'milestone-landing-list')">
                        <mat-icon class="actions-icon" [ngStyle]="{'color':childItem.color}"
                          tooltip="{{('milestone_attachments' | checkTooltip : this.formConfig: 'milestone-landing-list': 'Attach files')}}">attachment</mat-icon>
                      </div>
                      </div>
                    </ng-container>
                  </div>
                </ng-container>
                </ng-container>
          </ng-container>
        </div>
      </div>
      <div *ngIf="data.length==0">
        <div class="content">
          <div class="img">
            <img [src]="noDataImage ? noDataImage : 'https://assets.kebs.app/No-milestone-image.png'"
              class="img-data" />
          </div>
          <div class="inner-content pt-1">
            <div class="tittle pt-2">No Milestone Here</div>
            <div class="description pt-2" *ngIf="milestoneWriteAccess">Start Adding Milestones to mark your progress
            </div>
          </div>
          <button mat-raised-button class="save-button" *ngIf="milestoneWriteAccess" (click)="createMilestone()">Add
            Milestone</button>
        </div>
      </div>

    </div>
  </div>
</div>
