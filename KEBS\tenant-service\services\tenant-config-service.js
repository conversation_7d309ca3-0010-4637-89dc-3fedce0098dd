const _ = require("lodash");
const logger = require("../logger").logger;
const mongo = require("../mongo_conn_native").Connection;
const { ObjectId } = require("mongodb");
const axios = require("axios");
// const bcrypt = require("bcrypt");
const DB_NAME = "tenants_management";
const { v4: uuidv4 } = require("uuid");

var AWS = require("aws-sdk");

module.exports.getTenantInfo = async (req, res, next) => {
  try {
    const domain ="primadev1.kebs.app";
    const tenant_details = await mongo.client
      .db(DB_NAME)
      .collection("m_tenants")

      
      .findOne({
        tenant_domain: domain,
        is_active: true,
      });

    if (tenant_details)
      res.status(200).json({
        err: false,
        msg: "Tenant Details Retrieved !",
        data: tenant_details,
      });
    else
      res.status(501).json({
        err: true,
        msg: "Tenant not found",
      });
  } catch (err) {
    console.log(err);
    logger.info(err);
  }
};

module.exports.createTenant = async (tenant_info) => {
  try {
    const tenant_name = tenant_info.tenant_name;
    const admin_user_email = tenant_info.admin_user_email;
    const tenant_domain = tenant_info.tenant_domain;

    const tenant_details = await mongo.client
      .db(DB_NAME)
      .collection("m_tenants")
      .findOne({
        tenant_domain: tenant_domain,
      });

    if (tenant_details) return Promise.reject("Tenant Already Exists");
    else {
      const tenant_id = uuidv4();

      const tenant_doc = {
        tenant_id: tenant_id,
        tenant_name: tenant_name,
        admin_user_email: admin_user_email,
        tenant_domain: tenant_domain,
        db_prefix: null,
        subscription_id: null,
        created_at: new Date(),
        is_active: true,
      };

      await mongo.client
        .db(DB_NAME)
        .collection("m_tenants")
        .insertOne(tenant_doc)
        .then((result) => { })
        .catch((err) => {
          logger.info(err);
          console.log(err);
          return Promise.reject("Error inserting tenant document");
        });

      return Promise.resolve(tenant_id);
    }
  } catch (err) {
    console.log(err);
    logger.info(err);
    return Promise.reject(err);
  }
};

module.exports.createTenantDatabase = async (tenant_id) => {
  try {
    if (tenant_id == null) return Promise.reject("Tenant id is required");

    const tenant = await mongo.client
      .db(DB_NAME)
      .collection("m_tenants")
      .findOne({ tenant_id: tenant_id });

    console.log(tenant);

    if (tenant == null) return Promise.reject("Tenant not found");

    const db_name =
      tenant.db_name || `lms_${tenant.tenant_domain.split(".")[0]}`;

    if (tenant.db_name == null) {
      const update_tenant_db = await mongo.client
        .db(DB_NAME)
        .collection("m_tenants")
        .findOneAndUpdate(
          { tenant_id: tenant_id },
          { $set: { db_name: db_name } }
        );
    }

    // console.log(db_name);

    // Get the list of all databases
    const databases = await mongo.db
      .admin()
      .listDatabases()
      .then((result) => {
        return _.map(result["databases"], "name");
      });

    console.log(databases);

    //Check whether the db name already exists
    if (_.includes(databases, db_name))
      return Promise.reject(
        "There is already a database created under the tenant name"
      );

    let collections = await mongo.client
      .db("lms")
      .listCollections()
      .toArray()
      .then((result) => {
        return _.map(
          _.filter(result, (collection) => {
            return (
              collection.type == "collection" &&
              collection["name"].split("_")[0] == "m"
            );
          }),
          "name"
        );
      });

    collections = _.remove(collections, (collection) => {
      return collection != "m_users";
    });

    //Call the function that initializes queuing the process of cloning master collections
    let result = await this.cloneMasterCollections("lms", db_name, collections);

    return Promise.resolve(
      "Tenant database is initialized successfully and the master data are being written into the database"
    );
  } catch (err) {
    console.log(err);
    // logger.info(err);
    return Promise.reject(err);
  }
};

module.exports.cloneCollection = async (req, res, next) => {
  try {
    const from_db = req.body.fromDb;
    const to_db = req.body.toDb;
    const collection_name = req.body.collectionName;

    console.log(collection_name);

    await mongo.client
      .db(from_db)
      .collection(collection_name)
      .find()
      .forEach(async (docs) => {
        await mongo.client
          .db(to_db)
          .collection(collection_name)
          .insertOne(docs)
          .catch((err) => {
            return res.status(500).send({
              err: true,
              msg: err.msg,
              stack: err.stack,
            });
          });
      });

    // console.log({ from_db: from_db, to_db: to_db, collection_name: collection_name });

    return res.status(200).send({
      err: false,
      msg: "Collection cloned successfully",
    });
  } catch (err) {
    console.log(err);
    logger.info(err);
    res.status(500).send({
      err: true,
      msg: err.message,
      stack: err.stack,
    });
  }
};

module.exports.cloneMasterCollections = async (
  master_db_name,
  tenant_db_name,
  collections_arr
) => {
  try {
    const config = {
      url: `http://localhost:3900/queue/cloneMasterCollections`,
      method: "post",
      data: {
        collections: collections_arr,
        master_db_name: master_db_name,
        tenant_db_name: tenant_db_name,
        message_routing_key: "tenant_db_clone_key",
      },
    };
    let response_data = await axios(config);
    return Promise.resolve(response_data);
  } catch (err) {
    console.error(err);
    logger.info(err);
    return Promise.reject(err);
  }
};

module.exports.createTenantDomain = async (req, res, next) => {
  try {
    let domain_name = req.body.domainName;
    const server_ip = req.body.server_ip ? req.body.server_ip : process.env.server_ip;
    const aws_access_id = req.body.aws_access_id ? req.body.aws_access_id : process.env.aws_access_id;
    const secret_key = req.body.secret_key ? req.body.secret_key : process.env.secret_key;
    const hosted_zone_id = req.body.hosted_zone_id ? req.body.hosted_zone_id : process.env.hosted_zone_id;

    var route53 = new AWS.Route53({
      accessKeyId: aws_access_id,
      secretAccessKey: secret_key,
    });

    var params = {
      ChangeBatch: {
        Changes: [
          {
            Action: "CREATE",
            ResourceRecordSet: {
              Name: domain_name,
              ResourceRecords: [
                {
                  Value: server_ip,
                },
              ],
              TTL: 60,
              Type: "A",
            },
          },
        ],
        Comment: "testing",
      },
      HostedZoneId: hosted_zone_id,
    };
    route53.changeResourceRecordSets(params, async (err, data) => {
      if (err) console.log(err, err.stack);
      // an error occurred
      else {
        console.log(data); // successful response
        res.status(200).json({ err: false, msg: data });
      }
    });
  } catch (err) {
    console.error(err);
    logger.info(err);
    res.json({ err: true, msg: "Failed to create Domain" });
  }
};

module.exports.performTenantCreationOperation = async (req, res, next) => {
  try {
    const tenant_name = req.body.tenant_name;
    const admin_user_email = req.body.admin_user_email;
    const tenant_domain = req.body.tenant_domain;

    if (!tenant_name)
      res.status(501).json({
        err: true,
        msg: "Tenant name not found !",
      });

    if (!admin_user_email)
      res.status(501).json({
        err: true,
        msg: "Tenant admin user email not found !",
      });

    if (!tenant_domain)
      res.status(501).json({
        err: true,
        msg: "Tenant domain name not found !",
      });

    let tenant_info = {
      tenant_name: tenant_name,
      admin_user_email: admin_user_email,
      tenant_domain: tenant_domain,
    };
    let tenant_id = await this.createTenant(tenant_info);
    console.log(tenant_id + "$$$$");
    const config_domain = {
      url: `http://localhost:3900/queue/createTenantDomain`,
      method: "post",
      data: {
        domainName: tenant_domain,
        message_routing_key: "tenant_domain_creation_key",
      },
    };
    let domain_creation_response = await axios(config_domain);
    let tenant_db_creation_res = await this.createTenantDatabase(tenant_id);

    res.status(200).json({
      err: false,
      msg: "tenant creation job added successfully!",
    });
  } catch (err) {
    console.error(err);
    logger.info(err);
    res.json({ err: true, msg: "Failed to create Tenant" });
  }
};

module.exports.getActiveTenants = async (req, res, next) => {
  try {
    let active_tenants = await mongo.client
      .db(DB_NAME)
      .collection("m_tenants")
      .find({ is_active: true, is_local: false, environment: process.env.NODE_ENV })
      .toArray();

    res.status(200).json({
      err: false,
      msg: "active tenants retrieved successfully",
      data: active_tenants,
    });
  } catch (err) {
    console.error(err);
    logger.info(err);
    res.json({ err: true, msg: "Failed to fetch active tenants" });
  }
};
