import { Pipe, PipeTransform } from '@angular/core';
import * as moment from 'moment';

@Pipe({
  name: 'isTimesheetSubmissionLocked',
})
export class IsTimesheetSubmissionLockedPipe implements PipeTransform {
  transform(
    basicTsConfig: any,
    currentMonthEndDate: any,
    currentWeekEndDate: any,
    overallTimesheetStatusBeforeEdit: number,
    tsOverAllStatus: number,
    isPrevHistoryRejected: boolean,
    isTimesheetEdit: boolean,
    openTsForLeaveWeekIntegrationEdit: boolean
  ): boolean {
    if (basicTsConfig && currentMonthEndDate && currentWeekEndDate) {
      let currentDay = moment();

      let block_ts_when_rejected_for_prev_months = basicTsConfig?.block_ts_when_rejected_for_prev_months == '1' ? 1 : 0;

      // Week Timesheet Submission Falls On
      if (basicTsConfig.week_timesheet_subm_falls_on === '0') {
        currentWeekEndDate = moment(currentWeekEndDate).subtract(1, 'day');
      }

      // Week Timesheet Submission For Last Week of the Month
      if (
        moment(currentMonthEndDate).isSame(currentWeekEndDate) &&
        basicTsConfig.week_timesheet_subm_falls_on === '1'
      ) {
        currentWeekEndDate = moment(currentWeekEndDate)
          .add(1, 'week')
          .day(parseInt(basicTsConfig.week_timesheet_submission_end_day));
      }

      // Week Submission and Resubmission End Date and Time
      let weekSubmissionEndDate = moment(currentWeekEndDate).day(
        parseInt(basicTsConfig.week_timesheet_submission_end_day)
      );
      weekSubmissionEndDate.hour(
        parseInt(basicTsConfig.week_timesheet_submission_end_hours)
      );
      weekSubmissionEndDate.minute(
        parseInt(basicTsConfig.week_timesheet_submission_end_minutes)
      );
      let weekReSubmissionEndDate = moment(currentWeekEndDate).day(
        parseInt(basicTsConfig.week_timesheet_resubmission_end_day)
      );
      weekReSubmissionEndDate.hour(
        parseInt(basicTsConfig.week_timesheet_resubmission_end_hours)
      );
      weekReSubmissionEndDate.minute(
        parseInt(basicTsConfig.week_timesheet_resubmission_end_minutes)
      );

      // Month Timesheet Submission Falls On
      if (basicTsConfig.month_timesheet_subm_falls_on === '1') {
        currentMonthEndDate = moment(currentMonthEndDate).add(1, 'month');
      }

      // Month Submission and Resubmission End Date and Time
      let monthSubmissionEndDate;
      if (basicTsConfig.monthly_timesheet_submission_end_date !== 'END') {
        monthSubmissionEndDate = moment(currentMonthEndDate).date(
          parseInt(basicTsConfig.monthly_timesheet_submission_end_date)
        );
      } else {
        monthSubmissionEndDate = moment(currentMonthEndDate).endOf('month');
      }
      monthSubmissionEndDate.hour(
        parseInt(basicTsConfig.monthly_timesheet_submission_end_hours)
      );
      monthSubmissionEndDate.minute(
        parseInt(basicTsConfig.monthly_timesheet_submission_end_minutes)
      );

      let monthReSubmissionEndDate;
      if (basicTsConfig.monthly_timesheet_resubmission_end_date !== 'END') {
        monthReSubmissionEndDate = moment(currentMonthEndDate).date(
          parseInt(basicTsConfig.monthly_timesheet_resubmission_end_date)
        );
      } else {
        monthReSubmissionEndDate = moment(currentMonthEndDate).endOf('month');
      }
      monthReSubmissionEndDate.hour(
        parseInt(basicTsConfig.monthly_timesheet_resubmission_end_hours)
      );
      monthReSubmissionEndDate.minute(
        parseInt(basicTsConfig.monthly_timesheet_resubmission_end_minutes)
      );

      // Timesheet Type - Weekly + Monthly
      if (basicTsConfig.timesheet_type === 'weekly_monthly') {
        if(isTimesheetEdit){
          return false;
        }
        if(tsOverAllStatus == 1 && openTsForLeaveWeekIntegrationEdit){
          return false;
        }
        if (tsOverAllStatus == 3 && !block_ts_when_rejected_for_prev_months) {
          return false;
        } else if (overallTimesheetStatusBeforeEdit == 3 && !block_ts_when_rejected_for_prev_months) {
          return false;
        } else if (
          (tsOverAllStatus == 1 || tsOverAllStatus == 6) &&
          (isPrevHistoryRejected && !block_ts_when_rejected_for_prev_months)
        ) {
          return false;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isAfter(
            monthReSubmissionEndDate
          )
        ) {
          return true;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isBetween(
            monthSubmissionEndDate,
            monthReSubmissionEndDate,
            undefined,
            '[]'
          ) &&
          tsOverAllStatus !== 6 &&
          overallTimesheetStatusBeforeEdit !== 6 &&
          overallTimesheetStatusBeforeEdit !== 1
        ) {
          return false;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isBetween(
            monthSubmissionEndDate,
            monthReSubmissionEndDate,
            undefined,
            '[]'
          )
        ) {
          return true;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isAfter(
            weekReSubmissionEndDate
          )
        ) {
          return true;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isBetween(
            weekSubmissionEndDate,
            weekReSubmissionEndDate,
            undefined,
            '[]'
          ) &&
          tsOverAllStatus !== 6 &&
          overallTimesheetStatusBeforeEdit !== 6 &&
          overallTimesheetStatusBeforeEdit !== 1
        ) {
          return false;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isBetween(
            weekSubmissionEndDate,
            weekReSubmissionEndDate,
            undefined,
            '[]'
          )
        ) {
          return true;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isBefore(weekSubmissionEndDate)
        ) {
          return false;
        } else {
          return true;
        }
      }

      // Timesheet Type - Monthly
      if (basicTsConfig.timesheet_type === 'monthly') {
        if(isTimesheetEdit){
          return false;
        }
        if (tsOverAllStatus == 3 && !block_ts_when_rejected_for_prev_months) {
          return false;
        } else if (overallTimesheetStatusBeforeEdit == 3 && !block_ts_when_rejected_for_prev_months) {
          return false;
        } else if (
          (tsOverAllStatus == 1 || tsOverAllStatus == 6) &&
          (isPrevHistoryRejected && !block_ts_when_rejected_for_prev_months)
        ) {
          return false;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isAfter(
            monthReSubmissionEndDate
          )
        ) {
          return true;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isBetween(
            monthSubmissionEndDate,
            monthReSubmissionEndDate,
            undefined,
            '[]'
          ) &&
          tsOverAllStatus !== 6 &&
          overallTimesheetStatusBeforeEdit !== 6 &&
          overallTimesheetStatusBeforeEdit !== 1
        ) {
          return false;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isBefore(
            monthSubmissionEndDate
          )
        ) {
          return false;
        } else {
          return true;
        }
      }

      // Timesheet Type - Weekly
      if (basicTsConfig.timesheet_type === 'weekly') {
        if(isTimesheetEdit){
          return false;
        }
        if (tsOverAllStatus == 3 && !block_ts_when_rejected_for_prev_months) {
          return false;
        } else if (overallTimesheetStatusBeforeEdit == 3 && !block_ts_when_rejected_for_prev_months) {
          return false;
        } else if (
          (tsOverAllStatus == 1 || tsOverAllStatus == 6) &&
          (isPrevHistoryRejected && !block_ts_when_rejected_for_prev_months)
        ) {
          return false;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isAfter(
            weekReSubmissionEndDate
          )
        ) {
          return true;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isBetween(
            weekSubmissionEndDate,
            weekReSubmissionEndDate,
            undefined,
            '[]'
          ) &&
          tsOverAllStatus !== 6 &&
          overallTimesheetStatusBeforeEdit !== 6 &&
          overallTimesheetStatusBeforeEdit !== 1
        ) {
          return false;
        } else if (
          moment(currentDay, 'DD-MM-YYYY HH:mm').isBefore(weekSubmissionEndDate)
        ) {
          return false;
        } else {
          return true;
        }
      }
    }
  }
}
