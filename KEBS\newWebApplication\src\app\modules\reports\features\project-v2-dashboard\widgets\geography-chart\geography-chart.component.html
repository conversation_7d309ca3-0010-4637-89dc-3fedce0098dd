<div class="widget-container">
    <div class="header">
      <div class="d-flex align-items-center" class="title-header">
        <div class="chart-title">{{ widgetConfig?.label }}</div>
      </div>
       <div class="summary-icon" *ngIf="widgetConfig?.widget_config?.show_summary" (click)="openSummaryForm()">
                <svg width="17" height="15" viewBox="0 0 14 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <!-- Outer box -->
                    <rect x="0.5" y="0.5" width="13" height="10" rx="1" stroke="#B9C0CA" stroke-width="1" />
            
                    <!-- Horizontal line (centered) -->
                    <line x1="1" y1="5.5" x2="13" y2="5.5" stroke="#B9C0CA" stroke-width="1" />
            
                    <!-- Two vertical lines (at 1/3 and 2/3 positions) -->
                    <line x1="4.67" y1="1" x2="4.67" y2="10" stroke="#B9C0CA" stroke-width="1" />
                    <line x1="9.33" y1="1" x2="9.33" y2="10" stroke="#B9C0CA" stroke-width="1" />
                </svg>
            </div>
    </div>
    <ng-container *ngIf="data && !isLoading">
        <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight }">
            <dx-vector-map id="vector-map" [bounds]="[-180, 85, 180, -60]" [size]="{ height: calculatedChartWidgetHeight }" (onDrawn)="onMapDrawn($event)">
                <dxo-tooltip [enabled]="true" contentTemplate="tooltipContent"></dxo-tooltip>
                <dxo-common-settings
                borderWidth="0"
                ></dxo-common-settings>
                <dxi-layer
                [customize]="customizeLayers"
                [dataSource]="worldMap"
                name="areas"
                >
                    <dxo-label [enabled]="true" dataField="name"></dxo-label>
                </dxi-layer>
                <dxi-legend [customizeText]="customizeText">
                    <dxo-source layer="areas" grouping="color"></dxo-source>
                </dxi-legend>
                <div *dxTemplate="let info of 'tooltipContent'" class="state-tooltip">
                    <div>
                        <div>{{ data[info.attribute("name")]?.region }}</div>
                        <div>{{ info.attribute("total") }}</div>
                    </div>
                </div>
            </dx-vector-map> 

        </div>
    </ng-container>
    <ng-container *ngIf="!data && !isLoading">
        <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight }">
          <span class="empty-data">No Data Found!</span>
        </div>
    </ng-container>
</div>