<div class="dunning-page container-fluid px-0">
    <div class="row">
        <div class="col-12">
            <udrf-header></udrf-header>
        </div>
    </div>
    <div class="row pb-2">
        <div class="col-sm-12 col-lg-5 my-auto">
            <div class="row mt-2" *ngIf="multiple_mail_send_applicable">
                <div class="col-1 pr-0 pt-1">
                    <mat-checkbox (change)="selectAllItemData($event)" class="example-margin"></mat-checkbox>
                </div>
                <div class="groupby-type pl-0 col-4 pt-1">
                    Dunning by customer
                </div>

                <div class="col-3" *ngIf="this.udrfService.udrfUiData.checkedBodyItemIndex.length>0">
                    <button mat-raised-button (click)="sendMultipleDunning()" class="send-btn">
                        <span>
                            <mat-icon class="send-icon pr-4">send</mat-icon>
                        </span>
                        Send
                    </button>
                </div>
                <div class="col-1 my-auto" *ngIf="isOpenMailBoxLoading">
                    <mat-spinner role="progressbar" mode="indeterminate" diameter="30" 
                    style="width: 30px; height: 30px;">
                   </mat-spinner>
                </div>
                
            </div>

            <!-- <div class="row mt-2" *ngIf="this.udrfService.udrfUiData.checkedBodyItemIndex.length>0">
                
            </div> -->

            <!-- <div class="d-flex flex-row">
                <div class="mt-2">
                    <section>
                        <mat-checkbox class="groupby-type">Dunning for this week</mat-checkbox>
                    </section>
                </div>
                <div class="my-auto"> 
                    <span class="arrow-icon">
                        <button mat-icon-button (click)="handleDateRange('previous')" matTooltip="Previous week">
                            <mat-icon>chevron_left</mat-icon>
                        </button>
                    </span>
                    <span class="bold-text pr-1">
                        {{dateRange}}
                    </span>
                    <span class="pr-1 ">
                        <button mat-icon-button >
                            <mat-icon class="calender-icon">event</mat-icon>
                        </button>
                    </span>
                    <span class="arrow-icon">
                        <button mat-icon-button (click)="handleDateRange('next')" matTooltip="Next week">
                            <mat-icon>chevron_right</mat-icon>
                        </button>
                    </span>
                </div>
                
            </div> -->

        </div>
        <!-- <div class="col-sm-12 col-lg-7">
            <udrf-header></udrf-header>
        </div> -->
    </div>


    <div class="row">

    </div>

    <div class="row">
        <div class="col-12">
            <udrf-body></udrf-body>
        </div>
    </div>
</div>

<ngx-spinner size="medium" type="ball-clip-rotate"  bdColor="rgba(236, 233, 233, 0.8)" color="#cf0001"></ngx-spinner>   