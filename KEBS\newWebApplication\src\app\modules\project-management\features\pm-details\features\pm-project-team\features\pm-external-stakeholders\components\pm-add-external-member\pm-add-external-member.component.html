<div class="add-external-member">
    <div class="loader-container" *ngIf="isComponentLoading">
        <mat-spinner class="green-spinner loader" style diameter="40"></mat-spinner>
    </div>
    <div *ngIf="!isComponentLoading">
        <div class="row m-2 pl-4 pt-1 pb-1 d-flex header">
            <div class="col p-0 m-0 header-title">
                {{ ('allocate_people' | checkLabel : this.formConfig: 'add-external-member': 'Allocate External Stakeholder') }} 
            </div>

            <div class="col-2 d-flex pr-3 m-0 justify-content-end">
                <mat-icon class="close-button" (click)="onCloseClick()">clear</mat-icon>
            </div>

        </div>
        <div class="body">
            <form [formGroup]="addExternalMemberFormGroup">
                <div class="row pl-4 pr-4 mt-4 d-flex addMember-details">
                    <div class="col-12 p-0 m-0">
                        <div class="row d-flex" *ngIf="dialogData.mode == 'Add'">
                            <div class="col-4 p-0 ml-0 mb-4"
                                *ngIf="('employee_name' | checkActive : this.formConfig: 'add-external-member')">
                                <div class="row">
                                    <div class="col-9 p-0 m-0">
                                        <span class="form-header">
                                            {{ ('employee_name' | checkLabel : this.formConfig: 'add-external-member': 'Search
                                            Employee') }} <ng-template [ngTemplateOutlet]="mandatoryTemplate"
                                                *ngIf="('employee_name' | checkMandatedField : this.formConfig: 'add-external-member')"></ng-template>
                                        </span>
                                        <app-search-user-e360 class="employee_name" [isAutocomplete]="true"
                                            [required]="('employee_name' | checkMandatedField : this.formConfig: 'add-external-member')"
                                            formControlName="employee_name" [label]="'Search for member'"
                                            [disabled]="isSearchDisabled" [ngStyle]="{'background':(isSearchDisabled) ? '#E8E9EE' : 'white' }" ></app-search-user-e360>
                                    </div>
                                    <div class="col-3 p-0 m-0 textDiv"><span class="form-header"
                                            style="font-size: 15px !important;">or</span></div>
                                </div>
                            </div>
                            <div class="col-4 p-0 ml-0 mb-4"
                                *ngIf="('email' | checkActive : this.formConfig: 'add-external-member')">
                                <span class="form-header">
                                    {{ ('email' | checkLabel : this.formConfig: 'add-external-member': 'Email') }} <ng-template
                                        *ngIf="('email' | checkMandatedField : this.formConfig: 'add-external-member')"
                                        [ngTemplateOutlet]="mandatoryTemplate"></ng-template>
                                </span>
                                <div class="dateFieldBorder">
                                    <input type="email" formControlName="email" class="fieldvalue"
                                        [required]="('email' | checkMandatedField : this.formConfig: 'add-external-member')"
                                        matInput placeholder="Enter Email" style="border: none; outline: none;padding-left: 8px;margin-top: 0px;border-radius: 4px;"
                                        [readonly]="isEmailDisabled" [ngStyle]="{'background':(isEmailDisabled) ? '#E8E9EE' : 'white' }" />
                                </div>
                            </div>
                            <div class="col-4 p-0 ml-0 mb-4"
                                *ngIf="('external_name' | checkActive : this.formConfig: 'add-external-member')">
                                <span class="form-header">
                                    {{ ('external_name' | checkLabel : this.formConfig: 'add-member': 'User Name')
                                    }}
                                    <ng-template
                                        *ngIf="('external_name' | checkMandatedField : this.formConfig: 'add-external-member')"
                                        [ngTemplateOutlet]="mandatoryTemplate"></ng-template>
                                </span>
                                <div class="dateFieldBorder">
                                    <input formControlName="external_name" class="fieldvalue"
                                        [required]="('employee_name' | checkMandatedField : this.formConfig: 'add-external-member')"
                                        matInput placeholder="Enter Name" style="border: none; outline: none;padding-left: 8px;margin-top: 0px;border-radius: 4px;"
                                        [readonly]="isEmailDisabled" [ngStyle]="{'background':(isEmailDisabled) ? '#E8E9EE' : 'white' }"/>
                                </div>
                            </div>
                        </div>
                        <div class="row d-flex" *ngIf="dialogData.mode == 'Edit'">
                            <div class="col-4 p-0 ml-0 mb-4" *ngIf="type == 'I'">
                                        <span class="form-header">
                                            Employee AID<ng-template [ngTemplateOutlet]="mandatoryTemplate"
                                                *ngIf="('employee_name' | checkMandatedField : this.formConfig: 'add-external-member')"></ng-template>
                                        </span>
                                        <span class="form-header-val">{{dialogData.data.associate_id}}</span>
                                    

                               
                            </div>
                            <div class="col-4 p-0 ml-0 mb-4" *ngIf="type == 'I'">
                                <span class="form-header">
                                    Employee <ng-template [ngTemplateOutlet]="mandatoryTemplate"
                                        *ngIf="('employee_name' | checkMandatedField : this.formConfig: 'add-external-member')"></ng-template>
                                </span>
                                <span class="form-header-val">{{dialogData.data.name}}</span>
                            

                       
                    </div>
                            <div class="col-4 p-0 ml-0 mb-4" *ngIf="type == 'E'">
                                <span class="form-header">
                                    {{ ('email' | checkLabel : this.formConfig: 'add-external-member': 'Email') }} <ng-template
                                        *ngIf="('email' | checkMandatedField : this.formConfig: 'add-external-member')"
                                        [ngTemplateOutlet]="mandatoryTemplate"></ng-template>
                                </span>
                                <span class="form-header-val">
                                    {{ addExternalMemberFormGroup.get('email')?.value }}
                                </span>
                            </div>
                            <div class="col-4 p-0 ml-0 mb-4" *ngIf="type == 'E'">
                                <span class="form-header">
                                    {{ ('external_name' | checkLabel : this.formConfig: 'add-external-member': 'Employee Name') }}
                                    <ng-template
                                        *ngIf="('external_name' | checkMandatedField : this.formConfig: 'add-external-member')"
                                        [ngTemplateOutlet]="mandatoryTemplate"></ng-template>
                                </span>
                                <span class="form-header-val">
                                    {{ addExternalMemberFormGroup.get('external_name')?.value }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-4 p-0 ml-0 mb-4"
                        *ngIf="('projectRole' | checkActive : this.formConfig: 'add-external-member')">
                        <span class="form-header">
                            {{ ('projectRole' | checkLabel : this.formConfig: 'add-external-member': 'Project Role') }}
                            <ng-template
                                *ngIf="('projectRole' | checkMandatedField : this.formConfig: 'add-external-member')"
                                [ngTemplateOutlet]="mandatoryTemplate"></ng-template>
                        </span>
                        <app-input-search-name class="project_role" [list]="projectRoleList" placeholder="Select One"
                            [required]="('projectRole' | checkMandatedField : this.formConfig: 'add-external-member')"
                            formControlName="projectRole">
                        </app-input-search-name>
                    </div>
                    <div class="col-4 p-0 ml-0 mb-4"
                        *ngIf="('startDate' | checkActive : this.formConfig: 'add-external-member')">
                        <span class="form-header">
                            {{ ('startDate' | checkLabel : this.formConfig: 'add-external-member': 'Start Date') }} <ng-template
                                *ngIf="('startDate' | checkMandatedField : this.formConfig: 'add-external-member')"
                                [ngTemplateOutlet]="mandatoryTemplate"></ng-template>
                        </span>
                        <div class="dateFieldBorder" style="padding-left: 8px">
                            <input formControlName="startDate" class="fieldvalue"
                                required="('startDate' | checkMandatedField : this.formConfig: 'add-external-member')"
                                matInput [matDatepicker]="picker1" placeholder="DD-MMM-YYYY"
                                style="border: none; outline: none" [min]="projectStartDate"
                                [max]="getenddate ? getenddate : projectEndDate" />
                            <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                            <mat-datepicker #picker1></mat-datepicker>
                        </div>
                    </div>
                    <div class="col-4 p-0 ml-0 mb-4"
                        *ngIf="('endDate' | checkActive : this.formConfig: 'add-external-member')">
                        <span class="form-header">
                            {{ ('endDate' | checkLabel : this.formConfig: 'add-external-member': 'End Date') }} <ng-template
                                *ngIf="('endDate' | checkMandatedField : this.formConfig: 'add-external-member')"
                                [ngTemplateOutlet]="mandatoryTemplate"></ng-template>
                        </span>
                        <div class="dateFieldBorder" style="padding-left: 8px">
                            <input formControlName="endDate" class="fieldvalue"
                                required="('endDate' | checkMandatedField : this.formConfig: 'add-external-member')"
                                matInput [matDatepicker]="picker2" placeholder="DD-MMM-YYYY"
                                style="border: none; outline: none"
                                [min]="getstartdate ? getstartdate : projectStartDate" [max]="projectEndDate" />
                            <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                            <mat-datepicker #picker2></mat-datepicker>
                        </div>
                    </div>
                    <div
                    class="col-3 is-head"
                    *ngIf="('communication' | checkActive : this.formConfig : 'add-external-member')"
                  >
                    <mat-checkbox
                      class="is-head-name"
                      formControlName="communication"
                    >
                      {{
                        "cc"
                          | checkLabel : this.formConfig : "add-external-member" : "Communication"
                      }}
                    </mat-checkbox>
                  </div>
                </div>
                <ng-template #mandatoryTemplate>
                    <span class="required-star">*</span>
                </ng-template>
            </form>
        </div>
        <div class="row d-flex pr-4 justify-content-end footer-buttons">
            <button mat-button class="button-back" [disabled]="saveDisabled" (click)="onCloseClick()">Cancel</button>
            <div class="save-class">
                <button mat-button class="button-save" [disabled]="saveDisabled"
                    (click)="addExternalStakeholder()">Save</button>
            </div>

            <!-- <div class="col-1">
                <button mat-button class="button-next" [disabled]="saveDisabled" (click)="addMember()">Save</button>
                <div class="button-next" *ngIf="saveDisabled">
                    <mat-spinner class="green-spinner" diameter="20"></mat-spinner>
                </div>
            </div> -->
        </div>
    </div>
</div>