.add-external-member {
    width: 921px;
    height: 416px;
    top: -0.5px;
    border-radius: 4px;
    overflow: hidden;

    .header {
        background: #FFFFFF;
        align-items: center;
    }

    .header-title {
        font-family: <PERSON><PERSON>;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        letter-spacing: 0em;
        text-align: left;
        color: #111434;
    }

    .close-button {
        color: #7D838B;
        font-size: 20px;
        cursor: pointer;

    }

    .body {
        width: 921px;
        height: 303px;
        background: #F6F6F6;
        overflow: auto;
    }

    // .addMember-details {
        
    // }

    .first-line-details {
        width: 890px;
        height: 52px;
        gap: 16px;
        display: flex;
        margin-left: 25px;

    }

    .content-title {
        padding-top: 20px;
        padding-bottom: 4px;
        font-family: Roboto;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-align: left;
        color: #6E7B8F;
    }

    .input-field {
        border-radius: 5px;
        display: flow;
        background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 12px;
        font-family: <PERSON><PERSON>;
        /* font-size: 13px; */
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
        width: 267px;
    }

    ::ng-deep .mat-form-field .mat-form-field-wrapper {
        padding-bottom: 0px !important;
    }

    ::ng-deep .mat-form-field {
        /* display: inline-block; */
        position: relative;
        text-align: left;
        display: flow;
    }

    .button-next ::ng-deep .green-spinner circle {
        stroke: white !important;
    }

    .loader-container ::ng-deep .green-spinner circle {
        stroke: var(--extButton) !important;
    }

    .second-line-details {
        width: 223px;
        height: 24px;
        gap: 32px
    }

    .is-head {
        width: 71px;
        height: 24px;
        gap: 12px;
        margin-top: -18.5px;
        margin-left: -10px;

    }

    .third-line-details {
        // width: 157px;
        height: 40px;
        gap: 10px;
        margin-left: 25px;
        margin-top: -5px;
        display: flex;

    }

    .fourth-line-details {
        gap: 16px;
        display: flex;
        margin-left: 25px;
        margin-top: 40px;

    }

    .fifth-line-details {
        width: 223px;
        height: 24px;
        gap: 24px;
        margin-top: -20px;

    }

    .billable {
        width: 70px;
        height: 24px;
        gap: 12px;
        margin-top: 30px;
        margin-left: 40px;

    }

    .footer-buttons {
        background-color: whitesmoke;
        border-top: 1px solid grey;
        height: 66px;
        align-items: center;
    }

    .button-back {
        color: black;
        border: none;
        background: none;
    }

    .button-save {
        color: white;
        font-family: Roboto;
        font-size: 14px;
        font-weight: 700;
        letter-spacing: -0.02em;
        text-align: left;
        border: none;
        background-color: var(--extButton);
    }

    .save-class {
        background-color: var(--extButton) !important;
        // background-color: var(--memberButton);
        border-radius: 5px;
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 64px;
    }


    .add_new_color {
        width: 133px;
        height: 16px;
        font-family: Roboto;
        font-size: 14px;
        font-weight: 700;
        line-height: 16px;
        letter-spacing: -0.02em;
        text-align: left;
        color: var(---extButton) !important;


    }

    .business-unit {
        width: 157px;
        height: 40px;
        gap: 10px;
        margin-left: 40px;
        margin-top: 51px;

    }

    .is-head-name {
        width: 43px;
        height: 24px;
        font-family: Roboto;
        font-size: 12px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0.02em;
        text-align: left;
        color: #6E7B8F;


    }

    .billable-name {
        width: 42px;
        height: 24px;
        font-family: Roboto;
        font-size: 12px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0.02em;
        text-align: left;
        color: #6E7B8F;
    }

    .bussiness-division {
        margin-top: -25px;
        display: flex;
        margin-left: 25px;
    }

    .division-name {
        margin-left: 40px
    }

    .sub-division-name {
        margin-left: 264px
    }

    .business-unit-1 {

        width: 157px;
        height: 40px;
        gap: 10px;
        margin-left: 40px;
        margin-top: 40px;
    }

    .info-icon {
        font-size: 12px;
        margin-left: 5px;
        height: 10px;
        cursor: pointer;
        margin-top: 2px;
        color: grey;
        position: absolute;
    }

    .required-star {
        color: #EC5F6E;
    }

    .employee_name {
        border-radius: 5px;
        display: flow;
        background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 19px;
        font-family: Roboto;
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
        margin-top: -3px;
        width: 247px;
    }

    .project_role {
        border-radius: 5px;
        display: flow;
        // background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 12px;
        font-family: Roboto;
        /* font-size: 13px; */
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
        width: 267px;
        background: #ffff;
    }

    .reports_to {
        border-radius: 5px;
        display: flow;
        background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 12px;
        font-family: Roboto;
        /* font-size: 13px; */
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
    }

    .secondary_project_role {
        border-radius: 5px;
        display: flow;
        // background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 12px;
        font-family: Roboto;
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
        width: 267px;
    }

    .no-updown-arrows::-webkit-inner-spin-button,
    .no-updown-arrows::-webkit-outer-spin-button {
        -webkit-appearance: none;
        appearance: none;
        margin: 0;
    }

    ::ng-deep .mat-datepicker-toggle,
    .mat-datepicker-content .mat-calendar-next-button,
    .mat-datepicker-content .mat-calendar-previous-button {
        color: var(--extButton) !important;
    }

    .loader-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        /* Adjust the height as needed */
        background-color: white !important;
        /* Add a semi-transparent background */
    }

    .loader {
        color: var(--extButton)
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    ::-webkit-scrollbar {
        width: 5px !important;
        height: 5px !important;
    }

    .shift {
        border-radius: 5px;
        display: flow;
        background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 12px;
        font-family: Roboto;
        /* font-size: 13px; */
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
        width: 267px;
    }
    .fieldvalue {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        color: #8B95A5;
    }
    .form-header{
        font-family: Roboto;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-align: left;
        color: #6E7B8F;
    }
    .dateFieldBorder {
        border: 1px solid;
        border-color: #b9c0ca;
        width: 267px;
        height: 38px;
        display: flex;
        border-radius: 4px;
        background: #ffff;
      }
    .textDiv{
        align-items: center;
        display: flex;
        position: relative;
        left: 11px;
        justify-content: center;
        padding-top: 17px !important;
    }
    .form-header-val{
        display: block;
        margin-top: 3px;
        color: #6c757dde;
        font-family: Roboto;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-align: left;
    }

      .dateFieldBorder:focus {
        border: 2px solid var(--externalStakeField) !important; /* Change the border color on focus */
      }
      .dateFieldBorder:hover {
        border: 2px solid var(--externalStakeField) !important; /* Change the border color on focus */
      }
      ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick{
        color: var(--externalStakeField) !important;
      }
}