<div class="container-fluid pt-0 mb-2 notes"
*ngIf="isNewNoteActive && !isLoading">
    <div class="row pt-2 pb-1">
        <div class="col-1 px-0 normal-lt-txt my-auto">
            Notes
        </div>
        <div class="col-1"
        style="max-width: 1px;">
            <button  mat-icon-button
            (click)="toggleEditor()" 
            matTooltip="Add note"
            [ngClass]="showEditor ? 'add-btn-active' : 'add-btn'">
                <mat-icon>add</mat-icon>
            </button>
        </div>
    </div>

    <div class="row pb-2" *ngIf="showEditor" [formGroup]="noteFields">
        <div class="col-12 pl-3">
          <div class="card slide-in-top">
            <div class="card-body pt-2 pb-2 pl-3 pr-3" [ngStyle]="{backgroundColor:currentEditorColor}">
              <div class="row">
                <div class="col-1 my-auto pl-0">
                  <span class="account-info-details"> Title :
      
                  </span>
                </div>
                <div class="col-4 pl-1 pr-0">
                  <mat-form-field appearance="outline">
                    <mat-label>Title</mat-label>
                    <input matInput formControlName="title">
                  </mat-form-field>
      
                </div>
                <div class="col-5 d-flex">
                  <div matRipple class="colors mr-3 mt-2" [ngStyle]="{backgroundColor:item}"
                    *ngFor="let item of notes_bg_color; let i = index;" (click)="setNoteColor(item)">
                  </div>
                </div>
                <div class="col-2 d-flex">
                  <button mat-icon-button class="iconbtn ml-auto mr-4" matTooltip="Create note" (click)='saveNote()'
                    *ngIf="noteMode == 'create'">
                    <mat-icon style="font-size: 17px !important;"> done_all</mat-icon>
                  </button>
                  <button mat-icon-button class="iconbtn ml-auto mr-4" matTooltip="Update note" (click)='updateNote()'
                    *ngIf="noteMode == 'edit'">
                    <mat-icon style="font-size: 17px !important;"> done_all</mat-icon>
                  </button>
                  <button mat-icon-button matTooltip="Close" class="close" (click)="resetAll()">
                    <mat-icon class="smallCardIcon">close</mat-icon>
                  </button>
                </div>
              </div>
              <div class="row pt-2 pb-2">
                <div class="col-12 pl-0 pr-0">
                  <dx-html-editor class="dev-extreme-styles" (valueType)='valueType'
                    (valueChange)="notesValueChange($event)"
                    formControlName="body">
                    <dxo-toolbar>
                      <dxi-item name="undo"></dxi-item>
                      <dxi-item name="redo"></dxi-item>
                      <dxi-item name="separator"></dxi-item>
                      <dxi-item name="bold"></dxi-item>
                      <dxi-item name="italic"></dxi-item>
                      <dxi-item name="underline"></dxi-item>
                      <dxi-item name="separator"></dxi-item>
                      <dxi-item name="alignLeft"></dxi-item>
                      <dxi-item name="alignCenter"></dxi-item>
                      <dxi-item name="alignJustify"></dxi-item>
                      <dxi-item name="separator"></dxi-item>
                      <dxi-item name="orderedList"></dxi-item>
                      <dxi-item name="bulletList"></dxi-item>
                      <dxi-item name="separator"></dxi-item>
                      <dxi-item name="color"></dxi-item>
                      <dxi-item name="background"></dxi-item>
                    </dxo-toolbar>
                    <span *ngIf="noteMode == 'edit'" [innerHTML]="body ? (body | safeHTML) : ''"></span>
                  </dx-html-editor>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    <div class="row mt-3">
      <cdk-virtual-scroll-viewport itemSize="50" class="example-viewport">
        <div *cdkVirtualFor="let note of notesArray" class="example-item m-3 slide-from-down">
          <mat-card 
          style="height: 20vh;"
          *ngIf="note._id!=currentEditingNoteId"
          [ngStyle]="{'background-color':note.color}">
            <div class="row d-flex justify-content-between">
              <div>
                <span>Title :</span>
                <span class="ml-2 dark-red">{{note.note_title ? note.note_title : ''}}</span>
              </div>
              <div>
                  <button mat-icon-button matTooltip="View more" class=" ml-auto"
                  [matMenuTriggerFor]="note_options">
                  <mat-icon class="more-button ">more_vert</mat-icon>
                  </button>

                  <mat-menu #note_options="matMenu">
                    <button mat-menu-item class="drop-btn"
                      (click)="onEditNote(note)"
                    >
                      Edit
                    </button>
                    <button mat-menu-item class="drop-btn"
                    (click)="deleteNote(note)" >
                      Delete
                    </button>
                  </mat-menu>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col-12 notes-data px-0">
                <span style="text-align: start;" [innerHTML]="note.note_body ? (note.note_body | safeHTML) : ''"></span>
              </div>
            </div>
            <div class="row mt-2">
              <div class="col-12 px-0 d-flex">
                <span class="ml-auto sub-heading">
                  {{note.created_by}}
                  {{note.created_on ? (note.created_on | date: 'dd MMM yyyy') : '' }}
                </span>
              </div>
            </div>  
          </mat-card>
        </div>
      </cdk-virtual-scroll-viewport>
      
    </div>
</div>


<div *ngIf="isLoading"  class="justify-content-center">
  <div class="row pt-4 justify-content-center">
  <mat-spinner diameter="30"> </mat-spinner>
  </div>
</div>



<div *ngIf="notesArray.length == 0 && !isNewNoteActive && !isLoading" class="notes container-fluid">
    <div class="d-flex pb-2 justify-content-center align-items-center slide-in-top">
        <span class="extra-dark-txt">No notes found! Let's create one</span>
    </div>

    <div class="d-flex justify-content-center align-items-center slide-from-down pt-2 pb-2">
        <img _ngcontent-pru-c304="" src="https://assets.kebs.app/images/nomilestone.png" height="170" width="200" class="mt-2 mb-2">
    </div>

    <div class="d-flex justify-content-center slign-items-center slide-from-down">
      <button (click)="createNewNote()" mat-raised-button class="create-btn mt-2">
        Create Notes
      </button>
    </div>
</div>