<div class="widget-container">
    <div class="header">
      <div class="d-flex align-items-center">
        <div class="chart-title">{{ widgetConfig?.label }}</div>
      </div>
       <div class="summary-icon" *ngIf="widgetConfig?.widget_config?.show_summary" (click)="openSummaryForm()">
                <svg width="17" height="15" viewBox="0 0 14 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <!-- Outer box -->
                    <rect x="0.5" y="0.5" width="13" height="10" rx="1" stroke="#B9C0CA" stroke-width="1" />
            
                    <!-- Horizontal line (centered) -->
                    <line x1="1" y1="5.5" x2="13" y2="5.5" stroke="#B9C0CA" stroke-width="1" />
            
                    <!-- Two vertical lines (at 1/3 and 2/3 positions) -->
                    <line x1="4.67" y1="1" x2="4.67" y2="10" stroke="#B9C0CA" stroke-width="1" />
                    <line x1="9.33" y1="1" x2="9.33" y2="10" stroke="#B9C0CA" stroke-width="1" />
                </svg>
            </div>
    </div>
    <ng-container *ngIf="data?.total !== undefined && !isLoading">
      <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight + 'px' }">
        <div>
            <div class="total-area">
                <div class="total-header">Total:</div>
                <div class="total-label">{{data?.total}}</div>
            </div>
            <div class="legends-area">
                <ng-container *ngFor="let item of data?.data">
                    <div class="legends">
                        <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="3" cy="3" r="3"  [attr.fill]="item['color']"/>
                        </svg>                        
                        <div class="legend-label">{{item.label}}</div>
                    </div>
                </ng-container>
              </div>
        </div>      
        <div class="bars-container">
          <div class="bar" 
               *ngFor="let item of data?.data; let i = index" 
               [ngStyle]="{
                 height: (item.value / maxValue) * 100 + '%',
                 backgroundColor: item['color']
               }">
            <div class="value">{{ item.value }}</div>
          </div>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="!data?.total && !isLoading">
      <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight + 'px' }">
        <span class="empty-data">No Data Found!</span>
      </div>
    </ng-container>
  </div>
  