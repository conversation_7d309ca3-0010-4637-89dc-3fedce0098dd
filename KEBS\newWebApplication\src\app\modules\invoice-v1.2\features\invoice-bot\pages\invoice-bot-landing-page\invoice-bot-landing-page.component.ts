import { <PERSON>mpo<PERSON>, <PERSON><PERSON>nit, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON>, ViewChildren, AfterViewInit, EventEmitter, Output } from '@angular/core';
import { InvoiceBotService } from './../../services/invoice-bot.service';
import { InvoiceConfigService } from '../../../invoice-config/services/invoice-config.service';
import { InvoiceGenerationService } from '../../../invoice-generation/services/invoice-generation.service';
import { InvoiceFormBuilderComponent } from '../../components/invoice-form-builder/invoice-form-builder.component';
import { FormGroup, FormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TenantService } from 'src/app/services/tenant-service/tenant.service';
import { InvoiceCommonService } from "src/app/modules/invoice/common-services/invoice-common.service";
import { MatStepper } from '@angular/material/stepper';
import { <PERSON><PERSON><PERSON><PERSON> } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import swal from "sweetalert2";
import { UtilityService } from 'src/app/services/utility/utility.service';
import * as moment from 'moment';
import { NgxSpinnerService } from 'ngx-spinner';
import * as _ from 'underscore';
import { PdfViewerComponent } from 'src/app/modules/invoice/features/invoice-generation/components/pdf-viewer/pdf-viewer.component';
import { InvoicePdfComponent } from 'src/app/modules/invoice-v1.2/features/invoice-bot/components/invoice-pdf/invoice-pdf.component';
import { MatMenuTrigger } from '@angular/material/menu';
import { InvoiceBillingService } from "../../../invoice-billing/services/invoice-billing.service";
import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import { O365LoginService } from 'src/app/services/login/o365-login.service';
import {IntegrationSimulationComponent} from "../../components/integration-simulation/integration-simulation.component";
import { DocViewerComponent } from 'src/app/modules/shared-lazy-loaded-components/attachment-mgmt/components/doc-viewer/doc-viewer.component';
import { ChatCommentContextModalComponent } from 'src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component';
@Component({
  selector: 'app-invoice-bot-landing-page',
  templateUrl: './invoice-bot-landing-page.component.html',
  styleUrls: ['./invoice-bot-landing-page.component.scss'],
  providers: [
    {
      provide: STEPPER_GLOBAL_OPTIONS,
      useValue: { displayDefaultIndicatorType: false },
    }
  ]
})
export class InvoiceBotLandingPageComponent implements OnInit, AfterViewInit {

  invFields: any;
  taxMaster: any;
  currencyDetails: any;
  data: any;
  prefilledData: any;
  projectId: any;
  milestoneId: any;
  serviceTypeId: any;
  serviceTypeName: any;
  serviceTypeGroupId: any;
  invoiceTenantDetails: any;
  groupTaxMaster: any;
  projectCurrencyMaster: any;
  formValidities: boolean[] = [];
  isTenantExistingInvoiceAllowed: any;
  formValues: any;
  getTenantExistingInvoiceConf: any;
  finalFormValues: any;
  generateInvoice: boolean;
  checkCurrencyConversion: boolean;
  showSaveSpinner: boolean = false;
  showAdaptTemplate: boolean = false;
  showWarningFoTM: boolean = false;
  restrictSavePoOnAlert: boolean = false;
  otherMilestoneDataToPdf: any;
  chargeTypeMaster: any;
  billingId: any;
  adaptData: any;
  isDialogOpen: boolean = false;
  @ViewChild('MenuTrigger') MenuTrigger: MatMenuTrigger;
  @ViewChildren(InvoiceFormBuilderComponent) invoiceFormBuilders!: QueryList<InvoiceFormBuilderComponent>;
  @ViewChild('stepper') stepper!: MatStepper;
  @ViewChild('saveSpinner') saveSpinner: any;
  @Output() valueEmitter: EventEmitter<any> = new EventEmitter<any>();
  formGroups: FormGroup[] = [];
  show: boolean = false;
  legalEntityData: any;
  isTemplateApplied: boolean = false;
  customerId: any;
  invoiceForm: any;
  invoiceFormValue: any;
  entityFieldConfig: any[] = [];
  itemFieldConfig: any;
  fteFieldConfig: any;
  totalTaxAmount = 0;
  validItems: any[] = [];
  invalidItems: any[] = [];
  messages: any[] = [];
  costCenterFlag: boolean = false;
  milestoneValueCheck: boolean = false;
  itemId : any;
  poData:any;
  formControlForPoAlert: any;
  showPDF: boolean = false;
  storePDFInSharepoint: boolean = false;
  generateAndStorePDFInS3: boolean = false;
  legalEntityId: any;
  dateFormats:any;
  isToShowMilestoneTagOption: boolean = false;
  milestoneTagDetails: any;

  commentsInput = {
    application_id: null,
    unique_id_1: '',
    unique_id_2: '',
    application_name: null,
    title: '',
  }
  isCommentPresent: boolean = false;

  constructor(private botService: InvoiceBotService,
    private invConfigService: InvoiceConfigService,
    private invoiceService: InvoiceGenerationService,
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private tenantService: TenantService,
    private invoiceCommonService: InvoiceCommonService,
    private _snackBar: MatSnackBar,
    private utilityService: UtilityService,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private invoiceBillingService: InvoiceBillingService,
    private _o365: O365LoginService
  ) { }

  openMenu() {
    this.MenuTrigger.openMenu();
  }

  closeMenu() {
    this.MenuTrigger.closeMenu();
  }
  async ngOnInit() {
    this.botService.spinnerService.show();
    this.projectId = this.route.snapshot.params['projectId'];
    this.milestoneId = this.route.snapshot.params['milestoneId'];
    this.itemId = this.route.snapshot.params['itemId'];
    this.serviceTypeGroupId = this.route.snapshot.params['serviceTypeGroupId'];
    this.getTenantInfo()
    this.projectCurrencyMaster = await this.getProjectCurrencyMaster(this.projectId, this.itemId)
    this.getCurrencyDetails();
    this.poData = await this.getPoValue(this.itemId, this.milestoneId);
    this.getInvoiceConfig();
    this.dateFormats = await this.getTenantDateFormats();
    this.data = await this.getStepperInfo(this.projectId, this.milestoneId);
    let invoiceWithBillingId = _.filter(this.data, (l) => {
      if (l.billing_id) return l;
    });
    if (invoiceWithBillingId.length == 0)
      this.messages.push('Milestone activity completion is Pending !')
    let costCenter = this.data[0].profitCenter
    if (costCenter == '' || costCenter == null || costCenter == undefined) {
      this.costCenterFlag = true
      this.messages.push('Cost Center not found !')
    }
    let milestone_value = this.data[0].milestone_value || []
    let service_type_group_id = this.data[0].service_type_group_id
    let is_billable = this.data[0].is_billable
    if(is_billable){
      this.serviceTypeGroupId = this.data[0].service_type_group_id
    }
    if (service_type_group_id == 2) {
      if (milestone_value.length > 0) {
        const allZeros = milestone_value.every(milestone => milestone.value === 0);

        if (allZeros) {
          this.milestoneValueCheck = true
          this.messages.push('Please enter milestone value in projects for invoicing !');
        }
      }
      else {
        this.milestoneValueCheck = true
        this.messages.push('Please enter milestone value in projects for invoicing !');
      }
    }
    this.serviceTypeGroupId = this.data[0].service_type_group_id ?  this.serviceTypeGroupId = this.data[0].service_type_group_id : this.serviceTypeGroupId
    const combinedMessage = this.messages.join("\n");

    if (invoiceWithBillingId.length == 0 || this.costCenterFlag || this.milestoneValueCheck) {
      swal.fire({
        title: combinedMessage,
        icon: 'info',
        showCancelButton: true,
        confirmButtonText: 'Go to Billed', // Button 1 text
        cancelButtonText: 'Go to YTB', // Button 2 text
        confirmButtonColor: '#cf0001',
        cancelButtonColor: '#cf0001',
        allowOutsideClick: false,
        allowEscapeKey: false
      }).then((result) => {
        if (result.isConfirmed) {
          this.moveToBilled();
        } else if (result.dismiss === swal.DismissReason.cancel) {
          this.moveToYTB();
        }
      });
    }

    this.customerId = this.data[0].endCustomerId;
    this.legalEntityData = this.data.map(item => item.legal_entity_id);
    this.getTenantExistingInvoiceConf = await this.invoiceService.getTenantExistingInvoiceConf(this.legalEntityData, this.milestoneId).toPromise();
    if (this.getTenantExistingInvoiceConf["messType"] == "E") {
      this.isTenantExistingInvoiceAllowed = 0;
    }
    else if (this.getTenantExistingInvoiceConf["messType"] == "S") {
      this.isTenantExistingInvoiceAllowed = this.getTenantExistingInvoiceConf['data']['is_existing_invoice_allowed'];
    }
    else {
      this.isTenantExistingInvoiceAllowed = 0;
    }
    this.legalEntityId = this.data[0].legal_entity_id;
    this.taxMaster = await this.getTaxMaster()
    this.groupTaxMaster = await this.getGroupTaxMaster()
    this.chargeTypeMaster = await this.chargeTypeMasters()
    let milestoneType = this.data[0].milestoneTypeId;

    if(milestoneType == 6){
      let result = await this.getMilestoneDetailsForCredit(this.milestoneId, this.itemId);
      if(result){
        this.milestoneTagDetails = result
      }
    }
    else{
      this.isToShowMilestoneTagOption = false;
    }

    this.isCommentPresent = this.data[0].is_comment_present

    this.show = true
    // this.spinnerService.hide();


  }


  onFormValueChanges(event, index) {
    if (this.stepper?.selectedIndex == index) {
      this.invoiceForm = event;
      this.invoiceFormValue = this.invoiceForm.value;
      this.totalTaxAmount = this.calculateTotalAmount(this.invoiceFormValue.taxResultDetails);
    }
    this.setValidInvalid()
  }

  calculateTotalAmount(taxResultArray: any[]): number {
    let totalAmount = 0;
    for (const taxItem of taxResultArray) {
      totalAmount += taxItem.amount;
    }
    return totalAmount;
  }


  onEntityFieldDataChanges(event, index) {
    if (this.stepper?.selectedIndex == index) {
      this.entityFieldConfig = event;
      this.setValidInvalid()
    }
  }
  onItemFieldDataChanges(event, index) {
    if (this.stepper?.selectedIndex == index) {
      this.itemFieldConfig = event;
      this.setValidInvalid()
    }
  }

  onFteFieldDataChanges(event, index) {
    if (this.stepper?.selectedIndex == index) {
      this.fteFieldConfig = event;
      this.setValidInvalid()
    }
  }

  isFieldValid(index: number): boolean {
    // Assuming "key_name" corresponds to the field name in itemConfig
    const fieldName = this.itemFieldConfig[index].key_name;
    // Check if any FormGroup in "workSummary" has the field as invalid
    for (const formGroup of this.invoiceForm.get('workSummary').controls) {
      if (!formGroup.get(fieldName)?.valid) {
        return false; // If any form group has the field as invalid, return false
      }
    }

    return true; // If all form groups have the field as valid, return true

  }

  isFteFieldValid(index: number): boolean {
    // Assuming "key_name" corresponds to the field name in itemConfig
    const fieldName = this.fteFieldConfig[index].key_name;

    // Check if any FormGroup in "workSummary" has the field as invalid
    for (const formGroup of this.invoiceForm.get('fteDetails').controls) {
      if (!formGroup.get(fieldName)?.valid) {
        return false; // If any form group has the field as invalid, return false
      }
    }

    return true; // If all form groups have the field as valid, return true
  }

  setValidInvalid() {
    this.validItems = []
    this.invalidItems = []

    for (const item of this.entityFieldConfig) {
      if (item.is_mandatory == 1 && this.invoiceForm.controls[item.key_name].valid) {
        this.validItems.push(item);
      }
      if (item.is_mandatory == 1 && this.invoiceForm.controls[item.key_name].invalid) {
        this.invalidItems.push(item);
      }
    }
    for (let i = 0; i < this.itemFieldConfig?.length; i++) {
      if (this.itemFieldConfig[i].is_mandatory == 1) {
        if (this.isFieldValid(i)) {
          this.validItems.push(this.itemFieldConfig[i]);
        } else {
          this.invalidItems.push(this.itemFieldConfig[i]);
        }
      }
    }
    if (this.invoiceForm?.controls["serviceTypeGroupId"]?.value == 1) {
      for (let i = 0; i < this.fteFieldConfig?.length; i++) {
        if (this.fteFieldConfig[i].is_mandatory == 1) {
          if (this.isFteFieldValid(i)) {
            this.validItems.push(this.fteFieldConfig[i]);
          } else {
            this.invalidItems.push(this.fteFieldConfig[i]);
          }
        }
      }
    }
    if(this.invoiceForm?.controls['invoiceNo']?.valid){
      let temp = {label_name: 'Invoice No', key_name: 'invoiceNo'}
      this.validItems.push(temp);
    }

    if(this.invoiceForm?.controls['invoiceNo']?.invalid){
      let temp = {label_name: 'Invoice No', key_name: 'invoiceNo'}
      this.invalidItems.push(temp);
    }

  }


  ngAfterViewInit() {
  }

  async transferDataToNextStep(currentStepFormBuilder: InvoiceFormBuilderComponent, nextStepFormBuilder: InvoiceFormBuilderComponent) {
    const currentStepFormData = currentStepFormBuilder.getFormData().value;
    this.adaptData = currentStepFormData

    let currencyId = currentStepFormData.currencyId

    let conversion_rates = await this.convertCurrency(this.projectId, currencyId, 1)

    // Extract specific data that you want to transfer to the next step
    const specificDataToTransfer = {
      serviceTypeId: currentStepFormData.serviceTypeId,
      serviceTypeName: currentStepFormData.serviceTypeName,
      invoiceTemplateName: currentStepFormData.invoiceTemplateName,
      serviceTypeGroupId: currentStepFormData.serviceTypeGroupId,
      currency: currentStepFormData.currency,
      currencyId: currentStepFormData.currencyId,
      profitCenter: currentStepFormData.profitCenter,
      department: currentStepFormData.department,
      partnerMargin: currentStepFormData.partner_margin,
      milestoneId: currentStepFormData.milestoneId,
      workSummary: currentStepFormData.workSummary,
      invoiceNoType: currentStepFormData.invoiceNoType,
      ...(currentStepFormData.serviceTypeGroupId == 1 ? { fteDetails: currentStepFormData.fteDetails } : {}),
      conversionRates: conversion_rates,



      // ... add more fields as needed
    };


    nextStepFormBuilder.setFormDataSpecific(specificDataToTransfer);
  }




  getTaxMaster() {
    return new Promise((resolve, reject) => {
      this.invConfigService.getTaxMaster(this.legalEntityId).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });

  }

  getGroupTaxMaster() {
    return new Promise((resolve, reject) => {
      this.invConfigService.getGroupTaxMaster(this.legalEntityId).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });

  }

  chargeTypeMasters() {
    return new Promise((resolve, reject) => {
      this.invConfigService.chargeTypeMaster().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });

  }

  

  getProjectCurrencyMaster(project_id,item_id) {
    return new Promise((resolve, reject) => {
      this.invConfigService.getProjectCurrencyMaster(project_id,item_id).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });

  }


  getStepperInfo(projectId, milestoneId) {
    return new Promise((resolve, reject) => {
      this.invoiceService
        .getStepperDetailsV2(projectId, milestoneId).subscribe(
          (res: any) => {
            console.log(res)
            resolve(res.data);
            if (res["messType"] == "B") {
              let swalTemplate = {
                type: "success",
                title: "Milestone has already been billed."
              }
              swal.fire({
                title: 'Milestone has already been billed.!',
                icon: 'success',
                showCancelButton: true,
                confirmButtonText: 'Go to Billed', // Button 1 text
                cancelButtonText: 'Go to YTB', // Button 2 text
                confirmButtonColor: '#cf0001',
                cancelButtonColor: '#cf0001',
                allowOutsideClick: false,
                allowEscapeKey: false
              }).then((result) => {
                if (result.isConfirmed) {
                  this.moveToBilled();
                } else if (result.dismiss === swal.DismissReason.cancel) {
                  this.moveToYTB();
                }
              });
            }
            if(res["messType"] == "A"){
              let title = res['messText'] ? res['messText'] : "Account ledger name not found"
              // swal.fire({ title: title, icon: "info", showConfirmButton: true });
              swal.fire({
                title: title,
                icon: 'info',
                showConfirmButton: true
              }).then((result) => {
                if (result.isConfirmed) {
                  this.moveToYTB();
                } else if (result.dismiss === swal.DismissReason.cancel) {
                  this.moveToYTB();
                }
              });
            }
          },
          (err) => {
            console.log(err);
            let title = err?.error?.messText ?? err?.error?.errorDetails?.messText ?? err?.error?.errorDetails?.msg ?? "Error in retrieving Invoice Stepper Details!";
            // swal.fire({ title: title, icon: "info", showConfirmButton: true });
            swal.fire({
              title: title,
              icon: 'info',
              showConfirmButton: true
            }).then((result) => {
              if (result.isConfirmed) {
                this.moveToYTB();
              } else if (result.dismiss === swal.DismissReason.cancel) {
                this.moveToYTB();
              }
            });
            reject(err);
          }
        );
    });


  }

  async saveAsDraft(){
    this.showSaveSpinner = true;
    this.spinnerService.show();
    this.formGroups = this.invoiceFormBuilders.map(formBuilder => formBuilder.getFormData());
    this.formValues = this.formGroups.map(formGroup => formGroup.value);
    this.formValues?.forEach(item => {
      if (item?.invoiceDate) {
        item.invoiceDate = moment(item.invoiceDate).format('YYYY-MM-DD');
      }
      if (item?.dueDate) {
        item.dueDate = moment(item.dueDate).format('YYYY-MM-DD');
      }
      if (item?.poDate) {
        item.poDate = moment(item.poDate).format('YYYY-MM-DD');
      }
      if (item.tdsValue) {
        item.tdsValue = this.parseValue(item.tdsValue)
      }
      if (item.tcsValue) {
        item.tdsValue = this.parseValue(item.tdsValue)
      }
    });

    let token: any;
    if (this.storePDFInSharepoint) {
      await this._o365.getToken().then(data => {
        token = data;
      })
    }
    else {
      token = ''
    }
    this.invoiceService.saveInvoiceStepperDetailsAsDraft(this.formValues, token).subscribe(
      res => {
        if (res["messType"] == "S") {
          let swalTemplate = {
            // customClass: {
            //   title: "title-class",
            //   confirmButton: "confirm-button-class"
            // },
            type: "success",
            title: "Milestone Saved as Draft successfully"
          }
          this.spinnerService.hide();
          this.showSaveSpinner = false;
          swal.fire({
            title: 'Milestone Saved as Draft successfully!',
            icon: 'success',
            showCancelButton: true,
            confirmButtonText: 'Go to Billed', // Button 1 text
            cancelButtonText: 'Go to YTB', // Button 2 text
            confirmButtonColor: '#cf0001',
            cancelButtonColor: '#cf0001',
            allowOutsideClick: false,
            allowEscapeKey: false
          }).then((result) => {
            if (result.isConfirmed) {
              this.moveToBilled();
            } else if (result.dismiss === swal.DismissReason.cancel) {
              this.moveToYTB();
            }
          });
          // swal.fire({ title: "Milestone Billed successfully !", icon: "success", showConfirmButton: true });
        }
        else if (res["messType"] == "E") {
          this.spinnerService.hide();
          this.showSaveSpinner = false;
          swal.fire({ title: "Milestone Billing Failed !", icon: "info", showConfirmButton: true });
          this.utilityService.showMessage(res["messText"], 'dismiss', 3000);
        }
      },
      err => {
        console.log(err);
        this.spinnerService.hide();
        this.showSaveSpinner = false;
        swal.fire({ title: "Milestone Billing Failed !", icon: "info", showConfirmButton: true });
      }
    );
  }

  async save() {
    this.botService.setFlag(true);
    if (!this.isCurrentStepValid()) {
      return;
    }
    this.showSaveSpinner = true;
    this.spinnerService.show();
    this.formGroups = this.invoiceFormBuilders.map(formBuilder => formBuilder.getFormData());
    // this.formValues = this.formGroups.map(formGroup => formGroup.value);
    this.formValues = this.formGroups.map(formGroup => formGroup.getRawValue());

    this.formValues.forEach(item => {
      if (item.invoiceDate) {
        item.invoiceDate = moment(item.invoiceDate).format('YYYY-MM-DD');
      }
      if (item.dueDate) {
        item.dueDate = moment(item.dueDate).format('YYYY-MM-DD');
      }
      if (item.poDate) {
        item.poDate = moment(item.poDate).format('YYYY-MM-DD');
      }
      if (item.tdsValue) {
        item.tdsValue = this.parseValue(item.tdsValue)
      }
      if (item.tcsValue) {
        item.tdsValue = this.parseValue(item.tdsValue)
      }
      // You can add more date fields here if needed
    });

    // Check for negative or zero amount values
    const hasNegativeAmount = this.formValues.some(item => item.totalAmount <= 0);

    if (hasNegativeAmount) {
      // Display an error message using a snackbar
      this._snackBar.open('Please ensure that the total amount is greater than 0.', 'Close', {
        duration: 5000, // Adjust the duration as needed
        // verticalPosition: 'top', // Optional CSS class for styling
        panelClass: ['custom-snackbar']
      });
      this.spinnerService.hide();
      this.showSaveSpinner = false;
      return;
    }

    let milestoneType = this.formValues[0].milestoneTypeId;

    if(milestoneType == 6){
      let arCheck = await this.invoiceService.arCheckForCreditNote(this.formValues);
      if(arCheck["messType"] == 'S' && arCheck["data"] == false){
        swal.fire({ title: arCheck["messText"], icon: "info", showConfirmButton: true });
        this.showSaveSpinner = true;
        this.spinnerService.hide();
        return;
      }
      if(arCheck["messType"] == 'E'){
        swal.fire({ title: arCheck["messText"], icon: "info", showConfirmButton: true });
        this.showSaveSpinner = false;
        this.spinnerService.hide();
        return;
      }
    }

    let conversionCheck = await this.invoiceService.currencyConversionCheck(this.formValues)
    if (conversionCheck) {
      if (conversionCheck["messType"] == "C") {
        console.log(conversionCheck["data"].join(' & '))
        this.checkCurrencyConversion = false
        this.spinnerService.hide();
        this.showSaveSpinner = false;
        // swal.fire({ title: "Please add the missing currency conversion rate for " + conversionCheck["fromCurrency"] + " to " + conversionCheck["data"].join(', ') + " for the period " + conversionCheck["invoiceRaisedPeriod"] + " !", icon: "info", showConfirmButton: true });
        swal.fire({
          title: "Please add the missing currency conversion rate for " + conversionCheck["fromCurrency"] + " to " + conversionCheck["data"].join(', ') + " for the period " + conversionCheck["invoiceRaisedPeriod"] + " !",
          icon: 'info',
          showConfirmButton: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.moveToYTB();
          } else if (result.dismiss === swal.DismissReason.cancel) {
            this.moveToYTB();
          }
        });

        return;
      }
      else if (conversionCheck["messType"] == "S") {
        this.checkCurrencyConversion = true
      }
      else if (conversionCheck["messType"] == "E") {
        this.checkCurrencyConversion = false
        this.spinnerService.hide();
        this.showSaveSpinner = false;
        swal.fire({ title: "Currency Conversion Check Failed !", icon: "info", showConfirmButton: true });
        swal.fire({
          title: 'Currency Conversion Check Failed !',
          icon: 'info',
          showConfirmButton: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.moveToYTB();
          } else if (result.dismiss === swal.DismissReason.cancel) {
            this.moveToYTB();
          }
        });
        this.utilityService.showMessage(conversionCheck["messText"], 'dismiss', 3000);
        return;
      }
    }
    else {
      this.checkCurrencyConversion = false
      this.spinnerService.hide();
      this.showSaveSpinner = false;
      // swal.fire({ title: "Currency Conversion Check Failed !", icon: "info", showConfirmButton: true });
      swal.fire({
        title: 'Currency Conversion Check Failed !',
        icon: 'info',
        showConfirmButton: true
      }).then((result) => {
        if (result.isConfirmed) {
          this.moveToYTB();
        } else if (result.dismiss === swal.DismissReason.cancel) {
          this.moveToYTB();
        }
      });
      return;
    }

    if (this.invoiceTenantDetails?.hasOwnProperty('is_to_allow_invoice_simulation') && this.invoiceTenantDetails['is_to_allow_invoice_simulation']) {
      let invoiceIntegrationSimulation = null;
      invoiceIntegrationSimulation = await this.invoiceService.invoiceIntegrationSimulationAPI(this.formValues);
      if(invoiceIntegrationSimulation["messType"] == "E"){
        this.openDialogWithData(invoiceIntegrationSimulation)
      }
      if (invoiceIntegrationSimulation["messType"] == "S") {
          let formValuesWithInvoiceNo = invoiceIntegrationSimulation["data"]
          if(formValuesWithInvoiceNo && formValuesWithInvoiceNo?.length > 0){
            this.annexureDataCreator(formValuesWithInvoiceNo)
          }
          this.spinnerService.hide();
          this.showSaveSpinner = false;
          swal.fire({
            title: 'Milestone Billed successfully!',
            icon: 'success',
            showCancelButton: true,
            confirmButtonText: 'Go to Billed', // Button 1 text
            cancelButtonText: 'Go to YTB', // Button 2 text
            confirmButtonColor: '#cf0001',
            cancelButtonColor: '#cf0001',
            allowOutsideClick: false,
            allowEscapeKey: false
          }).then((result) => {
            if (result.isConfirmed) {
              this.moveToBilled();
            } else if (result.dismiss === swal.DismissReason.cancel) {
              this.moveToYTB();
            }
          });
          // swal.fire({ title: "Milestone Billed successfully !", icon: "success", showConfirmButton: true });
          console.log("invoiceIntegration Success")
      }
      console.log("invoiceIntegrationSimulation")
      console.log(invoiceIntegrationSimulation)
    }
    else {
      let invoiceNo = await this.generateInvoiceNo(this.formValues)
      this.annexureDataCreator(this.formValues)

      if (this.generateInvoice == true && this.checkCurrencyConversion == true) {
        // this.moveToBilled();
        console.log("went inside save details")
        let token: any;
        if (this.storePDFInSharepoint) {
          await this._o365.getToken().then(data => {
            token = data;
          })
        }
        else {
          token = ''
        }

        this.invoiceService.saveInvoiceStepperDetails(this.formValues, token).subscribe(
          res => {
            if (res["messType"] == "S") {
              let swalTemplate = {
                // customClass: {
                //   title: "title-class",
                //   confirmButton: "confirm-button-class"
                // },
                type: "success",
                title: "Milestone Billed successfully"
              }
              this.spinnerService.hide();
              this.showSaveSpinner = false;
              swal.fire({
                title: 'Milestone Billed successfully!',
                icon: 'success',
                showCancelButton: true,
                confirmButtonText: 'Go to Billed', // Button 1 text
                cancelButtonText: 'Go to YTB', // Button 2 text
                confirmButtonColor: '#cf0001',
                cancelButtonColor: '#cf0001',
                allowOutsideClick: false,
                allowEscapeKey: false
              }).then((result) => {
                if (result.isConfirmed) {
                  this.moveToBilled();
                } else if (result.dismiss === swal.DismissReason.cancel) {
                  this.moveToYTB();
                }
              });
              // swal.fire({ title: "Milestone Billed successfully !", icon: "success", showConfirmButton: true });
            }
            else if (res["messType"] == "E") {
              this.spinnerService.hide();
              this.showSaveSpinner = false;
              // swal.fire({ title: "Milestone Billing Failed !", icon: "info", showConfirmButton: true });
              swal.fire({
                title: 'Milestone Billing Failed!',
                icon: 'info',
                showConfirmButton: true
              }).then((result) => {
                if (result.isConfirmed) {
                  this.moveToYTB();
                } else if (result.dismiss === swal.DismissReason.cancel) {
                  this.moveToYTB();
                }
              });

              this.utilityService.showMessage(res["messText"], 'dismiss', 3000);
            }
          },
          err => {
            console.log(err);
            this.spinnerService.hide();
            this.showSaveSpinner = false;
            // swal.fire({ title: "Milestone Billing Failed !", icon: "info", showConfirmButton: true });
            swal.fire({
              title: 'Milestone Billing Failed!',
              icon: 'info',
              showConfirmButton: true
            }).then((result) => {
              if (result.isConfirmed) {
                this.moveToYTB();
              } else if (result.dismiss === swal.DismissReason.cancel) {
                this.moveToYTB();
              }
            });

          }
        );


      }
    }
  }

  moveToBilled() {
    this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/1");
  }

  moveToYTB() {
    this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/0");
  }

  /** 
* @description get Tenant Info
*/
  getTenantInfo() {
    this.tenantService.getTenantInfo().then(async (tenantInfo: any) => {
      let tenantData = tenantInfo;
      let tenant_role = await this.getInvoiceTenantRoleCheckDetail(tenantData.tenant_name, 'Date');
      this.invoiceTenantDetails = tenant_role['data'];
      if (this.invoiceTenantDetails.hasOwnProperty('is_to_show_adapt_template') && this.invoiceTenantDetails['is_to_show_adapt_template'] == 1)
        this.showAdaptTemplate = true
      if (this.invoiceTenantDetails.hasOwnProperty('is_warn_msg_for_t&m_applicable') && this.invoiceTenantDetails['is_warn_msg_for_t&m_applicable'])
        this.showWarningFoTM = true
      if (this.invoiceTenantDetails.hasOwnProperty('restrict_save_on_po_alert') && this.invoiceTenantDetails['restrict_save_on_po_alert'])
        this.restrictSavePoOnAlert = true
      if (this.invoiceTenantDetails.hasOwnProperty('form_control_for_po_alert') && this.invoiceTenantDetails['form_control_for_po_alert'])
        this.formControlForPoAlert = this.invoiceTenantDetails['form_control_for_po_alert']
      if (this.invoiceTenantDetails.hasOwnProperty('is_to_show_milestone_tag') && this.invoiceTenantDetails['is_to_show_milestone_tag'])
        this.isToShowMilestoneTagOption = this.invoiceTenantDetails['is_to_show_milestone_tag']
      if (this.invoiceTenantDetails.hasOwnProperty('is_to_allow_preview_pdf') && this.invoiceTenantDetails['is_to_allow_preview_pdf'])
        this.showPDF = this.invoiceTenantDetails['is_to_allow_preview_pdf']
    },
      err => {
        console.log(err)
      })
  }

  /** 
* @description getInvoiceTenantRoleCheckDetails
*/
  getInvoiceTenantRoleCheckDetail = (tenantName, checkType) => {
    return new Promise((resolve, reject) => {
      this.invoiceCommonService.getInvoiceTenantCheckDetail(tenantName, checkType).subscribe(res => {
        resolve(res);
      },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  };

  onFormValidityChange(index: number, isValid: boolean) {
    this.formValidities[index] = isValid;
  }

  isCurrentStepValid() {
    const currentStepIndex = this.stepper?.selectedIndex;

    // If the current step is not the last step before the "Adapt Template" step
    if (this.data.length === currentStepIndex) {
      return true; // Skip form validity check
    }

    return this.formValidities[currentStepIndex];
  }


  isLaststep(index: number): boolean {
    const lastStepIndex = this.stepper.steps.length - 1;
    return index === lastStepIndex;
  }


  isLastStep(index: number): boolean {
    if (this.data.length > 1) {
      return index === this.data.length - 1; // Show in the first step when data is more than 1
    }
    return true; // Show normally for a single step
  }


  nextStep() {
    this.stepper.next();
  }

  nextStepClicked() {
    this.botService.setFlag(true);
    const currentStepIndex = this.stepper?.selectedIndex;
    if (currentStepIndex < this.data.length - 1)
      this.botService.spinnerService.show();

    const currentStepFormBuilder = this.invoiceFormBuilders.toArray()[currentStepIndex];
    const nextStepFormBuilder = this.invoiceFormBuilders.toArray()[currentStepIndex + 1];
    const currentStepFormData = currentStepFormBuilder.getFormData().value;
    const totalAmount = currentStepFormData.totalAmount;
    const hasNegativeAmount = totalAmount <= 0;

    if (hasNegativeAmount) {
      // Display an error message using a snackbar
      this._snackBar.open('Please ensure that the total amount is greater than zero.', 'Close', {
        duration: 5000, // Adjust the duration as needed
        // verticalPosition: 'top', // Optional CSS class for styling
        panelClass: ['custom-snackbar']
      });
      this.spinnerService.hide();
      this.showSaveSpinner = false;
      return;
    }

    if (currentStepIndex < this.data.length - 1 && currentStepFormBuilder && nextStepFormBuilder) {
      this.transferDataToNextStep(currentStepFormBuilder, nextStepFormBuilder);
    }
    else {
      const currentStepFormData = currentStepFormBuilder.getFormData().value;
      this.adaptData = currentStepFormData

    }

    if (this.isCurrentStepValid()) {
      this.stepper.selected.completed = true;
      this.stepper.selected.editable = false;
      this.stepper.next();
    }

  }



  convertCurrency(projectId, CurrencyId, value) {
    return new Promise((resolve, reject) => {
      this.invoiceService.convertCurrency(projectId, CurrencyId, value).subscribe(
        (res: any) => {
          resolve(res);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  annexureDetails: any
  projectName: any
  annexureDataToBeShared: any
  // function that creates data for annexure
  async annexureDataCreator(value) {
    this.annexureDetails = [];
    let serviceTypeGroupId = value[0]?.serviceTypeGroupId;
    //T&M
    if (serviceTypeGroupId == 1) {
      value.forEach((element, index) => {
        this.annexureDetails.push({
          invoiceDate: moment(element.invoiceDate).format('YYYY-MM-DD'),
          milestoneName: element.milestoneName,
          projectName: element.projectName,
          itemName: element.itemName,
          consultantDetail: element.fteDetails,
          to: element.toCompanyCode,
          from: element.fromCompanyCode,
          totalAmount: element.totalAmount,
          subTotal: element.subTotal,
          selectedCurrency: element.currency,
          unit: element.unit,
          invoiceNo: element.invoiceNo
        })

        this.projectName = element.projectName;
      });
      return new Promise((resolve, reject) => {
        this.invoiceService.getAnnexureDetails(
          this.milestoneId,
          this.projectId,
          this.projectName,
          this.annexureDetails,
          serviceTypeGroupId,
          this.annexureDetails,
          serviceTypeGroupId
        )
          .subscribe(
            (res) => {
              console.log(res);
              this.otherMilestoneDataToPdf = res
              resolve(res)
            },
            (err) => {
              console.error(err);
            }
          );
      })
    }

    // Lumpsum
    if (serviceTypeGroupId == 2) {
      let annexuredata = [];
      value.forEach((element, index) => {

        annexuredata.push({
          invoiceNo: element.invoiceNo,
          invoiceDate: element.invoiceDate,
          projectName: element.projectName,
          itemName: element.itemName,
          currentMilestone: {
            from: element.fromCompanyCode,
            to: element.toCompanyCode,
            milestoneName: element.milestoneName,
            poRef: element.poRef,
            poDate: element.poDate,
            selectedCurrency: element.currency,
            milestoneValue: element.workSummary[0].rate,
            currencyId: element.currencyId,
            placeOfSupply: element.placeOfSupply,
            legalEntityId: element.legalEntityId,
            invoiceNo: element.invoiceNo,
          },
          poValue: element.po_value,
          poValueCurrency: element.currency
        });
      });
      this.annexureDataToBeShared = annexuredata;
      return new Promise((resolve, reject) => {
        this.invoiceService
          .getAnnexureDetails(
            this.milestoneId,
            this.projectId,
            this.projectName,
            this.annexureDataToBeShared,
            serviceTypeGroupId,
            this.annexureDataToBeShared,
            serviceTypeGroupId
          )
          .subscribe(
            (res) => {
              console.log(res);
              this.otherMilestoneDataToPdf = res
              resolve(res)
            },
            (err) => {
              console.error(err);
            }
          );
      })
    }

    //Support
    if (serviceTypeGroupId == 3
    ) {
      value.forEach((element, index) => {
        this.annexureDetails.push({
          invoiceDate: moment(element.invoiceDate).format('YYYY-MM-DD'),
          milestoneName: element.milestoneName,
          projectName: element.projectName,
          itemName: element.itemName,
          to: element.toCompanyCode,
          from: element.fromCompanyCode,
          milestoneValue: element.workSummary[0].amount,
          perHourRate: element.workSummary[0].rate,
          actualWorkingHours: element.workSummary[0]?.quantity || element.workSummary[0]?.hours,
          extraHoursCharged: element.workSummary[0].extraHours,
          total: element.subTotal,
          selectedCurrency: element.currency,
          invoiceNo: element.invoiceNo
        })
      });
      return new Promise((resolve, reject) => {
        this.invoiceService.getAnnexureDetails(
          this.milestoneId,
          this.projectId,
          this.projectName,
          this.annexureDetails,
          serviceTypeGroupId,
          this.annexureDetails,
          serviceTypeGroupId
        )
          .subscribe(
            (res) => {
              this.otherMilestoneDataToPdf = res
              resolve(res)
            },
            (err) => {
              console.error(err);
            }
          );
      })
    }
    if (serviceTypeGroupId == 4
    ) {
      value.forEach((element, index) => {
        this.annexureDetails.push({
          invoiceDate: moment(element.invoiceDate).format('YYYY-MM-DD'),
          milestoneName: element.milestoneName,
          projectName: element.projectName,
          itemName: element.itemName,
          to: element.toCompanyCode,
          from: element.fromCompanyCode,
          total: element.subTotal,
          selectedCurrency: element.currency,
          invoiceNo: element.invoiceNo,
          sublineItem: element.workSummary.map((sublineitemItem) => ({
            amount: sublineitemItem.amount,
            rate: sublineitemItem.rate,
            quantity: sublineitemItem.quantity,
            productServiceType: sublineitemItem.productServiceType,
            description: sublineitemItem.description,
          })),
        })
      });
      return new Promise((resolve, reject) => {
        this.invoiceService.getAnnexureDetails(
          this.milestoneId,
          this.projectId,
          this.projectName,
          this.annexureDetails,
          serviceTypeGroupId,
          this.annexureDetails,
          serviceTypeGroupId
        )
          .subscribe(
            (res) => {
              this.otherMilestoneDataToPdf = res
              resolve(res)
            },
            (err) => {
              console.error(err);
            }
          );
      })
    }

  }

  async generateInvoiceNo(formData) {

    let invoiceNumberArray;
    let getInvoiceNumber: any = [];
    let legalEntityData = [];
    let errorInvoiceNo = 0;

    // Get Legal entity Id to generate invoice no
    _.each(formData, entity => {
      legalEntityData.push(
        {
          legalEntityId: entity.legalEntityId,
          companyCode: entity.fromCompanyCode,
          invoiceDate: moment(entity.invoiceDate).format('YYYY-MM-DD')
        })
    })


    let invoiceType = formData[0]?.invoiceNoType
    if(invoiceType == 'manual'){
      this.generateInvoice = true;
      return;
    }
    if (invoiceType && invoiceType != 'manual') {
      getInvoiceNumber = await this.generateInvoiceNumber(legalEntityData, this.milestoneId, invoiceType)

      // Error in Generating Invoice number
      if (getInvoiceNumber["messType"] == "E") {
        this.generateInvoice = false;
        this.spinnerService.hide();
        this.showSaveSpinner = false;
        this._snackBar.open(getInvoiceNumber["messText"], 'dismiss', { duration: 5000 });
        return;
      }

      // Assign Invoice number based on legal entity on successful response
      if (getInvoiceNumber.length > 0) {
        invoiceNumberArray = getInvoiceNumber;
        _.each(formData, l => {

          let legalEntityId = l.legalEntityId

          if (l.fromCompanyCode == "KQFC")
            legalEntityId = 5

          let getInvoiceNo = _.where(invoiceNumberArray, { entity_id: legalEntityId });
          l.invoiceNo = getInvoiceNo.length > 0 ? getInvoiceNo[0].invoice_number : null;

          if (l.invoiceNo == null)
            errorInvoiceNo = 1;

        })


        // Invoice no missing for some legalentity 
        if (errorInvoiceNo == 1) {
          this.generateInvoice = true;
          return this._snackBar.open('Invoice number has been generated!', 'dismiss', { duration: 1000 });
        }


        // Alert for successful invoice no generation 
        if (invoiceType == 'new' || invoiceType == 'automated' || invoiceType == 'existing') {
          this.generateInvoice = true;
          this._snackBar.open('Invoice number has been generated!', 'dismiss', { duration: 1000 });
        }

      } else // If response returned empty array exception handling 
      {
        this.generateInvoice = false;
        this.spinnerService.hide();
        this.showSaveSpinner = false;
        await this.utilityService.openConfirmationSweetAlertWithCustom('Something went Wrong!', "");
      }
    }

  }

  generateInvoiceNumber(legal_entity_ids, milestone_id, invoiceType) {
    return new Promise((resolve, reject) => {
      this.invoiceService.getInvoiceNumber(legal_entity_ids, milestone_id, invoiceType).subscribe(
        (res) => {
          resolve(res);
        },
        (err) => {
          reject([]);
        }
      );
    })

  }

  async getCurrencyDetails() {
    this.currencyDetails = await this.invoiceService.getCurrencyDetails();
  }

  public isAdaptClicked(data) {
    this.isTemplateApplied = data;
  }

  // Function to determine button visibility
  isMiddleStep(index: number): boolean {
    return index > 0 && index < this.data.length - 1;
  }

  islastStep(index: number): boolean {
    return index === this.data.length - 1;
  }


  showPdfViewer: boolean = false;
  pdfFormValues: any;
  pdfServiceTypeGroupId: any;

  async preview() {

    if (this.isDialogOpen) {
      return; // Exit the function if the dialog is open
    }

    this.isDialogOpen = true;
    this.spinnerService.show();
    try {
    let formGroups = this.invoiceFormBuilders.map(formBuilder => formBuilder.getFormData());
    this.pdfFormValues = formGroups.map(formGroup => formGroup.value);
    this.pdfServiceTypeGroupId = this.pdfFormValues[0]?.serviceTypeGroupId;
    this.billingId = this.pdfFormValues[0]?.billingId;
    await this.annexureDataCreator(this.pdfFormValues)

    if(this.generateAndStorePDFInS3){
      let res: any = await this.invoiceService.generateAndUploadPDFInS3(this.pdfFormValues).toPromise();
      if(res["messType"] == 'S'){
        this.spinnerService.hide();
        // open new component 
      if (this.invoiceTenantDetails?.is_to_allow_preview_pdf === true && res.base64Url){

        if (res.base64Url.startsWith('JVBER')) {
          const base64Data = res.base64Url;
          const byteCharacters = atob(base64Data);
          const byteNumbers = new Array(byteCharacters.length);

          for (let i = 0; i < byteCharacters.length; i++)
            byteNumbers[i] = byteCharacters.charCodeAt(i);

          const byteArray = new Uint8Array(byteNumbers);
          const pdfFile = new Blob([byteArray], { type: 'application/pdf' });
          const fileURL = URL.createObjectURL(pdfFile);
          window.open(fileURL);
              this.isDialogOpen = false;
        } else
         { window.open(res.base64Url, '_blank');
              this.isDialogOpen = false;
            }
          }
        else{
        const dailog = this.dialog.open(DocViewerComponent, {
          width: '58%',
          height: '100%',
          data: {
            selectedFileUrl: res["data"],
            fileFormat: 'PDF',
          },
        });
        dailog.afterClosed().subscribe(() => {
          // Reset the flag when the dialog is closed
          this.isDialogOpen = false;
        });
      }

      }
      else{
        // send error message to kebs team
        this._snackBar.open('Kindly contact KEBS team to resolve', 'Close', {
          duration: 5000
        });
        this.isDialogOpen = false;
        this.spinnerService.hide();
      }
    }
    else{
        this.spinnerService.hide();
      const dialogRef = this.dialog.open(InvoicePdfComponent, {
        width: '100%',
        height: '80%',
        data: {
          serviceTypeGroupId: this.pdfServiceTypeGroupId,
          invoiceInfo: this.pdfFormValues,
          invoiceTenantDetails: this.invoiceTenantDetails,
          usedIn: 'draftPdf',
          otherMilestoneDataToPdf: this.otherMilestoneDataToPdf
        }
      });
      dialogRef.afterClosed().subscribe(() => {
        // Reset the flag when the dialog is closed
        this.isDialogOpen = false;
      });
      }
    } catch (error) {
      // Handle any unexpected errors and reset the flag
      console.error('Error in preview function:', error);
      this._snackBar.open('An error occurred while generating preview', 'Close', {
        duration: 5000
      });
      this.isDialogOpen = false;
      this.spinnerService.hide();
    }

  }

  previousStepClicked() {
    // this.stepper.selected.completed = false;
    const currentStepIndex = this.stepper?.selectedIndex;
    if (currentStepIndex > 0) {
      const previousStepIndex = currentStepIndex - 1;
      const currentStepFormBuilder = this.invoiceFormBuilders.toArray()[previousStepIndex];
      this.invoiceForm = currentStepFormBuilder.getFormData();
      const currentStepFormData = currentStepFormBuilder.getFormData().value;
      this.invoiceFormValue = currentStepFormData
    }
    this.setStepCompletion(currentStepIndex - 1, true)
    this.setEditable(currentStepIndex - 1, true)
    this.stepper.previous()
    this.setStepCompletion(currentStepIndex - 1, false)
    // this.setEditable(currentStepIndex-1,true)
    this.setValidInvalid()
  }
  fixNumberOnUI(no, currency) {
    if (no == 0 || no == '0' || no == '') {
      return '0.00';
    }
    let num = parseFloat(no);
    if (currency == '' || currency == null || currency == undefined)
      currency = 'INR'
    if (currency == "INR")
      return new Intl.NumberFormat('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num));
    else
      return new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num));
  }

  setStepCompletion(stepIndex: number, completed: boolean) {

    this.stepper.steps.toArray()[stepIndex].completed = completed;

  }

  setEditable(stepIndex: number, completed: boolean) {

    this.stepper.steps.toArray()[stepIndex].editable = completed;

  }

  parseValue(input: string | number): number {
    if (typeof input === 'string') {
      const sanitizedInput = input.trim(); // Remove leading/trailing spaces
      return sanitizedInput === '' ? 0.00 : parseFloat(sanitizedInput.replace(/,/g, ''));
      // return parseFloat(input.replace(/,/g, ''));
    } else if (typeof input === 'number') {
      return input;
    } else {
      return 0.00;
    }
  }

  formatNumberForDisplay(number, currency) {
    // Check if the number should be displayed in exponential notation
    let no = this.parseValue(number)
    if (Math.abs(no) >= 1e+12) {
      return no.toExponential();
    } else {
      // return no.toString();
      return this.fixNumberOnUI(no, currency)
    }
  }

  getPoValue(itemId, milestoneId) {
    return new Promise((resolve, reject) => {
      this.invoiceService
        .getPoValues(itemId, milestoneId).subscribe(
          (res: any) => {
            console.log(res)
            console.log("po res")
            resolve(res);
          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });
  }


  async getInvoiceConfig(){
    let invoiceConfig = await this.invoiceService.getInvoiceConfig();
    if(invoiceConfig['data'][0] && invoiceConfig['data'][0].config!=null){
      let invoiceConfigDetails = JSON.parse(invoiceConfig['data'][0].config);
      this.storePDFInSharepoint = invoiceConfigDetails.hasOwnProperty('store_pdf_in_sharepoint') && invoiceConfigDetails['store_pdf_in_sharepoint'] == 1 ? true : false;
      this.generateAndStorePDFInS3 = invoiceConfigDetails.hasOwnProperty('generate_store_pdf_in_s3') && invoiceConfigDetails['generate_store_pdf_in_s3'] ? invoiceConfigDetails['generate_store_pdf_in_s3'] : false;
    }   
  }

  getTenantDateFormats() {
    return new Promise((resolve, reject) => {
      this.invoiceCommonService.getTenantDateFormats().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          resolve(err);
        }
      );
    });
  }

  openDialogWithData(invoiceIntegrationSimulation: any) {
    this.spinnerService.hide();
    this.showSaveSpinner= false;
    let saveError = null;
    if(invoiceIntegrationSimulation && invoiceIntegrationSimulation["type"] == 'save'){
      saveError = invoiceIntegrationSimulation
    }
    const dialogRef = this.dialog.open(IntegrationSimulationComponent, {
      data: {
        simulationResults: invoiceIntegrationSimulation ? invoiceIntegrationSimulation["error"] : null,
        formValues: this.formValues,
        milestoneId: this.milestoneId,
        type: invoiceIntegrationSimulation ? invoiceIntegrationSimulation["type"] : null,
        saveError: saveError
      }
    });

    dialogRef.afterClosed().subscribe(async (result) => {
      if (result === 'submit') {
        // Re-trigger the same API call and reopen the dialog
        this.spinnerService.show();
        let updatedSimulationResults = await this.invoiceService.invoiceIntegrationSimulationAPI(this.formValues);
        this.spinnerService.hide();
        this.showSaveSpinner = false;
        if (updatedSimulationResults["messType"] === "E") {
          this.openDialogWithData(updatedSimulationResults);
        }
        if (updatedSimulationResults["messType"] == "S") {
          let formValuesWithInvoiceNo = updatedSimulationResults["data"]
          if(formValuesWithInvoiceNo && formValuesWithInvoiceNo?.length > 0){
            this.annexureDataCreator(formValuesWithInvoiceNo)
          }
          this.spinnerService.hide();
          this.showSaveSpinner = false;
          swal.fire({
            title: 'Milestone Billed successfully!',
            icon: 'success',
            showCancelButton: true,
            confirmButtonText: 'Go to Billed', // Button 1 text
            cancelButtonText: 'Go to YTB', // Button 2 text
            confirmButtonColor: '#cf0001',
            cancelButtonColor: '#cf0001',
            allowOutsideClick: false,
            allowEscapeKey: false
          }).then((result) => {
            if (result.isConfirmed) {
              this.moveToBilled();
            } else if (result.dismiss === swal.DismissReason.cancel) {
              this.moveToYTB();
            }
          });
          // swal.fire({ title: "Milestone Billed successfully !", icon: "success", showConfirmButton: true });
          console.log("invoiceIntegration Success")
        }
        
      }
      else{
        this.spinnerService.hide();
        this.showSaveSpinner = false;
      }
    });
  }


  getMilestoneDetailsForCredit(milestoneId, itemId) {
    return new Promise((resolve, reject) => {
      this.botService.getMilestoneDetailsForCredit(milestoneId, itemId).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });

  }

  addNotes() {
    this.commentsInput.application_id = 915;
    this.commentsInput.application_name = "Invoices";
    this.commentsInput.title = this.data[0].milestone_name;
    this.commentsInput.unique_id_1 = this.data[0].milestoneId;
    let invoice_date_format = this.dateFormats?.invoice_general_date_format ? this.dateFormats?.invoice_general_date_format : "DD-MM-YYYY"
    let modalParams = {
      inputData: this.commentsInput,
      context: {
        'Milestone Name': this.data[0].milestone_name,
        'Milestone Start Date': moment(this.data[0]?.planned_start_date).utc().format(invoice_date_format),
        'Milestone End Date': moment(this.data[0]?.planned_end_date).utc().format(invoice_date_format),
        'Milestone Status': this.data[0]?.milestone_status_name
      },
      commentBoxHeight: '100vh',
      commentBoxScrollHeight: '80%'
    };
    const openChatCommentContextModalComponent = this.dialog.open(ChatCommentContextModalComponent, {
      height: '100%',
      width: '75%',
      position: { right: '0px' },
      data: { modalParams: modalParams },
      disableClose: true
    });

    openChatCommentContextModalComponent.afterClosed().subscribe(async (res) => {

      let comments = await this.invoiceService.getCommentStatus(this.data[0]?.milestoneId);
      if(comments && comments["messType"] == 'S'){
        this.isCommentPresent = comments["data"]
      }
      else{
        this.isCommentPresent = false
      }

    })
  }


}

