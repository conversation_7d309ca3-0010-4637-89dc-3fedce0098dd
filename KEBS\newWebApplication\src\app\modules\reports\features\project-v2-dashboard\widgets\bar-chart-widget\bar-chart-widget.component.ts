import { Component, Input, OnInit, ViewContainerRef, TemplateRef, ViewChild } from '@angular/core';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DecimalPipe } from '@angular/common';
import { Overlay, OverlayConfig, OverlayRef } from "@angular/cdk/overlay";
import { TemplatePortal } from "@angular/cdk/portal";
import { SummaryDialogComponent } from 'src/app/modules/reports/features/project-v2-dashboard/widgets/summary-dialog/summary-dialog.component';
import { WidgetsService } from './../../services/widgets/widgets.service';
import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';
import { MatDialog } from '@angular/material/dialog';
@Component({
  selector: 'app-bar-chart-widget',
  templateUrl: './bar-chart-widget.component.html',
  styleUrls: ['./bar-chart-widget.component.scss'],
  providers:[DecimalPipe]
})
export class BarChartWidgetComponent implements OnInit {

  @Input() aid: any = null;
  @Input() oid: any = null;
  @Input() widgetConfig: any = {};
  @Input() widgetType: string = '';
  @Input() startDate: any = null;
  @Input() endDate: any = null;
  @Input() filterData: any = null;
  @Input() filterQuery: any = '';
  protected _onDestroy = new Subject<void>();

  calculatedWidgetHeight: string = '';
  calculatedChartWidgetHeight: number = null;
  seriesData: any;
  chartVisualRange: any;
  countLabel: any;
  isLoading: boolean = true;
summaryData: any = null; // Add this in the class
  private overlayRef: OverlayRef | null;

  @ViewChild("triggerInlineFilterTemplateRef", { static: false })
  triggerInlineFilterTemplateRef!: TemplateRef<HTMLElement>;

  data = [];
  constructor(
    private _widgetService: WidgetsService,
    private _toaster: ToasterService,
    private _decimalPipe: DecimalPipe,
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
    private _dialog: MatDialog
  ) { }

  async ngOnInit() {
    this.calculateHeight();
    await this.getChartData();
    this.chartVisualRange = await this.calculateChartVisualRange()
    this.isLoading = false;
  }

  /**
* @description To calculate height dynamically
*/
  calculateHeight() {
    if (this.widgetConfig && this.widgetConfig?.height) {
      this.calculatedWidgetHeight = `calc(${this.widgetConfig.height} - 75px)`;
      this.calculatedChartWidgetHeight =
        parseInt(this.widgetConfig.height) - (this.widgetConfig.reduce_chart_height ? this.widgetConfig.reduce_chart_height : 150);
      console.log("calculatedChartWidgetHeight", this.calculatedChartWidgetHeight)
    }
  }

  /**
* @description Get list view menu data
*/
  async getChartData() {
    if (
      !this.widgetConfig?.widget_config?.api ||
      this.widgetConfig?.widget_config?.api == ''
    ) {
      return;
    }

    let apiUrl = this.widgetConfig?.widget_config?.api;
    let payload = {
      aid: this.aid,
      oid: this.oid,
      startDate: this.startDate,
      endDate: this.endDate,
      inlineFilterData: this.widgetConfig?.widget_config?.inline_filter ? this.widgetConfig?.widget_config?.inline_filter : [],
      filterData: this.filterData,
      filterQuery: this.filterQuery
    };

    this.data = [];
    this.isLoading = true;

    return new Promise((resolve, reject) => {
      this._widgetService
        .getDataDynamically(apiUrl, payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              this.data =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
              this.seriesData =
                res[this.widgetConfig?.widget_config?.value_series_key] && res[this.widgetConfig?.widget_config?.value_series_key].length > 0 ? res[this.widgetConfig?.widget_config?.value_series_key] : [];
              this.countLabel =
                res['countLabel'] ? res['countLabel'] : ''
                this.summaryData = res['summary_data'] || null;
              if(this.widgetConfig?.widget_config?.scroll_bar_enabled && res['data'].length < 7)
                this.widgetConfig.widget_config.scroll_bar_enabled = false
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            this.isLoading = false;
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching Widget Data',
              7000
            );
            this.isLoading = false;
            reject();
          },
        });
    });
  }

  customizeLabel = ({ value }: any) => {
    return {
      visible: true,
      backgroundColor: '#ffffff',
      font: {
        color: '#5F6C81',
      },
      customizeText: this.customizeText,
    };
  };
  
  customizeText = ({ valueText }: any) => {
    return valueText;
  };

  getDefaultFilterLabel(filter: any): string {
    if (!filter || !filter.filter_data || !filter.default_filter_id) {
      return '';
    }
  
    const defaultFilter = filter.filter_data.find(
      (item: any) => item.id === filter.default_filter_id
    );
    return defaultFilter ? defaultFilter.label : '';
  }
openSummaryForm() {
  if (this.summaryData) {
    this._dialog.open(SummaryDialogComponent, {
      width: '90vw',
      data: {
        widgetData: this.summaryData
      }
    });
  } else {
    this._toaster.showWarning("Warning ⚠️", "Summary data is not available", 7000);
  }
}

  openFilterOverlay(event: MouseEvent, filter: any) {
    event.stopPropagation();

    // Close the overlay if it is already open
    if (this.overlayRef) {
      this.closeOverlay();
    } else {
      const target = event.currentTarget as HTMLElement;

      // Create overlay config
      const config = new OverlayConfig({
        hasBackdrop: true,
        backdropClass: "cdk-overlay-transparent-backdrop",
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(target)
          .withPositions([
            {
              originX: "end",
              originY: "bottom",
              overlayX: "end",
              overlayY: "top",
            },
          ]),
      });

      // Create overlay reference
      this.overlayRef = this.overlay.create(config);

      // Attach the template to the overlay
      this.overlayRef.attach(
        new TemplatePortal(this.triggerInlineFilterTemplateRef, this.viewContainerRef, {
          $implicit: filter // Ensure 'filter' is of the correct type
        } as any) // Optionally, use 'as any' if the type is complex or not directly inferred
      );

      // Close overlay on backdrop click
      this.overlayRef.backdropClick().subscribe(() => this.closeOverlay());
    }
  }

  closeOverlay() {
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
    }
  }

  async updateFilterData(filter_id, filter){
    filter.default_filter_id = filter_id;
    this.closeOverlay();
    await this.getChartData();
  }

    /**
   * @description calculate the visual range based on data
   */

    async calculateChartVisualRange(){
      let minCategories = this.widgetConfig?.widget_config?.min_Categories ? this.widgetConfig?.widget_config?.min_Categories : 10
      const categories = this.data.map(item => item[this.widgetConfig?.widget_config?.argument_field]);
  
      // If the number of categories is less than the minimum threshold (4 in this case), return the first and last categories
      if (categories.length < minCategories) {
        return [categories[0], categories[categories.length - 1]];
      }
    
      // Otherwise, return the first and last categories as the range
      return [categories[0], categories[minCategories-1]]; 
    }
  
}
